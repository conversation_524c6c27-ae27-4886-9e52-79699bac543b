<template>
	<view class="test-page">
		<view class="header">
			<text class="title">优化功能测试</text>
		</view>
		
		<!-- 搜索优化测试 -->
		<view class="test-section">
			<text class="section-title">🔍 搜索优化测试</text>
			<view class="test-item">
				<input 
					v-model="searchKeyword"
					@input="handleSearchInput"
					placeholder="输入关键词测试搜索优化"
					class="search-input"
				/>
				<button @click="testSearch" class="test-btn">测试搜索</button>
			</view>
			<view class="test-result">
				<text class="result-title">搜索建议：</text>
				<view class="suggestions">
					<text 
						class="suggestion-tag"
						v-for="(item, index) in suggestions"
						:key="index"
						@click="selectSuggestion(item)"
					>
						{{item}}
					</text>
				</view>
			</view>
			<view class="test-result" v-if="searchResults.length > 0">
				<text class="result-title">搜索结果 ({{searchResults.length}}条)：</text>
				<view class="result-list">
					<view 
						class="result-item"
						v-for="(item, index) in searchResults.slice(0, 3)"
						:key="index"
					>
						<text class="result-text">{{item.title || item}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 视频优化测试 -->
		<view class="test-section">
			<text class="section-title">🎬 视频播放优化测试</text>
			<view class="test-item">
				<button @click="testVideoOptimizer" class="test-btn">测试视频优化器</button>
				<button @click="testVideoPreload" class="test-btn">测试视频预加载</button>
			</view>
			<view class="test-result">
				<text class="result-title">优化器状态：</text>
				<text class="result-text">{{videoOptimizerStatus}}</text>
			</view>
			<view class="test-result">
				<text class="result-title">推荐质量：</text>
				<text class="result-text">{{recommendedQuality}}</text>
			</view>
		</view>
		
		<!-- 性能监控测试 -->
		<view class="test-section">
			<text class="section-title">📊 性能监控测试</text>
			<view class="test-item">
				<button @click="testPerformanceMonitor" class="test-btn">开始性能监控</button>
				<button @click="getPerformanceReport" class="test-btn">获取性能报告</button>
			</view>
			<view class="test-result">
				<text class="result-title">性能数据：</text>
				<text class="result-text">{{performanceData}}</text>
			</view>
		</view>
		
		<!-- 错误处理测试 -->
		<view class="test-section">
			<text class="section-title">🛡️ 错误处理测试</text>
			<view class="test-item">
				<button @click="testErrorHandler" class="test-btn">测试错误处理</button>
				<button @click="testNetworkError" class="test-btn">测试网络错误</button>
			</view>
			<view class="test-result">
				<text class="result-title">错误处理结果：</text>
				<text class="result-text">{{errorHandlerResult}}</text>
			</view>
		</view>
		
		<!-- 内存管理测试 -->
		<view class="test-section">
			<text class="section-title">💾 内存管理测试</text>
			<view class="test-item">
				<button @click="testMemoryManagement" class="test-btn">测试内存管理</button>
				<button @click="checkMemoryUsage" class="test-btn">检查内存使用</button>
			</view>
			<view class="test-result">
				<text class="result-title">内存状态：</text>
				<text class="result-text">{{memoryStatus}}</text>
			</view>
		</view>
	</view>
</template>

<script>
import { SearchOptimizer } from '@/utils/search-optimizer.js';
import { VideoOptimizer } from '@/utils/video-optimizer.js';
import Performance from '@/utils/performance.js';
import ErrorHandler from '@/utils/error-handler.js';
import cleanup from '@/mixins/cleanup.js';

export default {
	mixins: [cleanup],
	data() {
		return {
			searchKeyword: '',
			suggestions: [],
			searchResults: [],
			videoOptimizerStatus: '未初始化',
			recommendedQuality: '未检测',
			performanceData: '暂无数据',
			errorHandlerResult: '暂无测试',
			memoryStatus: '正常',
			
			searchOptimizer: null,
			videoOptimizer: null
		};
	},
	onLoad() {
		this.initOptimizers();
	},
	methods: {
		// 初始化优化器
		initOptimizers() {
			this.searchOptimizer = new SearchOptimizer();
			this.videoOptimizer = new VideoOptimizer();
			this.videoOptimizerStatus = '已初始化';
			this.recommendedQuality = this.videoOptimizer.getOptimalQuality();
		},
		
		// 搜索输入处理
		handleSearchInput() {
			if (this.searchOptimizer) {
				this.suggestions = this.searchOptimizer.getSuggestions(this.searchKeyword);
			}
		},
		
		// 测试搜索
		testSearch() {
			if (!this.searchOptimizer || !this.searchKeyword) return;
			
			this.searchOptimizer.debounceSearch(this.searchKeyword, (result) => {
				if (result.error) {
					this.searchResults = ['搜索失败: ' + result.message];
				} else {
					this.searchResults = result.results || result || ['模拟搜索结果1', '模拟搜索结果2', '模拟搜索结果3'];
				}
			});
		},
		
		// 选择搜索建议
		selectSuggestion(suggestion) {
			this.searchKeyword = suggestion;
			this.testSearch();
		},
		
		// 测试视频优化器
		testVideoOptimizer() {
			if (!this.videoOptimizer) return;
			
			// 测试质量选择
			const quality = this.videoOptimizer.getOptimalQuality();
			this.recommendedQuality = quality;
			
			// 测试进度保存
			this.videoOptimizer.batchSaveProgress('test_video_123', 120, 300);
			
			this.videoOptimizerStatus = '测试完成 - 质量: ' + quality;
		},
		
		// 测试视频预加载
		testVideoPreload() {
			if (!this.videoOptimizer) return;
			
			this.videoOptimizer.preloadVideo('https://example.com/video1.mp4', 'high');
			this.videoOptimizer.preloadVideo('https://example.com/video2.mp4', 'medium');
			
			this.videoOptimizerStatus = '预加载测试完成';
		},
		
		// 测试性能监控
		testPerformanceMonitor() {
			Performance.mark('test-operation');
			
			// 模拟一些操作
			this.$setTimeout(() => {
				const duration = Performance.measure('test-operation');
				this.performanceData = `操作耗时: ${duration}ms`;
			}, 100);
		},
		
		// 获取性能报告
		getPerformanceReport() {
			const report = Performance.getPerformanceReport();
			this.performanceData = JSON.stringify(report, null, 2);
		},
		
		// 测试错误处理
		testErrorHandler() {
			try {
				// 模拟一个错误
				throw new Error('这是一个测试错误');
			} catch (error) {
				ErrorHandler.handle(error, '优化测试页面');
				this.errorHandlerResult = '错误已被正确处理';
			}
		},
		
		// 测试网络错误
		testNetworkError() {
			const networkError = {
				errMsg: 'request:fail',
				statusCode: 500
			};
			
			ErrorHandler.handle(networkError, '网络请求测试');
			this.errorHandlerResult = '网络错误已被正确处理';
		},
		
		// 测试内存管理
		testMemoryManagement() {
			// 创建一些定时器测试自动清理
			this.$setTimeout(() => {
				console.log('定时器1执行');
			}, 1000);
			
			this.$setInterval(() => {
				console.log('定时器2执行');
			}, 2000);
			
			this.memoryStatus = '已创建测试定时器，页面销毁时会自动清理';
		},
		
		// 检查内存使用
		checkMemoryUsage() {
			if (this.videoOptimizer) {
				const stats = this.videoOptimizer.getPlaybackStats();
				this.memoryStatus = `播放统计: ${JSON.stringify(stats)}`;
			}
		}
	}
};
</script>

<style scoped>
.test-page {
	padding: 30rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.test-item {
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.search-input {
	flex: 1;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	font-size: 28rpx;
}

.test-btn {
	padding: 20rpx 30rpx;
	background: #2094CE;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	font-size: 26rpx;
}

.test-result {
	margin-bottom: 20rpx;
}

.result-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.result-text {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
}

.suggestions {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.suggestion-tag {
	background: #e9ecef;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #495057;
}

.result-list {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 20rpx;
}

.result-item {
	padding: 10rpx 0;
	border-bottom: 1rpx solid #e9ecef;
}

.result-item:last-child {
	border-bottom: none;
}
</style>
