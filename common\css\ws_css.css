/* Logo 字体 */
	@font-face {
	  font-family: 'iconfont';
	    src: url('@/static/icon/iconfont.eot');
	    src: url('@/static/icon/iconfont.eot?#iefix') format('embedded-opentype'),
	        url('@/static/icon/iconfont.woff2') format('woff2'),
	        url('@/static/icon/iconfont.woff') format('woff'),
	        url('@/static/icon/iconfont.ttf') format('truetype'),
	        url('@/static/icon/iconfont.svg#iconfont') format('svg');
	}
	.ws-icon{
		font-family: "iconfont"!important;
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
.ws-flex{
	display: flex;
	align-items: center;
}
.ws-padding{
	padding: 30rpx;
}
.ws-text-df{
	font-size: 28rpx;
}
.ws-text-red{
	color: red;
}
.ws-text-blue{
	color: blue;
}
.ws-text-center{
	text-align: center;
}
.ws-flex-between{
	display: flex;
	align-items: center;
	justify-content: space-between;
}

/**
 * flex
 */
.lp-flex {
	display: flex;
	flex-direction: row;
}

.lp-flex-column {
	display: flex;
	flex-direction: column;
}

.lp-flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.lp-flex-space-between{
	justify-content: space-between;
}