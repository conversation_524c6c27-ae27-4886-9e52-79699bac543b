(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["GraceUI5/components/gui-header-leading"],{"10cf":function(e,t,n){"use strict";var a=n("16a6"),i=n.n(a);i.a},1468:function(e,t,n){"use strict";n.r(t);var a=n("ad15"),i=n("775c");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("10cf");var u=n("828b"),c=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,"159673e5",null,!1,a["a"],void 0);t["default"]=c.exports},"16a6":function(e,t,n){},"619d":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"gui-header-leading",props:{homePage:{type:String,default:"/pages/index/index"},bgStyle:{type:String,default:""},buttonStyle:{type:String,default:""},onlyBack:{type:Boolean,default:!1},onlyHome:{type:Boolean,default:!1}},methods:{goback:function(){e.navigateBack({delta:1}),this.$emit("goback")},gohome:function(){""!=this.homePage&&e.switchTab({url:this.homePage}),this.$emit("gohome")}}};t.default=n}).call(this,n("df3c")["default"])},"775c":function(e,t,n){"use strict";n.r(t);var a=n("619d"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},ad15:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'GraceUI5/components/gui-header-leading-create-component',
    {
        'GraceUI5/components/gui-header-leading-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1468"))
        })
    },
    [['GraceUI5/components/gui-header-leading-create-component']]
]);
