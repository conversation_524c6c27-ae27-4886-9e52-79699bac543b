<gui-page class="root-box data-v-b90080f4" vue-id="670e9b80-1" fullPage="{{true}}" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-margin content-box data-v-b90080f4" slot="gBody"><view class="gui-align-items-center gui-rows gui-nowrap gui-flex data-v-b90080f4"><view class="gui-header-content nav-box data-v-b90080f4"><view data-event-opts="{{[['tap',[['onNavChangeHandler',[0]]]]]}}" class="{{['nav-btn','data-v-b90080f4',(currentIndex==0)?'active':'']}}" bindtap="__e">原文</view><view data-event-opts="{{[['tap',[['onNavChangeHandler',[1]]]]]}}" class="{{['nav-btn','data-v-b90080f4',(currentIndex==1)?'active':'']}}" bindtap="__e">译文</view></view></view><swiper class="tab-card-body swiper data-v-b90080f4" current="{{currentIndex}}" data-event-opts="{{[['change',[['onSwiperChangeHandler',['$event']]]]]}}" bindchange="__e"><swiper-item class="tab-card-item data-v-b90080f4"><lp-original vue-id="{{('670e9b80-2')+','+('670e9b80-1')}}" article_id="{{article_id}}" class="data-v-b90080f4" bind:__l="__l"></lp-original></swiper-item><swiper-item class="tab-card-item data-v-b90080f4"><lp-original vue-id="{{('670e9b80-3')+','+('670e9b80-1')}}" article_id="{{article_id}}" type="translation" class="data-v-b90080f4" bind:__l="__l"></lp-original></swiper-item></swiper></view></gui-page>