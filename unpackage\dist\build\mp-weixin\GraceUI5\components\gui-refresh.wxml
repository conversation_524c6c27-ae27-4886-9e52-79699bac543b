<view class="{{['gui-page-refresh','gui-flex','gui-rows','gui-justify-content-center','gui-align-items-center','data-v-19da84aa',refreshStatus==3?'gload-hide':'']}}" style="{{'height:'+(refreshHeight+'px')+';'+('background-color:'+(refreshBgColor[refreshStatus])+';')}}"><block wx:if="{{refreshStatus==0||refreshStatus==1}}"><text class="gui-page-refresh-icon gui-icons gui-block-text data-v-19da84aa" style="{{'font-size:'+(refreshFontSize)+';'+('color:'+(refreshColor[refreshStatus])+';')}}"></text></block><block wx:if="{{refreshStatus==2}}"><view data-ref="loadingIcon" class="gui-page-refresh-icon data-v-19da84aa vue-ref"><text class="gui-icons gui-rotate360 gui-block-text data-v-19da84aa" style="{{'font-size:'+(refreshFontSize)+';'+('color:'+(refreshColor[refreshStatus])+';')}}"></text></view></block><block wx:if="{{refreshStatus==3}}"><text class="gui-page-refresh-icon gui-icons data-v-19da84aa" style="{{'font-size:'+(refreshFontSize)+';'+('color:'+(refreshColor[refreshStatus])+';')}}"></text></block><text class="gui-page-refresh-text gui-block-text data-v-19da84aa" style="{{'font-size:'+(refreshFontSize)+';'+('color:'+(refreshColor[refreshStatus])+';')}}">{{refreshText[refreshStatus]}}</text></view>