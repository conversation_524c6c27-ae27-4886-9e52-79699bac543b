<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">

		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 头部轮播 -->
			<view class="carousel-section">
				<swiper class="carousel" circular @change="swiperChange" autoplay="true">
					<swiper-item v-for="(item, index) in carouselList" :key="index" class="carousel-item"
						@click="navToWeb(item)">
						<image :src="item.thumb" />
					</swiper-item>
				</swiper>
				<!-- 自定义swiper指示器 -->
				<view class="swiper-dots">
					<text class="num">{{swiperCurrent+1}}</text>
					<text class="sign">/</text>
					<text class="num">{{swiperLength}}</text>
				</view>
			</view>
			<!-- 公告 -->
			<view class="announce">
				<view class="announce-title">
					<image src="/static/notice.png"></image>
				</view>
				<view class="announce-item">
					<swiper class="announce-swiper" :autoplay="true" :vertical="true" :circular="true">
						<swiper-item v-for="(item, index) in iconList" @click="openAnnouncement(item.id)" :key="index">
							<view class="announce-swiper-item">{{item.name}}</view>
						</swiper-item>
					</swiper>
				</view>
			</view>
			<!--分类-->
			<swiper bind:change="handleChange" class="swiper-container-large" previousMargin="6rpx">
				<swiper-item class="swiper-item" data-index="index" >
					<view bind:tap="handleTap" class="srv-item-large" v-for="(item,index) in iconList"
						:key="index" @tap="navTo(item,'link')">
						<image class="srv-item-icon-large" :src="item.icon"></image>
						<text class="srv-item-title nowrap">{{item.name}}</text>
					</view>
				</swiper-item>
			</swiper>
			<!-- 分类 -->
			<view class="cate-section">
				<view class="cate-item" v-for="(item, index) in iconList" :key="index" @tap="toCate(item)">
					<image :src="item.icon"></image>
					<text>{{item.name}}</text>
				</view>
			</view>
			
			<!-- 秒杀楼层 -->
			<view class="seckill-section m-t">
				<view class="s-header">
					<text class="title">最新课程</text>
					<text class="yticon icon-you"></text>
				</view>
				<view>
					<view  v-for="(item, index) in newList" :key="index" 
					@click="navToDetailPage(item)" style="border-bottom: 1px solid #eee;"
						>
							<view style="display: flex;padding: 5rpx;">
								<view style="width:76%;color: #345292;
    display: inline-block;overflow: hidden;
					word-break: break-all;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;">{{item.ename}}</view>
								<view style="text-align: right;width: 23%;">
                                   <view>{{item.start_time}}</view>
								</view>
							</view>
					</view>
				</view>
			</view>
			<view v-for="(item, index) in goodsList" :key="index" >
				<view class="f-header m-t" @tap="toCate(item)">
					<!-- <image src="/static/temp/h1.png"></image> -->
					<view class="tit-box">
						<text class="tit">{{item.name}}</text>
						<!-- <text class="tit2">Guess You Like It</text> -->
					</view>
					<text class="yticon icon-you" ></text>
				</view>
				
				<view class="list-box">
					<view class="item-box lp-flex-column" v-for="(item1, index1) in item.list" :key="index1"
					@click="navToDetailPage(item1)"
						>
						<view class="top-box lp-flex">
							<view class="cover-box lp-flex-center" v-if="item1.status==1">
								<image class="cover" :src="item1.picture"></image>
								<view class="button"  style="background-color:#333;">{{item1.status_text}}</view>
							</view>
							<view class="cover-box lp-flex-center" v-else>
								<image class="cover" :src="item1.picture"></image>
								<view class="button"  >{{item1.status_text}}</view>
							</view>
							<view class="info-box lp-flex-column">
								<view class="title">{{item1.name}}</view>
								<view class="des">{{item1.des}}</view>
								<view class="end"><text style="text-align: right;float: right;">更多</text></view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			
			
		</view>
	</gui-page>
</template>
<script>
	var graceJs = require('@/GraceUI5/js/grace.js');
	// 模拟 api 请求数据，格式见 article.js
	var artciles = require('@/GraceUI5/demoData/article.js');
	import u_button from '@/components/uview-ui/components/u-button/u-button.vue';
	export default {
		data() {
			return {
				name: "",
				userInfo: {},
				ruleInfo: {},
				news: [], // 新闻推荐
				swiperCurrent: 0,
				swiperLength: 3,
				carouselList: [{
						src: "/static/temp/banner3.jpg",
						background: "rgb(203, 87, 60)",
					},
					{
						src: "/static/temp/banner2.jpg",
						background: "rgb(205, 215, 218)",
					},
					{
						src: "/static/temp/banner4.jpg",
						background: "rgb(183, 73, 69)",
					}
				],
				goodsList: [
				],
				iconList:[],
				newList:[]
			}
		},
		onLoad: function() {
			wx.showShareMenu({
					withShareTicket:true,
					menus:["shareAppMessage","shareTimeline"]
				})
			this.getNews();
			
		},
		onShareAppMessage() {
					return {
						title: "日语研修",//标题
						imageUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/shixilogo.jpg",//封面
						path: "/pages/index/index"//此处链接为要分享的页面链接	
					};
			},
			// 分享到朋友圈
			onShareTimeline() {
				return {
					title: "日语研修",//标题
					imageUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/shixilogo.jpg",//封面
					path: "/pages/index/index"//此处链接为要分享的页面链接	
				};
			},
		methods: {
			// 获取新闻推荐
			getNews() {
				this.$http.get("v1/index").then(res => {
					console.log(res);
					if (res.data.code == 0) {
						this.goodsList = res.data.data.list
						this.carouselList = res.data.data.banner.banner
						this.swiperLength = res.data.data.banner.banner.length
						this.iconList = res.data.data.projectType;
						this.newList = res.data.data.new;
					} 
				});
				// uni.request({
				// 	url: 'https://mini.jpworld.cn/api/v1/index', //仅为示例，并非真实接口地址。
				// 	header: {
				// 		'custom-header': 'hello' //自定义请求头信息
				// 	},
				// 	success: (res) => {
				// 		console.log(res.data);
				// 		this.carouselList = res.data.data.banner;
				// 	}
				// });
			},
			navToWeb(item) {
				switch(item.type){
					case "web":			// 项目外部跳转，需要使用web-view跳转外部H5页面
						uni.navigateTo({
							url:"/pages/webView/webView?data="+encodeURIComponent(JSON.stringify(item))
						})
						break;
					case "mini_app":	// 项目内部跳转
						uni.navigateTo({
							url:item.link
						})
						break;
					case "popu":		// 当前页内部弹窗，不跳转
						this.module = "show";
						this.moduleTitle = item.title;
						this.moduleContent = item.description;
						break;
				}
				return;
			},
			//轮播图切换修改背景色
			swiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
			},
			//详情页
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.id;
				uni.navigateTo({
					url: `/pages/product/product?id=${id}`
				})
			},
			toCate(item) {
				let id = item.id;
				let title = item.name;
				let path = item.path;
				// if(path == null || path == ''){
				// 	uni.navigateTo({
				// 		url: `/pages/category/list?id=${id}&title=${title}`,
				// 		})
				// }else{
				// 	uni.navigateTo({
				// 		url: `${path}?id=${id}&title=${title}`,
				// 		})
				// }
				if(id==4){
					uni.navigateTo({
						url: `/pages/course/list?id=${id}&title=${title}`,
						})
				}else{
					uni.navigateTo({
						url: `/pages/category/list?id=${id}&title=${title}`,
						})
				}
				
			},
		}
	}
</script>
<style lang="scss">
	.file-title {
		width: 75%;
		border: 1px solid #E1E1E1;
		border-radius: 20rpx;
		margin: 0 auto;
		height: 70rpx;
		line-height: 70rpx;
		padding: 0 20rpx;
		margin-top: 30rpx;
		margin-bottom: 100rpx;
	}

	.bottom_view {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		background-color: #FF7B33;
		color: #FFFFFF;
		/* font-size: 30rpx; */
		font-weight: bold;
	}

	/* .swiper {
	  height: 350rpx;
	} */

	.swiper-item {
		display: block;
		height: auto;
		width: 100%;
	}

	.uni-media-list-logo {
		width: 100rpx;
		height: 100rpx;
	}

	.schoollist-head-img {
		width: 100%;
		height: 180rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}

	.schoollist {
		margin-top: 20rpx;
	}

	.school-list-info {
		float: left;
		margin-left: 20rpx;
		margin-top: 20rpx;
		position: relative;
	}

	.school-list-photo {
		width: 350rpx;
		height: 250rpx;
	}

	.school-list-desc {
		display: block;
		background-color: #000;
		color: #fff;
		position: absolute;
		bottom: 3%;
		left: 0;
		width: 94%;
		text-align: center;
		font-size: 25rpx;
		padding: 10rpx;
		opacity: 0.7;
	}

	.uni-title {
		margin-left: 20rpx;
		font-size: 18px;
		font-weight: bold;
	}

	.uni-title text {
		border-left: 4px solid #3185ff;
		padding-left: 5px;
	}

	/* 新闻列表 */
	.grace-news-list {
		margin-top: 25rpx;
		padding-bottom: 25rpx;
	}

	.grace-news-item {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		position: relative;
		padding: 8rpx 0;
	}

	.grace-news-img {
		width: 256rpx;
		height: 144rpx;
		border-radius: 10rpx;
		flex-shrink: 0;
	}

	.grace-news-img-l {
		margin-right: 20rpx;
	}

	.grace-news-img-r {
		margin-left: 20rpx;
	}

	/*.grace-news-body{overflow:hidden; width:150rpx; flex:auto;}*/
	.grace-news-title {
		display: block;
		line-height: 36rpx;
		font-size: 26rpx;
		margin-top: -2rpx;
	}

	.grace-news-desc {
		display: block;
		line-height: 32rpx;
		color: #A5A7B2;
		font-size: 22rpx;
		margin-top: 8rpx;
	}

	.grace-news-info {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		margin-top: 8rpx;
	}

	.grace-news-info-text {
		font-size: 22rpx;
		color: #999999;
	}

	.grace-news-img-list {
		display: flex;
		flex-direction: row;
		margin: 10rpx 0;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.grace-news-imgs {
		width: 225rpx;
		height: 158rpx;
		margin: 8rpx 0;
		font-size: 0;
		border-radius: 5rpx;
		overflow: hidden;
	}

	.grace-news-imgs-img {
		width: 225rpx;
		height: 158rpx;
	}

	.ws-grace-news-title {
		line-height: 40rpx;
		font-weight: 600;
	}

	.ws-news-tag {
		font-size: 18rpx;
		padding: 6rpx 12rpx;
		border-radius: 24rpx;
		font-weight: 400;
		color: #bbbbbb;
		border: 1px solid #EEEEEE;
		display: inline;
	}

	.ws-grace-news-body {
		overflow: visible;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 10rpx 0;
	}

	/* 头部 轮播图 */
	.carousel-section {
		position: relative;
		padding-top: 10px;

		.titleNview-placing {
			height: var(--status-bar-height);
			padding-top: 44px;
			box-sizing: content-box;
		}

		.titleNview-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 426upx;
			transition: .4s;
		}
	}

	.carousel {
		width: 100%;
		height: 350upx;

		.carousel-item {
			width: 100%;
			height: 100%;
			padding: 0 28upx;
			overflow: hidden;
		}

		image {
			width: 100%;
			height: 100%;
			border-radius: 10upx;
		}
	}

	.swiper-dots {
		display: flex;
		position: absolute;
		left: 60upx;
		bottom: 15upx;
		width: 72upx;
		height: 36upx;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;

		.num {
			width: 36upx;
			height: 36upx;
			border-radius: 50px;
			font-size: 24upx;
			color: #fff;
			text-align: center;
			line-height: 36upx;
		}

		.sign {
			position: absolute;
			top: 0;
			left: 50%;
			line-height: 36upx;
			font-size: 12upx;
			color: #fff;
			transform: translateX(-50%);
		}
	}
	.f-header{
		display:flex;
		align-items:center;
		height: 60upx;
		padding: 6upx 30upx 8upx;
		background: #fff;
		image{
			flex-shrink: 0;
			width: 80upx;
			height: 80upx;
			margin-right: 20upx;
		}
		.tit-box{
			flex: 1;
			display: flex;
			flex-direction: column;
		}
		.tit{
			font-size: $font-lg +2upx;
			color: #font-color-dark;
			line-height: 1.3;
		}
		.tit2{
			font-size: $font-sm;
			color: $font-color-light;
		}
		.icon-you{
			font-size: $font-lg +2upx;
			color: $font-color-light;
		}
	}
	/* 猜你喜欢 */
	// .guess-section{
	// 	display:flex;
	// 	flex-wrap:wrap;
	// 	padding: 0 30upx;
	// 	background: #fff;
	// 	.guess-item{
	// 		display:flex;
	// 		flex-direction: column;
	// 		width: 98%;
	// 		padding-bottom: 40upx;
	// 	}
	// 	.image-wrapper{
	// 		width: 100%;
	// 		height: 330upx;
	// 		border-radius: 3px;
	// 		overflow: hidden;
	// 		image{
	// 			width: 100%;
	// 			height: 100%;
	// 			opacity: 1;
	// 		}
	// 	}
	// 	.title{
	// 		font-size: $font-lg;
	// 		color: $font-color-dark;
	// 		line-height: 80upx;
	// 	}
	// 	.price{
	// 		font-size: $font-lg;
	// 		color: $uni-color-primary;
	// 		line-height: 1;
	// 	}
	// }
	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		flex-wrap:wrap;
		padding: 30upx 22upx; 
		background: #fff;
		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: $font-sm + 2upx;
			color: $font-color-dark;
		}
		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			margin-bottom: 14upx;
			border-radius: 50%;
			opacity: .7;
			box-shadow: 4upx 4upx 20upx rgba(250, 67, 106, 0.3);
		}
	}
	.ad-1{
		width: 100%;
		height: 210upx;
		padding: 10upx 0;
		background: #fff;
		image{
			width:100%;
			height: 100%; 
		}
	}
	/* 猜你喜欢 */
	.guess-section{
		display:flex;
		flex-wrap:wrap;
		padding: 0 30upx;
		background: #fff;
		.guess-item{
			display:flex;
			flex-direction: column;
			width: 100%;
			padding-bottom: 40upx;
			&:nth-child(2n+1){
				margin-right: 4%;
			}
		}
		.image-wrapper{
			width: 100%;
			height: 250upx;
			border-radius: 3px;
			overflow: hidden;
			image{
				width: 100%;
				height: 100%;
				opacity: 1;
			}
		}
		.title{
			font-size: $font-lg;
			color: $font-color-dark;
			line-height: 40upx;
			font-size: 30rpx;
		}
		.price{
			font-size: $font-lg;
			color: $uni-color-primary;
			line-height: 1;
		}
	}
	.cont {
	        position: fixed;
	        top: 60rpx;
	        left: 50%;
	        transform: translateX(-50%);
	        width: 700rpx;
	}
	.list-box {
		padding-bottom: 20rpx;
	
		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding:20rpx 30rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
	
			.top-box {
				position: relative;
	
				.cover-box-hot {
					width: 50%;
					height: auto;
					min-height: 200rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
					.cover :after{
					    background-color: red;
					    border-radius: 10rpx;
					    color: #fff;
					    content: "hot";
					    font-size: 25rpx;
					    line-height: 1;
					    padding: 2rpx 6rpx;
					    position: absolute;
					    left: 5rpx;
					    top: 5rpx;
					}
					.button{
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
				
				.cover-box {
					width: 50%;
					height: auto;
					min-height: 200rpx;
					position: relative;
					
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
					.button{
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
				  
				
	
				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
	
					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}
	
					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}
	
					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #666666;
					}
	
					.end-date {
						font-size: 20rpx;
						color: #999999;
					}
	
					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
					
					.des{
						font-size:22rpx;
						color: #8f8f94;
					
						
					}
					.price{
						font-size:24rpx;
						color: red;
						float: right;
					}
					.end{
						font-size:24rpx;
						color: red;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}
	
			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;
	
				.text-sm {
					font-size: 24upx;
				}
	
				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}
	
				.uni-row {
					flex-direction: row;
				}
	
				.align-center {
					align-items: center;
				}
	
				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
	}
	/* 秒杀专区 */
	.seckill-section{
		margin-top: 20upx;
		margin-bottom: 20upx;
		padding: 4upx 30upx 24upx;
		background: #fff;
		.s-header{
			display:flex;
			align-items:center;
			height: 92upx;
			line-height: 1;
			.s-img{
				width: 140upx;
				height: 30upx;
			}
			.title{
				font-size: 34rpx;
				color: #font-color-dark;
				line-height: 1.3;
			}
			.tip{
				font-size: $font-base;
				color: $font-color-light;
				margin: 0 20upx 0 40upx;
			}
			.timer{
				display:inline-block;
				width: 40upx;
				height: 36upx;
				text-align:center;
				line-height: 36upx;
				margin-right: 14upx;
				font-size: $font-sm+2upx;
				color: #fff;
				border-radius: 2px;
				background: rgba(0,0,0,.8);
			}
			.icon-you{
				font-size: $font-lg;
				color: $font-color-light;
				flex: 1;
				text-align: right;
			}
		}
		.floor-list{
			white-space: nowrap;
		}
		.scoll-wrapper{
			display:flex;
			align-items: flex-start;
		}
		.floor-item{
			width: 150upx;
			margin-right: 20upx;
			font-size: $font-sm+2upx;
			color: $font-color-dark;
			line-height: 1.8;
			image{
				width: 150upx;
				height: 150upx;
				border-radius: 6upx;
			}
			.price{
				color: $uni-color-primary;
			}
		}
	}
	
	.f-header{
		display:flex;
		align-items:center;
		height: 140upx;
		padding: 6upx 30upx 8upx;
		background: #fff;
		image{
			flex-shrink: 0;
			width: 80upx;
			height: 80upx;
			margin-right: 20upx;
		}
		.tit-box{
			flex: 1;
			display: flex;
			flex-direction: column;
		}
		.tit{
			font-size: $font-lg +2upx;
			color: #font-color-dark;
			line-height: 1.3;
		}
		.tit2{
			font-size: $font-sm;
			color: $font-color-light;
		}
		.icon-you{
			font-size: $font-lg +2upx;
			color: $font-color-light;
		}
	}
	.swiper-container {
		height: 300rpx;
	}
	
	.swiper-container-large {
		margin: 20rpx 0;
		height: 350rpx;
	}
	
	.swiper-container .swiper-item {
		display: flex;
		flex-wrap: wrap;
	}
	
	.swiper-container-large .swiper-item {
		display: flex;
		flex-wrap: wrap;
	}
	
	.swiper-container-row {
		height: 166rpx;
	}
	
	.swiper-container-row .swiper-item {
		display: flex;
	}
	
	.srv-col {
		box-sizing: border-box;
		flex: 1;
		width: 160rpx;
	}
	
	.srv-item {
		align-items: center;
		display: flex;
		flex-direction: column;
		height: 144rpx;
		justify-content: center;
		text-align: center;
		width: 25%;
	}
	
	.srv-item-large {
		align-items: center;
		display: flex;
		flex-direction: column;
		height: 170rpx;
		justify-content: center;
		text-align: center;
		width: 25%;
	}
	
	.srv-item:nth-child(4n) {
		margin-right: 0rpx;
	}
	
	.srv-item-icon {
		height: 80rpx;
		margin-bottom: 12rpx;
		margin-top: 6rpx;
		width: 80rpx;
		border-radius: 80rpx;
	}
	
	.srv-item-icon-large {
		height: 120rpx;
		margin-bottom: 12rpx;
		margin-top: 6rpx;
		width: 120rpx;
		// border-radius: 80rpx;
	}
	
	.srv-item-title {
		box-sizing: border-box;
		color: #000;
		display: block;
		font-size: 26rpx;
		height: 36rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-align: center;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100%;
	}
	
	.indicator {
		display: flex;
		height: 8rpx;
		margin-bottom: 30rpx;
		margin-top: 12rpx;
		width: 670rpx;
	}
	
	.indicator-child {
		background: rgba(56, 136, 255, .5);
		border-radius: 4rpx;
		float: left;
		height: 8rpx;
		margin-right: 10rpx;
		transition: all .3s ease;
		width: 8rpx;
	}
	.announce {
		width: 700rpx;
		height: 90rpx;
		border-radius: 45rpx;
		padding: 0 35rpx;
		line-height: 90rpx;
		background: #f4f4f5;
		margin: 20rpx auto;
		display: flex;
	
		.announce-title {
			width: 100rpx;
			height: 90rpx;
			line-height: 90rpx;
			font-size: 34rpx;
			font-weight: 800;
	
			.font1 {
				margin-right: 6rpx;
			}
	
			.font2 {
				color: #5C7DFF;
				font-size: 35rpx;
			}
		}
	
		.announce-item {
			position: relative;
			width: 530rpx;
			height: 90rpx;
	
			.announce-swiper {
				width: 100%;
				height: 90rpx;
	
				.announce-swiper-item {
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					letter-spacing: 1rpx;
				}
			}
		}
	}
</style>
