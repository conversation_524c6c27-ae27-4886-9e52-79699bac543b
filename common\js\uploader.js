import VODUpload from '@/js_sdk/aliyun-upload-sdk/aliyun-upload-sdk-1.0.0.min.js';
import {
	http
} from './request.js'

export default {
	getUploader: function(config) {
		const uploader = new VODUpload({
			timeout: 60000,
			region: "cn-shanghai",
			// 添加文件成功
			addFileSuccess: function(uploadInfo) {
				if (config.debug) console.log("addFileSuccess");
				uploader.startUpload();
				if (config.addFileSuccess) {
					config.addFileSuccess(uploadInfo);
				} else {
					uni.showLoading({
						title: "文件上传中~",
						mask: true
					})
				}
			},
			// 开始上传
			onUploadstarted: function(uploadInfo) {
				if (config.debug) console.log('文件开始上传...');
				// https://skytest2.utools.club/api
				var url = "/video/token/aliyun/create?title=" + uploadInfo.coverUrl + "&filename=" + uploadInfo.url;
				http.get(url).then(res => {
					/* 
					data :
						{
							request_id: "91A1B63E-D4B9-4807-AE46-263AF96A26DA"
							upload_address: "eyJFbmRwb2ludCI6Imh0dHBzOi8vb3NzLWNuLXNoYW5naGFpLmFsaXl1bmNzLmNvbSIsIkJ1Y2tldCI6Im91dGluLTQzMGViNGJiOTAzZTExZTk4M2EzMDAxNjNlMWM5NTVjIiwiRmlsZU5hbWUiOiJzdi81MGI4MmJmZi0xNzc2NWQ2MjM1Zi81MGI4MmJmZi0xNzc2NWQ2MjM1Zi5tNGEifQ=="
							upload_auth: "eyJTZWN1cml0eVRva2VuIjoiQ0FJU3lBUjFxNkZ0NUIyeWZTaklyNWJjS3QzWHJLWncyckRaZFZMNXRYZ01SL3RtdGJEN216ejJJSDlJZEhWb0FPOGZ2dlUwbTJ0WTdQc1pscjBwRThVWUZSeVZOWTV0OXBCUStBK2RaSW5NdlpSbGZYSGNGdHYzZDFLSUFqdlhnZVV3Q0llUUZhRXhFWlhBUWxUQWtUQUpDdEdZRVJ5cFExMmlON0NRbEpkamRhNTVkd0trYkQxQWRqVThSRzVsczlSSURXYk5Fdnl2UHhYMjRBelhGMUU2Z2cxbmxVUjE2Nm0wM3EvazdRSEYzblRpMHVJVHE2UDdJSld2YzZzS080eGtBZmU0MWVCUmZLak0yekl5a2g5UjcvVlpnYkJqOHpYS3RjMjREa1ZZZ1dhQktQR0cwOXR1TUE1d2VxVWZCcXBZcmVEMWorRkZvdWpVbm9pVnNCRldKck53VGlERFJaaXAydGY1QU9ldVA5cGJFN0hnSUNid3l0U0lQNVhOdWhrNEF3Z2NMeGdZUnNJbExYWjhNUlUyVmhyZE1yN0ZpMWZSZVZXSVRLMmQxS3dxM0w5b3kwbnA1ZE9RTzJXWFI3S1EzVVJvUFlRblBXb2lNQVFLK0hIbGJxNGVlaFpRVGt0akFMK1pML1YwZHdzTWphTHpvU2pQVWpGaDFuaE5vOERtWS9UZnRydDlESUxrUmNCaTBKRUJYSjFjcjNCUWNqYVJjYisxalZvT2YyRklXS3RmMUxLWFdhV3k4N2lZMnU2ZUU3K2tWZmtNb1FkZGFpdUQ3WGpQRzNSTk1TejNvNEY0TVZlU3A4N2JuZlNYL3NvNUhWcCt2ZGtFVlZuY0w0MWc5bGMzdnZhNXFBK050NjNCS2luN29EcHBvb1NDb05NVXNoWS9KcTZlN3JYTTdtS0VqaHU1ZXJJa25jcmJZbXByVENtc2RtWnhxUExvM1MxYjkweFp5ajd0YTBkRXRBL0JqenZyWnJFeTF2MmEyM1Y2QmF0b2tPM0RjQ3U3eEdKa0JNSWtQRFM1NWwyMUZ4cUFBYUYwSFBvZXZISTFPMjI2S2JCUnprbENtWkhHcHVDTWF0aytRQm9xTU91SUljbXhTcXpYS3lyeUxyajFuUFZBWVNyTzgveGpxUCtUMVNsQTZtQXFweXIxTXQyMzFQUW4xVEtVYWVkMjhZVWdoQWRJbnM5UzVhRlVsdGFrMjR5WUk2czA1dlQzZWVCZHh5ck1Ub2ZaWHlnSngxWmVad0Q0a0FzOFdIMGdwazlaIiwiQWNjZXNzS2V5SWQiOiJTVFMuTlVpYWdjQXlRbXIyd3RIUXhZS3RKWnVQeSIsIkV4cGlyZVVUQ1RpbWUiOiIyMDIxLTAyLTAzVDAzOjU3OjEzWiIsIkFjY2Vzc0tleVNlY3JldCI6IjZ6UTF3OHM2dk5XTlFmM3piajRjb3V1UnVIeU5Vb2lmaFZkN252Tk05QzRKIiwiRXhwaXJhdGlvbiI6IjMzNjUiLCJSZWdpb24iOiJjbi1zaGFuZ2hhaSJ9"
							video_id: "91f524bef570415eb35d1f65334eb696", 
						}
					*/
					
					const data = res.data.data;
					if(config.onUploadstarted){
						config.onUploadstarted(data);
					}
					uploader.setUploadAuthAndAddress(uploadInfo, data.upload_auth, data.upload_address, data.video_id);
				}).catch(err => {
					console.error(err);
				})
			},
			// 文件上传成功
			onUploadSucceed: function(uploadInfo) {
				if (config.debug) console.log('文件上传成功!');
				if (config.onUploadSucceed) config.onUploadSucceed(uploadInfo);
			},
			// 文件上传失败
			onUploadFailed: function(uploadInfo, code, message) {
				if (config.onUploadFailed) {
					config.onUploadFailed(uploadInfo, code, message);
				} else {
					uni.showToast({
						title: "文件上传失败！",
						icon: "none"
					})
					uploader.stopUpload();
				}
			},
			// 取消文件上传
			onUploadCanceled: function(uploadInfo, code, message) {
				if (config.onUploadCanceled) {
					config.onUploadCanceled(uploadInfo, code, message);
				} else {
					uni.showToast({
						title: "文件已暂停上传！",
						icon: "none"
					})
					uploader.stopUpload();
				}
			},
			// 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
			onUploadProgress: function(uploadInfo, totalSize, progress) {
				if (config.debug) console.log(progress)
				if (config.onUploadProgress) {
					config.onUploadProgress(uploadInfo, totalSize, progress);
				}
			},
			// 上传凭证超时
			onUploadTokenExpired: function(uploadInfo) {
				if (config.onUploadTokenExpired) {
					config.onUploadTokenExpired(uploadInfo);
				} else {
					uni.showToast({
						title: "网络错误，请重新上传！",
						icon: "none"
					});
				}
			},
			// 全部文件上传结束
			onUploadEnd: function(uploadInfo) {
				if (config.onUploadEnd) {
					config.onUploadEnd(uploadInfo);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: "上传成功！"
					});
				}
			}
		});
		return uploader;
	}
}
