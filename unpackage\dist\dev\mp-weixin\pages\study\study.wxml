<gui-page class="vue-ref" vue-id="741027ac-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#ffffff;" slot="gBody"><view><view class="h2title">我的课程</view><block wx:if="{{$root.g0===0}}"><view class="empty"><image src="/static/null.png" mode="aspectFit"></image><view class="empty-tips">您还没有加入课程</view><view class="empty-tips">从推荐课程中看看有没有喜欢的吧~</view></view></block><block wx:else><view class="content"><view class="list-box"><block wx:for="{{goodsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['goodsList','',index]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item.title}}</view><view class="des">{{item.des}}</view></view></view></view></block></view></view></block></view><view style="margin-top:20rpx;"><view class="h2title">课程推荐</view><view class="content"><view class="list-box"><block wx:for="{{hotList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage2',['$0'],[[['hotList','',index]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item.title}}</view></view></view></view></block></view></view></view></view></gui-page>