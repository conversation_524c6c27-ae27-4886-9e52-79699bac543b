{"version": 3, "sources": ["webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?c1e5", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?0bae", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?c859", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?d547", "uni-app:///uni_modules/uview-ui/components/u-parse/node/node.vue", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?0433", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?0015", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?ff26", "webpack:///D:/日语云课/uni_modules/uview-ui/components/u-parse/node/node.vue?51ba"], "names": ["name", "options", "virtualHost", "data", "ctrl", "props", "attrs", "type", "default", "childs", "opts", "components", "node", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "toJSON", "play", "id", "flag", "ctx", "imgTap", "uni", "current", "urls", "imgLongTap", "imgLoad", "linkTap", "href", "success", "title", "url", "fail", "mediaError", "index", "source", "errMsg"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAilC;AACjlC;AACwD;AACL;AACa;;;AAGhE;AACyK;AACzK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+iCAAM;AACR,EAAE,wjCAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mjCAAU;AACZ;AACA;;AAEA;AACgG;AAChG,WAAW,kHAAM,iBAAiB,0HAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+nB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;gBCuGnpB;EACAA;EAEAC;IACAC;EACA;EAEAC;IACA;MACAC;IACA;EACA;EACAC;IACAL;IACAM;MACAC;MACAC;QACA;MACA;IACA;IACAC;IACAC;EACA;EACAC;IAEAC;EACA;EACAC;IACA;MAAA;IAAA;EAoBA;EACAC,yCAKA;EACAC;IAEAC;IAEA;AACA;AACA;AACA;IACAC;MAEA;QACA;UAAAC;QACA;UACA,mCACAC,iBAEA;QACA;QACA;QACA;UACA,qCAEA,KAEA;UACAC;UACA;QACA;MACA;IAEA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA,YACA;MACA,uBACA;MAIA;MACA;MACA,0BACAC;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC,oCAuBA;IAEA;AACA;AACA;AACA;IACAC;MACA;;MAEA;MACA,uBACA;QAGA;QACA,yDACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACAC;MACA;MACA;QACA;QACA,oBACA;QACA;QAAA,KACA;UACA;YAKAN;cACAnB;cACA0B;gBAAA,OACAP;kBACAQ;gBACA;cAAA;YACA;UAKA;QACA;QACA;QAAA,KAEAR;UACAS;UACAC;YACAV;cACAS;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACArB;MACA;MACA;QACA;QACA,6BACAsB;QACA,6BACA;MACA;MACA;MAAA,KACA,wCACA;MACA,eACA;QACAC;QACA7B;QACA8B;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACzUA;AAAA;AAAA;AAAA;AAA85B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;ACAl7B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAA4d,CAAgB,mfAAG,EAAC,C;;;;;;;;;;;;ACAhf;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "uni_modules/uview-ui/components/u-parse/node/node.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./node.vue?vue&type=template&id=85ba1196&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiLy8g6KGM5YaF5qCH562%2B5YiX6KGoXHJcbnZhciBpbmxpbmVUYWdzID0ge1xyXG4gIGFiYnI6IHRydWUsXHJcbiAgYjogdHJ1ZSxcclxuICBiaWc6IHRydWUsXHJcbiAgY29kZTogdHJ1ZSxcclxuICBkZWw6IHRydWUsXHJcbiAgZW06IHRydWUsXHJcbiAgaTogdHJ1ZSxcclxuICBpbnM6IHRydWUsXHJcbiAgbGFiZWw6IHRydWUsXHJcbiAgcTogdHJ1ZSxcclxuICBzbWFsbDogdHJ1ZSxcclxuICBzcGFuOiB0cnVlLFxyXG4gIHN0cm9uZzogdHJ1ZSxcclxuICBzdWI6IHRydWUsXHJcbiAgc3VwOiB0cnVlXHJcbn1cclxuLyoqXHJcbiAqIEBkZXNjcmlwdGlvbiDmmK%2FlkKbkvb%2FnlKggcmljaC10ZXh0IOaYvuekuuWJqeS9meWGheWuuVxyXG4gKi9cclxubW9kdWxlLmV4cG9ydHMgPSB7XHJcbiAgdXNlOiBmdW5jdGlvbiAoaXRlbSkge1xyXG4gIC8vIOW%2BruS%2FoeWSjCBRUSDnmoQgcmljaC10ZXh0IGlubGluZSDluIPlsYDml6DmlYhcclxuICBpZiAoaW5saW5lVGFnc1tpdGVtLm5hbWVdIHx8IChpdGVtLmF0dHJzLnN0eWxlIHx8ICcnKS5pbmRleE9mKCdkaXNwbGF5OmlubGluZScpICE9IC0xKVxyXG4gICAgcmV0dXJuIGZhbHNlXHJcbiAgcmV0dXJuICFpdGVtLmNcclxuICB9XHJcbn0iLCJzdGFydCI6NDk4OCwiYXR0cnMiOnsibW9kdWxlIjoiaGFuZGxlciIsImxhbmciOiJ3eHMifSwiZW5kIjo1NTAyfX0%3D&\"\nvar renderjs\nimport script from \"./node.vue?vue&type=script&lang=js&\"\nexport * from \"./node.vue?vue&type=script&lang=js&\"\nimport style0 from \"./node.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-parse/node/node.vue\"\nexport default component.exports", "export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=template&id=85ba1196&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiLy8g6KGM5YaF5qCH562%2B5YiX6KGoXHJcbnZhciBpbmxpbmVUYWdzID0ge1xyXG4gIGFiYnI6IHRydWUsXHJcbiAgYjogdHJ1ZSxcclxuICBiaWc6IHRydWUsXHJcbiAgY29kZTogdHJ1ZSxcclxuICBkZWw6IHRydWUsXHJcbiAgZW06IHRydWUsXHJcbiAgaTogdHJ1ZSxcclxuICBpbnM6IHRydWUsXHJcbiAgbGFiZWw6IHRydWUsXHJcbiAgcTogdHJ1ZSxcclxuICBzbWFsbDogdHJ1ZSxcclxuICBzcGFuOiB0cnVlLFxyXG4gIHN0cm9uZzogdHJ1ZSxcclxuICBzdWI6IHRydWUsXHJcbiAgc3VwOiB0cnVlXHJcbn1cclxuLyoqXHJcbiAqIEBkZXNjcmlwdGlvbiDmmK%2FlkKbkvb%2FnlKggcmljaC10ZXh0IOaYvuekuuWJqeS9meWGheWuuVxyXG4gKi9cclxubW9kdWxlLmV4cG9ydHMgPSB7XHJcbiAgdXNlOiBmdW5jdGlvbiAoaXRlbSkge1xyXG4gIC8vIOW%2BruS%2FoeWSjCBRUSDnmoQgcmljaC10ZXh0IGlubGluZSDluIPlsYDml6DmlYhcclxuICBpZiAoaW5saW5lVGFnc1tpdGVtLm5hbWVdIHx8IChpdGVtLmF0dHJzLnN0eWxlIHx8ICcnKS5pbmRleE9mKCdkaXNwbGF5OmlubGluZScpICE9IC0xKVxyXG4gICAgcmV0dXJuIGZhbHNlXHJcbiAgcmV0dXJuICFpdGVtLmNcclxuICB9XHJcbn0iLCJzdGFydCI6NDk4OCwiYXR0cnMiOnsibW9kdWxlIjoiaGFuZGxlciIsImxhbmciOiJ3eHMifSwiZW5kIjo1NTAyfX0%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view :id=\"attrs.id\" :class=\"'_'+name+' '+attrs.class\" :style=\"attrs.style\">\r\n    <block v-for=\"(n, i) in childs\" v-bind:key=\"i\">\r\n      <!-- 图片 -->\r\n      <!-- 占位图 -->\r\n      <image v-if=\"n.name=='img'&&((opts[1]&&!ctrl[i])||ctrl[i]<0)\" class=\"_img\" :style=\"n.attrs.style\" :src=\"ctrl[i]<0?opts[2]:opts[1]\" mode=\"widthFix\" />\r\n      <!-- 显示图片 -->\r\n      <!-- #ifdef H5 || APP-PLUS -->\r\n      <img v-if=\"n.name=='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]==-1?'display:none;':'')+n.attrs.style\" :src=\"n.attrs.src||(ctrl.load?n.attrs['data-src']:'')\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\"/>\r\n      <!-- #endif -->\r\n      <!-- #ifndef H5 || APP-PLUS -->\r\n      <image v-if=\"n.name=='img'\" :id=\"n.attrs.id\" :class=\"'_img '+n.attrs.class\" :style=\"(ctrl[i]==-1?'display:none;':'')+'width:'+(ctrl[i]||1)+'px;height:1px;'+n.attrs.style\" :src=\"n.attrs.src\" :mode=\"n.h?'':'widthFix'\" :lazy-load=\"opts[0]\" :webp=\"n.webp\" :show-menu-by-longpress=\"opts[3]&&!n.attrs.ignore\" :image-menu-prevent=\"!opts[3]||n.attrs.ignore\" :data-i=\"i\" @load=\"imgLoad\" @error=\"mediaError\" @tap.stop=\"imgTap\" @longpress=\"imgLongTap\" />\r\n      <!-- #endif -->\r\n      <!-- 文本 -->\r\n      <!-- #ifndef MP-BAIDU -->\r\n      <text v-else-if=\"n.type=='text'\" decode>{{n.text}}</text>\r\n      <!-- #endif -->\r\n      <text v-else-if=\"n.name=='br'\">\\n</text>\r\n      <!-- 链接 -->\r\n      <view v-else-if=\"n.name=='a'\" :id=\"n.attrs.id\" :class=\"(n.attrs.href?'_a ':'')+n.attrs.class\" hover-class=\"_hover\" :style=\"'display:inline;'+n.attrs.style\" :data-i=\"i\" @tap.stop=\"linkTap\">\r\n        <node name=\"span\" :childs=\"n.children\" :opts=\"opts\" style=\"display:inherit\" />\r\n      </view>\r\n      <!-- 视频 -->\r\n      <!-- #ifdef APP-PLUS -->\r\n      <view v-else-if=\"n.html\" :id=\"n.attrs.id\" :class=\"'_video '+n.attrs.class\" :style=\"n.attrs.style\" v-html=\"n.html\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef APP-PLUS -->\r\n      <video v-else-if=\"n.name=='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\r\n      <!-- #endif -->\r\n      <!-- #ifdef H5 || APP-PLUS -->\r\n      <iframe v-else-if=\"n.name=='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\" :src=\"n.attrs.src\" />\r\n      <embed v-else-if=\"n.name=='embed'\" :style=\"n.attrs.style\" :src=\"n.attrs.src\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef MP-TOUTIAO -->\r\n      <!-- 音频 -->\r\n      <audio v-else-if=\"n.name=='audio'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\" :src=\"n.src[ctrl[i]||0]\" :data-i=\"i\" @play=\"play\" @error=\"mediaError\" />\r\n      <!-- #endif -->\r\n      <view v-else-if=\"(n.name=='table'&&n.c)||n.name=='li'\" :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+n.attrs.class\" :style=\"n.attrs.style\">\r\n        <node v-if=\"n.name=='li'\" :childs=\"n.children\" :opts=\"opts\" />\r\n        <view v-else v-for=\"(tbody, x) in n.children\" v-bind:key=\"x\" :class=\"'_'+tbody.name+' '+tbody.attrs.class\" :style=\"tbody.attrs.style\">\r\n          <node v-if=\"tbody.name=='td'||tbody.name=='th'\" :childs=\"tbody.children\" :opts=\"opts\" />\r\n          <block v-else v-for=\"(tr, y) in tbody.children\" v-bind:key=\"y\">\r\n            <view v-if=\"tr.name=='td'||tr.name=='th'\" :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\r\n              <node :childs=\"tr.children\" :opts=\"opts\" />\r\n            </view>\r\n            <view v-else :class=\"'_'+tr.name+' '+tr.attrs.class\" :style=\"tr.attrs.style\">\r\n              <view v-for=\"(td, z) in tr.children\" v-bind:key=\"z\" :class=\"'_'+td.name+' '+td.attrs.class\" :style=\"td.attrs.style\">\r\n                <node :childs=\"td.children\" :opts=\"opts\" />\r\n              </view>\r\n            </view>\r\n          </block>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 富文本 -->\r\n      <!-- #ifdef H5 || MP-WEIXIN || MP-QQ || APP-PLUS || MP-360 -->\r\n      <rich-text v-else-if=\"handler.use(n)\" :id=\"n.attrs.id\" :style=\"n.f\" :nodes=\"[n]\" />\r\n      <!-- #endif -->\r\n      <!-- #ifndef H5 || MP-WEIXIN || MP-QQ || APP-PLUS || MP-360 -->\r\n      <rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :style=\"n.f+';display:inline'\" :preview=\"false\" :nodes=\"[n]\" />\r\n      <!-- #endif -->\r\n      <!-- 继续递归 -->\r\n      <view v-else-if=\"n.c==2\" :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+n.attrs.class\" :style=\"n.f+';'+n.attrs.style\">\r\n        <node v-for=\"(n2, j) in n.children\" v-bind:key=\"j\" :style=\"n2.f\" :name=\"n2.name\" :attrs=\"n2.attrs\" :childs=\"n2.children\" :opts=\"opts\" />\r\n      </view>\r\n      <node v-else :style=\"n.f\" :name=\"n.name\" :attrs=\"n.attrs\" :childs=\"n.children\" :opts=\"opts\" />\r\n    </block>\r\n  </view>\r\n</template>\r\n<script module=\"handler\" lang=\"wxs\">\r\n// 行内标签列表\r\nvar inlineTags = {\r\n  abbr: true,\r\n  b: true,\r\n  big: true,\r\n  code: true,\r\n  del: true,\r\n  em: true,\r\n  i: true,\r\n  ins: true,\r\n  label: true,\r\n  q: true,\r\n  small: true,\r\n  span: true,\r\n  strong: true,\r\n  sub: true,\r\n  sup: true\r\n}\r\n/**\r\n * @description 是否使用 rich-text 显示剩余内容\r\n */\r\nmodule.exports = {\r\n  use: function (item) {\r\n  // 微信和 QQ 的 rich-text inline 布局无效\r\n  if (inlineTags[item.name] || (item.attrs.style || '').indexOf('display:inline') != -1)\r\n    return false\r\n  return !item.c\r\n  }\r\n}\r\n</script>\r\n<script>\n\r\nimport node from './node'\r\nexport default {\r\n  name: 'node',\r\n  // #ifdef MP-WEIXIN\r\n  options: {\r\n    virtualHost: true\r\n  },\r\n  // #endif\r\n  data() {\r\n    return {\r\n      ctrl: {}\r\n    }\r\n  },\r\n  props: {\r\n    name: String,\r\n    attrs: {\r\n      type: Object,\r\n      default() {\r\n        return {}\r\n      }\r\n    },\r\n    childs: Array,\r\n    opts: Array\r\n  },\r\n  components: {\n\r\n    node\r\n  },\r\n  mounted() {\r\n    for (this.root = this.$parent; this.root.$options.name != 'mp-html'; this.root = this.root.$parent);\r\n    // #ifdef H5 || APP-PLUS\r\n    if (this.opts[0]) {\r\n      for (var i = this.childs.length; i--;)\r\n        if (this.childs[i].name == 'img')\r\n          break\r\n      if (i != -1) {\r\n        this.observer = uni.createIntersectionObserver(this).relativeToViewport({\r\n          top: 500,\r\n          bottom: 500\r\n        })\r\n        this.observer.observe('._img', res => {\r\n          if (res.intersectionRatio) {\r\n            this.$set(this.ctrl, 'load', 1)\r\n            this.observer.disconnect()\r\n          }\r\n        })\r\n      }\r\n    }\r\n    // #endif\r\n  },\r\n  beforeDestroy() {\r\n    // #ifdef H5 || APP-PLUS\r\n    if (this.observer)\r\n      this.observer.disconnect()\r\n    // #endif\r\n  },\r\n  methods:{\r\n    // #ifdef MP-WEIXIN\r\n    toJSON() { },\r\n    // #endif\r\n    /**\r\n     * @description 播放视频事件\r\n     * @param {Event} e \r\n     */\r\n    play(e) {\r\n      // #ifndef APP-PLUS\r\n      if (this.root.pauseVideo) {\r\n        var flag = false, id = e.target.id\r\n        for (var i = this.root._videos.length; i--;) {\r\n          if (this.root._videos[i].id == id)\r\n            flag = true\r\n          else\r\n            this.root._videos[i].pause() // 自动暂停其他视频\r\n        }\r\n        // 将自己加入列表\r\n        if (!flag) {\r\n          var ctx = uni.createVideoContext(id\r\n            // #ifndef MP-BAIDU\r\n            , this\r\n            // #endif\r\n          )\r\n          ctx.id = id\r\n          this.root._videos.push(ctx)\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 图片点击事件\r\n     * @param {Event} e \r\n     */\r\n    imgTap(e) {\r\n      var node = this.childs[e.currentTarget.dataset.i]\r\n      if (node.a)\r\n        return this.linkTap(node.a)\r\n      if (node.attrs.ignore)\r\n        return\r\n      // #ifdef H5 || APP-PLUS\r\n      node.attrs.src = node.attrs.src || node.attrs['data-src']\r\n      // #endif\r\n      this.root.$emit('imgtap', node.attrs)\r\n      // 自动预览图片\r\n      if (this.root.previewImg)\r\n        uni.previewImage({\r\n          current: parseInt(node.attrs.i),\r\n          urls: this.root.imgList\r\n        })\r\n    },\r\n\r\n    /**\r\n     * @description 图片长按\r\n     */\r\n    imgLongTap(e) {\r\n      // #ifdef APP-PLUS\r\n      var attrs = this.childs[e.currentTarget.dataset.i].attrs\r\n      if (!attrs.ignore)\r\n        uni.showActionSheet({\r\n          itemList: ['保存图片'],\r\n          success: () => {\r\n            uni.downloadFile({\r\n              url: this.root.imgList[attrs.i],\r\n              success: res => {\r\n                uni.saveImageToPhotosAlbum({\r\n                  filePath: res.tempFilePath,\r\n                  success() {\r\n                    uni.showToast({\r\n                      title: '保存成功'\r\n                    })\r\n                  }\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 图片加载完成事件\r\n     * @param {Event} e \r\n     */\r\n    imgLoad(e) {\r\n      var i = e.currentTarget.dataset.i\r\n      // #ifndef H5 || APP-PLUS\r\n      // 设置原宽度\r\n      if (!this.childs[i].w)\r\n        this.$set(this.ctrl, i, e.detail.width)\r\n      else\r\n        // #endif\r\n        // 加载完毕，取消加载中占位图\r\n        if ((this.opts[1] && !this.ctrl[i]) || this.ctrl[i] == -1)\r\n          this.$set(this.ctrl, i, 1)\r\n    },\r\n\r\n    /**\r\n     * @description 链接点击事件\r\n     * @param {Event} e \r\n     */\r\n    linkTap(e) {\r\n      var attrs = e.currentTarget ? this.childs[e.currentTarget.dataset.i].attrs : e,\r\n        href = attrs.href\r\n      this.root.$emit('linktap', attrs)\r\n      if (href) {\r\n        // 跳转锚点\r\n        if (href[0] == '#')\r\n          this.root.navigateTo(href.substring(1)).catch(() => { })\r\n        // 复制外部链接\r\n        else if (href.includes('://')) {\r\n          if (this.root.copyLink) {\r\n            // #ifdef H5\r\n            window.open(href)\r\n            // #endif\r\n            // #ifdef MP\r\n            uni.setClipboardData({\r\n              data: href,\r\n              success: () =>\r\n                uni.showToast({\r\n                  title: '链接已复制'\r\n                })\r\n            })\r\n            // #endif\r\n            // #ifdef APP-PLUS\r\n            plus.runtime.openWeb(href)\r\n            // #endif\r\n          }\r\n        }\r\n        // 跳转页面\r\n        else\r\n          uni.navigateTo({\r\n            url: href,\r\n            fail() {\r\n              uni.switchTab({\r\n                url: href,\r\n                fail() { }\r\n              })\r\n            }\r\n          })\r\n      }\r\n    },\r\n\r\n    /**\r\n     * @description 错误事件\r\n     * @param {Event} e \r\n     */\r\n    mediaError(e) {\r\n      var i = e.currentTarget.dataset.i,\r\n        node = this.childs[i]\r\n      // 加载其他源\r\n      if (node.name == 'video' || node.name == 'audio') {\r\n        var index = (this.ctrl[i] || 0) + 1\r\n        if (index > node.src.length)\r\n          index = 0\r\n        if (index < node.src.length)\r\n          return this.$set(this.ctrl, i, index)\r\n      }\r\n      // 显示错误占位图\r\n      else if (node.name == 'img' && this.opts[2])\r\n        this.$set(this.ctrl, i, -1)\r\n      if (this.root)\r\n        this.root.$emit('error', {\r\n          source: node.name,\r\n          attrs: node.attrs,\r\n          errMsg: e.detail.errMsg\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style>\r\n/* a 标签默认效果 */\r\n._a {\r\n  padding: 1.5px 0 1.5px 0;\r\n  color: #366092;\r\n  word-break: break-all;\r\n}\r\n\r\n/* a 标签点击态效果 */\r\n._hover {\r\n  text-decoration: underline;\r\n  opacity: 0.7;\r\n}\r\n\r\n/* 图片默认效果 */\r\n._img {\r\n  max-width: 100%;\r\n  -webkit-touch-callout: none;\r\n}\r\n\r\n/* 内部样式 */\r\n\r\n._b,\r\n._strong {\r\n  font-weight: bold;\r\n}\r\n\r\n._code {\r\n  font-family: monospace;\r\n}\r\n\r\n._del {\r\n  text-decoration: line-through;\r\n}\r\n\r\n._em,\r\n._i {\r\n  font-style: italic;\r\n}\r\n\r\n._h1 {\r\n  font-size: 2em;\r\n}\r\n\r\n._h2 {\r\n  font-size: 1.5em;\r\n}\r\n\r\n._h3 {\r\n  font-size: 1.17em;\r\n}\r\n\r\n._h5 {\r\n  font-size: 0.83em;\r\n}\r\n\r\n._h6 {\r\n  font-size: 0.67em;\r\n}\r\n\r\n._h1,\r\n._h2,\r\n._h3,\r\n._h4,\r\n._h5,\r\n._h6 {\r\n  display: block;\r\n  font-weight: bold;\r\n}\r\n\r\n._image {\r\n  height: 1px;\r\n}\r\n\r\n._ins {\r\n  text-decoration: underline;\r\n}\r\n\r\n._li {\r\n  display: list-item;\r\n}\r\n\r\n._ol {\r\n  list-style-type: decimal;\r\n}\r\n\r\n._ol,\r\n._ul {\r\n  display: block;\r\n  padding-left: 40px;\r\n  margin: 1em 0;\r\n}\r\n\r\n._q::before {\r\n  content: '\"';\r\n}\r\n\r\n._q::after {\r\n  content: '\"';\r\n}\r\n\r\n._sub {\r\n  font-size: smaller;\r\n  vertical-align: sub;\r\n}\r\n\r\n._sup {\r\n  font-size: smaller;\r\n  vertical-align: super;\r\n}\r\n\r\n._thead,\r\n._tbody,\r\n._tfoot {\r\n  display: table-row-group;\r\n}\r\n\r\n._tr {\r\n  display: table-row;\r\n}\r\n\r\n._td,\r\n._th {\r\n  display: table-cell;\r\n  vertical-align: middle;\r\n}\r\n\r\n._th {\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n._ul {\r\n  list-style-type: disc;\r\n}\r\n\r\n._ul ._ul {\r\n  margin: 0;\r\n  list-style-type: circle;\r\n}\r\n\r\n._ul ._ul ._ul {\r\n  list-style-type: square;\r\n}\r\n\r\n._abbr,\r\n._b,\r\n._code,\r\n._del,\r\n._em,\r\n._i,\r\n._ins,\r\n._label,\r\n._q,\r\n._span,\r\n._strong,\r\n._sub,\r\n._sup {\r\n  display: inline;\r\n}\r\n\r\n/* #ifdef APP-PLUS */\r\n._video {\r\n  width: 300px;\r\n  height: 225px;\r\n}\r\n/* #endif */\r\n</style>", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753663860802\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"; export default mod; export * from \"-!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./node.vue?vue&type=custom&index=0&blockType=script&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}