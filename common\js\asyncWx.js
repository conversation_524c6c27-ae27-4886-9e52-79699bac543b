/**
 * 消息提示框
 */

export const showToast = ({ title }) => {
	return new Promise((resolve, reject) => {
		uni.showToast({
			title: title,
			icon: 'none',
			duration: 2000,
			success: (result) => {
				resolve(result)
			},
			fail: (err) => {
				reject(err)
			}
		});
	})
}

// 上传资源
export const uploadFile = (data) => {
	uni.showLoading({ title: "正在上传..." });
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			url: data.url,
			filePath: data.filePath,
			fileType: data.fileType,
			name: data.name,
			header: {
				'content-type': 'multipart/form-data'
			},
			formData: data.formData,
			success: (res) => {
				resolve(res)
			},
			fail: (err) => {
				reject(err)
			},
			complete: () => {
				uni.hideLoading();
			}
		});
	})
}
