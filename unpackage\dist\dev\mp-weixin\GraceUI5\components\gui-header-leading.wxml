<block wx:if="{{onlyBack}}"><view class="gui-header-leader data-v-6e7769b9" style="padding:0;"><view class="gui-header-leader-btns data-v-6e7769b9" hover-class="gui-tap"><text class="gui-header-leader-btns gui-block-text gui-icons gui-primary-color data-v-6e7769b9" style="{{('text-align:left; '+buttonStyle)}}" hover-class="gui-tap" data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" bindtap="__e"></text></view></view></block><block wx:else><block wx:if="{{onlyHome}}"><view class="gui-header-leader data-v-6e7769b9" style="padding:0;"><view class="gui-header-leader-btns data-v-6e7769b9" hover-class="gui-tap"><text data-event-opts="{{[['tap',[['gohome',['$event']]]]]}}" class="gui-header-leader-btns gui-block-text gui-icons gui-primary-color data-v-6e7769b9" style="{{('text-align:left; font-size:35rpx; '+buttonStyle)}}" bindtap="__e"></text></view></view></block><block wx:else><view class="gui-header-leader gui-flex gui-rows gui-nowrap gui-align-items-center gui-header-buttons-bg gui-border-box data-v-6e7769b9" style="{{(bgStyle)}}"><view class="gui-header-leader-btns data-v-6e7769b9" hover-class="gui-tap"><text data-event-opts="{{[['tap',[['gohome',['$event']]]]]}}" class="gui-header-leader-btns gui-block-text gui-icons gui-header-buttons-color data-v-6e7769b9" style="{{('font-size:35rpx; '+buttonStyle)}}" bindtap="__e"></text></view><view class="gui-header-leader-btns data-v-6e7769b9" style="margin-left:12rpx;" hover-class="gui-tap"><text data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="gui-header-leader-btns gui-block-text gui-icons gui-header-buttons-color data-v-6e7769b9" style="{{(buttonStyle)}}" bindtap="__e"></text></view></view></block></block>