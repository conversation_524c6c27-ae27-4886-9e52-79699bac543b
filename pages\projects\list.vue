<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 空白页 -->
			<view v-if=" empty===true" class="empty">
				<image src="/static/emptyCart.jpg" mode="aspectFit"></image>
				<view class="empty-tips">
					空空如也
					<navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator>
				</view>
			</view>
			<view v-else>
				<!-- 列表 -->
				<scroll-view v-if="list.length>0" scroll-y="true" @scrolltolower="onScrolltolowerHandler">
					<view class="list-box">
						<view class="item-box lp-flex-column" v-for="(item,index) in list" :key="item.id"
							@click="navToDetailPage(item)">
							<view class="top-box lp-flex">
								<view class="cover-box lp-flex-center">
									<image class="cover" :src="item.picture"></image>
								</view>
								<view class="info-box lp-flex-column">
									<!-- <view class="name-box lp-flex lp-flex-space-between">
										<text class="publish-date">{{item.publish_date}}</text>
										<view class="lang-box lp-flex lp-flex-center">
											<text class="gui-icons" style="margin-right: 10rpx;">{{item.type == 2 ? '&#xe62f;' : '&#xe656;'}} </text>
											<text class="trans">{{item.trans}}</text>
										</view>
									</view> -->
									<text class="title">{{item.title}}</text>
									<text class="end-date">有效期至{{item.end_time}}</text>
									<text class="total">共{{item.num}}节课</text>
								</view>
							</view>
							<block v-if="item.p_id==3">
								<u-line color="#eeeeee" :hair-line="false"></u-line>
								<view class="margin-tb-sm uni-row align-center lp-flex">
									<view class="flex-sub">
										<text class="text-sm title">
											最近学习：{{item.current}}
										</text>
										<!-- <view class="uni-row align-center">
											<text class="text-sm">观看进度：</text>
											<u_line_progress inactive-color="#e6eaf2" active-color="#19be6b"
												:percent="item.progress" height="20"></u_line_progress>
										</view> -->
									</view>
									<view class="uni-row">
										<u_button  type="success" size="mini" 
											class="margin-left-sm">学习中</u_button>
												<u_button @click="download(item.certificate)" type="error" size="mini" v-if="item.certificate != '' && item.certificate != null"
													class="margin-left-sm">证书下载</u_button>
									</view>
								</view>
							</block>
							<block v-else>
								<u-line color="#eeeeee" :hair-line="false"></u-line>
								<view class="margin-tb-sm uni-row align-center lp-flex">
									<view class="flex-sub">
										<text class="text-sm title">
											最近学习：{{item.current}}
										</text>
										<view class="uni-row align-center">
											<text class="text-sm">观看进度：</text>
											<u_line_progress inactive-color="#e6eaf2" active-color="#19be6b"
												:percent="item.progress" height="20"></u_line_progress>
										</view>
									</view>
									<view class="uni-row">
										
										<u_button  type="success" size="mini" v-if="item.total_class==0"
											class="margin-left-sm">学习中</u_button>
												<u_button @click="navToDetailPage(item)" type="success" size="mini" v-else
													class="margin-left-sm">继续学习</u_button>
														<u_button  @click="download(item.certificate)" type="error" size="mini" v-if="item.certificate != '' && item.certificate != null"
															class="margin-left-sm">证书下载</u_button>
									</view>
								</view>
							</block>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import u_line_progress from '@/components/uview-ui/components/u-line-progress/u-line-progress.vue';
	import u_button from '@/components/uview-ui/components/u-button/u-button.vue';
	export default {
		components: {
			u_line_progress,
			u_button
		},
		data() {
			return {
				title: 'Hello',
				empty: true, //空白页现实  true|false
				// 加载中
				loading: false,
				current_page: 1,
				// 总页数
				total_page: 1,
				// 总数量
				total: 0,
				// 列表数据
				list: []
			}
		},
		onLoad() {

		},
		created() {
			if (!this.$store.state.user.token) {
				uni.showModal({
					title: '请先登录',
					content: '登录后才能进行操作',
					success: function(res) {
						if (res.confirm) {
							uni.navigateTo({
								url: "/pages/login/login"
							})
						} else if (res.cancel) {
							/* uni.switchtab({
								url: "/pages/index/index"
							}) */
						}
					}
				});
			}

		},
		onShow() {
			this.getCourseList(1, true);
		},
		methods: {
			getCourseList: function(page, force) {
				if (this.loading) {
					return
				}

				if (!force && page > this.total_page) {
					return
				}
				this.loading = true;
				this.apiGetCourseList(page).then(pagination => {
					this.current_page = pagination.current_page;
					this.total_page = pagination.last_page;
					this.total = pagination.total;
					this.list = pagination.data;
					if (pagination.data.length > 0) {
						this.empty = false;
					}
					this.loading = false;
				})
			},
			//-----------------------------------------------------------------------------------------------
			//
			// hander
			//
			//-----------------------------------------------------------------------------------------------
			onScrolltolowerHandler: function() {
				this.getCourseList(this.current_page + 1);
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiGetCourseList: function(page) {
				return this.$http.get('/v1/member/projectList', {
					params: {
						page,
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
			//详情页
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				if(item.total_class==0){
					
				}else{
					let id = item.project_id;
					uni.navigateTo({
						url: `/pages/projects/detail?state=0&project_id=${id}`
					})
				}
				
			},
			//详情页
			download(item) {
					uni.navigateTo({
						url: `/pages/certificate/certificate?img=${item}`
					})
				
				
			},
		}
	}
</script>

<style lang='scss'>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #F8F8F8;
	}

	.title {
		font-size: 36rpx;
		color: #8f8f94;
	}

	/* 空白页 */
	.empty {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100vh;
		padding-bottom: 100upx;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;
		background: #fff;

		image {
			width: 240upx;
			height: 160upx;
			margin-bottom: 30upx;
		}

		.empty-tips {
			display: flex;
			font-size: $font-sm+2upx;
			color: $font-color-disabled;

			.navigator {
				color: $uni-color-primary;
				margin-left: 16upx;
			}
		}
	}

	scroll-view {
		max-height: 100%;
	}

	.align-center {
		align-items: center;
	}

	.flex-sub {
		flex: 1;
	}

	.list-box {
		padding-bottom: 40rpx;

		.item-box {
			margin: 30rpx 30rpx 0 30rpx;
			padding: 30rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;

			.top-box {
				position: relative;

				.cover-box {
					width: 200rpx;
					height: 120rpx;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
				}

				.info-box {
					flex: 1;
					margin-left: 30rpx;

					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}

					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}

					.title {
						font-size: 24rpx;
						color: #8f8f94;
					}

					.end-date {
						font-size: 18rpx;
						color: #999999;
					}

					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
				}
			}

			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;

				.text-sm {
					font-size: 24upx;
				}

				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}

				.uni-row {
					flex-direction: row;
				}

				.align-center {
					align-items: center;
				}

				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
	}
</style>
