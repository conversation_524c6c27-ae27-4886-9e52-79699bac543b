{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?b9f1", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?7035", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?a8e0", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?e5f1", "uni-app:///pages/my-exchange/my-exchange.vue", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?b774", "webpack:///D:/日语云课/pages/my-exchange/my-exchange.vue?4ae0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "nodata", "data", "loading", "current_page", "total_page", "total", "list", "isHide", "onShow", "onHide", "created", "methods", "getCourseList", "onScrolltolowerHandler", "apiGetCourseList", "params", "page", "r_type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAylB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCuC7mB;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MAEA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAAgpC,CAAgB,4nCAAG,EAAC,C;;;;;;;;;;;ACApqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my-exchange/my-exchange.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my-exchange/my-exchange.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my-exchange.vue?vue&type=template&id=fc887460&scoped=true&\"\nvar renderjs\nimport script from \"./my-exchange.vue?vue&type=script&lang=js&\"\nexport * from \"./my-exchange.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my-exchange.vue?vue&type=style&index=0&id=fc887460&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fc887460\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my-exchange/my-exchange.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-exchange.vue?vue&type=template&id=fc887460&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    nodata: function () {\n      return import(\n        /* webpackChunkName: \"components/nodata/nodata\" */ \"@/components/nodata/nodata.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-exchange.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-exchange.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\"  class=\"root-box\">\n\t\t<!-- 页面主体 -->\n\t\t<view slot=\"gBody\" class=\" lp-flex-column\">\n\t\t\t<!-- 列表内容 -->\n\t\t\t<scroll-view v-if=\"list.length>0\" scroll-y=\"true\" @scrolltolower=\"onScrolltolowerHandler\">\n\t\t\t\t<view class=\"list-box\">\n\t\t\t\t\t<navigator v-for=\"(item,index) in list\" :key=\"item.id\">\n\t\t\t\t\t\t<view class=\"item-box\">\n\t\t\t\t\t\t\t<view class=\"top-box\">\n\t\t\t\t\t\t\t\t<view class=\"cover-box\">\n\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.picture\"></image>\n\t\t\t\t\t\t\t\t\t<image class=\"cover cover-mask\" src=\"/static/imgs/cover_mask.png\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"info-box\">\n\t\t\t\t\t\t\t\t\t<text class=\"title\">{{item.title}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"center-box lp-flex lp-flex-space-between\">\n\t\t\t\t\t\t\t\t<text>兑换时间：{{item.created_time}}</text>\n\t\t\t\t\t\t\t\t<text>兑换码：{{item.sn}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</navigator>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<!-- 暂无数据 -->\n\t\t\t<nodata v-else=\"total == 0\" text=\"暂无数据\"></nodata>\n\t\t\t<!-- 兑换 -->\n\t\t\t<view class=\"btn-box\">\n\t\t\t\t<navigator url=\"./exchange\">兑换</navigator>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\n\timport nodata from '@/components/nodata/nodata.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tnodata\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 加载中\n\t\t\t\tloading: false,\n\t\t\t\tcurrent_page: 1,\n\t\t\t\t// 总页数\n\t\t\t\ttotal_page: 1,\n\t\t\t\t// 总数量\n\t\t\t\ttotal: 0,\n\t\t\t\t// 列表数据\n\t\t\t\tlist: [],\n\t\t\t\t// 刷新\n\t\t\t\tisHide: false,\n\t\t\t}\n\t\t},\n\t\tonShow: function() {\n\t\t\tif(this.isHide){\n\t\t\t\tthis.isHide = false;\n\t\t\t\tthis.getCourseList(1, true);\n\t\t\t}\n\t\t},\n\t\tonHide: function() {\n\t\t\tthis.isHide = true;\n\t\t},\n\t\tcreated: function() {\n\t\t\tthis.getCourseList(1, true);\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tgetCourseList: function(page, force) {\n\t\t\t\tif (this.loading) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!force && page > this.total_page) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.apiGetCourseList(page,getApp().globalData.r_type).then(pagination => {\n\t\t\t\t\tthis.current_page = pagination.current_page;\n\t\t\t\t\tthis.total_page = pagination.last_page;\n\t\t\t\t\tthis.total = pagination.total;\n\t\t\t\t\tthis.list = pagination.data;\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t})\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// hander\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonScrolltolowerHandler: function() {\n\t\t\t\tthis.getCourseList(this.current_page + 1);\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tapiGetCourseList: function(page,r_type) {\n\t\t\t\treturn this.$http.get('/v1/member/exchangesList', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tpage,\r\n\t\t\t\t\t\tr_type\n\t\t\t\t\t}\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\n\t\tflex: 1;\n\t\tbackground-color: #f2f2f2;\n\n\t\tscroll-view {\n\t\t\tmax-height: 100%;\n\t\t}\n\n\t\t.list-box {\n\t\t\tpadding-bottom: 130rpx;\n\n\t\t\t.item-box {\n\t\t\t\tmargin: 30rpx 30rpx 0 30rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tcolor: #666;\n\t\t\t\tborder-radius: 0 0 10rpx 10rpx;\n\n\t\t\t\t.top-box {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\theight: 448rpx;\n\n\t\t\t\t\t.cover-box {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tbottom: 0;\n\n\t\t\t\t\t\t.cover {\n\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.info-box {\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tz-index: 1;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tpadding: 30rpx;\n\n\t\t\t\t\t\t.title {\n\t\t\t\t\t\t\tfont-size: 48rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.center-box {\n\t\t\t\t\tpadding: 30rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* 兑换按钮 */\n\t\t.btn-box {\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tz-index: 99;\n\n\t\t\tnavigator {\n\t\t\t\tpadding: 30rpx;\n\t\t\t\tbackground: $uni-color-error;\n\t\t\t\ttext-align: center;\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-exchange.vue?vue&type=style&index=0&id=fc887460&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my-exchange.vue?vue&type=style&index=0&id=fc887460&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753663861668\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}