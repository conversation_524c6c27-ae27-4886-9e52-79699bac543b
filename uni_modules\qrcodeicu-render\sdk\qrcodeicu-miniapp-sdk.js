/*! For license information please see qrcodeicu-miniapp-sdk.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("QrcodeIcuSdk",[],t):"object"==typeof exports?exports.QrcodeIcuSdk=t():e.QrcodeIcuSdk=t()}(self,(function(){return(()=>{var e={160:e=>{e.exports=function(e){for(var t,r="",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=new Uint8Array(e),i=o.byteLength,a=i%3,s=i-a,c=0;c<s;c+=3)r+=n[(16515072&(t=o[c]<<16|o[c+1]<<8|o[c+2]))>>18]+n[(258048&t)>>12]+n[(4032&t)>>6]+n[63&t];return 1==a?r+=n[(252&(t=o[s]))>>2]+n[(3&t)<<4]+"==":2==a&&(r+=n[(64512&(t=o[s]<<8|o[s+1]))>>10]+n[(1008&t)>>4]+n[(15&t)<<2]+"="),r}},568:e=>{"use strict";var t=Object.prototype.toString;function r(e){return"[object Array]"===t.call(e)}function n(e){return void 0===e}function o(e){return null!==e&&"object"==typeof e}function i(e){return"[object Function]"===t.call(e)}function a(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),r(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var s={isArray:r,isArrayBuffer:function(e){return"[object ArrayBuffer]"===t.call(e)},isBuffer:function(e){return null!==e&&!n(e)&&null!==e.constructor&&!n(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:o,isUndefined:n,isDate:function(e){return"[object Date]"===t.call(e)},isFile:function(e){return"[object File]"===t.call(e)},isBlob:function(e){return"[object Blob]"===t.call(e)},isFunction:i,isStream:function(e){return o(e)&&i(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:a,merge:function e(){var t={};function r(r,n){"object"==typeof t[n]&&"object"==typeof r?t[n]=e(t[n],r):t[n]=r}for(var n=0,o=arguments.length;n<o;n++)a(arguments[n],r);return t},deepMerge:function e(){var t={};function r(r,n){"object"==typeof t[n]&&"object"==typeof r?t[n]=e(t[n],r):t[n]="object"==typeof r?e({},r):r}for(var n=0,o=arguments.length;n<o;n++)a(arguments[n],r);return t},extend:function(e,t,r){return a(t,(function(t,n){e[n]=r&&"function"==typeof t?function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}(t,r):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}},c=function(e,t,r,n,o){return function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}(new Error(e),t,r,n,o)};function u(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var f=function(e,t,r){if(!t)return e;var n;if(r)n=r(t);else if(s.isURLSearchParams(t))n=t.toString();else{var o=[];s.forEach(t,(function(e,t){null!=e&&(s.isArray(e)?t+="[]":e=[e],s.forEach(e,(function(e){s.isDate(e)?e=e.toISOString():s.isObject(e)&&(e=JSON.stringify(e)),o.push(u(t)+"="+u(e))})))})),n=o.join("&")}if(n){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e},l="wechat",p=console.warn;e.exports=function(e){var t=function(){switch(!0){case"object"==typeof wx:return l="wechat",wx.request.bind(wx);case"object"==typeof swan:return l="baidu",swan.request.bind(swan);case"object"==typeof ft:return l="finclip",ft.request.bind(ft);case"object"==typeof my:return l="alipay",(my.request||my.httpRequest).bind(my);default:return wx.request.bind(wx)}}();return new Promise((function(r,n){var o,i,a,u=e.data,h=e.headers,d={method:e.method&&e.method.toUpperCase()||"GET",url:f((i=e.baseURL,a=e.url,i&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(a)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(i,a):a),e.params,e.paramsSerializer),success:function(t){var o=function(e,t,r){var n=e.header||e.headers,o=e.statusCode||e.status,i="";return 200===o?i="OK":400===o&&(i="Bad Request"),{data:e.data,status:o,statusText:i,headers:n,config:t,request:r}}(t,e,d);!function(e,t,r){var n=r.config.validateStatus;!n||n(r.status)?e(r):t(c("Request failed with status code "+r.status,r.config,null,r.request,r))}(r,n,o)},fail:function(t){!function(e,t,r){switch(l){case"wechat":-1!==e.errMsg.indexOf("request:fail abort")?t(c("Request aborted",r,"ECONNABORTED","")):-1!==e.errMsg.indexOf("timeout")?t(c("timeout of "+r.timeout+"ms exceeded",r,"ECONNABORTED","")):t(c("Network Error",r,null,""));break;case"alipay":[14,19].includes(e.error)?t(c("Request aborted",r,"ECONNABORTED","",e)):[13].includes(e.error)?t(c("timeout of "+r.timeout+"ms exceeded",r,"ECONNABORTED","",e)):t(c("Network Error",r,null,"",e));break;case"baidu":t(c("Network Error",r,null,""))}}(t,n,e)},complete:function(){o=void 0}};if(e.auth){var m=[e.auth.username||"",e.auth.password||""],g=m[0],y=m[1];h.Authorization="Basic "+function(e){for(var t,r,n=String(e),o=0,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a="";n.charAt(0|o)||(i="=",o%1);a+=i.charAt(63&t>>8-o%1*8)){if((r=n.charCodeAt(o+=3/4))>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");t=t<<8|r}return a}(g+":"+y)}0!==e.timeout&&p('The "timeout" option is not supported by miniprogram. For more information about usage see "https://developers.weixin.qq.com/miniprogram/dev/framework/config.html#全局配置"'),s.forEach(h,(function(e,t){var r=t.toLowerCase();(void 0===u&&"content-type"===r||"referer"===r)&&delete h[t]})),d.header=h,e.responseType&&(d.responseType=e.responseType),e.cancelToken&&e.cancelToken.promise.then((function(e){o&&(o.abort(),n(e),o=void 0)})),function(e){try{return"string"==typeof e&&e.length&&(e=JSON.parse(e))&&"[object Object]"===Object.prototype.toString.call(e)}catch(e){return!1}}(u)&&(u=JSON.parse(u)),void 0!==u&&(d.data=u),o=t(function(e){return"alipay"===l&&(e.headers=e.header,delete e.header),e}(d))}))}},521:(e,t,r)=>{"use strict";var n=r(669),o=r(727),i=r(325),a=r(795),s=function e(t){var r=new i(t),s=o(i.prototype.request,r);return n.extend(s,i.prototype,r),n.extend(s,r),s.create=function(r){return e(a(t,r))},s}(r(683));s.Axios=i,s.Cancel=r(101),s.CancelToken=r(534),s.isCancel=r(745),s.VERSION=r(665).version,s.all=function(e){return Promise.all(e)},s.spread=r(959),s.isAxiosError=r(702),e.exports=s,e.exports.default=s},101:e=>{"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},534:(e,t,r)=>{"use strict";var n=r(101);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;this.promise.then((function(e){if(r._listeners){var t,n=r._listeners.length;for(t=0;t<n;t++)r._listeners[t](e);r._listeners=null}})),this.promise.then=function(e){var t,n=new Promise((function(e){r.subscribe(e),t=e})).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},o.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},745:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},325:(e,t,r)=>{"use strict";var n=r(669),o=r(396),i=r(814),a=r(381),s=r(795),c=r(178),u=c.validators;function f(e){this.defaults=e,this.interceptors={request:new i,response:new i}}f.prototype.request=function(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},(t=s(this.defaults,t)).method?t.method=t.method.toLowerCase():this.defaults.method?t.method=this.defaults.method.toLowerCase():t.method="get";var r=t.transitional;void 0!==r&&c.assertOptions(r,{silentJSONParsing:u.transitional(u.boolean),forcedJSONParsing:u.transitional(u.boolean),clarifyTimeoutError:u.transitional(u.boolean)},!1);var n=[],o=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,n.unshift(e.fulfilled,e.rejected))}));var i,f=[];if(this.interceptors.response.forEach((function(e){f.push(e.fulfilled,e.rejected)})),!o){var l=[a,void 0];for(Array.prototype.unshift.apply(l,n),l=l.concat(f),i=Promise.resolve(t);l.length;)i=i.then(l.shift(),l.shift());return i}for(var p=t;n.length;){var h=n.shift(),d=n.shift();try{p=h(p)}catch(e){d(e);break}}try{i=a(p)}catch(e){return Promise.reject(e)}for(;f.length;)i=i.then(f.shift(),f.shift());return i},f.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],(function(e){f.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(e){f.prototype[e]=function(t,r,n){return this.request(s(n||{},{method:e,url:t,data:r}))}})),e.exports=f},814:(e,t,r)=>{"use strict";var n=r(669);function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},381:(e,t,r)=>{"use strict";var n=r(669),o=r(258),i=r(745),a=r(683),s=r(101);function c(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new s("canceled")}e.exports=function(e){return c(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||a.adapter)(e).then((function(t){return c(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(c(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},243:e=>{"use strict";e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},e}},795:(e,t,r)=>{"use strict";var n=r(669);e.exports=function(e,t){t=t||{};var r={};function o(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function i(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(e[r],t[r])}function a(e){if(!n.isUndefined(t[e]))return o(void 0,t[e])}function s(r){return n.isUndefined(t[r])?n.isUndefined(e[r])?void 0:o(void 0,e[r]):o(void 0,t[r])}function c(r){return r in t?o(e[r],t[r]):r in e?o(void 0,e[r]):void 0}var u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c};return n.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=u[e]||i,o=t(e);n.isUndefined(o)&&t!==c||(r[e]=o)})),r}},258:(e,t,r)=>{"use strict";var n=r(669),o=r(683);e.exports=function(e,t,r){var i=this||o;return n.forEach(r,(function(r){e=r.call(i,e,t)})),e}},683:(e,t,r)=>{"use strict";var n=r(669),o=r(18),i=r(243),a=r(274),s={"Content-Type":"application/x-www-form-urlencoded"};function c(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var u={transitional:a,adapter:void 0,transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(c(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)||t&&"application/json"===t["Content-Type"]?(c(t,"application/json"),function(e,t,r){if(n.isString(e))try{return(0,JSON.parse)(e),n.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||u.transitional,r=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,a=!r&&"json"===this.responseType;if(a||o&&n.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(a){if("SyntaxError"===e.name)throw i(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){u.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){u.headers[e]=n.merge(s)})),e.exports=u},274:e=>{"use strict";e.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},665:e=>{e.exports={version:"0.26.1"}},727:e=>{"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},396:(e,t,r)=>{"use strict";var n=r(669);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var i;if(r)i=r(t);else if(n.isURLSearchParams(t))i=t.toString();else{var a=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),a.push(o(t)+"="+o(e))})))})),i=a.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},702:(e,t,r)=>{"use strict";var n=r(669);e.exports=function(e){return n.isObject(e)&&!0===e.isAxiosError}},18:(e,t,r)=>{"use strict";var n=r(669);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},959:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},178:(e,t,r)=>{"use strict";var n=r(665).version,o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));var i={};o.transitional=function(e,t,r){function o(e,t){return"[Axios v"+n+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,n,a){if(!1===e)throw new Error(o(n," has been removed"+(t?" in "+t:"")));return t&&!i[n]&&(i[n]=!0,console.warn(o(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,a)}},e.exports={assertOptions:function(e,t,r){if("object"!=typeof e)throw new TypeError("options must be an object");for(var n=Object.keys(e),o=n.length;o-- >0;){var i=n[o],a=t[i];if(a){var s=e[i],c=void 0===s||a(s,i,e);if(!0!==c)throw new TypeError("option "+i+" must be "+c)}else if(!0!==r)throw Error("Unknown option "+i)}},validators:o}},669:(e,t,r)=>{"use strict";var n=r(727),o=Object.prototype.toString;function i(e){return Array.isArray(e)}function a(e){return void 0===e}function s(e){return"[object ArrayBuffer]"===o.call(e)}function c(e){return null!==e&&"object"==typeof e}function u(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function f(e){return"[object Function]"===o.call(e)}function l(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:i,isArrayBuffer:s,isBuffer:function(e){return null!==e&&!a(e)&&null!==e.constructor&&!a(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"[object FormData]"===o.call(e)},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&s(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:c,isPlainObject:u,isUndefined:a,isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:f,isStream:function(e){return c(e)&&f(e.pipe)},isURLSearchParams:function(e){return"[object URLSearchParams]"===o.call(e)},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:l,merge:function e(){var t={};function r(r,n){u(t[n])&&u(r)?t[n]=e(t[n],r):u(r)?t[n]=e({},r):i(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)l(arguments[n],r);return t},extend:function(e,t,r){return l(t,(function(t,o){e[o]=r&&"function"==typeof t?n(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},835:(e,t,r)=>{const n=r(521),o=r(868),{filter:i}=r(992);e.exports=class{constructor(){}init({qrcodeIcuEndPoint:e,renderEndPoint:t,appId:r,key:n,accessToken:o}){return this._renderEndPoint=t,this._qrcodeIcuEndPoint=e,this._appId=r,this._key=n,this._token=o,this}render(e,t){return this._=Symbol(),this._renderContext={[this._]:{posterId:e,posterStorageId:t}},this}addTextParam(e,t){return this._renderContext[e]=t,this}addImageFileBase64Param(e,t){return this._renderContext[e]={format:"fileBase64",contentType:"image/jpg",value:t},this}addImageDataURLParam(e,t){return this._renderContext[e]={format:"dataURL",contentType:"image/jpg",value:t},this}async fetch(){if(null==this._renderContext)throw new Error("请先执行 render() 函数");if(!this._token&&(this._token=await this._fetchToken(),null==this._token))return;const e=`${this._renderEndPoint}?a=render&pid=${this._renderContext[this._].posterId}&pcid=${this._renderContext[this._].posterStorageId}&boolSegment=false`,t={},r=Object.keys(this._renderContext);for(let e of r)if(this._renderContext[e].value instanceof Promise){const r=await this._renderContext[e].value;null!=r&&(t[e]=this._renderContext[e],t[e].value=r)}else t[e]=this._renderContext[e];const i=JSON.stringify(t),a=((new Date).getTime(),o.encode(i));return new Promise((t=>{n.post(e,a,{headers:{Authorization:`Bearer ${this._token.access_token}`}}).then((e=>{try{const r=e.data.content[0];r.totalCostTime=e.data.totalCostTime,r.dynamicValueCount=e.data.dynamicValueCount,r.outputWidth=e.data.outputWidth,r.outputHeight=e.data.outputHeight,r.itemCount=e.data.itemCount,t(r)}catch(e){console.log(e),t(null)}})).catch((e=>{console.log(e),t(null)}))}))}async _fetchToken(){const e=`${this._qrcodeIcuEndPoint}/oauth/token?client_id=${this._appId}&&client_secret=${this._key}&grant_type=client_credentials`;return new Promise((t=>{n.post(e).then((e=>{t(e.data)})).catch((e=>{console.log("error:",e),t(null)}))}))}async getDynamics(e){if(!this._token&&(this._token=await this._fetchToken(),null==this._token))return;const t=`${this._qrcodeIcuEndPoint}/posters/${e}/dynamics`;return new Promise((e=>{n.get(t,{headers:{Authorization:`Bearer ${this._token.access_token}`}}).then((t=>{try{e(t.data)}catch(t){e(null)}})).catch((t=>{console.log(t),e(null)}))}))}}},868:(e,t,r)=>{function n(e){return e.replace(/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,(function(e){return decodeURIComponent(escape(e))}))}var o,i={encode:function(e){if(e=e.replace(/[^\x00-\xff]/g,(function(e){return unescape(encodeURIComponent(e))})),r.g.btoa)return r.g.btoa(e);for(var t,n=[],o=0,i=0,a=e.length;o<a;o++){switch(3==++i&&(i=0),t=e.charCodeAt(o),i){case 1:n.push(t>>2&63);break;case 2:n.push(63&(e.charCodeAt(o-1)<<4|t>>4));break;case 0:n.push(63&(e.charCodeAt(o-1)<<2|t>>6)),n.push(63&t)}o===a-1&&i>0&&n.push(t<<(3-i<<1)&63)}for(o=0,a=n.length;o<a;o++)n[o]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n[o]);if(i)for(;3-i++>0;)n.push("=");return n.join("")},decode:(o=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,-1,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],function(e){if(r.g.atob)return n(r.g.atob(e));var t,i,a,s=[],c=0,u=0;for(c=0,i=o[e.charCodeAt(0)],a=e.length;c<a;)u++,t=i,-1===(i=o[e.charCodeAt(++c)])&&(c=a,i=0),s.push(t<<(u<<1)&255|i>>(3-u<<1)),3===u&&(c++,i=o[e.charCodeAt(c)],u=0);return n(String.fromCharCode.apply(String,s))})};e.exports=i},553:e=>{"use strict";function t(e,t){if("string"!=typeof e)return[e];var r=[e];"string"==typeof t||Array.isArray(t)?t={brackets:t}:t||(t={});var n=t.brackets?Array.isArray(t.brackets)?t.brackets:[t.brackets]:["{}","[]","()"],o=t.escape||"___",i=!!t.flat;n.forEach((function(e){var t=new RegExp(["\\",e[0],"[^\\",e[0],"\\",e[1],"]*\\",e[1]].join("")),n=[];function i(t,i,a){var s=r.push(t.slice(e[0].length,-e[1].length))-1;return n.push(s),o+s+o}r.forEach((function(e,n){for(var o,a=0;e!=o;)if(o=e,e=e.replace(t,i),a++>1e4)throw Error("References have circular dependency. Please, check them.");r[n]=e})),n=n.reverse(),r=r.map((function(t){return n.forEach((function(r){t=t.replace(new RegExp("(\\"+o+r+"\\"+o+")","g"),e[0]+"$1"+e[1])})),t}))}));var a=new RegExp("\\"+o+"([0-9]+)\\"+o);return i?r:function e(t,r,n){for(var o,i=[],s=0;o=a.exec(t);){if(s++>1e4)throw Error("Circular references in parenthesis");i.push(t.slice(0,o.index)),i.push(e(r[o[1]],r)),t=t.slice(o.index+o[0].length)}return i.push(t),i}(r[0],r)}function r(e,t){if(t&&t.flat){var r,n=t&&t.escape||"___",o=e[0];if(!o)return"";for(var i=new RegExp("\\"+n+"([0-9]+)\\"+n),a=0;o!=r;){if(a++>1e4)throw Error("Circular references in "+e);r=o,o=o.replace(i,s)}return o}return e.reduce((function e(t,r){return Array.isArray(r)&&(r=r.reduce(e,"")),t+r}),"");function s(t,r){if(null==e[r])throw Error("Reference "+r+"is undefined");return e[r]}}function n(e,n){return Array.isArray(e)?r(e,n):t(e,n)}n.parse=t,n.stringify=r,e.exports=n},992:(e,t,r)=>{"use strict";var n,o=r(134),i={font:{},variant:{}};const a=/^(normal|italic|oblique)$/,s=/^(normal|small-caps)$/,c=/^(normal|(semi-|extra-|ultra-)?(condensed|expanded))$/,u=/(?:xx?-)?small|smaller|medium|larger|(?:xx?-)?large|normal/,f=/^([\d\.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/,l=/^(normal|bold(er)?|lighter)$/,p=/^(1000|\d{1,3})$/,h=/([\w\-]+)\((.*?)\)/,d=e=>e.replace(/^(['"])(.*?)\1$/,"$2"),m=e=>u.test(e)||f.test(e);function g(e,t=16){if(n=f.exec(e)){let[e,r]=[parseFloat(n[1]),n[2]];return e*("px"==r?1:"pt"==r?1/.75:"%"==r?t/100:"pc"==r?16:"in"==r?96:"cm"==r?96/2.54:"mm"==r?96/25.4:"q"==r?96/25.4/4:r.match("r?em")?t:NaN)}return(n=u.exec(e))?t*(S[n[0]]||1):NaN}function y(e){return(n=p.exec(e))?parseInt(n[0])||NaN:(n=l.exec(e))?O[n[0]]:NaN}var v=/(blur|hue-rotate|brightness|contrast|grayscale|invert|opacity|saturate|sepia)\((.*?)\)/,b=/drop-shadow\((.*)\)/,x=/^(\+|-)?\d+%$/,w=/([\d\.]+)(deg|g?rad|turn)/;function j(e){return x.test(e.trim())?parseInt(e,10)/100:NaN}function E(e){if(n=w.exec(e.trim())){let[e,t]=[parseFloat(n[1]),n[2]];return"deg"==t?e:"rad"==t?360*e/(2*Math.PI):"grad"==t?360*e/400:"turn"==t?360*e:NaN}}const O={lighter:300,normal:400,bold:700,bolder:800},S={"xx-small":.6,"x-small":3/4,small:8/9,smaller:8/9,large:1.2,larger:1.2,"x-large":1.5,"xx-large":2,normal:1.2},A={normal:[],"common-ligatures":["liga","clig"],"no-common-ligatures":["-liga","-clig"],"discretionary-ligatures":["dlig"],"no-discretionary-ligatures":["-dlig"],"historical-ligatures":["hlig"],"no-historical-ligatures":["-hlig"],contextual:["calt"],"no-contextual":["-calt"],super:["sups"],sub:["subs"],"small-caps":["smcp"],"all-small-caps":["c2sc","smcp"],"petite-caps":["pcap"],"all-petite-caps":["c2pc","pcap"],unicase:["unic"],"titling-caps":["titl"],"lining-nums":["lnum"],"oldstyle-nums":["onum"],"proportional-nums":["pnum"],"tabular-nums":["tnum"],"diagonal-fractions":["frac"],"stacked-fractions":["afrc"],ordinal:["ordn"],"slashed-zero":["zero"],jis78:["jp78"],jis83:["jp83"],jis90:["jp90"],jis04:["jp04"],simplified:["smpl"],traditional:["trad"],"full-width":["fwid"],"proportional-width":["pwid"],ruby:["ruby"],"historical-forms":["hist"]},N={stylistic:"salt #",styleset:"ss##","character-variant":"cv##",swash:"swsh #",ornaments:"ornm #",annotation:"nalt #"};e.exports={font:function(e){if(void 0===i.font[e])try{if("string"!=typeof e)throw new Error("Font specification must be a string");if(!e)throw new Error("Font specification cannot be an empty string");let r,n={style:"normal",variant:"normal",weight:"normal",stretch:"normal"},u=e.replace(/\s*\/\*s/,"/"),f=o(u,/\s+/);for(;r=f.shift();){let u=a.test(r)?"style":s.test(r)?"variant":c.test(r)?"stretch":(t=r,l.test(t)||p.test(t)?"weight":m(r)?"size":null);switch(u){case"style":case"variant":case"stretch":case"weight":n[u]=r;break;case"size":let[t,a]=o(r,"/"),s=g(t),c=g((a||"1.2").replace(/(\d)$/,"$1em"),s),l=y(n.weight),p=o(f.join(" "),/\s*,\s*/).map(d),h="small-caps"==n.variant?{on:["smcp","onum"]}:{},{style:m,stretch:v,variant:b}=n,x=isFinite(s)?isFinite(c)?isFinite(l)?0==p.length&&`font family "${f.join(", ")}"`:`font weight "${n.weight}"`:`line height "${a}"`:`font size "${t}"`;if(!x)return i.font[e]=Object.assign(n,{size:s,lineHeight:c,weight:l,family:p,features:h,canonical:[m,b!==m&&b,-1==[b,m].indexOf(l)&&l,-1==[b,m,l].indexOf(v)&&v,`${s}px/${c}px`,p.map((e=>e.match(/\s/)?`"${e}"`:e)).join(", ")].filter(Boolean).join(" ")});throw new Error(`Invalid ${x}`);default:throw new Error(`Unrecognized font attribute "${r}"`)}}throw new Error("Could not find a font size value")}catch(t){i.font[e]=null}var t;return i.font[e]},variant:function(e){if(void 0===i.variant[e]){let t=[],r={on:[],off:[]};for(let i of o(e,/\s+/)){if("normal"==i)return{variants:[i],features:{on:[],off:[]}};if(i in A)A[i].forEach((e=>{"-"==e[0]?r.off.push(e.slice(1)):r.on.push(e)})),t.push(i);else{if(!(n=h.exec(i)))throw new Error(`Invalid font variant "${i}"`);{let e=N[n[1]],o=Math.max(0,Math.min(99,parseInt(n[2],10))),[i,a]=e.replace(/##/,o<10?"0"+o:o).replace(/#/,Math.min(9,o)).split(" ");void 0===a?r.on.push(i):r[i]=parseInt(a,10),t.push(`${n[1]}(${o})`)}}}i.variant[e]={variant:t.join(" "),features:r}}return i.variant[e]},size:g,filter:function(e){let t={},r=[];for(var i of o(e,/\s+/)||[])if(n=b.exec(i)){let e="drop-shadow",o=n[1].trim().split(/\s+/),i=o.slice(0,3),a=o.slice(3).join(" "),s=i.map((e=>g(e))).filter(isFinite);3==s.length&&a&&(t[e]=[...s,a],r.push(`${e}(${i.join(" ")} ${a.replace(/ /g,"")})`))}else if(n=v.exec(i)){let[e,o]=n.slice(1),i="blur"==e?g(o):"hue-rotate"==e?E(o):j(o);isFinite(i)&&(t[e]=i,r.push(`${e}(${o.trim()})`))}return"none"==e.trim()?{canonical:"none",filters:t}:r.length?{canonical:r.join(" "),filters:t}:null}}},134:(e,t,r)=>{"use strict";var n=r(553);e.exports=function(e,t,r){if(null==e)throw Error("First argument should be a string");if(null==t)throw Error("Separator should be a string or a RegExp");r?("string"==typeof r||Array.isArray(r))&&(r={ignore:r}):r={},null==r.escape&&(r.escape=!0),null==r.ignore?r.ignore=["[]","()","{}","<>",'""',"''","``","“”","«»"]:("string"==typeof r.ignore&&(r.ignore=[r.ignore]),r.ignore=r.ignore.map((function(e){return 1===e.length&&(e+=e),e})));var o=n.parse(e,{flat:!0,brackets:r.ignore}),i=o[0].split(t);if(r.escape){for(var a=[],s=0;s<i.length;s++){var c=i[s],u=i[s+1];"\\"===c[c.length-1]&&"\\"!==c[c.length-2]?(a.push(c+t+u),s++):a.push(c)}i=a}for(s=0;s<i.length;s++)o[0]=i[s],i[s]=n.stringify(o,{flat:!0});return i}}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};return(()=>{"use strict";r.d(n,{default:()=>i});const e=r(521);e.defaults.adapter=r(568);const t=r(835),o=r(160),i=class extends t{constructor(){super()}addImageFileBase64ByUrl(e,t){try{this.addImageFileBase64Param(e,this._loadImage(t))}catch(e){}return this}_loadImage(t){return new Promise((r=>{e.get(t,{responseType:"arraybuffer"}).then((e=>{const t=o(e.data);r(t)}))}))}}})(),n.default})()}));