(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["GraceUI5/components/gui-page-loading"],{"23e2":function(n,t,e){"use strict";e.r(t);var i=e("d315"),u=e("bccc");for(var a in u)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(a);e("7a8b");var o=e("828b"),c=Object(o["a"])(u["default"],i["b"],i["c"],!1,null,"6a6af1bb",null,!1,i["a"],void 0);t["default"]=c.exports},5236:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"gui-page-loading",props:{},data:function(){return{isLoading:!1,BindingXObjs:[null,null,null],AnimateObjs:[null,null,null],animateTimer:800,intervalID:null}},watch:{},methods:{stopfun:function(n){return n.stopPropagation(),null},open:function(){this.isLoading=!0},close:function(){var n=this;setTimeout((function(){n.isLoading=!1}),100)},getRefs:function(n,t,e){var i=this;if(t>=30)return null;var u=this.$refs[n];u?e(u):(t++,setTimeout((function(){i.getRefs(n,t,e)}),50))}}};t.default=i},"7a8b":function(n,t,e){"use strict";var i=e("c749"),u=e.n(i);u.a},bccc:function(n,t,e){"use strict";e.r(t);var i=e("5236"),u=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=u.a},c749:function(n,t,e){},d315:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return u})),e.d(t,"a",(function(){}));var i=function(){var n=this.$createElement;this._self._c},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'GraceUI5/components/gui-page-loading-create-component',
    {
        'GraceUI5/components/gui-page-loading-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("23e2"))
        })
    },
    [['GraceUI5/components/gui-page-loading-create-component']]
]);
