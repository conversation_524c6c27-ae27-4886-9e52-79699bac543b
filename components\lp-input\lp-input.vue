<template>
	<view class="root-box" :class="{show:show}" :style="{bottom: keyboardHeight+'px'}">
		<view class="input-box lp-flex" :class="{show:show}">
			<!-- :cursor-spacing="75" -->
			<textarea class="input" :maxlength="5000" :show-confirm-bar="false" :focus="focus" :adjust-position="false"
				v-model="text" @focus="onFocusHandler" @blur="onBlurHandler"
				@input="onChangeHandler" @confirm="onChangeHandler">
			 </textarea>
			<view class="close-box lp-flex-center" @tap="onCloseHandler">完成输入</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "input",
		data() {
			return {
				focus: false,
				show: false,
				text: '',
				keyboardHeight: -190
			};
		},
		created() {
			let self = this;
			uni.$on('lp-get-input', function(content) {
				if (!self.show) {
					self.text = content;
				}
				self.show = true;
				self.focus = true
			});
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			setText: function(v) {
				this.text = v;
			},
			getText: function() {
				return this.text;
			},

			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 得到焦点
			 */
			onFocusHandler: function(e) {
				this.focus = true;
				this.keyboardHeight = e.detail.height;
			},
			onBlurHandler: function(e) {
				this.focus = false;
				this.keyboardHeight = 0;
			},
			onCloseHandler: function() {
				this.keyboardHeight = -190;
				this.focus = false
				this.show = false;
				uni.$emit('lp-input-completed', this.text);
			},
			/**
			 * 笔译或者口译有变化时调用
			 */
			onChangeHandler: function() {},
		},
	}
</script>

<style lang="scss" scoped>
	.root-box {
		position: absolute;
		width: 100%;
		background: #fff;
		transition: all 0.3s ease;
		transform: translateY(100%);

		.input-box {
			padding: 20rpx;
			border-top: solid 1px #ddd;
			height: 150rpx;
			-moz-box-shadow: 0px -1px 5px #ddd;
			-webkit-box-shadow: 0px -1px 5px #ddd;
			box-shadow: 0px -1px 5px #ddd;

			.close-box {
				background: #28b28b;
				color: #fff;
				font-size: 24rpx;
				border-radius: 10rpx;
				margin-left: 10rpx;
				padding: 20rpx;
				//width: 50rpx;
			}

			.input {
				flex: 1;
				font-size: 26rpx;
				height: 128rpx;
				padding: 10rpx;
				/* 
				border: solid 1px #eee;
				border-radius: 10rpx;
				box-shadow: inset 0px 0px 3px 1px #eee; */

				border-radius: 10rpx;
				border: solid 1px $uni-color-primary;
			}
		}

		.show {}
	}

	.show {
		transform: translateY(0);
	}
</style>
