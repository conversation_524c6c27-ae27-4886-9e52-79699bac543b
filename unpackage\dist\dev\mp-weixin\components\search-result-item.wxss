
.search-result-item.data-v-0e7bdf99 {
	display: flex;
	padding: 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;
}
.search-result-item.data-v-0e7bdf99:active {
	background-color: #f8f9fa;
}
.course-image.data-v-0e7bdf99 {
	margin-right: 20rpx;
	flex-shrink: 0;
}
.item-content.data-v-0e7bdf99 {
	flex: 1;
	min-width: 0;
}
.course-title.data-v-0e7bdf99 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
.course-title.data-v-0e7bdf99 .highlight {
	color: #2094CE;
	background: rgba(32, 148, 206, 0.1);
	padding: 2rpx 4rpx;
	border-radius: 4rpx;
	font-weight: bold;
}
.course-desc.data-v-0e7bdf99 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
.course-meta.data-v-0e7bdf99 {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 12rpx;
	flex-wrap: wrap;
}
.price.data-v-0e7bdf99 {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4d4f;
}
.students.data-v-0e7bdf99, .lessons.data-v-0e7bdf99 {
	font-size: 24rpx;
	color: #999;
}
.course-tags.data-v-0e7bdf99 {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}
.tag.data-v-0e7bdf99 {
	font-size: 22rpx;
	color: #2094CE;
	background: rgba(32, 148, 206, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	border: 1rpx solid rgba(32, 148, 206, 0.2);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.search-result-item.data-v-0e7bdf99 {
		flex-direction: column;
		text-align: center;
}
.course-image.data-v-0e7bdf99 {
		margin-right: 0;
		margin-bottom: 20rpx;
		align-self: center;
}
.course-meta.data-v-0e7bdf99 {
		justify-content: center;
}
.course-tags.data-v-0e7bdf99 {
		justify-content: center;
}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
.search-result-item.data-v-0e7bdf99 {
		background: #1f1f1f;
		border-bottom-color: #333;
}
.search-result-item.data-v-0e7bdf99:active {
		background-color: #2a2a2a;
}
.course-title.data-v-0e7bdf99 {
		color: #fff;
}
.course-desc.data-v-0e7bdf99 {
		color: #ccc;
}
.students.data-v-0e7bdf99, .lessons.data-v-0e7bdf99 {
		color: #999;
}
}

