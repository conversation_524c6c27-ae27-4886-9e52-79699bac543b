@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-32f7efa7 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-32f7efa7 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-32f7efa7 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-32f7efa7 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-32f7efa7 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-32f7efa7 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-32f7efa7::after {
  border: none;
}
.u-hover-class.data-v-32f7efa7 {
  opacity: 0.7;
}
.u-primary-light.data-v-32f7efa7 {
  color: #ecf5ff;
}
.u-warning-light.data-v-32f7efa7 {
  color: #fdf6ec;
}
.u-success-light.data-v-32f7efa7 {
  color: #f5fff0;
}
.u-error-light.data-v-32f7efa7 {
  color: #fef0f0;
}
.u-info-light.data-v-32f7efa7 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-32f7efa7 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-32f7efa7 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-32f7efa7 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-32f7efa7 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-32f7efa7 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-32f7efa7 {
  color: #398ade;
}
.u-warning-dark.data-v-32f7efa7 {
  color: #f1a532;
}
.u-success-dark.data-v-32f7efa7 {
  color: #53c21d;
}
.u-error-dark.data-v-32f7efa7 {
  color: #e45656;
}
.u-info-dark.data-v-32f7efa7 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-32f7efa7 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-32f7efa7 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-32f7efa7 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-32f7efa7 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-32f7efa7 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-32f7efa7 {
  color: #9acafc;
}
.u-warning-disabled.data-v-32f7efa7 {
  color: #f9d39b;
}
.u-success-disabled.data-v-32f7efa7 {
  color: #a9e08f;
}
.u-error-disabled.data-v-32f7efa7 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-32f7efa7 {
  color: #c4c6c9;
}
.u-primary.data-v-32f7efa7 {
  color: #3c9cff;
}
.u-warning.data-v-32f7efa7 {
  color: #f9ae3d;
}
.u-success.data-v-32f7efa7 {
  color: #5ac725;
}
.u-error.data-v-32f7efa7 {
  color: #f56c6c;
}
.u-info.data-v-32f7efa7 {
  color: #909399;
}
.u-primary-bg.data-v-32f7efa7 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-32f7efa7 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-32f7efa7 {
  background-color: #5ac725;
}
.u-error-bg.data-v-32f7efa7 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-32f7efa7 {
  background-color: #909399;
}
.u-main-color.data-v-32f7efa7 {
  color: #303133;
}
.u-content-color.data-v-32f7efa7 {
  color: #606266;
}
.u-tips-color.data-v-32f7efa7 {
  color: #909193;
}
.u-light-color.data-v-32f7efa7 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-32f7efa7 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-32f7efa7 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-32f7efa7 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-32f7efa7 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-32f7efa7::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.root-box.data-v-32f7efa7 {
  flex: 1;
  background-color: #f2f2f2;
  /* 搜索框 */
  /* 列表 */
  /* 兑换按钮 */
}
.root-box .search-box.data-v-32f7efa7 {
  padding: 30rpx;
  font-size: 28rpx;
}
.root-box .search-box .input-box.data-v-32f7efa7 {
  flex: 1;
  padding: 20rpx 0;
  border: solid 1px #007aff;
  border-radius: 10rpx;
  margin-right: 30rpx;
}
.root-box .search-box .btn.data-v-32f7efa7 {
  background: #007aff;
  border: solid 1px #007aff;
  border-radius: 10rpx;
  color: #fff;
  padding: 0 20rpx;
  line-height: 80rpx;
}
.root-box scroll-view.data-v-32f7efa7 {
  max-height: 100%;
}
.root-box .list-box.data-v-32f7efa7 {
  padding-bottom: 20rpx;
}
.root-box .list-box .item-box.data-v-32f7efa7 {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
}
.root-box .list-box .item-box .top-box.data-v-32f7efa7 {
  position: relative;
}
.root-box .list-box .item-box .top-box .cover-box-hot.data-v-32f7efa7 {
  width: 50%;
  height: auto;
  min-height: 200rpx;
  position: relative;
}
.root-box .list-box .item-box .top-box .cover-box-hot .cover.data-v-32f7efa7 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.root-box .list-box .item-box .top-box .cover-box-hot .cover.data-v-32f7efa7 :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.root-box .list-box .item-box .top-box .cover-box-hot .button.data-v-32f7efa7 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.root-box .list-box .item-box .top-box .cover-box.data-v-32f7efa7 {
  width: 50%;
  height: auto;
  min-height: 200rpx;
  position: relative;
}
.root-box .list-box .item-box .top-box .cover-box .cover.data-v-32f7efa7 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.root-box .list-box .item-box .top-box .cover-box .button.data-v-32f7efa7 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.root-box .list-box .item-box .top-box .info-box.data-v-32f7efa7 {
  flex: 1;
  margin-left: 15rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.root-box .list-box .item-box .top-box .info-box .publish-date.data-v-32f7efa7 {
  font-size: 32rpx;
  font-weight: bold;
}
.root-box .list-box .item-box .top-box .info-box .lang-box.data-v-32f7efa7 {
  color: #999;
  font-size: 24rpx;
}
.root-box .list-box .item-box .top-box .info-box .title.data-v-32f7efa7 {
  font-weight: bold;
  font-size: 26rpx;
  color: #666666;
}
.root-box .list-box .item-box .top-box .info-box .end-date.data-v-32f7efa7 {
  font-size: 20rpx;
  color: #999999;
}
.root-box .list-box .item-box .top-box .info-box .total.data-v-32f7efa7 {
  font-size: 20rpx;
  color: #39b54a;
}
.root-box .list-box .item-box .top-box .info-box .des.data-v-32f7efa7 {
  font-size: 22rpx;
  color: #8f8f94;
}
.root-box .list-box .item-box .top-box .info-box .price.data-v-32f7efa7 {
  font-size: 24rpx;
  color: red;
  float: right;
}
.root-box .list-box .item-box .top-box .info-box .end.data-v-32f7efa7 {
  font-size: 24rpx;
  color: red;
  width: 100%;
}
.root-box .list-box .item-box .margin-tb-sm.data-v-32f7efa7 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.root-box .list-box .item-box .margin-tb-sm .text-sm.data-v-32f7efa7 {
  font-size: 24rpx;
}
.root-box .list-box .item-box .margin-tb-sm .title.data-v-32f7efa7 {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.root-box .list-box .item-box .margin-tb-sm .uni-row.data-v-32f7efa7 {
  flex-direction: row;
}
.root-box .list-box .item-box .margin-tb-sm .align-center.data-v-32f7efa7 {
  align-items: center;
}
.root-box .list-box .item-box .margin-tb-sm .margin-left-sm.data-v-32f7efa7 {
  margin-left: 20rpx;
}
.root-box .btn-box.data-v-32f7efa7 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.root-box .btn-box .btn.data-v-32f7efa7 {
  padding: 30rpx;
  background: #dd524d;
  text-align: center;
  color: #fff;
}
.root-box .btn-box .disabled.data-v-32f7efa7 {
  background: #f8f8f8;
  color: #999;
}

