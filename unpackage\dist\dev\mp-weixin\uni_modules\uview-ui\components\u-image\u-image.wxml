<u-transition vue-id="813ae896-1" mode="fade" show="{{show}}" duration="{{fade?1000:0}}" class="data-v-042b391e" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="u-image data-v-042b391e" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{!isError}}"><image class="u-image__image data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'10000px':$root.g0)+';'+('width:'+($root.g1)+';')+('height:'+($root.g2)+';')}}" src="{{src}}" mode="{{mode}}" show-menu-by-longpress="{{showMenuByLongpress}}" lazy-load="{{lazyLoad}}" data-event-opts="{{[['error',[['onErrorHandler',['$event']]]],['load',[['onLoadHandler',['$event']]]]]}}" binderror="__e" bindload="__e"></image></block><block wx:if="{{showLoading&&loading}}"><view class="u-image__loading data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'50%':$root.g3)+';'+('background-color:'+(this.bgColor)+';')}}"><block wx:if="{{$slots.loading}}"><slot name="loading"></slot></block><block wx:else><u-icon vue-id="{{('813ae896-2')+','+('813ae896-1')}}" name="{{loadingIcon}}" width="{{width}}" height="{{height}}" class="data-v-042b391e" bind:__l="__l"></u-icon></block></view></block><block wx:if="{{showError&&isError&&!loading}}"><view class="u-image__error data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'50%':$root.g4)+';'}}"><block wx:if="{{$slots.error}}"><slot name="error"></slot></block><block wx:else><u-icon vue-id="{{('813ae896-3')+','+('813ae896-1')}}" name="{{errorIcon}}" width="{{width}}" height="{{height}}" class="data-v-042b391e" bind:__l="__l"></u-icon></block></view></block></view></u-transition>