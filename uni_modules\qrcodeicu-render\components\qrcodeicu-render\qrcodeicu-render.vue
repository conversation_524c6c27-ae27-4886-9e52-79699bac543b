<template>
	<view class="container">
		<view class="fields-container" v-if="fields.length>0">
			<view class="title">参数设置</view>
			<view class="field-item" v-for="(field,index) in fields" v-bind:key="index">
				<view class="alias">{{field.alias}}
					<view v-if="field.boolDataTypeImage">（大小3M内）</view>
				</view>
				<input v-if="field.boolDataTypeText" class="input" v-model="field.value" auto-focus
					placeholder="输入参数" />
				<view @click="browse(field)" v-if="field.boolDataTypeImage" class="browse">
				</view>
				<view v-if="field.boolDataTypeImage&&field.valueImageFileBase64">（已选择）</view>
			</view>
			<view class="button-container" style="justify-content: flex-end;">
				<view class="button" @click="reRender">重新生成</view>
			</view>
		</view>
		<view class="content" v-if="poster">
			<view class="process-info">
				<view>耗时：{{poster.totalCostTime}} 毫秒</view>
				<view>尺寸：宽 {{poster.outputWidth}} × 高 {{poster.outputHeight}}</view>
				<view>图层数量：{{poster.itemCount}} 个</view>
				<view>动态参数：{{poster.dynamicValueCount}} 个</view>
			</view>
			<image :style="imageContainerHeight" mode="aspectFit" :src="poster.url" class="image" />
		</view>
		<view class="button-container save-button-container" v-if="poster">
			<view class="button" @click="onSavePosterClick">保存到本地</view>
		</view>
	</view>
</template>
<script>
	import QrcodeicuSdk from '@/uni_modules/qrcodeicu-render/sdk/qrcodeicu-miniapp-sdk.js'

	export default {
		data() {
			return {
				poster: null,
				imageContainerHeight: null,
				fields: [],
				sdk: null,
				demoConfig: null
			}
		},
		async mounted() {
			this.demoConfig = await this.fetchDemoConfig();
			this.sdk = new QrcodeicuSdk();
			this.sdk.init({
				qrcodeIcuEndPoint: this.demoConfig.qrcodeIcuEndPoint,
				renderEndPoint: this.demoConfig.renderEndPoint,
				appId: this.demoConfig.appId,
				key: this.demoConfig.key
			});

			const dynamics = await this.sdk.getDynamics(this.demoConfig.posterId);
			if (dynamics) {
				dynamics.forEach(item => {
					item.alias = decodeURIComponent(item.alias);
					item.boolDataTypeText = this.boolDataTypeText(item);
					item.boolDataTypeImage = this.boolDataTypeImage(item);
				});
				this.fields = dynamics;
			}

			this.doRender();
		},
		methods: {
			async doRender() {
				let screenWidth = uni.getWindowInfo().screenWidth;
				let imageContainerHeight;

				uni.showLoading({
					title: '正在生成中...',
				});

				const sdk = this.sdk.render(this.demoConfig.posterId, this.demoConfig.posterStorageId);
				this.fields.filter(field => (field.value != null && field.value !== "") || field
						.valueImageFileBase64 != null)
					.forEach(field => {
						if (this.boolDataTypeText(field)) {
							sdk.addTextParam(field.alias, field.value);
						}

						if (this.boolDataTypeImage(field)) {
							if (field.valueImageFileBase64) {
								sdk.addImageFileBase64Param(field.alias, field.valueImageFileBase64);
							}
						}
					});

				sdk.fetch()
					.then((data) => {
						this.imageContainerHeight = "height:" + screenWidth / (data.width / data.height) + "px";
						this.poster = data;
						uni.hideLoading();
					}).catch(() => {
						uni.hideLoading();
					});
			},

			reRender() {
				this.poster = null;
				this.doRender();
			},

			browse(field) {
				uni.chooseImage({
					count: 1,
					success: (res) => {
						const tempFile = res.tempFiles[0];
						if (tempFile.size > 3 * 1024 * 1024) {
							uni.showToast({
								title: '图片太大了',
							})
							return;
						}
						const fs = uni.getFileSystemManager();
						fs.readFile({
							filePath: tempFile.path,
							encoding: 'base64',
							position: 0,
							success: (res) => {
								field.valueImageFileBase64 = `${res.data}`;
								const p = this.fields;
								this.fields = [];
								this.fields = p;
							},
						})
					},
				});
			},

			async fetchDemoConfig() {
				return new Promise((resolve) => {
					uni.request({
						url: "https://fxl.ink/portal/demoConfigs/wechat/plugin",
						success: (res) => {
							resolve(res.data);
						}
					});
				});
			},

			onSavePosterClick() {
				uni.showLoading({
					title: '处理中...',
				});

				uni.downloadFile({
					url: this.poster.url,
					success(downres) {
						uni.saveImageToPhotosAlbum({
							filePath: downres.tempFilePath,
							success(res) {
								uni.hideLoading()
							},
							fail(err) {
								uni.hideLoading()
								if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err
									.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg ===
									"saveImageToPhotosAlbum:fail authorize no response") {
									uni.showModal({
										title: '提示',
										content: '请授权保存到相册',
										showCancel: false,
										success() {
											uni.openSetting({
												success(openres) {
													console.log("openres", openres)
													if (openres.authSetting[
															'scope.writePhotosAlbum']) {
														console.log('获取权限成功，再次点击图片即可保存')
													} else {
														console.log('获取权限失败，无法保存到相册哦~')
													}
												},
												fail(failerr) {
													console.log("failerr", failerr)
												}
											})
										}
									})
								}
							}
						})
					},
					fail() {
						uni.hideLoading()
						console.log('下载失败，请稍后再试')
					}
				})
			},

			boolDataTypeText(dynamic) {
				const DynamicTypeDataTypeText = 2;
				const DynamicTypeDataTypeQrcodeContent = 6;
				return this.getDataType(dynamic.type) === DynamicTypeDataTypeText ||
					this.getDataType(dynamic.type) === DynamicTypeDataTypeQrcodeContent;
			},

			boolDataTypeImage(dynamic) {
				const DynamicTypeDataTypeImageUri = 4;
				return this.getDataType(dynamic.type) === DynamicTypeDataTypeImageUri;
			},

			getDataType(v) {
				return v >> 8 & 0x00FF;
			},
		}
	}
</script>
<style lang="scss" scoped>
	.image {
		width: 100vw;
	}

	.container {
		width: 100vw;
		height: 100vh;
		position: relative;
	}

	.content {
		display: flex;
		flex-direction: column;
		position: relative;
	}

	.process-info {
		position: absolute;
		padding: 12rpx;
		background-color: black;
		opacity: 0.6;
		color: white;
		left: 0;
		top: 0;
	}

	.button-container {
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.button {
		color: #fff !important;
		background-color: #409eff !important;
		padding: 12px 18px;
	}

	.browse {
		width: 64rpx;
		height: 64rpx;
		margin-right: 12rpx;
		background-size: cover;
		background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAABD9JREFUeF7tmzurFFkQx3/3A6yBRiuKjxUFNRAUH8GyKyIaaKai+ADF10aiu8luoiaa+MDIJwo+UNRMA0VERQMVBQMVXHZ9gGiwaKAfYOUP3Zdzj93Tdc49M90zcwua4U7XOafqP1XVVdV1B+hzGuhz/RkBYMQC+hyBERdIaAB7gF+AXyv2vAvcA8RfO6W0gOfADKNGL4CZRt62sqUE4I7h18+VkRUsbKtmxs1TAiCTXmGwAv36V3vRBYyYN4stpQU0SzOjNFUArAeWAwuAcSV7xkZ1q8sYVRlkewvcBx4AJ6oWtwLgMrCyaoPsfkxUDwmaRjG+Y3sKzGm1uAyAJ8DsgFObCoBUuAKsKtOlCICtwPEA5ZvmAkWibwDOFd0oAuAsIN/PaS8gJXV1A03PfvHdjrClVlAEwBtgorNY2d3LbtDckVHpuGJMTu+B8VYL+N9jrHpS1InNPOBIJsAO4JEjjEmPIuVMC+vUOjtbpq7g65JrrSY9uhmAG8ASD4CbwNLsu54H4CEgF3BJLjC/iQBMAhYBa4GxwI+ZkB+BD8AF4DagAGwlKS8QXJLyeRxohAUomVJeoctCSl11KYOzkOLAoYxxl/e0qh2AP4F9Fi0KeP4C9keuzZfVCoAKHTcRidFFCdhw2ma1AaACSoWUT5+zRogqNV2in7NLjZTRBWuUwyuLi6FaAJDPq5Dy6RIgl1CpWkTKPGXyqwtuqpqzxgR3eS0AqIjyA15oJukLrqC4LcIEOg7AVOCVJ6gi8+FA4Xc6kT1fOg34O3CfjgPwO3DAEVIFyCzgU6DgY4BnXgfqD+Bg4D4dB8Dv8JwBNgUKnbOfBjY6a2Pa6B0HQOYvN8jpN+BYJADbgaPOWpm/3CCEOg7AF+AHR8IpwL8hEju8PwH/OH9/BUYF7lU7AHq0vQsUOmef4D0yYwBwXbK0Z5myHPZdQG2185EArPN6eDEukLfd/2v1MjYlAH4QjH1+CzM/n4gJgibsUwKwGTjpnKrUV5lhWfZXJqBcR5mfmxpvAU6ZNApkSgmAav7X3vlKgdcEynSxICWeHNgrMB+ZEoAi09V3daXCJhBSA1BWDCkl1vuGsqxQ2Z9eXuTNDVd4azGUT6horXkCJTUAOrysEaLU+BbwOPsU72JgbvZZ9PI1pDHiTqiYX9W1AwApVkdDxJT4+H7RLgB0TlljxOKbMY2QxgEgRdvdFHXBbCQAuYAqkjRosaykLX4duBZR83cNABazHy5Poy1guMpZ1vc8ANZJ1Cqwhgx0tPMpUCVI6P2QSdSqvQfzhG4akEg5VDVYXXbTiEyqsbohk6ophqSqzC30fuzQVbIgKIFDx+RClaziN+fyzkZJAdC+IYOSVQqF3m8EABLaMiobqlwVfyNcoErIJt5P7gJNVLKVTH0PgJsnmLvIof26JltFnidIRvN/pPQSAFE/zggAUbD10KK+t4BvrlrzQZ2bMiUAAAAASUVORK5CYII=');
	}

	.save-button-container {
		position: fixed;
		bottom: 20px;
	}

	.fields-container {
		display: flex;
		flex-direction: column;
		padding: 12rpx;
	}

	.field-item {
		padding: 18rpx 0;
		margin-bottom: 24rpx;
	}

	.field-item {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #dedede;
	}

	.fields-container .alias {
		flex-shrink: 0;
		margin-right: 32rpx;
	}

	.input {
		width: 100%;
	}

	.title {
		text-align: center;
		font-weight: bold;
		font-size: 120%;
	}

	.alias {
		display: flex;
	}
</style>
