<view class="content"><view class="navbar"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabClick',[index]]]]]}}" class="{{['nav-item',(tabCurrentIndex===index)?'current':'']}}" bindtap="__e">{{''+item.text+''}}</view></block></view><swiper class="swiper-box" current="{{tabCurrentIndex}}" duration="300" data-event-opts="{{[['change',[['changeTab',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="tabItem" wx:for-index="tabIndex" wx:key="tabIndex"><swiper-item class="tab-content"><scroll-view class="list-scroll-content" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['loadData',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{tabItem.g0}}"><empty vue-id="{{'16bd28ee-1-'+tabIndex}}" bind:__l="__l"></empty></block><block wx:for="{{tabItem.$orig.orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleSpec',['$0'],[[['navList','',tabIndex],['orderList','',index]]]]]]]}}" class="order-item" bindtap="__e"><view class="i-top b-b"><text class="time">{{item.start_time}}</text><text class="state" style="{{'color:'+(item.stateTipColor)+';'}}">{{item.stateTip}}</text></view><view class="goods-box-single"><view class="right"><text class="title clamp">{{item.name}}</text></view></view><view class="price-box"><view class="uni-row align-center"><text class="text-sm">观看进度：</text><u_line_progress vue-id="{{'16bd28ee-2-'+tabIndex+'-'+index}}" inactive-color="#e6eaf2" active-color="#19be6b" percent="{{item.progress}}" height="20" bind:__l="__l"></u_line_progress></view></view><view class="action-box b-t"><button class="action-btn recom">立即学习</button></view></view></block><uni-load-more vue-id="{{'16bd28ee-3-'+tabIndex}}" status="{{tabItem.$orig.loadingType}}" bind:__l="__l"></uni-load-more></scroll-view></swiper-item></block></swiper><view data-event-opts="{{[['touchmove',[['stopPrevent',['$event']]]],['tap',[['toggleSpec',['$event']]]]]}}" class="{{['popup','spec',specClass]}}" catchtouchmove="__e" bindtap="__e"><view class="mask"></view><view data-event-opts="{{[['tap',[['stopPrevent',['$event']]]]]}}" class="layer attr-content" catchtap="__e"><view style="padding:20rpx;color:#999999;font-size:25rpx;background-color:#f1f1f1;text-align:center;border-radius:20rpx 20rpx 0 0;"><view style="font-weight:bold;color:#000;font-size:35rpx;">课程目录</view><view>(点击课程名称进入学习)</view></view><block wx:for="{{specList.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="attr-list" style="padding:20rpx;"><text data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['specList.list','',index]]]]]]]}}" style="padding:20rpx;font-size:30rpx;" bindtap="__e">{{"任务"+(index+1)+":"+item.title}}</text></view></block></view></view></view>