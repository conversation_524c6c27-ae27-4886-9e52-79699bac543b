/**
 * @version 3.0.4
 * <AUTHOR>
 * @Email <EMAIL>
 * 文档: https://www.quanzhan.co/luch-request/
 * github: https://github.com/lei-mu/luch-request
 * DCloud: http://ext.dcloud.net.cn/plugin?id=392
 * HBuilderX: beat-2.7.14 alpha-2.8.0
 */
import Request from '@/js_sdk/luch-request/index.js';
import config from '@/common/js/config.js';


// 获得本地缓存的token
const getTokenStorage = () => {
  let token = ''
  try {
    token = uni.getStorageSync('token')
  } catch (e) {
    //TODO handle the exception
  }
  return token
}

var url = config.server.api;

const http = new Request()
http.setConfig((config) => { /* 设置全局配置 */
  config.baseURL = url /* 根域名不同 */
  // config.header = {
  //   ...config.header,
  //   a: 1, // 演示
  //   b: 2 // 演示
  // }
  return config
})


http.interceptors.request.use((config) => { /* 请求之前拦截器。可以使用async await 做异步操作 */
	// uni.showLoading({
	// 	title:"加载中...",
	// 	mask:true
	// })
	let Base = getTokenStorage()
	config.header = {
		...config.header,
		// Authorization: Base.token_type + ' ' + Base.access_token
		Authorization: 'Bearer ' + Base.access_token
	}
	/*
	if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
	return Promise.reject(config)
	}
	*/
	return config
}, (config) => {
  return Promise.reject(config)
})


http.interceptors.response.use(async (response) => { /* 请求之后拦截器。可以使用async await 做异步操作  */
  uni.hideLoading()
  if(response.data.code == 4001){
	  uni.showModal({
	      title: '请登录',
	      content: '未登录或登录失效，\n登录后才可进行操作',
	      success: function (res) {
	          if (res.confirm) {
	              console.log('用户点击确定');
	  			uni.navigateTo({
	  				url:"/pages/login/login"
	  			})
	          } else if (res.cancel) {
	              console.log('用户点击取消');
	          }
	      }
	  });
	  return Promise.reject(response)
  }
  if(response.data.code != 0){
  	 uni.showToast({
  	 	title: response.data.message,
		icon: "none",
		duration: 4000
  	 })
  	 return Promise.reject(response)
  }
  return response
}, (response) => { // 请求错误做点什么。可以使用async await 做异步操作
  console.log(response)
  uni.hideLoading();
  uni.showToast({
	title:"未知错误，请求失败",
	icon:"none"
  })
  return Promise.reject(response)
})

export {
  http
}
