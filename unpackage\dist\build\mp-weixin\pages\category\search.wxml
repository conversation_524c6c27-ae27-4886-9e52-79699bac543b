<view class="column data-v-73841ea4"><view class="search data-v-73841ea4"><view class="data-v-73841ea4"><um-dropdown vue-id="191c49d4-1" width="200rpx" defaultIndex="{{def}}" rangeKey="label" optionList="{{optionList1}}" data-event-opts="{{[['^change',[['fnChange']]]]}}" bind:change="__e" class="data-v-73841ea4" bind:__l="__l"></um-dropdown></view><view class="search-input data-v-73841ea4"><text class="yzb yzb-search data-v-73841ea4"></text><input class="text-normal data-v-73841ea4" type="text" placeholder="{{placeholder}}" data-event-opts="{{[['input',[['searchInput',['$event']]]],['confirm',[['confirm',['$event']]]]]}}" value="{{searchValue}}" bindinput="__e" bindconfirm="__e"/></view><block wx:if="{{popularShow==false}}"><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="btn-search data-v-73841ea4" bindtap="__e">搜索</view></block><block wx:if="{{popularShow==true}}"><view data-event-opts="{{[['tap',[['cancelSearch',['$event']]]]]}}" class="btn-cancel data-v-73841ea4" bindtap="__e">取消</view></block></view><view class="placeholder-90 data-v-73841ea4"></view><block wx:if="{{$root.g0}}"><view class="center-algin data-v-73841ea4" style="margin-top:40%;"><m-empty-data vue-id="191c49d4-2" coverUrl="/static/null.png" noTxt="暂无搜索记录" class="data-v-73841ea4" bind:__l="__l"></m-empty-data></view></block><block wx:if="{{searchRecent==true}}"><view class="searchRecent padding-20 data-v-73841ea4"><view class="searchRecent-title text-grey space-between-algin data-v-73841ea4"><text class="data-v-73841ea4">最近搜索</text><text data-event-opts="{{[['tap',[['clearRecent',['$event']]]]]}}" bindtap="__e" class="data-v-73841ea4">清空</text></view><view class="history data-v-73841ea4"><block wx:for="{{searchRecentList}}" wx:for-item="item" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['recentClick',['$0'],[[['searchRecentList','',index1]]]]]]]}}" class="searchRecent-content data-v-73841ea4" bindtap="__e"><text class="text-size-mim data-v-73841ea4">{{item}}</text></view></block></view></view></block><block wx:if="{{$root.g1}}"><view class="top-line data-v-73841ea4" style="background-color:#efefef;width:100%;"><view class="list-box data-v-73841ea4"><block wx:for="{{list}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['list','',index1]]]]]]]}}" class="item-box lp-flex-column data-v-73841ea4" bindtap="__e"><view class="top-box lp-flex data-v-73841ea4"><view class="cover-box lp-flex-center data-v-73841ea4"><image class="cover data-v-73841ea4" src="{{item1.picture}}"></image></view><view class="info-box lp-flex-column data-v-73841ea4"><view class="title data-v-73841ea4">{{item1.title}}</view></view></view></view></block></view></view></block></view>