<view class="course-container data-v-3e425c46"><view class="search-header data-v-3e425c46"><view class="search-box data-v-3e425c46"><text class="search-icon data-v-3e425c46">🔍</text><input class="search-input data-v-3e425c46" placeholder="搜索课程..." data-event-opts="{{[['input',[['__set_model',['','keyword','$event',[]]],['onSearchInput',['$event']]]]]}}" value="{{keyword}}" bindinput="__e"/></view></view><view class="main-content data-v-3e425c46"><scroll-view class="category-nav data-v-3e425c46" scroll-y="true"><block wx:for="{{classifyList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['postSonflData',['$0',index],[[['classifyList','',index,'id']]]]]]]}}" class="{{['category-item','data-v-3e425c46',(activeIndex===index)?'active':'']}}" bindtap="__e"><block wx:if="{{activeIndex===index}}"><view class="category-indicator data-v-3e425c46"></view></block><text class="category-name data-v-3e425c46">{{item.name}}</text><block wx:if="{{item.count}}"><view class="category-count data-v-3e425c46">{{item.count}}</view></block></view></block></scroll-view><scroll-view class="course-list data-v-3e425c46" scroll-y="true"><block wx:if="{{isLoading}}"><view class="loading-container data-v-3e425c46"><view class="loading-spinner data-v-3e425c46"></view><text class="loading-text data-v-3e425c46">加载中...</text></view></block><block wx:else><view class="course-groups data-v-3e425c46"><uni-collapse bind:input="__e" vue-id="ebeca500-1" data-ref="collapse" value="{{value}}" data-event-opts="{{[['^input',[['__set_model',['','value','$event',[]]]]]]}}" class="data-v-3e425c46 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><uni-collapse-item class="course-group data-v-3e425c46" vue-id="{{('ebeca500-2-'+index)+','+('ebeca500-1')}}" title="{{item.$orig.name}}" open="{{open==0?false:true}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{item.g0===0}}"><view class="empty-state data-v-3e425c46"><text class="empty-icon data-v-3e425c46">📚</text><text class="empty-text data-v-3e425c46">暂无课程</text></view></block><block wx:else><view class="course-grid data-v-3e425c46"><block wx:for="{{item.l1}}" wx:for-item="course" wx:for-index="courseIndex" wx:key="courseIndex"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['son_fls','',index],['class','',courseIndex]]]]]]]}}" class="course-card data-v-3e425c46" bindtap="__e"><view class="course-cover data-v-3e425c46"><image class="cover-image data-v-3e425c46" src="{{course.$orig.picture||defaultCover}}" mode="aspectFill" data-event-opts="{{[['error',[['handleImageError',['$event']]]]]}}" binderror="__e"></image><block wx:if="{{course.$orig.is_free}}"><view class="course-badge data-v-3e425c46">免费</view></block><block wx:else><block wx:if="{{course.$orig.price}}"><view class="course-badge premium data-v-3e425c46">{{"¥"+course.$orig.price}}</view></block></block></view><view class="course-info data-v-3e425c46"><text class="course-title data-v-3e425c46">{{course.$orig.title}}</text><view class="course-meta data-v-3e425c46"><block wx:if="{{course.$orig.teacher}}"><text class="course-teacher data-v-3e425c46">{{course.$orig.teacher}}</text></block><block wx:if="{{course.$orig.student_count}}"><text class="course-students data-v-3e425c46">{{course.$orig.student_count+"人学习"}}</text></block></view><block wx:if="{{course.$orig.tags}}"><view class="course-tags data-v-3e425c46"><block wx:for="{{course.l0}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><text class="course-tag data-v-3e425c46">{{tag}}</text></block></view></block></view></view></block></view></block></uni-collapse-item></block></uni-collapse></view></block></scroll-view></view></view>