<gui-page class="root-box data-v-fc887460" vue-id="94851594-1" fullPage="{{true}}" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="lp-flex-column data-v-fc887460" slot="gBody"><block wx:if="{{$root.g0>0}}"><scroll-view scroll-y="true" data-event-opts="{{[['scrolltolower',[['onScrolltolowerHandler',['$event']]]]]}}" bindscrolltolower="__e" class="data-v-fc887460"><view class="list-box data-v-fc887460"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><navigator class="data-v-fc887460"><view class="item-box data-v-fc887460"><view class="top-box data-v-fc887460"><view class="cover-box data-v-fc887460"><image class="cover data-v-fc887460" src="{{item.picture}}"></image><image class="cover cover-mask data-v-fc887460" src="/static/imgs/cover_mask.png"></image></view><view class="info-box data-v-fc887460"><text class="title data-v-fc887460">{{item.title}}</text></view></view><view class="center-box lp-flex lp-flex-space-between data-v-fc887460"><text class="data-v-fc887460">{{"兑换时间："+item.created_time}}</text><text class="data-v-fc887460">{{"兑换码："+item.sn}}</text></view></view></navigator></block></view></scroll-view></block><block wx:else><nodata vue-id="{{('94851594-2')+','+('94851594-1')}}" text="暂无数据" class="data-v-fc887460" bind:__l="__l"></nodata></block><view class="btn-box data-v-fc887460"><navigator url="./exchange" class="data-v-fc887460">兑换</navigator></view></view></gui-page>