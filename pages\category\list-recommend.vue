<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#fff;">
			<view class="navbar">
				<view v-for="(item, index) in listtabs1" :key="index" class="nav-item"
					:class="{current: currenttabs1 === index}" @click="tabClick(index)">
					{{item.text}}
				</view>
			</view>
			<!--科目考题 结束-->
			<swiper :current="currenttabs1" class="swiper-box" duration="300" @change="changeTab"
				:style="{height:mainHeight+'px'}">
				<swiper-item class="tab-content" v-for="(tabItem,tabIndex) in listtabs1" :key="tabIndex">
					<scroll-view class="list-scroll-content" scroll-y>
						<!-- 空白页 -->
						<!-- <empty v-if="tabItem.loaded === true && tabItem.orderList.length === 0 && style!=4"></empty> -->
						<empty1 v-if="tabItem.loaded === true && tabItem.orderList.length === 0"></empty1>
						<view>
							<view class="list-box">
								<view class="item-box lp-flex-column" v-for="(item1, index1) in tabItem.orderList"
									:key="index1" @tap="navToWeb(item1)">
									<view class="top-box lp-flex">

										<view class="cover-box lp-flex-center">
											<image class="cover" :src="item1.thumb"></image>
										</view>
										<view class="info-box lp-flex-column">
											<view class="title">{{item1.title}}</view>
											<view class="des">{{item1.description}}</view>
											<view class="end"><text style="text-align: right;float: right;">详情</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
	</gui-page>
</template>

<script>
	import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
	var graceJs = require('@/GraceUI5/js/grace.js');
	import empty from "@/components/empty";
	import empty1 from "@/components/null";
	export default {
		components: {
			uniLoadMore,
			empty,
			empty1
		},
		data() {
			return {
				cateMaskState: 0, //分类面板展开状态
				headerPosition: "fixed",
				headerTop: "0px",
				loadingType: 'more', //加载更多状态
				filterIndex: 0,
				cateId: 0, //已选三级分类id
				priceOrder: 0, //1 价格从低到高 2价格从高到低
				cateList: [],
				goodsList: [],
				title: '',
				loading: false,
				listtabs1: [{
						status: 'word',
						text: '热词对译',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},
					{
						status: 'current',
						text: '时政资料',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},
					{
						status: 'course',
						text: '公开讲座',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},	
					{
						status: 'study',
						text: '学习资料',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},					
				],
				listtabs2: [{
						status: 0,
						text: '正在报名',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},
					{
						status: 1,
						text: '现已开课',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					},
					{
						status: 2,
						text: '往期课程',
						loadingType: 'more',
						orderList: [],
						current_page: 1
					}
				],
				currenttabs1: 0,
				mainHeight: 200,
				style: 1,
				courseList: []
			};
		},

		onLoad(options) {
			// #ifdef H5
			this.headerTop = document.getElementsByTagName('uni-page-head')[0].offsetHeight + 'px';
			// #endif
			this.cateId = options.id;
			this.style = options.style;
			this.title = options.title;
			console.log(this.title);
			// 动态设置标题
			uni.setNavigationBarTitle({
				title: this.title
			});
			graceJs.getRefs('guiPage', this, 0, (ref) => {
				ref.getDomSize('guiPageBody', (e) => {
					// 主体高度 = 页面高度 - 自定义区域高度
					// graceJs.select('#myheader', (e2) => {
					// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换
					this.mainHeight = e.height;
					this.pageLoading = false;
					// 第一次加载数据
					this.loadCateList();
					this.loadData();
					// });
				});
			});


		},
		onPageScroll(e) {
			//兼容iOS端下拉时顶部漂移
			if (e.scrollTop >= 0) {
				this.headerPosition = "fixed";
			} else {
				this.headerPosition = "absolute";
			}
		},
		//下拉刷新
		onPullDownRefresh() {
			// this.loadData('refresh');
		},
		//加载更多
		onReachBottom() {
			// this.loadData();
		},
		methods: {

			//加载分类
			async loadCateList(fid, sid) {
				this.$http.get("v1/index").then(res => {
					console.log(res);
					if (res.data.code == 0) {
						this.courseList = res.data.data.course;
					}
				});
			},
			loadData: function(source) {
				//这里是将订单挂载到tab列表下
				let index = this.currenttabs1;
				let navItem = this.listtabs1[index];
				let status = navItem.status;
				let page = navItem.current_page;

				if (source === 'tabChange' && navItem.loaded === true) {
					//tab切换只有第一次需要加载数据
					return;
				}
				if (navItem.loadingType === 'loading') {
					//防止重复加载
					return;
				}

				navItem.loadingType = 'loading';
				uni.request({
					url: 'https://practice.jpworld.cn/api/v1/getRecommend', //仅为示例，并非真实接口地址。
					data: {
						page: page,
					},
					header: {
						'custom-header': 'hello' //自定义请求头信息
					},
					success: (res) => {
						console.log(res.data.data)
						this.empty = false;
						// this.current_page = res.data.data.list.current_page;
						// this.total_page = res.data.data.list.last_page;
						// this.total = res.data.data.list.total;
						this.loading = false;
						
						let orderList = res.data.data.filter(item => {
							//添加不同状态下订单的表现形式
							item = Object.assign(item, this.orderStateExp(item.group_name));
							//演示数据所以自己进行状态筛选
							// if (status === 0) {
							// 	if (item.status === 0 || item.status === 1) {
							// 		//0为全部订单
							// 		return item;
							// 	}
						
							// }
							return item.group_name === status
						});
						//let orderList = pagination.data;
						console.log(orderList)
						orderList.forEach(item => {
							navItem.orderList.push(item);
						})
						//loaded新字段用于表示数据加载完毕，如果为空可以显示空白页
						this.$set(navItem, 'loaded', true);
						
						//判断是否还有数据， 有改为 more， 没有改为noMore 
						navItem.loadingType = 'noMore';
						
						page++;
						this.listtabs1[index].current_page = page;
						console.log(this.listtabs1)
					}
				});
				
			},

			//订单状态文字和颜色
			orderStateExp(status) {
				let stateTip = '',
					stateTipColor = '#fa436a';
				switch (+status) {
					case 1:
						stateTip = '现已开课';
						break;
					case 2:
						stateTip = '往期课程';
						break;
					case 3:
						stateTip = '已完成';
						break;
					case 9:
						stateTip = '订单已关闭';
						stateTipColor = '#909399';
						break;

						//更多自定义
				}
				return {
					stateTip,
					stateTipColor
				};
			},

			tabClick(index) {
				this.currenttabs1 = index;

			},
			changeTab(e) {
				this.currenttabs1 = e.target.current;
				this.loadData('tabChange');
			},

			//筛选点击
			// tabClick(index) {
			// 	if (this.filterIndex === index && index !== 2) {
			// 		return;
			// 	}
			// 	this.filterIndex = index;
			// 	if (index === 2) {
			// 		this.priceOrder = this.priceOrder === 1 ? 2 : 1;
			// 	} else {
			// 		this.priceOrder = 0;
			// 	}
			// 	uni.pageScrollTo({
			// 		duration: 300,
			// 		scrollTop: 0
			// 	})
			// 	this.loadData('refresh', 1);
			// 	uni.showLoading({
			// 		title: '正在加载'
			// 	})
			// },
			//显示分类面板
			toggleCateMask(type) {
				let timer = type === 'show' ? 10 : 300;
				let state = type === 'show' ? 1 : 0;
				this.cateMaskState = 2;
				setTimeout(() => {
					this.cateMaskState = state;
				}, timer)
			},
			//分类点击
			changeCate(item) {
				this.cateId = item.id;
				this.toggleCateMask();
				uni.pageScrollTo({
					duration: 300,
					scrollTop: 0
				})
				this.loadData('refresh', 1);
				uni.showLoading({
					title: '正在加载'
				})
			},
			//详情
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.id;
				uni.navigateTo({
					url: `/pages/product/product?id=${id}`
				})
			},
			stopPrevent() {},
			navToWeb(item) {
				switch (item.type) {
					case "web": // 项目外部跳转，需要使用web-view跳转外部H5页面
						uni.navigateTo({
							url: "/pages/webView/webView?data=" + encodeURIComponent(JSON.stringify(item))
						})
						break;
					case "mini_app": // 项目内部跳转
						uni.navigateTo({
							url: item.link
						})
						break;
					case "popu": // 当前页内部弹窗，不跳转
						this.module = "show";
						this.moduleTitle = item.title;
						this.moduleContent = item.description;
						break;
					case "other_mini": // 当前页内部弹窗，不跳转
						this.getUrl(item.app_id, item.link)
						break;
					case "file": // 当前页内部弹窗，不跳转
						let downloadTask = uni.downloadFile({
							url:item.file,
							success:function(res){
								var filePath = res.tempFilePath;
								uni.openDocument({
									filePath:filePath,
									showMenu:true,
									success:function(res){
										console.log('打开成功');
									},
									fail:function(err){
										console.log('打开失败：',err);
									}
								})
							}
						})
						break;
				}
				return;
			},
		},
	}
</script>

<style lang="scss">
	page,
	.content {
		// background: $page-color-base;
		// height: 100%;
	}

	.swiper-box {
		// height: 100%;
	}

	.list-scroll-content {
		height: 100%;
	}

	.navbar {
		display: flex;
		height: 40px;
		padding: 0 5px;
		background: #fff;
		box-shadow: 0 1px 5px rgba(0, 0, 0, .06);
		position: relative;
		z-index: 10;

		.nav-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 15px;
			color: $font-color-dark;
			position: relative;

			&.current {
				color: #0070C0;

				&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 44px;
					height: 0;
					border-bottom: 2px solid #0070C0;
				}
			}
		}
	}


	.list-box {
		// padding-bottom: 20rpx;

		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding: 10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			border-bottom: 1rpx solid #ebebeb;


			.top-box {
				position: relative;
				padding: 20rpx;

				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
						min-height: 150rpx;
					}

					.cover :after {
						background-color: red;
						border-radius: 10rpx;
						color: #fff;
						content: "hot";
						font-size: 25rpx;
						line-height: 1;
						padding: 2rpx 6rpx;
						position: absolute;
						left: 5rpx;
						top: 5rpx;
					}

					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}

				.cover-box {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
						min-height: 150rpx;
					}

					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}

				.cover-large-box {
					width: 50%;
					height: auto;
					min-height: 200rpx;
					position: relative;

					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}

					.button {
						position: absolute;
						bottom: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: rgba(0, 0, 0, .5) !important;
						color: white;
						padding: 15rpx 20rpx;
						font-size: 20rpx;
					}
				}

				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;

					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}

					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}

					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #666666;
					}

					.end-date {
						font-size: 20rpx;
						color: #999999;
					}

					.total {
						font-size: 20rpx;
						color: #39b54a;
					}

					.des {
						font-size: 22rpx;
						color: #8f8f94;


					}

					.price {
						font-size: 24rpx;
						color: red;
						float: right;
					}

					.end {
						font-size: 24rpx;
						color: #0070C0;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}

			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;

				.text-sm {
					font-size: 24upx;
				}

				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}

				.uni-row {
					flex-direction: row;
				}

				.align-center {
					align-items: center;
				}

				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}

		:last-child {
			// border-bottom: 1rpx solid #fff;
		}
	}

	.service-main {
		margin: 0 auto;
	}

	.h2title {
		color: #000;
		display: block;
		font-size: 35rpx;
		height: 60rpx;
		line-height: 60rpx;
		margin-left: 40rpx;
	}

	.listbox {
		background: #fff;
		border: 1rpx solid #ebebeb;
		border-radius: 8rpx;
		// margin: 40rpx 40rpx 0;
		// padding-bottom: 20rpx;
	}

	.titlebox {
		align-items: center;
		color: #3888ff;
		display: flex;
		height: 60rpx;
		justify-content: space-between;
		padding-bottom: 18rpx;
		padding-top: 36rpx;
	}

	.service-list {
		background-color: initial !important;
		border: 1rpx solid transparent !important;
		box-shadow: none !important;
		margin-top: 0 !important;
	}

	.service-list-title {
		padding-left: 0rpx !important;
	}

	.viewtitle {
		font-weight: 700;
	}

	.service-main .service-hot-title {
		align-items: center;
		color: #3888ff;
		display: inline-flex;
		font-size: 30rpx;
		height: 40rpx;
		justify-content: space-between;
		line-height: 40rpx;
		width: 133rpx;
	}

	.content.service-hot-list {
		background-color: #fff;
		border-radius: 8rpx;
		// margin-top: 20rpx;
	}

	.service-main .service-hot-title .refresh-icon {
		height: 27rpx;
		width: 30rpx;
	}

	.service-main .service-hot-list .service-hot-item {
		align-items: center;
		box-shadow: inset 0 -1rpx 0 0 #ebebeb;
		display: flex;
		margin: 0 40rpx;
		padding: 36rpx 0;
		position: relative;
	}

	.service-main .service-hot-list .service-hot-item .title {
		color: #000;
		font-family: PingFangSC-Regular;
		font-size: 30rpx;
		line-height: 40rpx;
		max-width: 540rpx;
	}

	.service-main .service-hot-list .service-hot-item .tag {
		background: rgba(66, 147, 244, .1);
		border-radius: 4rpx;
		color: #4293f4;
		display: inline-block;
		font-family: PingFangSC-Regular;
		font-size: 26rpx;
		font-weight: 700;
		height: 36rpx;
		line-height: 36rpx;
		margin-left: 12rpx;
		padding: 0 12rpx;
	}

	.service-main .service-hot-list .service-hot-item .arrow {
		height: 24rpx;
		position: absolute;
		right: 0;
		width: 14rpx;
	}

	.service-main .service-hot-list .service-hot-item:last-child {
		box-shadow: none;
	}
</style>
