<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 练习类型选择 -->
			<view class="practice-types">
				<view class="type-card" @click="goToListening">
					<view class="type-icon listening">
						<text class="iconfont icon-headphone"></text>
					</view>
					<view class="type-info">
						<view class="type-title">听力练习</view>
						<view class="type-desc">提升日语听力理解能力</view>
						<view class="type-stats">
							<text>已完成 {{listeningStats.completed}} / {{listeningStats.total}}</text>
						</view>
					</view>
					<view class="type-progress">
						<view class="progress-circle">
							<text>{{Math.round(listeningStats.completed / listeningStats.total * 100)}}%</text>
						</view>
					</view>
				</view>

				<view class="type-card" @click="goToQuiz">
					<view class="type-icon quiz">
						<text class="iconfont icon-edit"></text>
					</view>
					<view class="type-info">
						<view class="type-title">答题练习</view>
						<view class="type-desc">巩固语法词汇知识点</view>
						<view class="type-stats">
							<text>已完成 {{quizStats.completed}} / {{quizStats.total}}</text>
						</view>
					</view>
					<view class="type-progress">
						<view class="progress-circle">
							<text>{{Math.round(quizStats.completed / quizStats.total * 100)}}%</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 最近练习记录 -->
			<view class="recent-practice">
				<view class="section-title">最近练习</view>
				<view class="practice-list">
					<view 
						class="practice-item" 
						v-for="(item, index) in recentPractice" 
						:key="index"
						@click="continuePractice(item)"
					>
						<view class="practice-icon" :class="item.type">
							<text class="iconfont" :class="item.type === 'listening' ? 'icon-headphone' : 'icon-edit'"></text>
						</view>
						<view class="practice-info">
							<view class="practice-title">{{item.title}}</view>
							<view class="practice-meta">
								<text class="meta-type">{{item.type === 'listening' ? '听力练习' : '答题练习'}}</text>
								<text class="meta-time">{{item.practiceTime}}</text>
							</view>
						</view>
						<view class="practice-score" v-if="item.score !== null">
							<text :class="{ 'high-score': item.score >= 80, 'low-score': item.score < 60 }">
								{{item.score}}分
							</text>
						</view>
						<view class="practice-status" v-else>
							<text class="status-text">未完成</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 学习建议 -->
			<view class="study-suggestion">
				<view class="section-title">学习建议</view>
				<view class="suggestion-card">
					<view class="suggestion-icon">
						<text class="iconfont icon-bulb"></text>
					</view>
					<view class="suggestion-content">
						<view class="suggestion-title">{{currentSuggestion.title}}</view>
						<view class="suggestion-desc">{{currentSuggestion.content}}</view>
					</view>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			groupId: '',
			listeningStats: {
				completed: 8,
				total: 12
			},
			quizStats: {
				completed: 15,
				total: 20
			},
			recentPractice: [
				{
					id: 1,
					title: '第3课听力：日常对话',
					type: 'listening',
					practiceTime: '2024-01-15 16:30',
					score: 85
				},
				{
					id: 2,
					title: '语法练习：助词の用法',
					type: 'quiz',
					practiceTime: '2024-01-15 14:20',
					score: 92
				},
				{
					id: 3,
					title: '第4课听力：购物场景',
					type: 'listening',
					practiceTime: '2024-01-14 19:15',
					score: null
				},
				{
					id: 4,
					title: '词汇练习：家族称呼',
					type: 'quiz',
					practiceTime: '2024-01-14 15:45',
					score: 78
				}
			],
			suggestions: [
				{
					title: '听力练习建议',
					content: '建议每天坚持听力练习20分钟，可以提高语感和理解能力。'
				},
				{
					title: '语法巩固建议',
					content: '多做语法练习题，重点关注助词和动词变位的用法。'
				},
				{
					title: '词汇记忆建议',
					content: '使用间隔重复的方法记忆单词，效果更佳。'
				}
			]
		}
	},
	computed: {
		currentSuggestion() {
			// 根据用户的练习情况给出建议
			const listeningProgress = this.listeningStats.completed / this.listeningStats.total;
			const quizProgress = this.quizStats.completed / this.quizStats.total;
			
			if (listeningProgress < quizProgress) {
				return this.suggestions[0]; // 听力练习建议
			} else if (quizProgress < listeningProgress) {
				return this.suggestions[1]; // 语法巩固建议
			} else {
				return this.suggestions[2]; // 词汇记忆建议
			}
		}
	},
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId;
			this.loadPracticeData();
		}
	},
	methods: {
		// 加载练习数据
		loadPracticeData() {
			// 这里应该调用API获取练习数据
			this.pageLoading = true;
			setTimeout(() => {
				this.pageLoading = false;
			}, 1000);
		},

		// 跳转到听力练习
		goToListening() {
			uni.navigateTo({
				url: `/pages/groups/listening-practice?groupId=${this.groupId}`
			});
		},

		// 跳转到答题练习
		goToQuiz() {
			uni.navigateTo({
				url: `/pages/groups/quiz-practice?groupId=${this.groupId}`
			});
		},

		// 继续练习
		continuePractice(item) {
			if (item.type === 'listening') {
				uni.navigateTo({
					url: `/pages/groups/listening-practice?groupId=${this.groupId}&practiceId=${item.id}`
				});
			} else {
				uni.navigateTo({
					url: `/pages/groups/quiz-practice?groupId=${this.groupId}&practiceId=${item.id}`
				});
			}
		}
	}
}
</script>

<style scoped>
.practice-types {
	padding: 30rpx;
}

.type-card {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.type-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.type-icon.listening {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.type-icon.quiz {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.type-icon .iconfont {
	color: #fff;
	font-size: 48rpx;
}

.type-info {
	flex: 1;
}

.type-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.type-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.type-stats {
	font-size: 24rpx;
	color: #999;
}

.type-progress {
	margin-left: 20rpx;
}

.progress-circle {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	color: #666;
	font-weight: bold;
}

.recent-practice, .study-suggestion {
	background: #fff;
	margin: 0 30rpx 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.practice-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.practice-item:last-child {
	border-bottom: none;
}

.practice-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.practice-icon.listening {
	background: rgba(102, 126, 234, 0.1);
}

.practice-icon.quiz {
	background: rgba(240, 147, 251, 0.1);
}

.practice-icon .iconfont {
	font-size: 28rpx;
}

.practice-icon.listening .iconfont {
	color: #667eea;
}

.practice-icon.quiz .iconfont {
	color: #f093fb;
}

.practice-info {
	flex: 1;
}

.practice-title {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.practice-meta {
	display: flex;
	gap: 15rpx;
}

.meta-type, .meta-time {
	font-size: 24rpx;
	color: #999;
}

.practice-score, .practice-status {
	margin-left: 20rpx;
}

.practice-score text {
	font-size: 28rpx;
	font-weight: bold;
}

.high-score {
	color: #52c41a;
}

.low-score {
	color: #ff4d4f;
}

.status-text {
	font-size: 24rpx;
	color: #999;
}

.suggestion-card {
	display: flex;
	align-items: flex-start;
}

.suggestion-icon {
	width: 60rpx;
	height: 60rpx;
	background: #fff7e6;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.suggestion-icon .iconfont {
	color: #fa8c16;
	font-size: 28rpx;
}

.suggestion-content {
	flex: 1;
}

.suggestion-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.suggestion-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}
</style>
