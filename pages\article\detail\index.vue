<template>
	<gui-page :fullPage="true" class="root-box">
		<!-- 页面主体 -->
		<view slot="gBody" class="gui-margin content-box">
			<view class="gui-align-items-center gui-rows gui-nowrap gui-flex ">
				<view class="gui-header-content nav-box">
					<view class="nav-btn" :class="{active:currentIndex==0}" @tap="onNavChangeHandler(0)">原文</view>
					<view class="nav-btn" :class="{active:currentIndex==1}" @tap="onNavChangeHandler(1)">译文</view>
				</view>
			</view>
			<swiper class="tab-card-body swiper" :current="currentIndex" @change="onSwiperChangeHandler">
				<!-- 轮播项目数量对应 上面的选项标签 -->
				<swiper-item class="tab-card-item">
					<lp-original :article_id="article_id"></lp-original>
				</swiper-item>
				<swiper-item class="tab-card-item">
					<lp-original :article_id="article_id" type="translation"></lp-original>
				</swiper-item>
			</swiper>
		</view>
	</gui-page>
</template>
<script>
	import lpOriginal from './original.vue';
	export default {
		components: {
			lpOriginal,
		},
		props: {
			article_id: {
				type: String,
				default: 4
			}
		},
		data() {
			return {
				statusBarHeight: 0,
				currentIndex: 0,
			}
		},
		created() {
			this.statusBarHeight = this.$store.state.statusBar;
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/login/login"
								})
							} else if (res.cancel) {
								/* uni.switchtab({
									url: "/pages/index/index"
								}) */
							}
						}
					});
				}
		},
		onLoad() {
			console.log('onLoad');
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		},
		onUnload() {
			console.log('onUnload');
			uni.setKeepScreenOn({
				keepScreenOn: false
			});
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------

			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 内容列表改变
			 */
			onNavChangeHandler: function(index) {
				this.currentIndex = index;
			},
			/**
			 * 内容列表改变
			 */
			onSwiperChangeHandler: function(e) {
				var index = e.detail.current;
				this.currentIndex = index;
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		display: flex;
		margin: 0rpx;

		.head-box {
			.nav-box {
				display: flex;
				justify-content: center;

				.nav-btn {
					border: solid 1px $uni-color-primary;
					border-radius: 40rpx 0 0 40rpx;
					padding: 8rpx 30rpx;
					font-size: 28rpx;
					color: $uni-color-primary;
				}

				.nav-btn:last-child {
					border-radius: 0 40rpx 40rpx 0
				}

				.active {
					background-color: $uni-color-primary;
					color: #fff;
				}
			}
		}

		.content-box {
			flex: 1;
			border-top: solid 1px #eee;
			margin: 0;

			.swiper {
				height: 100%;
			}
		}
	}

	.gui-header-content {
		width: 100%;
		flex: 1;
		text-align: center;
		margin:0;
	}

	.nav-box {
		display: flex;
		justify-content: center;
		border-bottom: solid 1px #eeeeee;

		.nav-btn {
			border: solid 1px $uni-color-primary;
			border-radius: 40rpx 0 0 40rpx;
			padding: 8rpx 30rpx;
			font-size: 35rpx;
			color: $uni-color-primary;
		}

		.nav-btn:last-child {
			border-radius: 0 40rpx 40rpx 0
		}

		.active {
			background-color: $uni-color-primary;
			color: #fff;
		}
	}

	.gui-align-items-center {
		align-items: center;
	}

	.gui-rows {
		flex-direction: row;
	}

	.gui-nowrap {
		flex-direction: row;
		flex-wrap: nowrap;
	}

	.gui-flex {
		display: flex;
	}

	.gui-padding {
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	/* 左右内间距 */
</style>
