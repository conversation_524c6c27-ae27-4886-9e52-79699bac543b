<template>
	<view class="search-result-item" @click="handleClick">
		<common-image 
			:src="item.picture" 
			width="160rpx" 
			height="120rpx"
			:lazy-load="true"
			border-radius="8rpx"
			class="course-image"
		/>
		<view class="item-content">
			<text class="course-title" v-html="highlightedTitle"></text>
			<text class="course-desc">{{item.des || item.description || item.jianjie}}</text>
			<view class="course-meta">
				<text class="price">￥{{item.Cost || item.price || 0}}</text>
				<text class="students">{{item.student_count || item.viewnum || 0}}人学习</text>
				<text class="lessons">{{item.lesson_count || item.count || 0}}课时</text>
			</view>
			<view class="course-tags" v-if="item.tags && item.tags.length > 0">
				<text 
					class="tag" 
					v-for="tag in item.tags.slice(0, 3)" 
					:key="tag"
				>
					{{tag}}
				</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SearchResultItem',
	props: {
		item: {
			type: Object,
			required: true
		},
		keyword: {
			type: String,
			default: ''
		}
	},
	computed: {
		highlightedTitle() {
			if (!this.keyword || !this.item.title) return this.item.title;

			// 简化版高亮，避免正则表达式在小程序中的兼容性问题
			const title = this.item.title;
			const keyword = this.keyword.toLowerCase();
			const titleLower = title.toLowerCase();

			if (titleLower.includes(keyword)) {
				const index = titleLower.indexOf(keyword);
				const before = title.substring(0, index);
				const match = title.substring(index, index + keyword.length);
				const after = title.substring(index + keyword.length);
				return `${before}<span class="highlight">${match}</span>${after}`;
			}

			return title;
		}
	},
	methods: {
		handleClick() {
			this.$emit('click', this.item);
		}
	}
};
</script>

<style scoped>
.search-result-item {
	display: flex;
	padding: 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;
}

.search-result-item:active {
	background-color: #f8f9fa;
}

.course-image {
	margin-right: 20rpx;
	flex-shrink: 0;
}

.item-content {
	flex: 1;
	min-width: 0;
}

.course-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.course-title /deep/ .highlight {
	color: #2094CE;
	background: rgba(32, 148, 206, 0.1);
	padding: 2rpx 4rpx;
	border-radius: 4rpx;
	font-weight: bold;
}

.course-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.course-meta {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 12rpx;
	flex-wrap: wrap;
}

.price {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff4d4f;
}

.students, .lessons {
	font-size: 24rpx;
	color: #999;
}

.course-tags {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.tag {
	font-size: 22rpx;
	color: #2094CE;
	background: rgba(32, 148, 206, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	border: 1rpx solid rgba(32, 148, 206, 0.2);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.search-result-item {
		flex-direction: column;
		text-align: center;
	}
	
	.course-image {
		margin-right: 0;
		margin-bottom: 20rpx;
		align-self: center;
	}
	
	.course-meta {
		justify-content: center;
	}
	
	.course-tags {
		justify-content: center;
	}
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	.search-result-item {
		background: #1f1f1f;
		border-bottom-color: #333;
	}
	
	.search-result-item:active {
		background-color: #2a2a2a;
	}
	
	.course-title {
		color: #fff;
	}
	
	.course-desc {
		color: #ccc;
	}
	
	.students, .lessons {
		color: #999;
	}
}
</style>
