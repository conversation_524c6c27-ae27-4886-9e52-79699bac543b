(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["GraceUI5/components/gui-loadmore"],{"16e4":function(t,n,e){"use strict";var o=e("a5cb"),a=e.n(o);a.a},"47d5":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},a=[]},"5cf3":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"gui-loadmore",props:{loadMoreText:{type:Array,default:function(){return["","更多数据加载中","已加载全部数据","暂无数据"]}},loadMoreColor:{type:Array,default:function(){return["rgba(69, 90, 100, 0.6)","rgba(69, 90, 100, 0.6)","rgba(69, 90, 100, 0.8)","rgba(69, 90, 100, 0.6)"]}},loadMoreFontSize:{type:String,default:"26rpx"},status:{type:Number,default:0}},data:function(){return{loadMoreStatus:0,hidden:!1}},created:function(){this.loadMoreStatus=this.status},methods:{loading:function(){this.loadMoreStatus=1},stoploadmore:function(){this.loadMoreStatus=0},nomore:function(){this.loadMoreStatus=2},empty:function(){this.loadMoreStatus=3},hide:function(){this.hidden=!0},rotate360:function(){var t=this.$refs.loadingiconforloadmore;animation.transition(t,{styles:{transform:"rotate(7200deg)"},duration:2e4,timingFunction:"linear",needLayout:!1,delay:0})},tapme:function(){0==this.loadMoreStatus&&this.$emit("tapme")}}};n.default=o},a5cb:function(t,n,e){},ad513:function(t,n,e){"use strict";e.r(n);var o=e("47d5"),a=e("ae61");for(var r in a)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(r);e("16e4");var i=e("828b"),u=Object(i["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=u.exports},ae61:function(t,n,e){"use strict";e.r(n);var o=e("5cf3"),a=e.n(o);for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);n["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'GraceUI5/components/gui-loadmore-create-component',
    {
        'GraceUI5/components/gui-loadmore-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ad513"))
        })
    },
    [['GraceUI5/components/gui-loadmore-create-component']]
]);
