(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/gaoyia-parse/parse"],{"263f":function(t,e,n){"use strict";n.r(e);var i=n("e58c"),a=n("a0ad");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=r.exports},"842d":function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("8547")),o={name:"wxParse",props:{userSelect:{type:String,default:"text"},imgOptions:{type:[Object,Boolean],default:function(){return{loop:!1,indicator:"number",longPressActions:!1}}},loading:{type:Boolean,default:!1},className:{type:String,default:""},content:{type:String,default:""},noData:{type:String,default:'<div style="color: red;">数据不能为空</div>'},startHandler:{type:Function,default:function(){return function(t){t.attr.class=null,t.attr.style=null}}},endHandler:{type:Function,default:null},charsHandler:{type:Function,default:null},imageProp:{type:Object,default:function(){return{mode:"aspectFit",padding:0,lazyLoad:!1,domain:""}}}},components:{wxParseTemplate:function(){n.e("components/gaoyia-parse/components/wxParseTemplate0").then(function(){return resolve(n("3977"))}.bind(null,n)).catch(n.oe)}},data:function(){return{nodes:{},imageUrls:[],wxParseWidth:{value:0}}},computed:{},mounted:function(){this.setHtml()},methods:{setHtml:function(){var t=this;this.getWidth().then((function(e){t.wxParseWidth.value=e}));var e=this.content,n=this.noData,i=this.imageProp,o=this.startHandler,s=this.endHandler,r=this.charsHandler,l=e||n,u={start:o,end:s,chars:r},c=(0,a.default)(l,u,i,this);this.imageUrls=c.imageUrls,this.nodes=[],c.nodes.forEach((function(e){setTimeout((function(){t.nodes.push(e)}),0)}))},getWidth:function(){var e=this;return new Promise((function(n,i){t.createSelectorQuery().in(e).select(".wxParse").fields({size:!0,scrollOffset:!0},(function(t){n(t.width)})).exec()}))},navigate:function(t,e,n){console.log(t,n),this.$emit("navigate",t,e)},preview:function(e,n){this.imageUrls.length&&"boolean"!==typeof this.imgOptions&&t.previewImage({current:e,urls:this.imageUrls,loop:this.imgOptions.loop,indicator:this.imgOptions.indicator,longPressActions:this.imgOptions.longPressActions}),this.$emit("preview",e,n)},removeImageUrl:function(t){var e=this.imageUrls;e.splice(e.indexOf(t),1)}},provide:function(){return{parseWidth:this.wxParseWidth,parseSelect:this.userSelect}},watch:{content:function(){this.setHtml()}}};e.default=o}).call(this,n("df3c")["default"])},a0ad:function(t,e,n){"use strict";n.r(e);var i=n("842d"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},e58c:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/gaoyia-parse/parse-create-component',
    {
        'components/gaoyia-parse/parse-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("263f"))
        })
    },
    [['components/gaoyia-parse/parse-create-component']]
]);
