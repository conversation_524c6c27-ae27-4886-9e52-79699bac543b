<gui-page class="vue-ref" vue-id="6abd83d0-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#F8F8F8;" slot="gBody"><view class="carousel-section"><swiper class="carousel" circular="{{true}}" autoplay="true" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{carouselList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['navTo',['$0','swiper'],[[['carouselList','',index]]]]]]]}}" class="carousel-item" bindtap="__e"><image src="{{item.thumb}}"></image></swiper-item></block></swiper><view class="swiper-dots"><text class="num">{{swiperCurrent+1}}</text><text class="sign">/</text><text class="num">{{swiperLength}}</text></view></view><block wx:if="{{$root.g0>0}}"><view class="flexbox hbclass"><block wx:for="{{superInList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navTo',['$0','link'],[[['superInList','',index]]]]]]]}}" class="flex1" bindtap="__e"><image class="icon120" mode="aspectFit" src="{{item.thumb}}"></image><view class="txt26 nowrap">{{item.title}}</view></view></block></view></block><block wx:if="{{$root.g1>0}}"><view class="bg-box hbclass"><view class="hot-service"><image class="bg-img" ariaHidden="true" lazyLoad="true" mode="aspectFill" src="{{rmfwBgUrl}}"></image><view class="hot-service-title" ariaRole="heading"><view class="hot-service-title-h3">更多服务</view></view><view class="hot-service-content"><swiper class="swiper-container-row" bind:change="handleChange" previousMargin="6rpx"><block wx:for="{{otherList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="swiper-item" data-index="index"><block wx:for="{{otherList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="srv-item" bind:tap="handleTap" data-event-opts="{{[['tap',[['navTo',['$0','link'],[[['otherList','',index]]]]]]]}}" bindtap="__e"><image class="srv-item-icon" src="{{item.picture}}"></image><text class="srv-item-title nowrap">{{item.name}}</text></view></block></swiper-item></block></swiper></view></view></view></block><block wx:if="{{$root.g2>0}}"><view class="bg-box hbclass"><view class="hot-service"><image class="bg-img" ariaHidden="true" lazyLoad="true" mode="aspectFill" src="{{rmfwBgUrl}}"></image><view class="hot-service-title" ariaRole="heading"><view class="hot-service-title-h3">更多课程</view></view><view class="hot-service-content"><swiper class="{{[$root.g3>4?'swiper-container':'swiper-container-row']}}" bind:change="handleChange" previousMargin="6rpx"><block wx:for="{{courseList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="swiper-item" data-index="index"><block wx:for="{{courseList}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view class="srv-item" style="width:25%;" bind:tap="handleTap" data-event-opts="{{[['tap',[['navTo',['$0','link'],[[['courseList','',index1]]]]]]]}}" bindtap="__e"><image class="srv-item-icon1" style="width:60rpx;height:60rpx;border-radius:60rpx;" src="{{item1.icon}}"></image><text class="srv-item-title nowrap">{{item1.name}}</text></view></block></swiper-item></block></swiper></view></view></view></block><view class="everyone-doing bg hbclass"><view class="service-main"><view class="listbox service-list" ariaLabel="大家都在办" ariaRole="text"><view class="titlebox" ariaLabel="换一换" ariaRole="button" bind:tap="handleTitleTap"><view class="h2title viewtitle">推荐阅读</view><view class="service-hot-title" ariaLabel="换一换" ariaRole="button" bindtap="handleRefresh"></view></view><view class="content service-hot-list"><view class="list-box"><block wx:for="{{newsList}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navTo',['$0','link'],[[['newsList','',index1]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item1.thumb}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item1.title}}</view><view class="end"><text style="text-align:right;float:right;">更多</text></view></view></view></view></block></view></view></view></view></view></view></gui-page>