<template>
	<view class="root-box">
		<view class="border translate" @tap="onShowTranHandler">
			<textarea class="translate-input" :value="!tran.content ? '点击输入...' : tran.content" disabled="true" maxlength="5000" autoHeight="true" >
				
			</textarea>
		</view>

		<!-- <view class="border interpret">
			<template v-if="tran.file == ''">
				<view class="tips">请点击【录音】开始录音，最长录音10分钟</view>

				<lp-record class="lp-record" :maxTime="600" :minTime="5" :canPuase="true" :source="source"
					@recconfirm="onSaveRecordHandler" @open="recordIsOpen=1" @close="recordIsOpen=0">
				</lp-record>
			</template>

			<template v-if="tran.file != ''">
				<view class="tips">我的录音</view>
				<view class="btns-box lp-flex">
					<view class="btn-box" style="flex:1;">
						<lp-audio-player class="my-audio" :mini="false" :nobroud="true" :audio="{src:tran.file}">
						</lp-audio-player>
					</view>
					<view class="btn-box lp-flex-column lp-flex-center">
						<view class="btn reset" @tap="onResetInterpretHandler"><text class="gui-icons">&#xe684;</text>
						</view>
						<text class="label">重录</text>
					</view>
				</view>
			</template>

		</view> -->

		<view class="btn-box">
			<view class="btn" :class="{disabled:!dirty}" @tap="onSubmitHandler">提交</view>
		</view>
	</view>
</template>

<script>
	import lpRecord from '@/components/lp-record/lp-record.vue';
	import lpAudioPlayer from '@/components/audio-player/audio-player.vue';
	import Uploader from '@/common/js/uploader.js';

	export default {
		components: {
			lpRecord,
			lpAudioPlayer
		},
		props: {
			type: {
				type: String,
				default: 'original'
			},
			article: { //默认地址
				type: Object,
				default: null
			},
		},
		data() {
			return {
				// 笔译编辑中
				isTranslateEdit: false,
				recordIsOpen: 0,
				dirty: false,
				tran: {
					content: '',
					file: ''
				},
				keyboardHeight: 0,
			}
		},
		created() {
			this.tran = this.article.trans.length ? this.article.trans[0] : {
				content: '',
				file: ''
			};
		},
		watch: {},
		computed: {
			source: function() {
				return this.article[this.type][0].file;
			}
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 检查是否需要上传文件
			 */
			checkInterpretFileUpload: function() {
				const self = this;
				if (this.tran.need_upload) {
					let uploader = Uploader.getUploader({
						onUploadstarted: function(data) {
							self.tran.video_id = data.video_id;
						},
						onUploadSucceed: function(data) {
							self.tran.file_name = data.object;
						},
						onUploadEnd: function() {
							self.submit();
						}
					});
					let title = "人民中国打卡作业_" + this.article.id;
					var file = {
						url: this.tran.file,
						coverUrl: title
					};
					uploader.addFile(file, null, null, null, '{"Vod":{}}');
				} else {
					self.submit();
				}
			},

			submit: function() {
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: "/pages/login/login"
								})
							} else if (res.cancel) {
								/* uni.switchtab({
									url: "/pages/index/index"
								}) */
							}
						}
					});
				}else{
					if (!this.dirty) {
						return;
					}
					if (this.tran.content == '' && this.tran.file == '') {
						uni.showToast({
							title: "内容不能为空"
						});
						return;
					}
					this.dirty = false;
					this.apiSubmitTran(this.article.id, this.tran.content, this.tran.file_name, this.tran.video_id).then(
						data => {
							if(data.code==0 && data.data!=''){
								uni.showToast({
									title: "提交成功",
									icon: 'success'
								});
							}else{
								uni.showToast({
									title: "提交失败，请重新提交",
									icon: 'warn'
								});
								this.dirty = true;
							}
							
						});
				}
				
			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onSaveRecordHandler: function(path) {
				this.tran.need_upload = true;
				this.tran.file = path;
				this.onTranChangeHandler();
			},
			/**
			 * 重置口译音频
			 */
			onResetInterpretHandler: function() {
				this.tran.need_upload = false;
				this.tran.file = '';
				this.tran.file_name = '';
				this.tran.video_id = '';
				this.onTranChangeHandler();
			},
			/**
			 * 提交答案
			 */
			onSubmitHandler: function() {
				this.checkInterpretFileUpload();
			},
			onShowTranHandler: function() {
				let self = this;
				uni.$off('lp-input-completed');
				uni.$emit('lp-get-input', this.tran.content);
				uni.$once('lp-input-completed', function(text) {
					self.tran.content = text;
					self.onTranChangeHandler();
				})
			},
			/**
			 * 笔译或者口译有变化时调用
			 */
			onTranChangeHandler: function() {
				this.dirty = true;
				this.$emit('dirty');
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 提交作业
			 * @param {Object} article_id
			 * @param {Object} content
			 * @param {Object} file_name
			 * @param {Object} video_id
			 */
			apiSubmitTran: function(article_id, content, file_name, video_id) {
				return this.$http.post('/v1/article/store', {
					article_id,
					content,
					file_name,
					video_id
				}).then(res => {
					return Promise.resolve(res.data);
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		display: flex;
		flex-direction: column;
		align-items: stretch;

		.border {
			position: relative;
			border: solid 1px #eee;
			border-radius: 10rpx;
			padding: 30rpx;
			margin-bottom: 30rpx;
			z-index: 0;
		}

		.translate {
			.close-box {
				background: #28b28b;
				color: #fff;
				font-size: 24rpx;
				line-height: 100rpx;
				border-radius: 10rpx;
				margin-left: 10rpx;
				padding: 20rpx;
			}

			.translate-input {
				flex: 1;
				font-size: 24rpx;
				min-height: 100rpx;
				width:100%;
			}
		}

		.interpret {
			display: flex;
			flex-direction: column;
			/* align-items: center; */

			.lp-record {
				z-index: 99;
			}

			.my-audio {
				border: unset;
			}

			.tips {
				color: $uni-text-color-grey;
				font-size: 24rpx;
			}

			.play {
				background-color: $uni-color-primary !important;

				text {
					font-size: 32rpx !important;
					margin-left: 10rpx;
				}
			}

			.record {}

			.reset {}

			.btn-box {
				font-size: 24rpx;
				margin: 20rpx;

				.btn {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: $uni-color-error;
					width: 96rpx;
					height: 96rpx;
					padding: unset;
					border-radius: 50% !important;

					.ws-icon,
					.gui-icons {
						font-size: 40rpx;
						line-height: 40rpx;
					}
				}
			}

			.label {
				text-align: center;
				line-height: 40rpx;
			}
		}

		.btns-box {
			display: flex;
		}

		.btn-box {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.btn {
				border: solid 1px $uni-color-error;
				background-color: $uni-color-error;
				color: #fff;
				font-size: 32rpx;
				padding: 20rpx;
				text-align: center;
				border-radius: 40rpx;
			}

			.done {
				background-color: unset;
				color: $uni-color-error;
			}

			.disabled {
				background-color: $uni-bg-color-grey;
				color: #aaa;
				border: solid 1px #aaa;
			}
		}
	}
</style>
