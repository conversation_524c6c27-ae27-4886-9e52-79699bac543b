<gui-page class="root-box data-v-32f7efa7" vue-id="2c06863f-1" fullPage="{{true}}" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="root-box data-v-32f7efa7" slot="gBody"><view class="search-box lp-flex data-v-32f7efa7"><view class="input-box lp-flex-center data-v-32f7efa7"><input class="sn-input data-v-32f7efa7" style="text-align:center;" placeholder="请输入兑换码并提交验证" data-event-opts="{{[['input',[['__set_model',['','sn','$event',[]]]]]]}}" value="{{sn}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['onExchangeValidHandler',['$event']]]]]}}" class="btn data-v-32f7efa7" bindtap="__e">提交验证</view></view><block wx:if="{{$root.g0>0}}"><scroll-view scroll-y="true" data-event-opts="{{[['scrolltolower',[['onScrolltolowerHandler',['$event']]]]]}}" bindscrolltolower="__e" class="data-v-32f7efa7"><view class="list-box data-v-32f7efa7"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['list','',index]]]]]]]}}" class="item-box lp-flex-column data-v-32f7efa7" bindtap="__e"><view class="top-box lp-flex data-v-32f7efa7"><view class="cover-box lp-flex-center data-v-32f7efa7"><image class="cover data-v-32f7efa7" src="{{item.wk.picture}}"></image></view><view class="info-box lp-flex-column data-v-32f7efa7"><view class="title data-v-32f7efa7">{{item.title}}</view></view></view></view></block></view></scroll-view></block><block wx:else><block wx:if="{{total==0}}"><nodata vue-id="{{('2c06863f-2')+','+('2c06863f-1')}}" text="暂无数据" class="data-v-32f7efa7" bind:__l="__l"></nodata></block></block><view class="btn-box data-v-32f7efa7"><view data-event-opts="{{[['tap',[['onExchangeHandler',['$event']]]]]}}" class="{{['btn','data-v-32f7efa7',(!valided)?'disabled':'']}}" bindtap="__e">确认兑换</view></view></view></gui-page>