(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/lp-record/lp-record"],{6609:function(e,t,n){"use strict";n.r(t);var r=n("fda8"),o=n("9567");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("7ac7");var s=n("828b"),c=Object(s["a"])(o["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);t["default"]=c.exports},"7ac7":function(e,t,n){"use strict";var r=n("8b23"),o=n.n(r);o.a},"8b23":function(e,t,n){},9567:function(e,t,n){"use strict";n.r(t);var r=n("ab0f"),o=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=o.a},ab0f:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={name:"lp-record",components:{lpAudioPlayer:function(){n.e("components/audio-player/audio-player").then(function(){return resolve(n("e8d8"))}.bind(null,n)).catch(n.oe)},recordClock:function(){n.e("components/lp-record/record-clock").then(function(){return resolve(n("f252"))}.bind(null,n)).catch(n.oe)},zaudio:function(){Promise.all([n.e("common/vendor"),n.e("components/uniapp-zaudio/zaudio")]).then(function(){return resolve(n("7e5e"))}.bind(null,n)).catch(n.oe)}},props:{voicePath:{type:String,default:""},source:{type:String,default:""},maxTime:{type:Number,default:15},minTime:{type:Number,default:5},canPuase:{type:Boolean,default:!1}},data:function(){return{frame:50,recordTime:0,isRecording:!1,isRecordPaused:!1,playing:0,playPath:"",recorderManager:null,innerAudioContext:null,themelist:[{name:"样式1",val:"theme1"},{name:"样式2",val:"theme2"},{name:"样式3",val:"theme3"}]}},computed:{showRecordTime:function(){var e="",t=Math.floor(this.recordTime/60);t<10&&(e="0"+t);var n=this.recordTime%60;return e+=n<10?":0"+n:":"+n,e}},watch:{},created:function(){this.initValue();try{e.authorize({scope:"scope.record",success:function(){}})}catch(t){console.error(t)}this.showPicker()},beforeDestroy:function(){this.isRecording&&this.recordEnd(!0),this.stopVoice(),this.pauseTime()},onReady:function(){console.log("onReady")},methods:{initValue:function(){},showPicker:function(){this.recordReset(),this.initSound()},closePicker:function(){this.isRecording&&this.recordEnd(),this.destorySound(),this.stopVoice(),this.$refs.audioPlayer&&this.$refs.audioPlayer.pause()},recordReset:function(){this.playPath="",this.stopVoice(),this.resetTime(),this.resetDraw()},recordStart:function(){console.log("recordStart",this.recorderManager),this.resetTime(),this.recorderManager.start({duration:1e3*this.maxTime}),this.resetDraw()},recordPause:function(){this.recorderManager.pause()},recordResume:function(){this.recorderManager.resume()},recordEnd:function(t){var n=this.recordTime;if(t=!!t,!t&&n<this.minTime)return n<=0||e.showToast({title:"不能小于"+this.minTime+"秒,请重新录制",icon:"none"}),!1;console.log("recordEnd"),this.recorderManager.stop()},playVoice:function(){this.playPath&&(this.innerAudioContext.src=this.playPath,this.innerAudioContext.play(),this.playing=1)},stopVoice:function(){this.innerAudioContext&&this.innerAudioContext.stop(),this.playing=0},initSound:function(){this.recorderManager=e.getRecorderManager(),this.innerAudioContext=e.createInnerAudioContext();var t=this;this.recorderManager.onStart((function(){console.log("开始录音"),t.startTime(),t.isRecording=!0})),this.recorderManager.onPause((function(){console.log("录音暂停"),t.isRecordPaused=!0,t.pauseTime()})),this.recorderManager.onResume((function(){console.log("录音继续"),t.isRecordPaused=!1,t.startTime()})),this.recorderManager.onStop((function(e){console.log("开始结束"+JSON.stringify(e)),t.pauseTime(),t.isRecording=!1,t.playPath=e.tempFilePath,t.onConfirmHandler()}))},destorySound:function(){this.recorderManager&&this.recorderManager.stop(),this.innerAudioContext&&this.innerAudioContext.stop()},resetTime:function(){this.recordTime=0,this.pauseTime()},startTime:function(){var e=this;this.pauseTime(),e.timeObj=setInterval((function(){e.recordTime++,e.recordTime==e.maxTime&&e.recordEnd()}),1e3)},pauseTime:function(){clearInterval(this.timeObj)},resetDraw:function(){},startDraw:function(){},pauseDraw:function(){},onMoveHandler:function(){return!1},onTouchStartHandler:function(){this.canPuase?this.isRecording?this.isRecordPaused?this.recordResume():this.recordPause():(this.recordReset(),this.recordStart()):this.recordReset()},onLongpressHandler:function(){this.canPuase||this.recordStart()},onTouchEndHandler:function(){this.canPuase||this.recordEnd()},onRecordEndHandler:function(){this.recordEnd()},onConfirmHandler:function(){this.$emit("recconfirm",this.playPath),this.closePicker()}}};t.default=r}).call(this,n("df3c")["default"])},fda8:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var r=function(){var e=this.$createElement;this._self._c},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/lp-record/lp-record-create-component',
    {
        'components/lp-record/lp-record-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6609"))
        })
    },
    [['components/lp-record/lp-record-create-component']]
]);
