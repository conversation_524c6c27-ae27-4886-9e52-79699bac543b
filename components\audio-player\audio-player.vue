<template>
	<view class="gui-player" :class="{mini:mini,nobroud:nobroud}">
		<!-- 播放控制 -->
		<view class="gui-player-console">
			<view class="gui-player-console-c">
				<text class="gui-player-tool gui-icons" style="font-size:33rpx;" @tap="pause" v-if="playStatus == 1">&#xe64b;</text>
				<text class="gui-player-tool gui-icons" style="font-size:33rpx;margin-left: 4rpx;" @tap="play" v-if="playStatus == 2">&#xe649;</text>
			</view>
			<!-- 播放进度 -->
			<view v-if="!mini" class="progress-bar">
				<gui-single-slider ref="graceSingleSlider" @change="progressChange" :barWidth="100" :barText="playTime" barColor="#999"
				 barBgColor="linear-gradient(to right, #eeeeee,#eeeeee)" bglineColor="#eeeeee" bglineAColor="#28b28b"></gui-single-slider>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		props: {
			color: {
				type: String,
				default: '#333'
			},
			mini: {
				type: Boolean,
				default: false
			},
			nobroud:{
				type: Boolean,
				default: false
			},
			autoPlay: {
				type: Boolean,
				default: false
			},
			audio: {
				type: Object,
				default: {
					title: "我们都一样",
					singer: "张杰",
					epname: "杰哥精选",
					coverImgUrl: "https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/images/jie02.jpg?sign=00e5e68d81145037000a162e2220736a&t=1556345760",
					src: "https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/song/%E6%88%91%E4%BB%AC%E9%83%BD%E4%B8%80%E6%A0%B7%20-%20%E5%BC%A0%E6%9D%B0.mp3?sign=008d62b6bea06a8a6814b5f284fac0ac&t=1556345730"
				}
			}
		},
		data() {
			return {
				playStatus: 2,
				player: null,
				playTime: '00:00',
				timer: null,
				audioLength: 1
			}
		},
		beforeDestroy() {
			this.destroy();
		},
		created: function() {
			this.player = uni.createInnerAudioContext();
			this.player.onTimeUpdate(() => {
				try {
					if (this.playStatus != 1) {
						return;
					}
					this.audioLength = this.player.duration;
					// 调整进度
					var progress = this.player.currentTime / this.audioLength;
					progress = Math.round(progress * 100);
					var ref = this.$refs.graceSingleSlider;
					if (ref) {
						ref.setProgress(progress);
					}
					this.playTime = this.timeFormat(this.player.currentTime);
				} catch (e) {};
			});
			this.player.onPlay(() => {
				this.playStatus = 1;
				this.audioLength = this.player.duration;
			});
			this.player.onPause(() => {
				this.playStatus = 2;
			});
			this.player.onEnded(() => {
				this.playStatus = 2;
			});
			this.load(this.audio, this.autoPlay);
			if (this.player.currentTime < 1) {
				
			}
		},
		methods: {
			load: function(audio, autoPlay) {
				//this.player.title = audio.title;
				console.log('audio.src'+audio.src);
				this.player.singer = audio.singer;
				this.player.coverImgUrl = audio.coverImgUrl;
				this.player.src = audio.src;
				autoPlay && this.player.play();
			},
			progressChange: function(e) {
				if (this.timer != null) {
					clearTimeout(this.timer);
				}
				this.player.pause();
				var needTime = this.audioLength * e / 100;
				needTime = Math.round(needTime);
				this.playTime = this.timeFormat(needTime);
				this.timer = setTimeout(() => {
					this.player.seek(needTime);
					this.player.play();
				}, 800);
			},
			timeFormat: function(s) {
				s = Math.round(s);
				if (s < 60) {
					if (s < 10) {
						return '00:0' + s;
					}
					return '00:' + s;
				} else {
					var second = s % 60;
					s = s - second;
					var minute = s / 60;
					if (minute < 10) {
						minute = '0' + minute;
					}
					if (second < 10) {
						second = '0' + second;
					}
					return minute + ':' + second;
				}

			},

			pause: function() {
				this.player && this.player.pause();
			},
			play: function() {
				this.player && this.player.play();
			},
			destroy: function() {
				this.player && this.player.destroy();
			},
		}
	}
</script>
<style lang="scss" scoped>
	.gui-player {
		padding: 30rpx;
		border: solid 1px #eee;
		border-radius: 10rpx;

		.gui-player-console {
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.gui-player-tool {
			width: 96rpx;
			line-height: 96rpx;
			text-align: center;
			font-size: 33rpx;
			display: block;
			flex-shrink: 0;
			color: #fff;
		}

		.gui-player-console-c {
			display: flex;
			align-items: center;
			background: #28b28b;
			border-radius: 50%;
			width: 96rpx;
			height: 96rpx;
		}

		.progress-bar {
			flex: 1;
			padding: 25rpx;
		}
	}

	.mini ,.nobroud{
		padding: unset;
		border: unset;
	}
</style>
