<template>
	<view class="lp-record">
		<view @tap="showPicker">
			<slot></slot>
		</view>
		<!-- 遮罩层 -->
		<!-- <view class="mask" @tap.stop="closePicker" v-if="isShow" @touchmove.stop.prevent="onMoveHandler"></view> -->
		<!-- 多选控件 -->
		<view class="conbox record">
			<view class="panel source-player" v-if="source">
				<view class="head">原音播放</view>
				<!-- <lp-audio-player ref="audioPlayer" :nobroud="true" :audio="{src:source}"></lp-audio-player> -->
				<zaudio :theme="themelist[0].val" :autoplay="true" :continue="true"></zaudio>
			</view>

			<view class="panel">
				<!-- 此处可放置倒计时，可根据需要自行添加 -->
				<view class="time">
					{{showRecordTime}}
				</view>
				<view class="c999">
					最短{{minTime}}秒，最长{{maxTime}}秒
				</view>
				<view class="record-box" @touchmove.stop.prevent="onMoveHandler">
					<!-- 空占位 -->
					<view class="btn empty" v-if="isRecording" style="background: unset;"></view>
					<template v-else>
						<view class="btn stop" @touchstart.stop="stopVoice" v-if="playPath && playing==1"><text class="gui-icons">&#xe64b;</text></view>
						<view class="btn play" @touchstart.stop="playVoice" v-if="playPath && playing==0"><text class="gui-icons" style="margin-left: 8rpx;">&#xe649;</text></view>
					</template>
					<view class="btn recording" @touchstart="onTouchStartHandler" @longpress="onLongpressHandler" @touchend="onTouchEndHandler">
						<text class="ws-icon" :class="{flash:isRecording&&!isRecordPaused}" style="font-size: 70rpx;">
							{{isRecording&&!isRecordPaused ? '&#xe76a;' : '&#xeb6b;'}}
						</text>
					</view>
					<view class="btn confirm" @touchstart.stop="onRecordEndHandler" v-if="isRecording"><text class="ws-icon">&#xe611;</text></view>
					<view class="btn confirm" @touchstart.stop="onConfirmHandler" v-if="!isRecording && playPath"><text class="gui-icons">&#xe60f;</text></view>
				</view>
				<view class="c666 fz32 domess">{{isRecording ? (isRecordPaused ? '已暂停' : '录音中') : (playPath ? '录音完成' : '点击开始录音')}}</view>
			</view>
		</view>
	</view>
</template>
<script>
	import lpAudioPlayer from '@/components/audio-player/audio-player.vue';
	import recordClock from './record-clock.vue';
	import zaudio from '@/components/uniapp-zaudio/zaudio';

	export default {
		name: 'lp-record',
		components: {
			lpAudioPlayer,
			recordClock,
			zaudio
		},
		props: {
			voicePath: { //默认地址
				type: String,
				default: ''
			},
			// 原声
			source: {
				type: String,
				default: ''
			},
			maxTime: { // 录音最大时长，单位秒
				type: Number,
				default: 15
			},
			minTime: { // 录音最大时长，单位毫秒
				type: Number,
				default: 5
			},
			canPuase: { // 能否录制过程中暂停，App不可以
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				frame: 50, // 执行绘画的频率，单位毫秒
				recordTime: 0, //录音时长
				isRecording: false, //是否录制中
				isRecordPaused: false, //是否录制暂停
				playing: 0, //是否播放中
				playPath: "",


				recorderManager: null, //录音
				innerAudioContext: null, //播放
				themelist: [
					{
						name:'样式1',val:'theme1',
					},
					{
						name:'样式2',val:'theme2',
					},
					{
						name:'样式3',val:'theme3',
					}
				]
			}
		},
		computed: {
			showRecordTime() {
				var strs = "";
				var m = Math.floor(this.recordTime / 60);
				if (m < 10) strs = "0" + m;

				var s = this.recordTime % 60;
				strs += (s < 10) ? ":0" + s : ":" + s;

				return strs
			},
		},
		watch: {},
		created() {
			var _this = this;

			this.initValue();
			//获取录音权限
			try {
				uni.authorize({
					scope: 'scope.record',
					success() {}
				})
			} catch (e) {
				console.error(e);
			}
			this.showPicker();
		},
		beforeDestroy() {
			if (this.isRecording) {
				this.recordEnd(true);
			}
			this.stopVoice();
			this.pauseTime();
		},
		onReady() {
			console.log('onReady');
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			//组件数据初始化  进入时、关闭时调用初始化
			initValue() {},
			//显示组件
			showPicker() {
				this.recordReset();
				this.initSound();
				//this.$emit('open');
			},
			//关闭组件
			closePicker() {
				//点遮罩 点取消关闭说明用户不想修改，所以这里对数据进行初始化
				//this.initValue(); 
				if (this.isRecording) {
					this.recordEnd();
				}
				this.destorySound();
				this.stopVoice();
				if (this.$refs.audioPlayer) {
					this.$refs.audioPlayer.pause();
				}
				//this.$emit('close');
			},

			recordReset: function() {
				this.playPath = ""; //音频地址		
				this.stopVoice();
				this.resetTime();
				this.resetDraw();
			},

			recordStart: function() {
				let _this = this;
				
				console.log('recordStart',this.recorderManager);

				_this.resetTime();

				// 开始录音
				this.recorderManager.start({duration:this.maxTime*1000});

				_this.resetDraw();
			},

			recordPause: function() {
				this.recorderManager.pause();
			},

			recordResume: function() {
				this.recorderManager.resume();
			},

			recordEnd: function(force) {
				let recordTime = this.recordTime;
				force = !!force;

				if (!force && recordTime < this.minTime) {
					if (recordTime <= 0) {
						//==点击事件==;
						return false;
					}
					//==小于5秒==;
					uni.showToast({
						title: "不能小于" + this.minTime + "秒,请重新录制",
						icon: "none"
					})
					return false;
				}
				console.log('recordEnd');
				this.recorderManager.stop();
			},

			playVoice() {
				if (this.playPath) {
					this.innerAudioContext.src = this.playPath;
					this.innerAudioContext.play();
					this.playing = 1;
				}
			},
			stopVoice() {
				if(this.innerAudioContext){
					this.innerAudioContext.stop();
				}
				
				this.playing = 0;
			},
			//-----------------------------------------------------------------------------------------------
			//
			// Source
			//
			//-----------------------------------------------------------------------------------------------
			initSound: function() {
				this.recorderManager = uni.getRecorderManager(); //录音
				this.innerAudioContext = uni.createInnerAudioContext(); //播放
				var _this = this;
				
				this.recorderManager.onStart(function(){
					console.log('开始录音');
					_this.startTime();
					_this.isRecording = true;
				});

				//录音暂停事件
				this.recorderManager.onPause(function() {
					console.log('录音暂停');
					_this.isRecordPaused = true;
					_this.pauseTime();
				});

				// 录音恢复事件
				this.recorderManager.onResume(function() {
					console.log('录音继续');
					_this.isRecordPaused = false;
					_this.startTime();
				})

				//录音停止事件
				this.recorderManager.onStop(function(res) {
					console.log('开始结束' + JSON.stringify(res));
					_this.pauseTime();
					_this.isRecording = false;
					_this.playPath = res.tempFilePath;
					_this.onConfirmHandler();
				});
			},

			destorySound: function() {
				if (this.recorderManager) {
					this.recorderManager.stop();
				}
				if (this.innerAudioContext) {
					this.innerAudioContext.stop();
				}
			},
			//-----------------------------------------------------------------------------------------------
			//
			// Timer
			//
			//-----------------------------------------------------------------------------------------------
			resetTime: function() {
				this.recordTime = 0;
				this.pauseTime();

			},

			startTime: function() {
				let _this = this;
				this.pauseTime();
				_this.timeObj = setInterval(function() {
					_this.recordTime++;
					//this.$refs.recordClock.setValue(_this.recordTime / _this.maxTime);
					if (_this.recordTime == _this.maxTime) _this.recordEnd();
				}, 1000);

			},

			pauseTime: function() {
				clearInterval(this.timeObj);
			},
			//-----------------------------------------------------------------------------------------------
			//
			// Draw
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 从置
			 */
			resetDraw: function() {
				//this.$refs.recordClock.resetDraw();
			},

			startDraw: function() {
				//this.$refs.recordClock.startDraw();
			},

			pauseDraw: function() {
				//this.$refs.recordClock.pauseDraw();
			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 事件取消
			 */
			onMoveHandler() {
				return false;
			},

			/**
			 * 
			 */
			onTouchStartHandler: function() {
				// 可以暂停情况下，开始录制
				if (this.canPuase) {
					if (this.isRecording) {
						this.isRecordPaused ? this.recordResume() : this.recordPause();
					} else {
						this.recordReset();
						this.recordStart();
					}
				} else {
					this.recordReset();
				}
			},

			/**
			 * 长按
			 */
			onLongpressHandler: function() {
				// 不可以暂停情况下，开始录制
				if (!this.canPuase) {
					this.recordStart();
				}

			},

			/**
			 * 长按结束
			 */
			onTouchEndHandler: function() {
				if (!this.canPuase) {
					this.recordEnd();
				}
			},

			onRecordEndHandler: function() {
				this.recordEnd();
			},

			//点击确定
			onConfirmHandler() {
				// var data = {},list = {},textStr = "",indexStr = "";								
				this.$emit('recconfirm', this.playPath)

				//确定后更新默认初始值,这样再次进入默认初值就是最后选择的
				// this.defaultArr = textStr;
				//关闭
				this.closePicker();
			},
		}
	}
</script>

<style lang="scss">
	.lp-record {
		position: relative;
		z-index: 99;

		.mask {
			position: fixed;
			z-index: 1000;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.6);
		}

		.conbox {
			background: #fff;
		}

		.c666 {
			color: #666;
		}

		.c999 {
			color: #999;
		}

		.fz28 {
			font-size: 28rpx;
		}

		.fz32 {
			font-size: 32rpx;
		}

		.source-player {
			padding: 50rpx 0rpx;
			border-bottom: solid 1px #eeeeee;

			.head {
				text-align: left;
				font-size: 30rpx;
				margin-bottom: 20rpx;
			}
		}

		.record {
			text-align: center;

			.time {
				text-align: center;
				font-size: 60rpx;
				color: #000;
				line-height: 100rpx;
				margin-top: 50rpx;
			}

			.domess {
				margin-bottom: 50rpx;
			}


			.record-box {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
			}

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				color: #fff;
				background-color: $uni-color-primary;
				
				text{
					font-size: 40rpx;
				}
			}

			.recording {
				//position: absolute;
				//top: 10px;
				//left: 10px;
				margin: 20rpx;
				width: 80px;
				height: 80px;
				background-color: $uni-color-error;
				z-index: 100;
				font-size: 35px;
			}


			.stop {}

			.play {
				margin-left: 5rpx;
			}

			.confirm {}


		}

	}

	.flash {
		animation: 2s opacity2 0s infinite;
		-webkit-animation: 2s opacity2 0s infinite;
		-moz-animation: 2s opacity2 0s infinite;
	}

	@keyframes opacity2 {
		0% {
			opacity: 0.1
		}

		50% {
			opacity: .8;
		}

		100% {
			opacity: 0.1;
		}
	}

	@-webkit-keyframes opacity2 {
		0% {
			opacity: 0.1
		}

		50% {
			opacity: .8;
		}

		100% {
			opacity: 0.1;
		}
	}

	@-moz-keyframes opacity2 {
		0% {
			opacity: 0.1
		}

		50% {
			opacity: .8;
		}

		100% {
			opacity: 0.1;
		}
	}
</style>
