











































































































































































































































































































































































































































































































































































































/* 基础样式与听力练习页面类似，这里只列出关键差异 */
.practice-list.data-v-5012569e, .practice-content.data-v-5012569e, .result-page.data-v-5012569e {
	padding: 30rpx;
}
.practice-item.data-v-5012569e {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.item-icon.grammar.data-v-5012569e {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.item-icon.vocabulary.data-v-5012569e {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.item-icon.reading.data-v-5012569e {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.item-icon.comprehensive.data-v-5012569e {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.question-input.data-v-5012569e {
	margin: 30rpx 0;
}
.input-field.data-v-5012569e {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	padding: 0 20rpx;
	font-size: 32rpx;
	background: #fff;
}
.question-judge.data-v-5012569e {
	display: flex;
	gap: 30rpx;
	margin: 30rpx 0;
}
.judge-option.data-v-5012569e {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	transition: all 0.3s;
}
.judge-option.selected.data-v-5012569e {
	border-color: #2094CE;
	background: #f0f9ff;
}
.judge-icon.data-v-5012569e {
	font-size: 48rpx;
	margin-right: 15rpx;
}
.judge-text.data-v-5012569e {
	font-size: 32rpx;
}
.question-explanation.data-v-5012569e {
	margin-top: 30rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
}
.explanation-title.data-v-5012569e {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	margin-bottom: 10rpx;
}
.explanation-content.data-v-5012569e {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
.btn-check.data-v-5012569e {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
	background: #fa8c16;
	color: #fff;
}
.result-score.excellent.data-v-5012569e {
	color: #52c41a;
}
.result-score.good.data-v-5012569e {
	color: #2094CE;
}
.result-score.pass.data-v-5012569e {
	color: #fa8c16;
}
.result-score.fail.data-v-5012569e {
	color: #ff4d4f;
}
.time-left.data-v-5012569e {
	color: #ff4d4f;
	font-weight: bold;
}

