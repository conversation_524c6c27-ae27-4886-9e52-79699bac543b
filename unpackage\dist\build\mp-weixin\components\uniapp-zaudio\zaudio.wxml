<block wx:if="{{$root.g0>0}}"><view class="{{['imt-audio','data-v-dcf7ba36',''+theme]}}"><block wx:if="{{theme=='theme3'}}"><slider class="audio-slider data-v-dcf7ba36" activeColor="{{themeColor}}" block-size="0" value="{{$root.m0}}" max="{{$root.m1}}" disabled="{{!renderIsPlay}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"></slider><view class="top data-v-dcf7ba36"><view class="audio-control-wrapper data-v-dcf7ba36"><image class="{{['cover','data-v-dcf7ba36',(!$root.m2)?'on':'']}}" src="{{$root.m3}}" mode="aspectFill"></image><block wx:if="{{loading}}"><image class="play loading data-v-dcf7ba36" src="{{$root.m4}}"></image></block><block wx:else><block wx:if="{{$root.m5}}"><image class="play data-v-dcf7ba36" src="{{$root.m6}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><image class="play data-v-dcf7ba36" src="{{$root.m7}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block></block></view></view><view class="audio-wrapper data-v-dcf7ba36"><view class="titlebox data-v-dcf7ba36"><view class="title data-v-dcf7ba36">{{$root.m8}}</view><view class="singer data-v-dcf7ba36">{{$root.m9}}</view></view><view class="slidebox data-v-dcf7ba36"><view class="data-v-dcf7ba36">{{$root.m10+"/ "+$root.m11}}</view><view class="data-v-dcf7ba36"><text data-event-opts="{{[['tap',[['changeplay',[-1]]]]]}}" bindtap="__e" class="data-v-dcf7ba36">上一首</text><text data-event-opts="{{[['tap',[['changeplay',[1]]]]]}}" bindtap="__e" class="data-v-dcf7ba36">下一首</text></view></view></view></block><block wx:if="{{theme=='theme2'}}"><view class="top data-v-dcf7ba36"><view class="audio-control-wrapper data-v-dcf7ba36" style="{{'background-image:'+('url('+$root.m12+')')+';'}}"><block wx:if="{{loading}}"><image class="play loading data-v-dcf7ba36" src="{{$root.m13}}"></image></block><block wx:else><block wx:if="{{$root.m14}}"><image class="play data-v-dcf7ba36" src="{{$root.m15}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><image class="play data-v-dcf7ba36" src="{{$root.m16}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block></block></view><view class="data-v-dcf7ba36"><view class="title data-v-dcf7ba36"><text class="data-v-dcf7ba36">{{$root.m17}}</text><view class="audio-number data-v-dcf7ba36">{{$root.m18+"/"+$root.m19}}</view></view><view class="singer data-v-dcf7ba36">{{$root.m20}}</view></view></view></block><block wx:if="{{theme=='theme1'}}"><view class="top data-v-dcf7ba36"><view class="audio-control-wrapper data-v-dcf7ba36"><image class="{{['cover','data-v-dcf7ba36',(!$root.m21)?'on':'']}}" src="{{$root.m22}}" mode="aspectFill"></image></view><view class="data-v-dcf7ba36"><view class="title data-v-dcf7ba36">{{$root.m23}}</view><view class="singer data-v-dcf7ba36">{{$root.m24}}</view></view></view><view class="audio-wrapper data-v-dcf7ba36"><view class="audio-number data-v-dcf7ba36">{{$root.m25}}</view><slider class="audio-slider data-v-dcf7ba36" activeColor="{{themeColor}}" block-size="16" value="{{$root.m26}}" max="{{$root.m27}}" disabled="{{!renderIsPlay}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"></slider><view class="audio-number data-v-dcf7ba36">{{$root.m28}}</view></view><view class="audio-button-box data-v-dcf7ba36"><image class="prevbtn data-v-dcf7ba36" src="{{$root.m29}}" mode="widthFix" data-event-opts="{{[['tap',[['stepPlay',[-15]]]]]}}" bindtap="__e"></image><image class="prevplay data-v-dcf7ba36" src="{{$root.m30}}" mode="widthFix" data-event-opts="{{[['tap',[['changeplay',[-1]]]]]}}" bindtap="__e"></image><view class="playbox _div data-v-dcf7ba36"><block wx:if="{{loading}}"><image class="play loading data-v-dcf7ba36" src="{{$root.m31}}"></image></block><block wx:else><block wx:if="{{$root.m32}}"><image class="play data-v-dcf7ba36" src="{{$root.m33}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><image class="pause data-v-dcf7ba36" src="{{$root.m34}}" alt data-event-opts="{{[['tap',[['operate',['$event']]]]]}}" bindtap="__e"></image></block></block></view><image class="nextplay data-v-dcf7ba36" src="{{$root.m35}}" mode="widthFix" data-event-opts="{{[['tap',[['changeplay',[1]]]]]}}" bindtap="__e"></image><image class="nextbtn data-v-dcf7ba36" src="{{$root.m36}}" mode="widthFix" data-event-opts="{{[['tap',[['stepPlay',[15]]]]]}}" bindtap="__e"></image></view></block></view></block>