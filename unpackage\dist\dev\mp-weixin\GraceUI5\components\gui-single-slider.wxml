<view class="gui-sg-slider data-v-3c244fe3 vue-ref" style="{{'height:'+(barHeight+'rpx')+';'}}" id="gracesgslider" data-ref="gracesgslider" data-event-opts="{{[['touchstart',[['touchstart',['$event']]]],['touchmove',[['touchmove',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchstart="__e" catchtouchmove="__e" bindtouchend="__e"><view class="gui-sg-slider-line data-v-3c244fe3" style="{{'height:'+(bglineSize+'rpx')+';'+('background-color:'+(bglineColor)+';')+('margin-top:'+((barHeight-bglineSize)/2+'rpx')+';')+('border-radius:'+(borderRadius)+';')}}"></view><view class="gui-sg-slider-a-line data-v-3c244fe3" style="{{'width:'+(left+25+'px')+';'+('top:'+((barHeight-bglineSize)/2+'rpx')+';')+('background-color:'+(bglineAColor)+';')+('height:'+(bglineSize+'rpx')+';')+('border-radius:'+(borderRadius)+';')}}"></view><text class="gui-sg-slider-bar gui-block-text data-v-3c244fe3" style="{{'width:'+(barWidth+'rpx')+';'+('height:'+(barHeight+'rpx')+';')+('line-height:'+(barHeight+'rpx')+';')+('background-image:'+(barBgColor)+';')+('color:'+(barColor)+';')+('left:'+(left+'px')+';')+('font-size:'+(barTextSize)+';')+('border-radius:'+(borderRadius)+';')}}">{{barText}}</text></view>