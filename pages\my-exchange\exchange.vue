<template>
	<gui-page :fullPage="true"  class="root-box">
		<!-- 页面主体 -->
		<view slot="gBody" class="root-box">
			<view class="search-box lp-flex">
				<view class="input-box lp-flex-center">
					<input class="sn-input" v-model="sn" style="text-align: center;" placeholder="请输入兑换码并提交验证" />
				</view>
				<view class="btn" @tap="onExchangeValidHandler">提交验证</view>
			</view>
			<scroll-view v-if="list.length>0" scroll-y="true" @scrolltolower="onScrolltolowerHandler">
				<view class="list-box">
					<view class="item-box lp-flex-column" v-for="(item, index) in list" :key="index"
						@click="navToDetailPage(item)">
						<view class="top-box lp-flex">
							<view class="cover-box lp-flex-center" >
								<image class="cover" :src="item.wk.picture"></image>
							</view>
							<view class="info-box lp-flex-column">
								<view class="title">{{item.title}}</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<nodata v-else-if="total == 0" text="暂无数据"></nodata>
			<!-- 兑换 -->
			<view class="btn-box">
				<view class="btn" :class="{disabled:!valided}" @tap="onExchangeHandler">确认兑换</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import nodata from '@/components/nodata/nodata.vue';

	export default {
		components: {
			nodata
		},
		data() {
			return {
				sn: '',
				list: []
			}
		},
		onShow: function() {},
		onHide: function() {},
		created: function() {},
		computed: {
			valided: function() {
				return this.list.length > 0;
			},
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			exchangeValid: function() {
				if (this.sn == '') {
					uni.showToast({
						icon: 'none',
						title: '请填写有效兑换码'
					});
					return;
				}
				this.apiExchangeValid(this.sn).then(data => {
					if (data) {
						this.list = [data]
						console.log(this.list)
					} else {
						this.list = [];
						uni.showToast({
							icon: 'none',
							title: '找不到数据'
						})
					}
				});
			},
			//-----------------------------------------------------------------------------------------------
			//
			// hander
			//
			//-----------------------------------------------------------------------------------------------			
			/**
			 * 核实
			 */
			onExchangeValidHandler: function() {
				this.exchangeValid();
			},
			/**
			 * 兑换
			 */
			onExchangeHandler: function() {
				let type = ''
				if(this.list[0].course_id==0){
					type = 'member'
				}
				if (this.valided) {
					this.apiExchange(this.list[0].id,getApp().globalData.r_type,type).then(data => {
						if (data.status) {
							// uni.showToast({
							// 	title: '兑换成功',
							// });
							// setTimeout(() => {
							// 	uni.navigateBack();
							// }, 1500);
							uni.navigateTo({
								url: `/pages/my-exchange/exchange-success`
							})
						}
					});
				}
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiExchangeValid: function(sn) {
				return this.$http.get('/v1/member/course_detail', {
					params: {
						id: sn,
						r_type:getApp().globalData.r_type
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
			apiExchange: function(id,r_type,type) {
				return this.$http.post('/v1/member/doExchange', {
					id,
					r_type,
					type
				}).then(res => {
					return Promise.resolve(res.data);
				}).catch(res => {
					uni.showModal({
						showCancel: false,
						title: res.data.code
					})
					return Promise.resolve(res.data);
				})
			},
			//详情
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.course_id;
				uni.navigateTo({
					url: `/pages/course/course?id=${id}`
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		flex: 1;
		background-color: #f2f2f2;

		/* 搜索框 */
		.search-box {
			padding: 30rpx;
			font-size: 28rpx;

			.input-box {
				flex: 1;
				padding: 20rpx 0;
				border: solid 1px $uni-color-primary;
				border-radius: 10rpx;
				margin-right: 30rpx;
			}

			.btn {
				background: $uni-color-primary;
				border: solid 1px $uni-color-primary;
				border-radius: 10rpx;
				color: #fff;
				padding: 0 20rpx;
				line-height: 80rpx;
			}
		}

		/* 列表 */
		scroll-view {
			max-height: 100%;
		}

		.list-box {
			padding-bottom: 20rpx;
		
			.item-box {
				// margin: 30rpx 30rpx 0 30rpx;
				padding: 20rpx 30rpx;
				background-color: #fff;
				border-radius: 10rpx;
				color: #666;
				font-size: 28rpx;
		
				.top-box {
					position: relative;
		
					.cover-box-hot {
						width: 50%;
						height: auto;
						min-height: 200rpx;
						position: relative;
		
						.cover {
							width: 100%;
							height: 100%;
							border-radius: 10rpx;
						}
		
						.cover :after {
							background-color: red;
							border-radius: 10rpx;
							color: #fff;
							content: "hot";
							font-size: 25rpx;
							line-height: 1;
							padding: 2rpx 6rpx;
							position: absolute;
							left: 5rpx;
							top: 5rpx;
						}
		
						.button {
							position: absolute;
							top: 0;
							right: 0;
							transform: translateX(0);
							border-radius: 20rpx;
							background-color: blue;
							color: white;
							padding: 5rpx 10rpx;
							font-size: 20rpx;
						}
					}
		
					.cover-box {
						width: 50%;
						height: auto;
						min-height: 200rpx;
						position: relative;
		
						.cover {
							width: 100%;
							height: 100%;
							border-radius: 10rpx;
						}
		
						.button {
							position: absolute;
							top: 0;
							right: 0;
							transform: translateX(0);
							border-radius: 20rpx;
							background-color: blue;
							color: white;
							padding: 5rpx 10rpx;
							font-size: 20rpx;
						}
					}
		
		
		
					.info-box {
						flex: 1;
						margin-left: 15rpx;
						height: auto;
						overflow: hidden;
						display: flex;
						flex-direction: column;
						align-items: flex-start;
						justify-content: space-between;
		
						.publish-date {
							font-size: 32rpx;
							font-weight: bold;
						}
		
						.lang-box {
							color: $uni-text-color-grey;
							font-size: 24rpx;
						}
		
						.title {
							font-weight: bold;
							font-size: 26rpx;
							color: #666666;
						}
		
						.end-date {
							font-size: 20rpx;
							color: #999999;
						}
		
						.total {
							font-size: 20rpx;
							color: #39b54a;
						}
		
						.des {
							font-size: 22rpx;
							color: #8f8f94;
		
		
						}
		
						.price {
							font-size: 24rpx;
							color: red;
							float: right;
						}
		
						.end {
							font-size: 24rpx;
							color: red;
							// display: flex;
							// justify-content: space-between;
							// align-items: center;
							width: 100%;
						}
					}
				}
		
				.margin-tb-sm {
					margin-top: 20upx;
					margin-bottom: 20upx;
					position: relative;
		
					.text-sm {
						font-size: 24upx;
					}
		
					.title {
						overflow: hidden;
						word-break: break-all;
						/* break-all(允许在单词内换行。) */
						text-overflow: ellipsis;
						/* 超出部分省略号 */
						display: -webkit-box;
						/** 对象作为伸缩盒子模型显示 **/
						-webkit-box-orient: vertical;
						/** 设置或检索伸缩盒对象的子元素的排列方式 **/
						-webkit-line-clamp: 2;
						/** 显示的行数 **/
					}
		
					.uni-row {
						flex-direction: row;
					}
		
					.align-center {
						align-items: center;
					}
		
					.margin-left-sm {
						margin-left: 20upx;
					}
				}
			}
		}

		/* 兑换按钮 */
		.btn-box {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;

			.btn {
				padding: 30rpx;
				background: $uni-color-error;
				text-align: center;
				color: #fff;
			}

			.disabled {
				background: $uni-bg-color-grey;
				color: $uni-text-color-grey;
			}
		}
	}
</style>
