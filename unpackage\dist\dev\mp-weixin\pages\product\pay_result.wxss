@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-3368feed {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-3368feed {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-3368feed {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-3368feed {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-3368feed {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-3368feed {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-3368feed::after {
  border: none;
}
.u-hover-class.data-v-3368feed {
  opacity: 0.7;
}
.u-primary-light.data-v-3368feed {
  color: #ecf5ff;
}
.u-warning-light.data-v-3368feed {
  color: #fdf6ec;
}
.u-success-light.data-v-3368feed {
  color: #f5fff0;
}
.u-error-light.data-v-3368feed {
  color: #fef0f0;
}
.u-info-light.data-v-3368feed {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-3368feed {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-3368feed {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-3368feed {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-3368feed {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-3368feed {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-3368feed {
  color: #398ade;
}
.u-warning-dark.data-v-3368feed {
  color: #f1a532;
}
.u-success-dark.data-v-3368feed {
  color: #53c21d;
}
.u-error-dark.data-v-3368feed {
  color: #e45656;
}
.u-info-dark.data-v-3368feed {
  color: #767a82;
}
.u-primary-dark-bg.data-v-3368feed {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-3368feed {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-3368feed {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-3368feed {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-3368feed {
  background-color: #767a82;
}
.u-primary-disabled.data-v-3368feed {
  color: #9acafc;
}
.u-warning-disabled.data-v-3368feed {
  color: #f9d39b;
}
.u-success-disabled.data-v-3368feed {
  color: #a9e08f;
}
.u-error-disabled.data-v-3368feed {
  color: #f7b2b2;
}
.u-info-disabled.data-v-3368feed {
  color: #c4c6c9;
}
.u-primary.data-v-3368feed {
  color: #3c9cff;
}
.u-warning.data-v-3368feed {
  color: #f9ae3d;
}
.u-success.data-v-3368feed {
  color: #5ac725;
}
.u-error.data-v-3368feed {
  color: #f56c6c;
}
.u-info.data-v-3368feed {
  color: #909399;
}
.u-primary-bg.data-v-3368feed {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-3368feed {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-3368feed {
  background-color: #5ac725;
}
.u-error-bg.data-v-3368feed {
  background-color: #f56c6c;
}
.u-info-bg.data-v-3368feed {
  background-color: #909399;
}
.u-main-color.data-v-3368feed {
  color: #303133;
}
.u-content-color.data-v-3368feed {
  color: #606266;
}
.u-tips-color.data-v-3368feed {
  color: #909193;
}
.u-light-color.data-v-3368feed {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-3368feed {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-3368feed {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-3368feed {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-3368feed {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-3368feed::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.root-box.data-v-3368feed {
  color: #666;
}
.root-box .head.data-v-3368feed {
  padding: 50rpx 0;
}
.root-box .head .gui-icons.data-v-3368feed {
  font-size: 100rpx;
  color: #28b28b;
}
.root-box .head .success-text.data-v-3368feed {
  margin: 20rpx 0;
  font-size: 36rpx;
  font-weight: bold;
}
.root-box .content-box.data-v-3368feed {
  font-size: 28rpx;
  padding: 60rpx;
  text-align: center;
}

