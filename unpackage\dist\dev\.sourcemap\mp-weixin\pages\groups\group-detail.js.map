{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/groups/group-detail.vue?eccd", "webpack:///D:/日语云课/pages/groups/group-detail.vue?1056", "webpack:///D:/日语云课/pages/groups/group-detail.vue?bbcb", "webpack:///D:/日语云课/pages/groups/group-detail.vue?ff3c", "uni-app:///pages/groups/group-detail.vue", "webpack:///D:/日语云课/pages/groups/group-detail.vue?c5e9", "webpack:///D:/日语云课/pages/groups/group-detail.vue?7487"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "groupId", "groupInfo", "name", "description", "banner", "courseCount", "memberCount", "progress", "recentStudy", "id", "title", "type", "studyTime", "stats", "totalHours", "completedCourses", "practiceCount", "averageScore", "onLoad", "uni", "methods", "loadGroupInfo", "goToCourseReview", "url", "goToPractice", "continueStudy"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACqC;;;AAGhG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA0lB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+F9mB;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;QACAL;MACA,GACA;QACAE;QACAC;QACAC;QACAC;QACAL;MACA,EACA;MACAM;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;IACA;MACAC;QACAT;MACA;IACA;EACA;EACAU;IACA;IACAC;MACA;MACA;MACA;QACA;UAAAnB;UAAAC;UAAAE;UAAAC;UAAAC;QAAA;QACA;UAAAL;UAAAC;UAAAE;UAAAC;UAAAC;QAAA;QACA;UAAAL;UAAAC;UAAAE;UAAAC;UAAAC;QAAA;QACA;UAAAL;UAAAC;UAAAE;UAAAC;UAAAC;QAAA;QACA;UAAAL;UAAAC;UAAAE;UAAAC;UAAAC;QAAA;MACA;MAEA;IACA;IAEA;IACAe;MACAH;QACAI;MACA;IACA;IAEA;IACAC;MACAL;QACAI;MACA;IACA;IAEA;IACAE;MACA;QACA;QACAN;UACAI;QACA;MACA;QACA;QACAJ;UACAI;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5LA;AAAA;AAAA;AAAA;AAA+3B,CAAgB,m4BAAG,EAAC,C;;;;;;;;;;;ACAn5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/group-detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/group-detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./group-detail.vue?vue&type=template&id=7368b85e&scoped=true&\"\nvar renderjs\nimport script from \"./group-detail.vue?vue&type=script&lang=js&\"\nexport * from \"./group-detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./group-detail.vue?vue&type=style&index=0&id=7368b85e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7368b85e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/group-detail.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./group-detail.vue?vue&type=template&id=7368b85e&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./group-detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./group-detail.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\n\t\t\t<!-- 小组信息头部 -->\n\t\t\t<view class=\"group-header\">\n\t\t\t\t<view class=\"group-banner\">\n\t\t\t\t\t<image :src=\"groupInfo.banner\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"group-info\">\n\t\t\t\t\t<view class=\"group-title\">{{groupInfo.name}}</view>\n\t\t\t\t\t<view class=\"group-subtitle\">{{groupInfo.description}}</view>\n\t\t\t\t\t<view class=\"group-meta\">\n\t\t\t\t\t\t<text class=\"meta-item\">课程 {{groupInfo.courseCount}}</text>\n\t\t\t\t\t\t<text class=\"meta-item\">成员 {{groupInfo.memberCount}}</text>\n\t\t\t\t\t\t<text class=\"meta-item\">进度 {{groupInfo.progress}}%</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 功能菜单 -->\n\t\t\t<view class=\"function-menu\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToCourseReview\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<text class=\"iconfont icon-book\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"menu-text\">\n\t\t\t\t\t\t<view class=\"menu-title\">课程回顾</view>\n\t\t\t\t\t\t<view class=\"menu-desc\">查看已学习的课程内容</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"iconfont icon-arrow-right\"></text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"menu-item\" @click=\"goToPractice\">\n\t\t\t\t\t<view class=\"menu-icon\">\n\t\t\t\t\t\t<text class=\"iconfont icon-edit\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"menu-text\">\n\t\t\t\t\t\t<view class=\"menu-title\">课后练习</view>\n\t\t\t\t\t\t<view class=\"menu-desc\">听力练习、答题练习</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"iconfont icon-arrow-right\"></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 最近学习 -->\n\t\t\t<view class=\"recent-study\">\n\t\t\t\t<view class=\"section-title\">最近学习</view>\n\t\t\t\t<view class=\"study-list\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"study-item\" \n\t\t\t\t\t\tv-for=\"(item, index) in recentStudy\" \n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"continueStudy(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"study-icon\">\n\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"item.type === 'course' ? 'icon-video' : 'icon-practice'\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"study-info\">\n\t\t\t\t\t\t\t<view class=\"study-title\">{{item.title}}</view>\n\t\t\t\t\t\t\t<view class=\"study-time\">{{item.studyTime}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"study-progress\">\n\t\t\t\t\t\t\t<text>{{item.progress}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 学习统计 -->\n\t\t\t<view class=\"study-stats\">\n\t\t\t\t<view class=\"section-title\">学习统计</view>\n\t\t\t\t<view class=\"stats-grid\">\n\t\t\t\t\t<view class=\"stat-card\">\n\t\t\t\t\t\t<view class=\"stat-number\">{{stats.totalHours}}</view>\n\t\t\t\t\t\t<view class=\"stat-label\">学习时长(小时)</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-card\">\n\t\t\t\t\t\t<view class=\"stat-number\">{{stats.completedCourses}}</view>\n\t\t\t\t\t\t<view class=\"stat-label\">完成课程</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-card\">\n\t\t\t\t\t\t<view class=\"stat-number\">{{stats.practiceCount}}</view>\n\t\t\t\t\t\t<view class=\"stat-label\">练习次数</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-card\">\n\t\t\t\t\t\t<view class=\"stat-number\">{{stats.averageScore}}</view>\n\t\t\t\t\t\t<view class=\"stat-label\">平均分数</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\tgroupId: '',\n\t\t\tgroupInfo: {\n\t\t\t\tname: '',\n\t\t\t\tdescription: '',\n\t\t\t\tbanner: '/static/imgs/group-banner.jpg',\n\t\t\t\tcourseCount: 0,\n\t\t\t\tmemberCount: 0,\n\t\t\t\tprogress: 0\n\t\t\t},\n\t\t\trecentStudy: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '第5课：日常会话练习',\n\t\t\t\t\ttype: 'course',\n\t\t\t\t\tstudyTime: '2024-01-15 14:30',\n\t\t\t\t\tprogress: 75\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '听力练习：购物场景',\n\t\t\t\t\ttype: 'practice',\n\t\t\t\t\tstudyTime: '2024-01-14 16:20',\n\t\t\t\t\tprogress: 100\n\t\t\t\t}\n\t\t\t],\n\t\t\tstats: {\n\t\t\t\ttotalHours: 25,\n\t\t\t\tcompletedCourses: 8,\n\t\t\t\tpracticeCount: 32,\n\t\t\t\taverageScore: 85\n\t\t\t}\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.groupId) {\n\t\t\tthis.groupId = options.groupId;\n\t\t\tthis.loadGroupInfo();\n\t\t}\n\t\tif (options.groupName) {\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: options.groupName\n\t\t\t});\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载小组信息\n\t\tloadGroupInfo() {\n\t\t\t// 这里应该根据groupId调用API获取小组详细信息\n\t\t\t// 暂时使用模拟数据\n\t\t\tconst groupData = {\n\t\t\t\t1: { name: '初级日语小组', description: '适合日语初学者', courseCount: 12, memberCount: 25, progress: 60 },\n\t\t\t\t2: { name: '中级日语小组', description: '适合有一定基础的学员', courseCount: 15, memberCount: 18, progress: 45 },\n\t\t\t\t3: { name: '高级日语小组', description: '适合高级学员', courseCount: 10, memberCount: 12, progress: 80 },\n\t\t\t\t4: { name: '商务日语小组', description: '专注商务日语', courseCount: 8, memberCount: 15, progress: 30 },\n\t\t\t\t5: { name: '考试冲刺小组', description: '针对JLPT等考试', courseCount: 20, memberCount: 30, progress: 70 }\n\t\t\t};\n\t\t\t\n\t\t\tthis.groupInfo = { ...this.groupInfo, ...groupData[this.groupId] };\n\t\t},\n\n\t\t// 跳转到课程回顾\n\t\tgoToCourseReview() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/course-review?groupId=${this.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到课后练习\n\t\tgoToPractice() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/practice?groupId=${this.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 继续学习\n\t\tcontinueStudy(item) {\n\t\t\tif (item.type === 'course') {\n\t\t\t\t// 跳转到课程详情\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/course-review?groupId=${this.groupId}&courseId=${item.id}`\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 跳转到练习\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/practice?groupId=${this.groupId}&practiceId=${item.id}`\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.group-header {\n\tbackground: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.group-banner {\n\theight: 300rpx;\n\toverflow: hidden;\n}\n\n.group-banner image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.group-info {\n\tpadding: 30rpx;\n}\n\n.group-title {\n\tfont-size: 42rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.group-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 20rpx;\n}\n\n.group-meta {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.meta-item {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tbackground: #f8f8f8;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n}\n\n.function-menu {\n\tbackground: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.menu-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: #2094CE;\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.menu-icon .iconfont {\n\tcolor: #fff;\n\tfont-size: 36rpx;\n}\n\n.menu-text {\n\tflex: 1;\n}\n\n.menu-title {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.menu-desc {\n\tfont-size: 26rpx;\n\tcolor: #999;\n}\n\n.recent-study, .study-stats {\n\tbackground: #fff;\n\tmargin-bottom: 20rpx;\n\tpadding: 30rpx;\n}\n\n.section-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.study-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.study-item:last-child {\n\tborder-bottom: none;\n}\n\n.study-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.study-info {\n\tflex: 1;\n}\n\n.study-title {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.study-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.study-progress {\n\tfont-size: 26rpx;\n\tcolor: #2094CE;\n}\n\n.stats-grid {\n\tdisplay: grid;\n\tgrid-template-columns: 1fr 1fr;\n\tgap: 20rpx;\n}\n\n.stat-card {\n\ttext-align: center;\n\tpadding: 30rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 16rpx;\n}\n\n.stat-number {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #2094CE;\n\tmargin-bottom: 10rpx;\n}\n\n.stat-label {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./group-detail.vue?vue&type=style&index=0&id=7368b85e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./group-detail.vue?vue&type=style&index=0&id=7368b85e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662925943\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}