<gui-page vue-id="03295755-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-7368b85e vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1 data-v-7368b85e" style="background-color:#F8F8F8;" slot="gBody"><view class="group-header data-v-7368b85e"><view class="group-banner data-v-7368b85e"><image src="{{groupInfo.banner}}" mode="aspectFill" class="data-v-7368b85e"></image></view><view class="group-info data-v-7368b85e"><view class="group-title data-v-7368b85e">{{groupInfo.name}}</view><view class="group-subtitle data-v-7368b85e">{{groupInfo.description}}</view><view class="group-meta data-v-7368b85e"><text class="meta-item data-v-7368b85e">{{"课程 "+groupInfo.courseCount}}</text><text class="meta-item data-v-7368b85e">{{"成员 "+groupInfo.memberCount}}</text><text class="meta-item data-v-7368b85e">{{"进度 "+groupInfo.progress+"%"}}</text></view></view></view><view class="function-menu data-v-7368b85e"><view data-event-opts="{{[['tap',[['goToCourseReview',['$event']]]]]}}" class="menu-item data-v-7368b85e" bindtap="__e"><view class="menu-icon data-v-7368b85e"><text class="iconfont icon-book data-v-7368b85e"></text></view><view class="menu-text data-v-7368b85e"><view class="menu-title data-v-7368b85e">课程回顾</view><view class="menu-desc data-v-7368b85e">查看已学习的课程内容</view></view><text class="iconfont icon-arrow-right data-v-7368b85e"></text></view><view data-event-opts="{{[['tap',[['goToPractice',['$event']]]]]}}" class="menu-item data-v-7368b85e" bindtap="__e"><view class="menu-icon data-v-7368b85e"><text class="iconfont icon-edit data-v-7368b85e"></text></view><view class="menu-text data-v-7368b85e"><view class="menu-title data-v-7368b85e">课后练习</view><view class="menu-desc data-v-7368b85e">听力练习、答题练习</view></view><text class="iconfont icon-arrow-right data-v-7368b85e"></text></view></view><view class="recent-study data-v-7368b85e"><view class="section-title data-v-7368b85e">最近学习</view><view class="study-list data-v-7368b85e"><block wx:for="{{recentStudy}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['continueStudy',['$0'],[[['recentStudy','',index]]]]]]]}}" class="study-item data-v-7368b85e" bindtap="__e"><view class="study-icon data-v-7368b85e"><text class="{{['iconfont','data-v-7368b85e',item.type==='course'?'icon-video':'icon-practice']}}"></text></view><view class="study-info data-v-7368b85e"><view class="study-title data-v-7368b85e">{{item.title}}</view><view class="study-time data-v-7368b85e">{{item.studyTime}}</view></view><view class="study-progress data-v-7368b85e"><text class="data-v-7368b85e">{{item.progress+"%"}}</text></view></view></block></view></view><view class="study-stats data-v-7368b85e"><view class="section-title data-v-7368b85e">学习统计</view><view class="stats-grid data-v-7368b85e"><view class="stat-card data-v-7368b85e"><view class="stat-number data-v-7368b85e">{{stats.totalHours}}</view><view class="stat-label data-v-7368b85e">学习时长(小时)</view></view><view class="stat-card data-v-7368b85e"><view class="stat-number data-v-7368b85e">{{stats.completedCourses}}</view><view class="stat-label data-v-7368b85e">完成课程</view></view><view class="stat-card data-v-7368b85e"><view class="stat-number data-v-7368b85e">{{stats.practiceCount}}</view><view class="stat-label data-v-7368b85e">练习次数</view></view><view class="stat-card data-v-7368b85e"><view class="stat-number data-v-7368b85e">{{stats.averageScore}}</view><view class="stat-label data-v-7368b85e">平均分数</view></view></view></view></view></gui-page>