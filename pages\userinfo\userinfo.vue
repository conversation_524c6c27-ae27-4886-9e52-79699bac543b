<template>
	<gracePage>
		<view slot="gHeader">
			<ws-head color="black"></ws-head>
		</view>
		<view slot="gBody" >
	<view class="info-set">
		<button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="changeAvatar">
			<img :src="avatar" class="avatar-img" />
		</button>
		<form @submit="formSubmit">
			<view class="username-content">
				<view class="user-title">昵称</view>
				<input class="user-input" type="nickname" name="input" placeholder="请输入昵称" :value="userInfo.name"/>
			</view>
			<!-- <view class="username-content">
				<view class="user-title">手机号</view>
				<input class="user-input" type="phone" name="input" placeholder="请输入手机号" :value="userInfo.mobile"/>
				<button class="phone-login-btn" type="default" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">修改手机号</button>
			</view> -->
			<button class="submit-btn" formType="submit">提交</button>
		</form>
	</view>
	</view>
	</gracePage>
</template>

<script>
import { showToast, uploadFile } from '@/common/js/asyncWx.js';
import { isHttpOrHttps, randomString } from '@/common/js/replace.js';
// 此方法是项目获取用户信息方法，根据自己项目进行替换
// import { getStorageUserInfo, setStorageUserInfo } from '@/utils/storage.js';
// 此方法是项目获取oss参数接口，根据自己项目进行替换
// import { getOssPolicy } from '@/api/ossUpload';
export default {
	data() {
		return {
			// 上传oss所需签名
			uploadOssPolicy: {},
			userInfo: '',
			avatar: ''
		};
	},
	methods: {
		changeAvatar(e) {
			console.log(e)
			this.avatar = e.detail.avatarUrl;
		},
		async formSubmit(e) {
			if(this.avatar.startsWith('http://tmp')||this.avatar.startsWith('wxfile://tmp')){
				// 后缀名
				const suffix = this.avatar.split('.').pop();
				console.log('avatar'+this.avatar)
				// 图片的正式路径（项目只需要上传半路径，因此此路径不包含基准路径）
				const imgRelativePath = this.uploadOssPolicy.dir + '/' + randomString(6, suffix);
				console.log('imgRelativePath'+imgRelativePath)
				const imageType = /\.(jpg|jpeg|png|GIF|JPG|PNG)$/.test(imgRelativePath);
				if (!this.avatar || !imageType) return showToast({ title: '请上传图片或正确图片' });
				if (!e.detail.value.input) return showToast({ title: '请输入昵称' });
				// oss上传参数，根据自己项目进行替换
				const uploadData = {
					name: imgRelativePath,
					key: imgRelativePath,
					policy: this.uploadOssPolicy.policy,
					OSSAccessKeyId: this.uploadOssPolicy.accessid,
					signature: this.uploadOssPolicy.signature,
					success_action_status: '200',
					//callback: this.uploadOssPolicy.callback
				};
				// 上传图片所有参数，属性值根据自己项目进行替换
				const uploadImagesData = {
					url: this.uploadOssPolicy.host,
					filePath: this.avatar,
					fileType: 'image',
					name: 'file',
					formData: uploadData
				};
				// 由于图片路径为临时路径，因此需要上传至项目服务器转化为正式路径
				await uploadFile(uploadImagesData);
				const data = {
					avatar: imgRelativePath,
					name: e.detail.value.input
				};
				this.userInfo.avatar = this.uploadOssPolicy.host+imgRelativePath;
			}else{
				this.userInfo.avatar = this.avatar;
				
			}
			this.userInfo.name = e.detail.value.input;
			// 发送信息修改请求
			this.setUserInfo(this.userInfo);
		},
		getOssPolicy(){
			this.$http.get("v1/user/sign").then(res => {
				console.log(res)
				if (res.data.code == 0) {
					this.uploadOssPolicy = res.data.data;
				} else {

				}
			})
		},
		getUserInfo(){
			this.$http.get("v1/user/getUserInfo").then(res => {
				console.log(res)
				if (res.data.code == 0) {
					this.userInfo = res.data.data
					this.avatar = res.data.data.avatar
				} else {

				}
			})
		},
		setUserInfo(userInfo){
			this.$http.post("v1/user/setUserInfo", {
				userInfo: userInfo
			}).then(res => {
				console.log(res)
				if (res.data.code == 0) {
					uni.showToast({
						title: '修改成功',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 2000)
				} else {

				}
			})
		},
		getPhoneNumber(e) {
			console.log(e)
			// console.log(e.detail.errMsg)
			// console.log(e.detail.iv)
			// console.log(e.detail.encryptedData)
			// console.log('userInfo'+uni.getStorageSync('userInfo'))
			// uni.login({
			// 	provider: 'weixin',
			// 	success: (res) => {
			// 		if (res.code) {
			// 			this.code = res.code;
			// 			this.$http.post("auth/decrypt_phone", {
			// 				code: this.code,
			// 				iv: e.detail.iv,
			// 				encryptedData: e.detail.encryptedData,
			// 				id:uni.getStorageSync('userInfo').id
			// 			}).then(res => {
			// 				if (res.data.code == 0) {
			// 					this.$store.commit('setUserInfo', {
			// 						userInfo: res.data.data,
			// 						saveStorage: true
			// 					});
			// 					uni.switchTab({
			// 						url: "/pages/index/index"
			// 					})
			// 				} else {
			// 					uni.showToast({
			// 						title: res.data.data.message,
			// 						icon: "none"
			// 					});
			// 				}
			// 				})
			// 		} else {
			// 			console.log('登录失败！' + res.errMsg)
			// 		}
			// 	}
			// })
			
		},
		
	},
	onLoad() {
		// getOssPolicy().then(res => {
		// 	this.uploadOssPolicy = res.data;
		// });
		this.getOssPolicy()
		this.getUserInfo()
	}
};
</script>

<style lang="scss" scoped>
.info-set {
	.avatar-btn {
		width: 200rpx;
		height: 200rpx;
		margin: 50rpx auto;
		padding: 0;
		background: none;
		border-radius: 50%;

		.avatar-img {
			width: 100%;
			height: 100%;
		}
	}

	.username-content {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-top: 1px solid #f5f5f5;
		border-bottom: 1px solid #f5f5f5;

		.user-title {
			width: 20%;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
		}

		.user-input {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			padding: 0 28rpx;
			color: #333;
			font-size: 28rpx;
		}
	}

	.submit-btn {
		margin: 80rpx 24rpx 0;
		color: #fff;
		font-size: 32rpx;
		background-color: #04BCD6;
		border-radius: 4rpx;
		box-sizing: border-box;
	}
}
</style>

