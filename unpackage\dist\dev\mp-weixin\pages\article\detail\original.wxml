<block wx:if="{{article}}"><view class="root lp-flex-column data-v-04522d66" style="height:100%;"><block wx:if="{{type=='original'||article.is_trans}}"><scroll-view class="root-box data-v-04522d66" enable-flex="true" scroll-y="true" scroll-top="{{custom_y}}" data-event-opts="{{[['scroll',[['onScrollHandler',['$event']]]]]}}" bindscroll="__e"><view class="panel main-box data-v-04522d66"><view class="head head-box data-v-04522d66"><view class="left-box data-v-04522d66"><view class="title-icon data-v-04522d66"></view><text class="publish-date data-v-04522d66">{{article.publish_date}}</text><text class="lang data-v-04522d66">{{$root.g0}}</text></view><view class="right-box data-v-04522d66"><text data-event-opts="{{[['tap',[['onLikeHandler',['$event']]]]]}}" class="gui-icons data-v-04522d66" bindtap="__e">{{article.is_collect?'':''}}</text></view></view><view class="body data-v-04522d66"><view class="title-box data-v-04522d66">{{contents[0].title}}</view><view class="cover-box data-v-04522d66"><image class="cover data-v-04522d66" mode="aspectFill" src="{{article.picture}}"></image></view><view class="content-box data-v-04522d66"><block wx:if="{{article.type!=2}}"><view class="rich-box data-v-04522d66"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><rich-text nodes="{{item.f0}}" data-event-opts="{{[['longpress',[['onSetClipboardDataHandler',['$0'],[[['contents','id',item.$orig.id,'content']]]]]]]}}" bindlongpress="__e" class="data-v-04522d66"></rich-text></block></view></block></view><view class="content-foot-box data-v-04522d66"><text class="data-v-04522d66"><text style="width:120rpx;" class="data-v-04522d66">文章来源：</text>{{article.source?article.source:'--'}}</text><text class="declaration data-v-04522d66"><text style="width:120rpx;" class="data-v-04522d66">声明：</text>{{"本材料为独家材料，版权归《"+(article.source?article.source:'--')+"》所有，仅供培训班内学员学习使用，禁止转载传播，一经发现将追究法律责任。"}}</text></view></view></view><block wx:if="{{isOriginal}}"><view class="panel tran-box data-v-04522d66"><view class="head data-v-04522d66"><view class="title-icon data-v-04522d66"></view><view class="title-box data-v-04522d66"><text class="data-v-04522d66">翻译</text></view></view><view class="body data-v-04522d66"><lp-tran vue-id="85f27d4e-1" article="{{article}}" type="{{type}}" data-event-opts="{{[['^getInput',[['onGetInputHandler']]],['^dirty',[['onTranDirtyHandler']]]]}}" bind:getInput="__e" bind:dirty="__e" class="data-v-04522d66" bind:__l="__l"></lp-tran></view></view></block><block wx:if="{{$root.g1}}"><view class="panel analyse-box data-v-04522d66"><view class="head data-v-04522d66"><view class="title-icon data-v-04522d66"></view><view class="title-box data-v-04522d66"><text class="data-v-04522d66">译文解析</text></view></view><view class="body data-v-04522d66"><block wx:for="{{article.comment}}" wx:for-item="item" wx:for-index="index" wx:key="id"><lp-analyse vue-id="{{'85f27d4e-2-'+index}}" comment="{{item}}" class="data-v-04522d66" bind:__l="__l"></lp-analyse></block></view></view></block><block wx:if="{{$root.g2}}"><view class="panel comment-box data-v-04522d66"><view class="head data-v-04522d66"><view class="title-icon data-v-04522d66"></view><view class="title-box data-v-04522d66"><text class="data-v-04522d66">老师点评</text></view></view><view class="body data-v-04522d66"><view class="content-box data-v-04522d66"><block wx:for="{{$root.l1}}" wx:for-item="comment" wx:for-index="index" wx:key="id"><block wx:if="{{comment.$orig.author}}"><view style="font-size:32rpx;font-weight:bold;" class="data-v-04522d66">{{comment.$orig.author.name+"点评："}}</view></block><rich-text nodes="{{comment.f1}}" data-event-opts="{{[['longpress',[['onSetClipboardDataHandler',['$0'],[[['article.trans.__$n0.comments','id',comment.$orig.id,'content']]]]]]]}}" bindlongpress="__e" class="data-v-04522d66"></rich-text></block></view></view></view></block></scroll-view></block><block wx:else><view class="nopublish lp-flex-column lp-flex-center data-v-04522d66"><text class="gui-icons data-v-04522d66" style="font-size:100rpx;"></text><text class="data-v-04522d66">译文即将公布!</text></view></block><block wx:if="{{isOriginal}}"><lp-input vue-id="85f27d4e-3" data-ref="input" class="data-v-04522d66 vue-ref" bind:__l="__l"></lp-input></block></view></block><block wx:else><view class="nopublish lp-flex-column lp-flex-center data-v-04522d66"><text class="gui-icons data-v-04522d66">数据正在玩命加载中...</text></view></block>