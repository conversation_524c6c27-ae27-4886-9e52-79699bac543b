{"version": 3, "sources": ["webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?3277", "webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?fd60", "webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?3cf5", "webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?b053", "uni-app:///uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue", "webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?d41b", "webpack:///D:/日语云课/uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue?7a1e"], "names": ["name", "emits", "props", "value", "type", "default", "modelValue", "accordion", "data", "computed", "dataValue", "Array", "watch", "created", "mounted", "methods", "<PERSON><PERSON><PERSON>", "console", "vm", "val", "setAccordion", "resize", "onChange", "activeItem", "emit"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACM5oB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,eAQA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;EACA;EACAG;IACA;EACA;EACAC;IACA;IACAC;MACA,mEACAC;MACA,kFACAA;MACA;QACA;MACA;MACA;QACA;MACA;MAEA;IACA;EACA;EACAC;IACAF;MACA;IACA;EACA;EACAG;IACA;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACA;UACA;YACA;cACAC;cACA;YACA;YACAC;UACA;QACA;QACA;UACAC;YACA;cACA;gBACAF;gBACA;cACA;cACAC;YACA;UACA;QACA;MACA;MACA;IACA;IACAE;MACA;MACA;QACA;UACAF;QACA;MACA;IACA;IACAG;MACA;QAEAH;MAKA;IACA;IACAI;MACA;MAEA;QACAC;MACA;QACA;UACA;YACAA;UACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpIA;AAAA;AAAA;AAAA;AAA+qC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACAnsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-collapse/components/uni-collapse/uni-collapse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-collapse.vue?vue&type=template&id=275068f4&\"\nvar renderjs\nimport script from \"./uni-collapse.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-collapse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-collapse.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-collapse/components/uni-collapse/uni-collapse.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-collapse.vue?vue&type=template&id=275068f4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-collapse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-collapse.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-collapse\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n<script>\r\n\t/**\r\n\t * Collapse 折叠面板\r\n\t * @description 展示可以折叠 / 展开的内容区域\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=23\r\n\t * @property {String|Array} value 当前激活面板改变时触发(如果是手风琴模式，参数类型为string，否则为array)\r\n\t * @property {Boolean} accordion = [true|false] 是否开启手风琴效果是否开启手风琴效果\r\n\t * @event {Function} change 切换面板时触发，如果是手风琴模式，返回类型为string，否则为array\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniCollapse',\r\n\t\temits:['change','activeItem','input','update:modelValue'],\r\n\t\tprops: {\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [String, Array],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [String, Array],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\taccordion: {\r\n\t\t\t\t// 是否开启手风琴效果\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// TODO 兼容 vue2 和 vue3\r\n\t\t\tdataValue() {\r\n\t\t\t\tlet value = (typeof this.value === 'string' && this.value === '') ||\r\n\t\t\t\t\t(Array.isArray(this.value) && this.value.length === 0)\r\n\t\t\t\tlet modelValue = (typeof this.modelValue === 'string' && this.modelValue === '') ||\r\n\t\t\t\t\t(Array.isArray(this.modelValue) && this.modelValue.length === 0)\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\treturn this.modelValue\r\n\t\t\t\t}\r\n\t\t\t\tif (modelValue) {\r\n\t\t\t\t\treturn this.value\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn this.value\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdataValue(val) {\n\t\t\t\tthis.setOpen(val)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.childrens = []\r\n\t\t\tthis.names = []\r\n\t\t},\r\n\t\tmounted() {\n\t\t\tthis.$nextTick(()=>{\n\t\t\t\tthis.setOpen(this.dataValue)\n\t\t\t})\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetOpen(val) {\r\n\t\t\t\tlet str = typeof val === 'string'\r\n\t\t\t\tlet arr = Array.isArray(val)\n\t\t\t\tthis.childrens.forEach((vm, index) => {\r\n\t\t\t\t\tif (str) {\r\n\t\t\t\t\t\tif (val === vm.nameSync) {\r\n\t\t\t\t\t\t\tif (!this.accordion) {\r\n\t\t\t\t\t\t\t\tconsole.warn('accordion 属性为 false ,v-model 类型应该为 array')\r\n\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tvm.isOpen = true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\n\t\t\t\t\tif (arr) {\r\n\t\t\t\t\t\tval.forEach(v => {\r\n\t\t\t\t\t\t\tif (v === vm.nameSync) {\n\t\t\t\t\t\t\t\tif (this.accordion) {\r\n\t\t\t\t\t\t\t\t\tconsole.warn('accordion 属性为 true ,v-model 类型应该为 string')\r\n\t\t\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tvm.isOpen = true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.emit(val)\r\n\t\t\t},\r\n\t\t\tsetAccordion(self) {\r\n\t\t\t\tif (!this.accordion) return\r\n\t\t\t\tthis.childrens.forEach((vm, index) => {\r\n\t\t\t\t\tif (self !== vm) {\r\n\t\t\t\t\t\tvm.isOpen = false\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tresize() {\r\n\t\t\t\tthis.childrens.forEach((vm, index) => {\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tvm.getCollapseHeight()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tvm.getNvueHwight()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonChange(isOpen, self) {\r\n\t\t\t\tlet activeItem = []\r\n\r\n\t\t\t\tif (this.accordion) {\r\n\t\t\t\t\tactiveItem = isOpen ? self.nameSync : ''\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.childrens.forEach((vm, index) => {\r\n\t\t\t\t\t\tif (vm.isOpen) {\r\n\t\t\t\t\t\t\tactiveItem.push(vm.nameSync)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('change', activeItem)\r\n\t\t\t\tthis.emit(activeItem)\r\n\t\t\t},\r\n\t\t\temit(val){\r\n\t\t\t\tthis.$emit('input', val)\r\n\t\t\t\tthis.$emit('update:modelValue', val)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" >\r\n\t.uni-collapse {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tflex: 1;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-collapse.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-collapse.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662930404\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}