<block wx:if="{{node.node=='element'}}"><block><block wx:if="{{node.tag=='button'}}"><button class="{{[node.classStr]}}" style="{{(node.styleStr)}}" type="default" size="mini"><weixin-parse-template vue-id="dcec9718-1" node="{{node}}" bind:__l="__l"></weixin-parse-template></button></block><block wx:else><block wx:if="{{node.tag=='a'}}"><view class="{{[node.classStr]}}" style="{{(node.styleStr)}}" data-href="{{node.attr.href}}" data-event-opts="{{[['tap',[['wxParseATap',['$0','$event'],['node.attr']]]]]}}" bindtap="__e"><block wx:for="{{node.nodes}}" wx:for-item="node" wx:for-index="index" wx:key="index"><block><weixin-parse-template vue-id="{{'dcec9718-2-'+index}}" node="{{node}}" bind:__l="__l"></weixin-parse-template></block></block></view></block><block wx:else><block wx:if="{{node.tag=='li'}}"><view class="{{[node.classStr]}}" style="{{(node.styleStr)}}"><block wx:for="{{node.nodes}}" wx:for-item="node" wx:for-index="index" wx:key="index"><block><weixin-parse-template vue-id="{{'dcec9718-3-'+index}}" node="{{node}}" bind:__l="__l"></weixin-parse-template></block></block></view></block><block wx:else><block wx:if="{{node.tag=='table'}}"><weixin-parse-table class="{{[node.classStr]}}" style="{{(node.styleStr)}}" vue-id="dcec9718-4" node="{{node}}" bind:__l="__l"></weixin-parse-table></block><block wx:else><block wx:if="{{node.tag=='br'}}"><text>\n</text></block><block wx:else><block wx:if="{{node.tag=='video'}}"><weixin-parse-video vue-id="dcec9718-5" node="{{node}}" bind:__l="__l"></weixin-parse-video></block><block wx:else><block wx:if="{{node.tag=='audio'}}"><weixin-parse-audio vue-id="dcec9718-6" node="{{node}}" bind:__l="__l"></weixin-parse-audio></block><block wx:else><block wx:if="{{node.tag=='img'}}"><weixin-parse-img style="{{(node.styleStr)}}" vue-id="dcec9718-7" node="{{node}}" bind:__l="__l"></weixin-parse-img></block><block wx:else><view class="{{[node.classStr]}}" style="{{(node.styleStr)}}"><block wx:for="{{node.nodes}}" wx:for-item="node" wx:for-index="index" wx:key="index"><block><weixin-parse-template vue-id="{{'dcec9718-8-'+index}}" node="{{node}}" bind:__l="__l"></weixin-parse-template></block></block></view></block></block></block></block></block></block></block></block></block></block><block wx:else><block wx:if="{{node.node=='text'}}"><block>{{node.text}}</block></block></block>