<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 小组信息头部 -->
			<view class="group-header">
				<view class="group-banner">
					<image :src="groupInfo.banner" mode="aspectFill"></image>
				</view>
				<view class="group-info">
					<view class="group-title">{{groupInfo.name}}</view>
					<view class="group-subtitle">{{groupInfo.description}}</view>
					<view class="group-meta">
						<text class="meta-item">课程 {{groupInfo.courseCount}}</text>
						<text class="meta-item">成员 {{groupInfo.memberCount}}</text>
						<text class="meta-item">进度 {{groupInfo.progress}}%</text>
					</view>
				</view>
			</view>

			<!-- 功能菜单 -->
			<view class="function-menu">
				<view class="menu-item" @click="goToCourseReview">
					<view class="menu-icon">
						<text class="iconfont icon-book"></text>
					</view>
					<view class="menu-text">
						<view class="menu-title">课程回顾</view>
						<view class="menu-desc">查看已学习的课程内容</view>
					</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>

				<view class="menu-item" @click="goToPractice">
					<view class="menu-icon">
						<text class="iconfont icon-edit"></text>
					</view>
					<view class="menu-text">
						<view class="menu-title">课后练习</view>
						<view class="menu-desc">听力练习、答题练习</view>
					</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>
			</view>

			<!-- 最近学习 -->
			<view class="recent-study">
				<view class="section-title">最近学习</view>
				<view class="study-list">
					<view 
						class="study-item" 
						v-for="(item, index) in recentStudy" 
						:key="index"
						@click="continueStudy(item)"
					>
						<view class="study-icon">
							<text class="iconfont" :class="item.type === 'course' ? 'icon-video' : 'icon-practice'"></text>
						</view>
						<view class="study-info">
							<view class="study-title">{{item.title}}</view>
							<view class="study-time">{{item.studyTime}}</view>
						</view>
						<view class="study-progress">
							<text>{{item.progress}}%</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 学习统计 -->
			<view class="study-stats">
				<view class="section-title">学习统计</view>
				<view class="stats-grid">
					<view class="stat-card">
						<view class="stat-number">{{stats.totalHours}}</view>
						<view class="stat-label">学习时长(小时)</view>
					</view>
					<view class="stat-card">
						<view class="stat-number">{{stats.completedCourses}}</view>
						<view class="stat-label">完成课程</view>
					</view>
					<view class="stat-card">
						<view class="stat-number">{{stats.practiceCount}}</view>
						<view class="stat-label">练习次数</view>
					</view>
					<view class="stat-card">
						<view class="stat-number">{{stats.averageScore}}</view>
						<view class="stat-label">平均分数</view>
					</view>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			groupId: '',
			groupInfo: {
				name: '',
				description: '',
				banner: '/static/imgs/group-banner.jpg',
				courseCount: 0,
				memberCount: 0,
				progress: 0
			},
			recentStudy: [
				{
					id: 1,
					title: '第5课：日常会话练习',
					type: 'course',
					studyTime: '2024-01-15 14:30',
					progress: 75
				},
				{
					id: 2,
					title: '听力练习：购物场景',
					type: 'practice',
					studyTime: '2024-01-14 16:20',
					progress: 100
				}
			],
			stats: {
				totalHours: 25,
				completedCourses: 8,
				practiceCount: 32,
				averageScore: 85
			}
		}
	},
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId;
			this.loadGroupInfo();
		}
		if (options.groupName) {
			uni.setNavigationBarTitle({
				title: options.groupName
			});
		}
	},
	methods: {
		// 加载小组信息
		loadGroupInfo() {
			// 这里应该根据groupId调用API获取小组详细信息
			// 暂时使用模拟数据
			const groupData = {
				1: { name: '初级日语小组', description: '适合日语初学者', courseCount: 12, memberCount: 25, progress: 60 },
				2: { name: '中级日语小组', description: '适合有一定基础的学员', courseCount: 15, memberCount: 18, progress: 45 },
				3: { name: '高级日语小组', description: '适合高级学员', courseCount: 10, memberCount: 12, progress: 80 },
				4: { name: '商务日语小组', description: '专注商务日语', courseCount: 8, memberCount: 15, progress: 30 },
				5: { name: '考试冲刺小组', description: '针对JLPT等考试', courseCount: 20, memberCount: 30, progress: 70 }
			};
			
			this.groupInfo = { ...this.groupInfo, ...groupData[this.groupId] };
		},

		// 跳转到课程回顾
		goToCourseReview() {
			uni.navigateTo({
				url: `/pages/groups/course-review?groupId=${this.groupId}`
			});
		},

		// 跳转到课后练习
		goToPractice() {
			uni.navigateTo({
				url: `/pages/groups/practice?groupId=${this.groupId}`
			});
		},

		// 继续学习
		continueStudy(item) {
			if (item.type === 'course') {
				// 跳转到课程详情
				uni.navigateTo({
					url: `/pages/groups/course-review?groupId=${this.groupId}&courseId=${item.id}`
				});
			} else {
				// 跳转到练习
				uni.navigateTo({
					url: `/pages/groups/practice?groupId=${this.groupId}&practiceId=${item.id}`
				});
			}
		}
	}
}
</script>

<style scoped>
.group-header {
	background: #fff;
	margin-bottom: 20rpx;
}

.group-banner {
	height: 300rpx;
	overflow: hidden;
}

.group-banner image {
	width: 100%;
	height: 100%;
}

.group-info {
	padding: 30rpx;
}

.group-title {
	font-size: 42rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.group-subtitle {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.group-meta {
	display: flex;
	gap: 20rpx;
}

.meta-item {
	font-size: 24rpx;
	color: #999;
	background: #f8f8f8;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.function-menu {
	background: #fff;
	margin-bottom: 20rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	width: 80rpx;
	height: 80rpx;
	background: #2094CE;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.menu-icon .iconfont {
	color: #fff;
	font-size: 36rpx;
}

.menu-text {
	flex: 1;
}

.menu-title {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.menu-desc {
	font-size: 26rpx;
	color: #999;
}

.recent-study, .study-stats {
	background: #fff;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.study-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.study-item:last-child {
	border-bottom: none;
}

.study-icon {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.study-info {
	flex: 1;
}

.study-title {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.study-time {
	font-size: 24rpx;
	color: #999;
}

.study-progress {
	font-size: 26rpx;
	color: #2094CE;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-card {
	text-align: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #2094CE;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 26rpx;
	color: #666;
}
</style>
