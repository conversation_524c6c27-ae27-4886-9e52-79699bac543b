<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="modern-homepage">
			<!-- 现代化搜索栏 -->
			<view class="search-section">
				<view class="search-box" @click="search">
					<text class="search-icon">🔍</text>
					<text class="search-text">搜索日语课程、老师、话题...</text>
					<view class="search-btn">搜索</view>
				</view>
			</view>

			<!-- 轮播图 -->
			<view class="carousel-section" v-if="carouselList.length > 0">
				<swiper 
					class="modern-carousel" 
					circular 
					@change="swiperChange" 
					autoplay="true"
					interval="4000"
				>
					<swiper-item 
						v-for="(item, index) in carouselList" 
						:key="index" 
						@tap="navToWeb(item,'swiper')"
					>
						<image class="carousel-image" :src="item.thumb" mode="aspectFill" />
						<view class="carousel-overlay">
							<text class="carousel-title" v-if="item.title">{{item.title}}</text>
						</view>
					</swiper-item>
				</swiper>
				
				<!-- 指示器 -->
				<view class="carousel-dots">
					<view 
						class="dot" 
						:class="{ active: index === swiperCurrent }"
						v-for="(item, index) in carouselList" 
						:key="index"
					></view>
				</view>
			</view>

			<!-- 分类网格 -->
			<view class="category-section" v-if="iconList1.length > 0">
				<view class="section-title">热门分类</view>
				<view class="category-grid">
					<view 
						class="category-item" 
						v-for="(item, index) in iconList1" 
						:key="index"
						@tap="navTo(item, 'category')"
					>
						<image class="category-icon" :src="item.thumb" mode="aspectFit" />
						<text class="category-name">{{item.title}}</text>
					</view>
				</view>
			</view>

			<!-- 推荐内容 -->
			<view class="content-section" v-if="courseList.length > 0">
				<view class="section-title">推荐阅读</view>
				<view class="content-list">
					<view 
						class="content-item" 
						v-for="(item, index) in courseList" 
						:key="index" 
						@tap="navToWeb(item)"
					>
						<image class="content-image" :src="item.thumb" mode="aspectFill" />
						<view class="content-info">
							<text class="content-title">{{item.title}}</text>
							<text class="content-more">查看详情 ></text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	export default {
		data() {
			return {
				pageLoading: false,
				carouselList: [],
				iconList1: [],
				courseList: [],
				swiperCurrent: 0,
				swiperLength: 0
			};
		},
		onLoad() {
			this.loadData();
		},
		methods: {
			loadData() {
				// 这里调用原有的数据加载方法
				this.loadHotData();
			},
			
			search() {
				uni.navigateTo({
					url: '/pages/search/search'
				});
			},
			
			swiperChange(e) {
				this.swiperCurrent = e.detail.current;
			},
			
			navTo(item, type) {
				// 导航逻辑
				console.log('导航到:', item, type);
			},
			
			navToWeb(item) {
				// 网页导航逻辑
				console.log('打开网页:', item);
			},
			
			// 原有的数据加载方法（简化版）
			async loadHotData() {
				this.pageLoading = true;
				try {
					// 模拟数据加载
					setTimeout(() => {
						this.carouselList = [
							{ id: 1, thumb: '/static/imgs/banner1.jpg', title: '日语入门课程' },
							{ id: 2, thumb: '/static/imgs/banner2.jpg', title: '商务日语' }
						];
						this.iconList1 = [
							{ id: 1, thumb: '/static/imgs/icon1.png', title: '基础语法' },
							{ id: 2, thumb: '/static/imgs/icon2.png', title: '日常对话' },
							{ id: 3, thumb: '/static/imgs/icon3.png', title: '商务日语' },
							{ id: 4, thumb: '/static/imgs/icon4.png', title: '考试辅导' }
						];
						this.courseList = [
							{ id: 1, thumb: '/static/imgs/course1.jpg', title: '零基础日语入门' },
							{ id: 2, thumb: '/static/imgs/course2.jpg', title: '日语语法精讲' }
						];
						this.swiperLength = this.carouselList.length;
						this.pageLoading = false;
					}, 1000);
				} catch (error) {
					console.error('加载数据失败:', error);
					this.pageLoading = false;
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.modern-homepage {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	// 搜索栏
	.search-section {
		background: rgba(255, 255, 255, 0.95);
		padding: 30rpx;
		border-radius: 0 0 40rpx 40rpx;
		margin-bottom: 20rpx;
	}

	.search-box {
		background: #f8f9fa;
		border-radius: 50rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		color: #666;
	}

	.search-text {
		flex: 1;
		color: #999;
		font-size: 28rpx;
	}

	.search-btn {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		padding: 12rpx 24rpx;
		border-radius: 25rpx;
		font-size: 24rpx;
	}

	// 轮播图
	.carousel-section {
		position: relative;
		margin: 0 20rpx 20rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
	}

	.modern-carousel {
		height: 400rpx;
	}

	.carousel-image {
		width: 100%;
		height: 100%;
	}

	.carousel-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		padding: 30rpx;
	}

	.carousel-title {
		color: white;
		font-size: 32rpx;
		font-weight: 600;
	}

	.carousel-dots {
		position: absolute;
		bottom: 20rpx;
		left: 30rpx;
		display: flex;
		gap: 10rpx;
	}

	.dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.5);
		transition: all 0.3s ease;

		&.active {
			background: white;
			transform: scale(1.2);
		}
	}

	// 分类和内容区域
	.category-section, .content-section {
		background: white;
		margin: 0 20rpx 20rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 30rpx;
	}

	.category-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30rpx;
	}

	.category-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			background: #f8f9fa;
		}
	}

	.category-icon {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 15rpx;
	}

	.category-name {
		font-size: 24rpx;
		color: #333;
		text-align: center;
	}

	.content-list {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.content-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			background: #f8f9fa;
		}
	}

	.content-image {
		width: 120rpx;
		height: 80rpx;
		border-radius: 12rpx;
		margin-right: 20rpx;
	}

	.content-info {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.content-title {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.content-more {
		font-size: 24rpx;
		color: #667eea;
	}
</style>
