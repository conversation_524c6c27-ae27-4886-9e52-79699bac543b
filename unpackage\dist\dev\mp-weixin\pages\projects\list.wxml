<gui-page class="vue-ref" vue-id="64487e5c-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#F8F8F8;" slot="gBody"><block wx:if="{{empty===true}}"><view class="empty"><image src="/static/emptyCart.jpg" mode="aspectFit"></image><view class="empty-tips">空空如也<navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator></view></view></block><block wx:else><view><block wx:if="{{$root.g0>0}}"><scroll-view scroll-y="true" data-event-opts="{{[['scrolltolower',[['onScrolltolowerHandler',['$event']]]]]}}" bindscrolltolower="__e"><view class="list-box"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['list','id',item.id]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view><view class="info-box lp-flex-column"><text class="title">{{item.title}}</text><text class="end-date">{{"有效期至"+item.end_time}}</text><text class="total">{{"共"+item.num+"节课"}}</text></view></view><block wx:if="{{item.p_id==3}}"><block><u-line vue-id="{{('64487e5c-2-'+index)+','+('64487e5c-1')}}" color="#eeeeee" hair-line="{{false}}" bind:__l="__l"></u-line><view class="margin-tb-sm uni-row align-center lp-flex"><view class="flex-sub"><text class="text-sm title">{{'最近学习：'+item.current+''}}</text></view><view class="uni-row"><u_button class="margin-left-sm" vue-id="{{('64487e5c-3-'+index)+','+('64487e5c-1')}}" type="success" size="mini" bind:__l="__l" vue-slots="{{['default']}}">学习中</u_button><block wx:if="{{item.certificate!=''&&item.certificate!=null}}"><u_button class="margin-left-sm" vue-id="{{('64487e5c-4-'+index)+','+('64487e5c-1')}}" type="error" size="mini" data-event-opts="{{[['^click',[['download',['$0'],[[['list','id',item.id,'certificate']]]]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">证书下载</u_button></block></view></view></block></block><block wx:else><block><u-line vue-id="{{('64487e5c-5-'+index)+','+('64487e5c-1')}}" color="#eeeeee" hair-line="{{false}}" bind:__l="__l"></u-line><view class="margin-tb-sm uni-row align-center lp-flex"><view class="flex-sub"><text class="text-sm title">{{'最近学习：'+item.current+''}}</text><view class="uni-row align-center"><text class="text-sm">观看进度：</text><u_line_progress vue-id="{{('64487e5c-6-'+index)+','+('64487e5c-1')}}" inactive-color="#e6eaf2" active-color="#19be6b" percent="{{item.progress}}" height="20" bind:__l="__l"></u_line_progress></view></view><view class="uni-row"><block wx:if="{{item.total_class==0}}"><u_button class="margin-left-sm" vue-id="{{('64487e5c-7-'+index)+','+('64487e5c-1')}}" type="success" size="mini" bind:__l="__l" vue-slots="{{['default']}}">学习中</u_button></block><block wx:else><u_button class="margin-left-sm" vue-id="{{('64487e5c-8-'+index)+','+('64487e5c-1')}}" type="success" size="mini" data-event-opts="{{[['^click',[['navToDetailPage',['$0'],[[['list','id',item.id]]]]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">继续学习</u_button></block><block wx:if="{{item.certificate!=''&&item.certificate!=null}}"><u_button class="margin-left-sm" vue-id="{{('64487e5c-9-'+index)+','+('64487e5c-1')}}" type="error" size="mini" data-event-opts="{{[['^click',[['download',['$0'],[[['list','id',item.id,'certificate']]]]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">证书下载</u_button></block></view></view></block></block></view></block></view></scroll-view></block></view></block></view></gui-page>