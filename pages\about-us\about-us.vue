<template>
	<gui-page :fullPage="true" :customHeader="false" class="root-box">

		<!-- 页面主体 -->
		<view slot="gBody" class="root-box lp-flex-column">
			<view class="logo-box lp-flex-center">
				<text class="gui-icons">&#xe626;</text>
			</view>
			<view class="info-box lp-flex-column">
				<text class="main">如果您在使用日语云课小程序的过程中有疑问或是对我们课程有宝贵的意见，欢迎用以下方式联系我们。</text>
				<text style="margin-top: 20rpx;"></text>
				<text>微信：</text>
				<text>【日语快讯】lp18922156620</text>
				<!-- <text>【崔老师】13501356325</text> -->
				<text style="margin-top: 20rpx;"></text>
				<!-- <text>电话：</text>
				<text>【译期译会】18925162859</text> -->
				<!-- <text>【崔老师】13501356325</text> -->
				<text style="margin-top: 20rpx;"></text>
				<text>公众号：</text>
				<text>联普日语社区</text>
				<!-- <text class="ver">程序版本：{{ver}}</text> -->
			</view>
		</view>
	</gui-page>
</template>

<script>

	export default {
		data() {
			return {
				ver: this.$store.state.ver,
			};
		}
	}
</script>

<style lang="scss">
	.root-box {
		.ver {
			color: #aaa;
			margin-top: 20rpx;
		}

		.logo-box {
			height: 500rpx;

			.gui-icons {
				font-size: 300rpx;
				color: $uni-text-color-grey;
			}
		}

		.info-box {
			padding:30rpx 100rpx;
			font-size: 30rpx;
			color: #333;
			text-align: justify;
		}
	}
</style>
