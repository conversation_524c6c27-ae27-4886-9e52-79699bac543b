@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.root-box .ver {
  color: #aaa;
  margin-top: 20rpx;
}
.root-box .logo-box {
  height: 500rpx;
}
.root-box .logo-box .gui-icons {
  font-size: 300rpx;
  color: #999;
}
.root-box .info-box {
  padding: 30rpx 100rpx;
  font-size: 30rpx;
  color: #333;
  text-align: justify;
}

