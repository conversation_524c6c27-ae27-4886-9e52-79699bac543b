@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-8ef4aa1a {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-8ef4aa1a {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-8ef4aa1a::after {
  border: none;
}
.u-hover-class.data-v-8ef4aa1a {
  opacity: 0.7;
}
.u-primary-light.data-v-8ef4aa1a {
  color: #ecf5ff;
}
.u-warning-light.data-v-8ef4aa1a {
  color: #fdf6ec;
}
.u-success-light.data-v-8ef4aa1a {
  color: #f5fff0;
}
.u-error-light.data-v-8ef4aa1a {
  color: #fef0f0;
}
.u-info-light.data-v-8ef4aa1a {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-8ef4aa1a {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-8ef4aa1a {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-8ef4aa1a {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-8ef4aa1a {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-8ef4aa1a {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-8ef4aa1a {
  color: #398ade;
}
.u-warning-dark.data-v-8ef4aa1a {
  color: #f1a532;
}
.u-success-dark.data-v-8ef4aa1a {
  color: #53c21d;
}
.u-error-dark.data-v-8ef4aa1a {
  color: #e45656;
}
.u-info-dark.data-v-8ef4aa1a {
  color: #767a82;
}
.u-primary-dark-bg.data-v-8ef4aa1a {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-8ef4aa1a {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-8ef4aa1a {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-8ef4aa1a {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-8ef4aa1a {
  background-color: #767a82;
}
.u-primary-disabled.data-v-8ef4aa1a {
  color: #9acafc;
}
.u-warning-disabled.data-v-8ef4aa1a {
  color: #f9d39b;
}
.u-success-disabled.data-v-8ef4aa1a {
  color: #a9e08f;
}
.u-error-disabled.data-v-8ef4aa1a {
  color: #f7b2b2;
}
.u-info-disabled.data-v-8ef4aa1a {
  color: #c4c6c9;
}
.u-primary.data-v-8ef4aa1a {
  color: #3c9cff;
}
.u-warning.data-v-8ef4aa1a {
  color: #f9ae3d;
}
.u-success.data-v-8ef4aa1a {
  color: #5ac725;
}
.u-error.data-v-8ef4aa1a {
  color: #f56c6c;
}
.u-info.data-v-8ef4aa1a {
  color: #909399;
}
.u-primary-bg.data-v-8ef4aa1a {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-8ef4aa1a {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-8ef4aa1a {
  background-color: #5ac725;
}
.u-error-bg.data-v-8ef4aa1a {
  background-color: #f56c6c;
}
.u-info-bg.data-v-8ef4aa1a {
  background-color: #909399;
}
.u-main-color.data-v-8ef4aa1a {
  color: #303133;
}
.u-content-color.data-v-8ef4aa1a {
  color: #606266;
}
.u-tips-color.data-v-8ef4aa1a {
  color: #909193;
}
.u-light-color.data-v-8ef4aa1a {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-8ef4aa1a {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-8ef4aa1a {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-8ef4aa1a {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-8ef4aa1a {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-8ef4aa1a::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-btn.data-v-8ef4aa1a::after {
  border: none;
}
.u-btn.data-v-8ef4aa1a {
  position: relative;
  border: 0;
  display: inline-flex;
  overflow: visible;
  line-height: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 40rpx;
  z-index: 1;
  box-sizing: border-box;
  transition: all 0.15s;
}
.u-btn--bold-border.data-v-8ef4aa1a {
  border: 1px solid #ffffff;
}
.u-btn--default.data-v-8ef4aa1a {
  color: #606266;
  border-color: #c0c4cc;
  background-color: #ffffff;
}
.u-btn--primary.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #2979ff;
  background-color: #2979ff;
}
.u-btn--success.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #19be6b;
  background-color: #19be6b;
}
.u-btn--error.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #fa3534;
  background-color: #fa3534;
}
.u-btn--warning.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #ff9900;
  background-color: #ff9900;
}
.u-btn--default--disabled.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #e4e7ed;
  background-color: #ffffff;
}
.u-btn--primary--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #a0cfff !important;
  background-color: #a0cfff !important;
}
.u-btn--success--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #71d5a1 !important;
  background-color: #71d5a1 !important;
}
.u-btn--error--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #fab6b6 !important;
  background-color: #fab6b6 !important;
}
.u-btn--warning--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #fcbd71 !important;
  background-color: #fcbd71 !important;
}
.u-btn--primary--plain.data-v-8ef4aa1a {
  color: #2979ff !important;
  border-color: #a0cfff !important;
  background-color: #ecf5ff !important;
}
.u-btn--success--plain.data-v-8ef4aa1a {
  color: #19be6b !important;
  border-color: #71d5a1 !important;
  background-color: #dbf1e1 !important;
}
.u-btn--error--plain.data-v-8ef4aa1a {
  color: #fa3534 !important;
  border-color: #fab6b6 !important;
  background-color: #fef0f0 !important;
}
.u-btn--warning--plain.data-v-8ef4aa1a {
  color: #ff9900 !important;
  border-color: #fcbd71 !important;
  background-color: #fdf6ec !important;
}
.u-hairline-border.data-v-8ef4aa1a:after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  left: 0;
  top: 0;
  width: 199.8%;
  height: 199.7%;
  -webkit-transform: scale(0.5, 0.5);
  transform: scale(0.5, 0.5);
  border: 1px solid currentColor;
  z-index: 1;
}
.u-wave-ripple.data-v-8ef4aa1a {
  z-index: 0;
  position: absolute;
  border-radius: 100%;
  background-clip: padding-box;
  pointer-events: none;
  -webkit-user-select: none;
          user-select: none;
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 1;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.u-wave-ripple.u-wave-active.data-v-8ef4aa1a {
  opacity: 0;
  -webkit-transform: scale(2);
          transform: scale(2);
  transition: opacity 1s linear, -webkit-transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear, -webkit-transform 0.4s linear;
}
.u-round-circle.data-v-8ef4aa1a {
  border-radius: 100rpx;
}
.u-round-circle.data-v-8ef4aa1a::after {
  border-radius: 100rpx;
}
.u-loading.data-v-8ef4aa1a::after {
  background-color: rgba(255, 255, 255, 0.35);
}
.u-size-default.data-v-8ef4aa1a {
  font-size: 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.u-size-medium.data-v-8ef4aa1a {
  display: inline-flex;
  width: auto;
  font-size: 26rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 80rpx;
}
.u-size-mini.data-v-8ef4aa1a {
  display: inline-flex;
  width: auto;
  font-size: 22rpx;
  padding-top: 1px;
  height: 50rpx;
  line-height: 50rpx;
  padding: 0 20rpx;
}
.u-primary-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #2b85e4 !important;
}
.u-default-plain-hover.data-v-8ef4aa1a {
  color: #2b85e4 !important;
  background: #ecf5ff !important;
}
.u-success-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #18b566 !important;
}
.u-warning-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #f29100 !important;
}
.u-error-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #dd6161 !important;
}
.u-info-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #82848a !important;
}
.u-default-hover.data-v-8ef4aa1a {
  color: #2b85e4 !important;
  border-color: #2b85e4 !important;
  background-color: #ecf5ff !important;
}
.u-primary-hover.data-v-8ef4aa1a {
  background: #2b85e4 !important;
  color: #fff;
}
.u-success-hover.data-v-8ef4aa1a {
  background: #18b566 !important;
  color: #fff;
}
.u-info-hover.data-v-8ef4aa1a {
  background: #82848a !important;
  color: #fff;
}
.u-warning-hover.data-v-8ef4aa1a {
  background: #f29100 !important;
  color: #fff;
}
.u-error-hover.data-v-8ef4aa1a {
  background: #dd6161 !important;
  color: #fff;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header {
  padding: 15rpx 30rpx;
  height: 100rpx;
}
/* 头部 轮播图 */
.carousel-section {
  position: relative;
  padding-top: 10rpx;
}
.carousel-section .titleNview-placing {
  height: 25px;
  padding-top: 44px;
  box-sizing: content-box;
}
.carousel-section .titleNview-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 426rpx;
  transition: .4s;
}
.carousel {
  width: 100%;
  height: 350rpx;
}
.carousel .carousel-item {
  width: 100%;
  height: 100%;
  padding: 0 28rpx;
  overflow: hidden;
}
.carousel image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.swiper-dots {
  display: flex;
  position: absolute;
  left: 60rpx;
  bottom: 15rpx;
  width: 72rpx;
  height: 36rpx;
  background-image: url(data:image/png;base64,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);
  background-size: 100% 100%;
}
.swiper-dots .num {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50px;
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  line-height: 36rpx;
}
.swiper-dots .sign {
  position: absolute;
  top: 0;
  left: 50%;
  line-height: 36rpx;
  font-size: 12rpx;
  color: #fff;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
/* 分类 */
.cate-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  padding: 30rpx 22rpx;
  background: #fff;
  /* 原图标颜色太深,不想改图了,所以加了透明度 */
}
.cate-section .cate-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 26rpx;
  color: #303133;
}
.cate-section image {
  width: 88rpx;
  height: 88rpx;
  margin-bottom: 14rpx;
  border-radius: 50%;
  opacity: .7;
  box-shadow: 4rpx 4rpx 20rpx rgba(250, 67, 106, 0.3);
}
.ad-1 {
  width: 100%;
  height: 210rpx;
  padding: 10rpx 0;
  background: #fff;
}
.ad-1 image {
  width: 100%;
  height: 100%;
}
.rich-text {
  padding: 30rpx;
  margin-top: 30rpx;
}
.content {
  padding: 10rpx 30rpx;
}
.flexbox {
  width: 700rpx;
  border-radius: 20rpx;
  background: #fff;
  margin: 15rpx auto;
  padding: 10rpx;
}
.flex1,
.flexbox {
  display: flex;
}
.flex1 {
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 25%;
}
.icon120 {
  height: 80rpx;
  width: 80rpx;
}
.nowrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.txt26 {
  color: #000;
  display: block;
  font-size: 26rpx;
  height: 36rpx;
  line-height: 36rpx;
  margin-top: 6rpx;
  text-align: center;
  width: 130rpx;
}
.bg-box {
  align-items: center;
  display: flex;
  justify-content: center;
  width: 100%;
}
.hot-service {
  background: #fff;
  border-radius: 8rpx;
  box-sizing: border-box;
  overflow: hidden;
  width: 700rpx;
}
.bg-img {
  height: 88rpx;
  position: absolute;
  width: 700rpx;
  z-index: 1;
}
.hot-service-content {
  padding: 6rpx 6rpx 0;
}
.hot-service-title {
  background-position: 50%;
  background-size: cover;
  border-radius: 8rpx 8rpx 0 0;
  display: block;
  height: 88rpx;
  position: relative;
  width: 700rpx;
  z-index: 2;
}
.hot-service-title-h3 {
  color: #2e3f56;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 88rpx;
  margin-left: 30rpx;
}
.swiper-container {
  height: 300rpx;
  width: 700rpx;
  border-radius: 20rpx;
  background: #fff;
  margin: 10rpx auto;
}
.swiper-container-large {
  margin: 20rpx 0;
  height: 350rpx;
}
.swiper-container-small {
  margin: 20rpx 0;
  height: 100rpx;
}
.swiper-container .swiper-item {
  display: flex;
  flex-wrap: wrap;
}
.swiper-container-large .swiper-item {
  display: flex;
  flex-wrap: wrap;
}
.swiper-container-small .swiper-item {
  display: flex;
  flex-wrap: wrap;
}
.swiper-container-row {
  height: 120rpx;
}
.swiper-container-row .swiper-item {
  display: flex;
}
.srv-col {
  box-sizing: border-box;
  flex: 1;
  width: 160rpx;
}
.srv-item {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 130rpx;
  justify-content: center;
  text-align: center;
  width: 25%;
  margin-top: 10rpx;
}
.srv-item-large {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 170rpx;
  justify-content: center;
  text-align: center;
  width: 25%;
}
.srv-item-small {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 50rpx;
  justify-content: center;
  text-align: center;
  width: 20%;
}
.srv-item:nth-child(4n) {
  margin-right: 0rpx;
}
.srv-item-icon {
  height: 80rpx;
  margin-bottom: 20rpx;
  margin-top: 10rpx;
  width: 80rpx;
  border-radius: 80rpx;
}
.srv-item-icon-large {
  height: 120rpx;
  margin-bottom: 12rpx;
  margin-top: 6rpx;
  width: 120rpx;
}
.srv-item-title {
  box-sizing: border-box;
  color: #000;
  display: block;
  font-size: 26rpx;
  height: 36rpx;
  line-height: 36rpx;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  margin-top: 10rpx;
}
.indicator {
  display: flex;
  height: 8rpx;
  margin-bottom: 30rpx;
  margin-top: 12rpx;
  width: 670rpx;
}
.indicator-child {
  background: rgba(56, 136, 255, 0.5);
  border-radius: 4rpx;
  float: left;
  height: 8rpx;
  margin-right: 10rpx;
  transition: all .3s ease;
  width: 8rpx;
}
.active {
  background-color: #3888ff;
  width: 50rpx;
}
.bg {
  width: 100%;
}
.service-main {
  margin: 0 auto;
}
.h2title {
  color: #000;
  display: block;
  font-size: 32rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin-left: 40rpx;
}
.listbox {
  background: #fff;
  border: 1rpx solid #ebebeb;
  border-radius: 8rpx;
}
.titlebox {
  align-items: center;
  color: #3888ff;
  display: flex;
  height: 60rpx;
  justify-content: space-between;
}
.service-list {
  background-color: initial !important;
  border: 1rpx solid transparent !important;
  box-shadow: none !important;
  margin-top: 0 !important;
}
.service-list-title {
  padding-left: 0rpx !important;
}
.viewtitle {
  font-weight: 700;
}
.service-main .service-hot-title {
  align-items: center;
  color: #3888ff;
  display: inline-flex;
  font-size: 30rpx;
  height: 40rpx;
  justify-content: space-between;
  line-height: 40rpx;
  width: 133rpx;
}
.content.service-hot-list {
  border-radius: 8rpx;
}
.service-main .service-hot-title .refresh-icon {
  height: 27rpx;
  width: 30rpx;
}
.service-main .service-hot-list .service-hot-item {
  align-items: center;
  box-shadow: inset 0 -1rpx 0 0 #ebebeb;
  display: flex;
  margin: 0 40rpx;
  padding: 36rpx 0;
  position: relative;
}
.service-main .service-hot-list .service-hot-item .title {
  color: #000;
  font-family: PingFangSC-Regular;
  font-size: 30rpx;
  line-height: 40rpx;
  max-width: 540rpx;
}
.service-main .service-hot-list .service-hot-item .tag {
  background: rgba(66, 147, 244, 0.1);
  border-radius: 4rpx;
  color: #4293f4;
  display: inline-block;
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  font-weight: 700;
  height: 36rpx;
  line-height: 36rpx;
  margin-left: 12rpx;
  padding: 0 12rpx;
}
.service-main .service-hot-list .service-hot-item .arrow {
  height: 24rpx;
  position: absolute;
  right: 0;
  width: 14rpx;
}
.service-main .service-hot-list .service-hot-item:last-child {
  box-shadow: none;
}
.twoNoWrap {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
}
.nowrap,
.twoNoWrap {
  overflow: hidden;
  text-overflow: ellipsis;
}
.nowrap {
  white-space: nowrap;
}
.item {
  border-bottom: 1rpx solid #ebebeb;
  box-sizing: border-box;
  display: flex;
  height: 160rpx;
  margin: 0 auto;
  width: 610rpx;
}
.pop-box .item {
  padding-left: 0rpx;
  width: 670rpx;
}
.item-icon {
  height: 150rpx;
  margin-right: 30rpx;
  vertical-align: middle;
  width: 150rpx;
}
.item-text {
  display: flex;
  flex-direction: column;
}
.item-title {
  color: #000;
  font-size: 34rpx;
  height: 48rpx;
  line-height: 48rpx;
  margin-bottom: 6rpx;
  margin-top: 36rpx;
}
.item-title .nowrap {
  display: inline-block;
  font-weight: 700;
  margin-right: 10rpx;
  max-width: 500rpx;
  vertical-align: middle;
}
.item-desc {
  color: rgba(0, 0, 0, 0.3);
  font-size: 24rpx;
  height: 34rpx;
  line-height: 34rpx;
  margin-bottom: 20rpx;
  width: 456rpx;
}
.item-title .topic-tip {
  background: rgba(69, 154, 255, 0.1);
  border-radius: 4rpx;
  color: #3888ff;
  display: inline-block;
  font-size: 26rpx;
  font-weight: 700;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  width: 50rpx;
}
.nowrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.service-banner__list {
  padding-left: 0;
}
.content__desc {
  color: rgba(0, 0, 0, 0.3) !important;
  font-size: 24rpx !important;
}
.pop-item {
  margin: 0;
}
.pop-item:last-child {
  border-bottom: none;
}
.list-box .item-box {
  padding: 10rpx 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
  border-bottom: 1rpx solid #ebebeb;
}
.list-box .item-box .top-box {
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot {
  width: 35%;
  height: auto;
  min-height: 120rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot .cover {
  width: 100%;
  height: 100%;
  min-height: 150rpx;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box-hot .cover :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.list-box .item-box .top-box .cover-box-hot .button {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-box {
  width: 35%;
  height: auto;
  min-height: 120rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box .cover {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  min-height: 150rpx;
}
.list-box .item-box .top-box .cover-box .button {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-large-box {
  width: 50%;
  height: auto;
  min-height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-large-box .cover {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-large-box .button {
  position: absolute;
  bottom: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: white;
  padding: 15rpx 20rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .info-box {
  flex: 1;
  margin-left: 15rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.list-box .item-box .top-box .info-box .publish-date {
  font-size: 32rpx;
  font-weight: bold;
}
.list-box .item-box .top-box .info-box .lang-box {
  color: #999;
  font-size: 24rpx;
}
.list-box .item-box .top-box .info-box .title {
  font-weight: bold;
  font-size: 26rpx;
  color: #666666;
}
.list-box .item-box .top-box .info-box .end-date {
  font-size: 20rpx;
  color: #999999;
}
.list-box .item-box .top-box .info-box .total {
  font-size: 20rpx;
  color: #39b54a;
}
.list-box .item-box .top-box .info-box .des {
  font-size: 22rpx;
  color: #8f8f94;
}
.list-box .item-box .top-box .info-box .price {
  font-size: 24rpx;
  color: red;
  float: right;
}
.list-box .item-box .top-box .info-box .end {
  font-size: 24rpx;
  color: #0070C0;
  width: 100%;
}
.list-box .item-box .margin-tb-sm {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list-box .item-box .margin-tb-sm .text-sm {
  font-size: 24rpx;
}
.list-box .item-box .margin-tb-sm .title {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.list-box .item-box .margin-tb-sm .uni-row {
  flex-direction: row;
}
.list-box .item-box .margin-tb-sm .align-center {
  align-items: center;
}
.list-box .item-box .margin-tb-sm .margin-left-sm {
  margin-left: 20rpx;
}
.lp-flex {
  display: flex;
  flex-direction: row;
}
.lp-flex-column {
  display: flex;
  flex-direction: column;
}
/*弹出层2 交卷 开始*/
.view-pup2 {
  width: 100%;
  background-color: #FFFFFF;
}
.view-pup2-box {
  height: 28rpx;
  background-color: #FFFFFF;
}
.view-pup2-warn {
  font-size: 26rpx;
  text-align: center;
  padding: 20rpx 40rpx;
}
.view-pup2-warn .view-pup2-warn-title {
  font-size: 40rpx;
  font-weight: 700;
}
.view-pup2-warn .view-pup2-warn-text {
  color: #999;
  padding: 20rpx 0;
  max-width: 600rpx;
}
.view-pup2-button {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  border: 4rpx solid #f5f5f5;
  margin-top: 10rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.view-pup2-button .view-pup2-button-list {
  width: 100%;
  text-align: center;
}
.view-pup2-button .view-pup2-button-list1 {
  border-left: 4rpx solid #f5f5f5;
  color: #079B48;
}
/*弹出层2 交卷  结束*/
/*公告*/
.announce {
  width: 700rpx;
  height: 70rpx;
  border-radius: 20rpx;
  padding: 0 35rpx;
  line-height: 70rpx;
  background: #fff;
  margin: 10rpx auto;
  display: flex;
}
.announce .announce-title {
  width: 100rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 32rpx;
  font-weight: 800;
}
.announce .announce-title .font1 {
  margin-right: 6rpx;
}
.announce .announce-title .font2 {
  color: #5C7DFF;
  font-size: 35rpx;
}
.announce .announce-item {
  position: relative;
  width: 530rpx;
  height: 70rpx;
}
.announce .announce-item .announce-swiper {
  width: 100%;
  height: 70rpx;
}
.announce .announce-item .announce-swiper .announce-swiper-item {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  letter-spacing: 1rpx;
}
.an {
  -webkit-animation: rotation 2s infinite linear;
          animation: rotation 2s infinite linear;
}
.pause {
  -webkit-animation-play-state: paused;
          animation-play-state: paused;
}
.top {
  width: 200rpx;
  margin: auto;
  height: 60rpx;
  border-radius: 20rpx;
  padding: 0 35rpx;
  line-height: 60rpx;
  background: #f1f1f1;
  text-align: center;
  color: blue;
}
.top image {
  width: 30rpx;
  height: 22rpx;
}
.f-header {
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 6rpx 30rpx 8rpx;
}
.f-header image {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.f-header .tit-box {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.f-header .tit {
  font-size: 32rpx;
  color: #font-color-dark;
  line-height: 1.3;
  font-weight: bold;
  margin-left: 10rpx;
}
.f-header .tit2 {
  font-size: 24rpx;
  color: #909399;
}
.f-header .icon-you {
  font-size: 34rpx;
  color: #909399;
}
.search-box {
  height: 70rpx;
  line-height: 70rpx;
  background-color: #fff;
  text-align: left;
  padding-left: 20rpx;
  border-radius: 70rpx;
  color: #666666;
}
/*弹出层2 交卷 开始*/
.view-pup2 {
  width: 100%;
  background-color: #FFFFFF;
}
.view-pup2-box {
  height: 28rpx;
  background-color: #FFFFFF;
}
.view-pup2-warn {
  font-size: 26rpx;
  text-align: center;
  padding: 20rpx 40rpx;
}
.view-pup2-warn .view-pup2-warn-title {
  font-size: 40rpx;
  font-weight: 700;
}
.view-pup2-warn .view-pup2-warn-text {
  color: #999;
  padding: 20rpx 0;
  max-width: 600rpx;
}
.view-pup2-button {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  border: 4rpx solid #f5f5f5;
  margin-top: 10rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.view-pup2-button .view-pup2-button-list {
  width: 100%;
  text-align: center;
}
.view-pup2-button .view-pup2-button-list1 {
  border-left: 4rpx solid #f5f5f5;
  color: #079B48;
}
/*弹出层2 交卷  结束*/

