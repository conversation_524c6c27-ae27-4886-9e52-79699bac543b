<view class="data-v-0c77a034"><swiper class="swiper data-v-0c77a034" style="{{'height:'+(height*col+'rpx')+';'}}" indicator-dots="{{isShowDot&&showDot}}" current="{{curDot}}" circular="{{circular}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{listdivInfo}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="swiper-item data-v-0c77a034"><block wx:for="{{item}}" wx:for-item="child" wx:for-index="code" wx:key="code"><view data-event-opts="{{[['tap',[['navToPage',['$0'],[[['listdivInfo','',index],['','',code]]]]]]]}}" class="smallItem data-v-0c77a034" style="{{'width:'+(width+'%')+';'}}" bindtap="__e"><view class="image data-v-0c77a034"><u-image vue-id="{{'027ea5d0-1-'+index+'-'+code}}" src="{{child.icon}}" width="{{size+'rpx'}}" height="{{size+'rpx'}}" class="data-v-0c77a034" bind:__l="__l"></u-image></view><view class="name data-v-0c77a034">{{child.name}}</view></view></block></swiper-item></block></swiper><u-popup bind:input="__e" vue-id="027ea5d0-2" width="80%" mode="center" border-radius="10" show="{{tip}}" safeAreaInsetBottom="{{false}}" value="{{tip}}" data-event-opts="{{[['^input',[['__set_model',['','tip','$event',[]]]]]]}}" class="data-v-0c77a034" bind:__l="__l" vue-slots="{{['default']}}"><view class="view-pup2 data-v-0c77a034"><view class="view-pup2-box data-v-0c77a034"></view><view class="view-pup2-warn data-v-0c77a034"><view class="view-pup2-warn-title data-v-0c77a034">温馨提示</view><view class="view-pup2-warn-text data-v-0c77a034" style="text-align:left;">{{moduleContent}}</view></view><view class="view-pup2-button data-v-0c77a034"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="view-pup2-button-list view-pup2-button-list1 data-v-0c77a034" bindtap="__e">确定</view></view></view></u-popup></view>