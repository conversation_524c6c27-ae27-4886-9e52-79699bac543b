<view class="{{['gui-player','data-v-bf9f8ef8',(mini)?'mini':'',(nobroud)?'nobroud':'']}}"><view class="gui-player-console data-v-bf9f8ef8"><view class="gui-player-console-c data-v-bf9f8ef8"><block wx:if="{{playStatus==1}}"><text data-event-opts="{{[['tap',[['pause',['$event']]]]]}}" class="gui-player-tool gui-icons data-v-bf9f8ef8" style="font-size:33rpx;" bindtap="__e"></text></block><block wx:if="{{playStatus==2}}"><text data-event-opts="{{[['tap',[['play',['$event']]]]]}}" class="gui-player-tool gui-icons data-v-bf9f8ef8" style="font-size:33rpx;margin-left:4rpx;" bindtap="__e"></text></block></view><block wx:if="{{!mini}}"><view class="progress-bar data-v-bf9f8ef8"><gui-single-slider vue-id="65d75902-1" barWidth="{{100}}" barText="{{playTime}}" barColor="#999" barBgColor="linear-gradient(to right, #eeeeee,#eeeeee)" bglineColor="#eeeeee" bglineAColor="#28b28b" data-ref="graceSingleSlider" data-event-opts="{{[['^change',[['progressChange']]]]}}" bind:change="__e" class="data-v-bf9f8ef8 vue-ref" bind:__l="__l"></gui-single-slider></view></block></view></view>