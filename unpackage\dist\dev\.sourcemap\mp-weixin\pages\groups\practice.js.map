{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/groups/practice.vue?9582", "webpack:///D:/日语云课/pages/groups/practice.vue?30c0", "webpack:///D:/日语云课/pages/groups/practice.vue?8ada", "webpack:///D:/日语云课/pages/groups/practice.vue?3660", "uni-app:///pages/groups/practice.vue", "webpack:///D:/日语云课/pages/groups/practice.vue?b75f", "webpack:///D:/日语云课/pages/groups/practice.vue?387f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "groupId", "listeningStats", "completed", "total", "quizStats", "recentPractice", "id", "title", "type", "practiceTime", "score", "suggestions", "content", "computed", "currentSuggestion", "onLoad", "methods", "loadPracticeData", "setTimeout", "goToListening", "uni", "url", "goToQuiz", "continuePractice"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAslB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4F1mB;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAF;QACAC;MACA;MACAE,iBACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC,cACA;QACAJ;QACAK;MACA,GACA;QACAL;QACAK;MACA,GACA;QACAL;QACAK;MACA;IAEA;EACA;EACAC;IACAC;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACA;QACAH;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjNA;AAAA;AAAA;AAAA;AAA23B,CAAgB,+3BAAG,EAAC,C;;;;;;;;;;;ACA/4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/practice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/practice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./practice.vue?vue&type=template&id=a14cf6a6&scoped=true&\"\nvar renderjs\nimport script from \"./practice.vue?vue&type=script&lang=js&\"\nexport * from \"./practice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./practice.vue?vue&type=style&index=0&id=a14cf6a6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a14cf6a6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/practice.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=template&id=a14cf6a6&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = Math.round(\n    (_vm.listeningStats.completed / _vm.listeningStats.total) * 100\n  )\n  var g1 = Math.round((_vm.quizStats.completed / _vm.quizStats.total) * 100)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\n\t\t\t<!-- 练习类型选择 -->\n\t\t\t<view class=\"practice-types\">\n\t\t\t\t<view class=\"type-card\" @click=\"goToListening\">\n\t\t\t\t\t<view class=\"type-icon listening\">\n\t\t\t\t\t\t<text class=\"iconfont icon-headphone\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"type-info\">\n\t\t\t\t\t\t<view class=\"type-title\">听力练习</view>\n\t\t\t\t\t\t<view class=\"type-desc\">提升日语听力理解能力</view>\n\t\t\t\t\t\t<view class=\"type-stats\">\n\t\t\t\t\t\t\t<text>已完成 {{listeningStats.completed}} / {{listeningStats.total}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"type-progress\">\n\t\t\t\t\t\t<view class=\"progress-circle\">\n\t\t\t\t\t\t\t<text>{{Math.round(listeningStats.completed / listeningStats.total * 100)}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"type-card\" @click=\"goToQuiz\">\n\t\t\t\t\t<view class=\"type-icon quiz\">\n\t\t\t\t\t\t<text class=\"iconfont icon-edit\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"type-info\">\n\t\t\t\t\t\t<view class=\"type-title\">答题练习</view>\n\t\t\t\t\t\t<view class=\"type-desc\">巩固语法词汇知识点</view>\n\t\t\t\t\t\t<view class=\"type-stats\">\n\t\t\t\t\t\t\t<text>已完成 {{quizStats.completed}} / {{quizStats.total}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"type-progress\">\n\t\t\t\t\t\t<view class=\"progress-circle\">\n\t\t\t\t\t\t\t<text>{{Math.round(quizStats.completed / quizStats.total * 100)}}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 最近练习记录 -->\n\t\t\t<view class=\"recent-practice\">\n\t\t\t\t<view class=\"section-title\">最近练习</view>\n\t\t\t\t<view class=\"practice-list\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"practice-item\" \n\t\t\t\t\t\tv-for=\"(item, index) in recentPractice\" \n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"continuePractice(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"practice-icon\" :class=\"item.type\">\n\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"item.type === 'listening' ? 'icon-headphone' : 'icon-edit'\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"practice-info\">\n\t\t\t\t\t\t\t<view class=\"practice-title\">{{item.title}}</view>\n\t\t\t\t\t\t\t<view class=\"practice-meta\">\n\t\t\t\t\t\t\t\t<text class=\"meta-type\">{{item.type === 'listening' ? '听力练习' : '答题练习'}}</text>\n\t\t\t\t\t\t\t\t<text class=\"meta-time\">{{item.practiceTime}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"practice-score\" v-if=\"item.score !== null\">\n\t\t\t\t\t\t\t<text :class=\"{ 'high-score': item.score >= 80, 'low-score': item.score < 60 }\">\n\t\t\t\t\t\t\t\t{{item.score}}分\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"practice-status\" v-else>\n\t\t\t\t\t\t\t<text class=\"status-text\">未完成</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 学习建议 -->\n\t\t\t<view class=\"study-suggestion\">\n\t\t\t\t<view class=\"section-title\">学习建议</view>\n\t\t\t\t<view class=\"suggestion-card\">\n\t\t\t\t\t<view class=\"suggestion-icon\">\n\t\t\t\t\t\t<text class=\"iconfont icon-bulb\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"suggestion-content\">\n\t\t\t\t\t\t<view class=\"suggestion-title\">{{currentSuggestion.title}}</view>\n\t\t\t\t\t\t<view class=\"suggestion-desc\">{{currentSuggestion.content}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\tgroupId: '',\n\t\t\tlisteningStats: {\n\t\t\t\tcompleted: 8,\n\t\t\t\ttotal: 12\n\t\t\t},\n\t\t\tquizStats: {\n\t\t\t\tcompleted: 15,\n\t\t\t\ttotal: 20\n\t\t\t},\n\t\t\trecentPractice: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '第3课听力：日常对话',\n\t\t\t\t\ttype: 'listening',\n\t\t\t\t\tpracticeTime: '2024-01-15 16:30',\n\t\t\t\t\tscore: 85\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '语法练习：助词の用法',\n\t\t\t\t\ttype: 'quiz',\n\t\t\t\t\tpracticeTime: '2024-01-15 14:20',\n\t\t\t\t\tscore: 92\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '第4课听力：购物场景',\n\t\t\t\t\ttype: 'listening',\n\t\t\t\t\tpracticeTime: '2024-01-14 19:15',\n\t\t\t\t\tscore: null\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\ttitle: '词汇练习：家族称呼',\n\t\t\t\t\ttype: 'quiz',\n\t\t\t\t\tpracticeTime: '2024-01-14 15:45',\n\t\t\t\t\tscore: 78\n\t\t\t\t}\n\t\t\t],\n\t\t\tsuggestions: [\n\t\t\t\t{\n\t\t\t\t\ttitle: '听力练习建议',\n\t\t\t\t\tcontent: '建议每天坚持听力练习20分钟，可以提高语感和理解能力。'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\ttitle: '语法巩固建议',\n\t\t\t\t\tcontent: '多做语法练习题，重点关注助词和动词变位的用法。'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\ttitle: '词汇记忆建议',\n\t\t\t\t\tcontent: '使用间隔重复的方法记忆单词，效果更佳。'\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\tcurrentSuggestion() {\n\t\t\t// 根据用户的练习情况给出建议\n\t\t\tconst listeningProgress = this.listeningStats.completed / this.listeningStats.total;\n\t\t\tconst quizProgress = this.quizStats.completed / this.quizStats.total;\n\t\t\t\n\t\t\tif (listeningProgress < quizProgress) {\n\t\t\t\treturn this.suggestions[0]; // 听力练习建议\n\t\t\t} else if (quizProgress < listeningProgress) {\n\t\t\t\treturn this.suggestions[1]; // 语法巩固建议\n\t\t\t} else {\n\t\t\t\treturn this.suggestions[2]; // 词汇记忆建议\n\t\t\t}\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.groupId) {\n\t\t\tthis.groupId = options.groupId;\n\t\t\tthis.loadPracticeData();\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载练习数据\n\t\tloadPracticeData() {\n\t\t\t// 这里应该调用API获取练习数据\n\t\t\tthis.pageLoading = true;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.pageLoading = false;\n\t\t\t}, 1000);\n\t\t},\n\n\t\t// 跳转到听力练习\n\t\tgoToListening() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/listening-practice?groupId=${this.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到答题练习\n\t\tgoToQuiz() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/quiz-practice?groupId=${this.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 继续练习\n\t\tcontinuePractice(item) {\n\t\t\tif (item.type === 'listening') {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/listening-practice?groupId=${this.groupId}&practiceId=${item.id}`\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/quiz-practice?groupId=${this.groupId}&practiceId=${item.id}`\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.practice-types {\n\tpadding: 30rpx;\n}\n\n.type-card {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.type-icon {\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.type-icon.listening {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.type-icon.quiz {\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.type-icon .iconfont {\n\tcolor: #fff;\n\tfont-size: 48rpx;\n}\n\n.type-info {\n\tflex: 1;\n}\n\n.type-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.type-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 12rpx;\n}\n\n.type-stats {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.type-progress {\n\tmargin-left: 20rpx;\n}\n\n.progress-circle {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tbackground: #f0f0f0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tfont-weight: bold;\n}\n\n.recent-practice, .study-suggestion {\n\tbackground: #fff;\n\tmargin: 0 30rpx 20rpx;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n}\n\n.section-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.practice-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.practice-item:last-child {\n\tborder-bottom: none;\n}\n\n.practice-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.practice-icon.listening {\n\tbackground: rgba(102, 126, 234, 0.1);\n}\n\n.practice-icon.quiz {\n\tbackground: rgba(240, 147, 251, 0.1);\n}\n\n.practice-icon .iconfont {\n\tfont-size: 28rpx;\n}\n\n.practice-icon.listening .iconfont {\n\tcolor: #667eea;\n}\n\n.practice-icon.quiz .iconfont {\n\tcolor: #f093fb;\n}\n\n.practice-info {\n\tflex: 1;\n}\n\n.practice-title {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.practice-meta {\n\tdisplay: flex;\n\tgap: 15rpx;\n}\n\n.meta-type, .meta-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.practice-score, .practice-status {\n\tmargin-left: 20rpx;\n}\n\n.practice-score text {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n}\n\n.high-score {\n\tcolor: #52c41a;\n}\n\n.low-score {\n\tcolor: #ff4d4f;\n}\n\n.status-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.suggestion-card {\n\tdisplay: flex;\n\talign-items: flex-start;\n}\n\n.suggestion-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: #fff7e6;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.suggestion-icon .iconfont {\n\tcolor: #fa8c16;\n\tfont-size: 28rpx;\n}\n\n.suggestion-content {\n\tflex: 1;\n}\n\n.suggestion-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.suggestion-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=style&index=0&id=a14cf6a6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=style&index=0&id=a14cf6a6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753663858344\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}