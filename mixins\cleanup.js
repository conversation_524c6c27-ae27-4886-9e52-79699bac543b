/**
 * 内存清理和资源管理 Mixin
 * 用于防止内存泄漏和自动清理资源
 */

export default {
  data() {
    return {
      // 定时器集合
      _timers: [],
      // 事件监听器集合
      _listeners: [],
      // 网络请求集合
      _requests: [],
      // 观察者集合
      _observers: [],
      // 其他需要清理的资源
      _resources: []
    };
  },

  methods: {
    /**
     * 安全的setTimeout
     * @param {Function} fn 回调函数
     * @param {Number} delay 延迟时间
     * @returns {Number} 定时器ID
     */
    $setTimeout(fn, delay) {
      // 确保 _timers 数组存在
      if (!this._timers) {
        this._timers = [];
      }

      const timer = setTimeout(() => {
        // 执行完成后自动从集合中移除
        this._removeTimer(timer);
        fn();
      }, delay);

      this._timers.push(timer);
      return timer;
    },

    /**
     * 安全的setInterval
     * @param {Function} fn 回调函数
     * @param {Number} delay 间隔时间
     * @returns {Number} 定时器ID
     */
    $setInterval(fn, delay) {
      // 确保 _timers 数组存在
      if (!this._timers) {
        this._timers = [];
      }
      const timer = setInterval(fn, delay);
      this._timers.push(timer);
      return timer;
    },

    /**
     * 清除定时器
     * @param {Number} timer 定时器ID
     */
    $clearTimer(timer) {
      clearTimeout(timer);
      clearInterval(timer);
      this._removeTimer(timer);
    },

    /**
     * 从定时器集合中移除
     * @param {Number} timer 定时器ID
     */
    _removeTimer(timer) {
      // 确保 _timers 数组存在
      if (!this._timers) {
        this._timers = [];
        return;
      }

      const index = this._timers.indexOf(timer);
      if (index > -1) {
        this._timers.splice(index, 1);
      }
    },

    /**
     * 安全的事件监听
     * @param {Object} target 目标对象
     * @param {String} event 事件名称
     * @param {Function} handler 事件处理函数
     * @param {Object} options 选项
     */
    $addEventListener(target, event, handler, options = {}) {
      target.addEventListener(event, handler, options);

      // 确保 _listeners 数组存在
      if (!this._listeners) {
        this._listeners = [];
      }

      this._listeners.push({
        target,
        event,
        handler,
        options
      });
    },

    /**
     * 移除事件监听
     * @param {Object} target 目标对象
     * @param {String} event 事件名称
     * @param {Function} handler 事件处理函数
     */
    $removeEventListener(target, event, handler) {
      target.removeEventListener(event, handler);
      
      const index = this._listeners.findIndex(listener => 
        listener.target === target && 
        listener.event === event && 
        listener.handler === handler
      );
      
      if (index > -1) {
        this._listeners.splice(index, 1);
      }
    },

    /**
     * 安全的网络请求
     * @param {Object} options 请求选项
     * @returns {Promise} 请求Promise
     */
    $request(options) {
      const requestTask = uni.request({
        ...options,
        success: (res) => {
          this._removeRequest(requestTask);
          options.success && options.success(res);
        },
        fail: (err) => {
          this._removeRequest(requestTask);
          options.fail && options.fail(err);
        },
        complete: (res) => {
          this._removeRequest(requestTask);
          options.complete && options.complete(res);
        }
      });

      // 确保 _requests 数组存在
      if (!this._requests) {
        this._requests = [];
      }

      this._requests.push(requestTask);
      return requestTask;
    },

    /**
     * 从请求集合中移除
     * @param {Object} requestTask 请求任务
     */
    _removeRequest(requestTask) {
      const index = this._requests.indexOf(requestTask);
      if (index > -1) {
        this._requests.splice(index, 1);
      }
    },

    /**
     * 取消所有网络请求
     */
    $cancelAllRequests() {
      this._requests.forEach(request => {
        try {
          request.abort && request.abort();
        } catch (e) {
          console.warn('取消请求失败:', e);
        }
      });
      this._requests = [];
    },

    /**
     * 添加观察者
     * @param {Object} observer 观察者对象
     */
    $addObserver(observer) {
      // 确保 _observers 数组存在
      if (!this._observers) {
        this._observers = [];
      }

      this._observers.push(observer);
    },

    /**
     * 移除观察者
     * @param {Object} observer 观察者对象
     */
    $removeObserver(observer) {
      const index = this._observers.indexOf(observer);
      if (index > -1) {
        this._observers.splice(index, 1);
      }
    },

    /**
     * 添加需要清理的资源
     * @param {Object} resource 资源对象
     * @param {Function} cleanupFn 清理函数
     */
    $addResource(resource, cleanupFn) {
      // 确保 _resources 数组存在
      if (!this._resources) {
        this._resources = [];
      }

      this._resources.push({
        resource,
        cleanup: cleanupFn
      });
    },

    /**
     * 移除资源
     * @param {Object} resource 资源对象
     */
    $removeResource(resource) {
      const index = this._resources.findIndex(item => item.resource === resource);
      if (index > -1) {
        const item = this._resources[index];
        try {
          item.cleanup && item.cleanup();
        } catch (e) {
          console.warn('清理资源失败:', e);
        }
        this._resources.splice(index, 1);
      }
    },

    /**
     * 手动清理所有资源
     */
    $cleanup() {
      this._cleanupTimers();
      this._cleanupListeners();
      this._cleanupRequests();
      this._cleanupObservers();
      this._cleanupResources();
    },

    /**
     * 清理定时器
     */
    _cleanupTimers() {
      // 确保 _timers 数组存在
      if (!this._timers) {
        this._timers = [];
        return;
      }

      this._timers.forEach(timer => {
        try {
          clearTimeout(timer);
          clearInterval(timer);
        } catch (e) {
          console.warn('清理定时器失败:', e);
        }
      });
      this._timers = [];
    },

    /**
     * 清理事件监听器
     */
    _cleanupListeners() {
      // 确保 _listeners 数组存在
      if (!this._listeners) {
        this._listeners = [];
        return;
      }

      this._listeners.forEach(({ target, event, handler }) => {
        try {
          target.removeEventListener(event, handler);
        } catch (e) {
          console.warn('清理事件监听器失败:', e);
        }
      });
      this._listeners = [];
    },

    /**
     * 清理网络请求
     */
    _cleanupRequests() {
      // 确保 _requests 数组存在
      if (!this._requests) {
        this._requests = [];
        return;
      }

      this._requests.forEach(request => {
        try {
          request.abort && request.abort();
        } catch (e) {
          console.warn('清理网络请求失败:', e);
        }
      });
      this._requests = [];
    },

    /**
     * 清理观察者
     */
    _cleanupObservers() {
      // 确保 _observers 数组存在
      if (!this._observers) {
        this._observers = [];
        return;
      }

      this._observers.forEach(observer => {
        try {
          // 如果观察者有disconnect方法（如IntersectionObserver）
          if (observer.disconnect) {
            observer.disconnect();
          }
          // 如果观察者有unobserve方法
          if (observer.unobserve) {
            observer.unobserve();
          }
          // 如果观察者有destroy方法
          if (observer.destroy) {
            observer.destroy();
          }
        } catch (e) {
          console.warn('清理观察者失败:', e);
        }
      });
      this._observers = [];
    },

    /**
     * 清理其他资源
     */
    _cleanupResources() {
      // 确保 _resources 数组存在
      if (!this._resources) {
        this._resources = [];
        return;
      }

      this._resources.forEach(({ cleanup }) => {
        try {
          cleanup && cleanup();
        } catch (e) {
          console.warn('清理资源失败:', e);
        }
      });
      this._resources = [];
    }
  },

  // 页面隐藏时清理部分资源
  onHide() {
    // 清理定时器（可选）
    // this._cleanupTimers();
    
    // 取消网络请求
    this.$cancelAllRequests();
  },

  // 页面卸载时清理所有资源
  beforeDestroy() {
    try {
      this.$cleanup();
    } catch (e) {
      console.error('清理资源时发生错误:', e);
    }
  },

  // uni-app页面卸载
  onUnload() {
    this.$cleanup();
  },

  // 组件销毁时清理
  destroyed() {
    this.$cleanup();
  }
};
