<template>
	<gui-page :fullPage="true"  class="root-box lp-flex-column">
		<view slot="gBody" >
			<!-- 基本信息 -->
			<view class="panel base-box lp-flex-column">
				<view class="cover-box">
					<image  show-menu-by-longpress='1' class="cover" :src="project.picture" mode="scaleToFill"></image>
				</view>
				<view class="info-box lp-flex-column">
					<view class="title-box lp-flex lp-flex-space-between">
						<text class="title">{{project.name}}</text>
					</view>
					<view class="lp-flex lp-flex-space-between">
						<view>
							<text class="label">课程时间：</text>
							<text class="course_date">{{project.class_time}} 至 {{project.end_time}} </text>
						</view>
						<!-- <view>
							<text class="label">状态：</text>
							<text>{{project.status_text}}</text>
						</view> -->
					</view>
					
				</view>
			</view>
			
			<!-- 主讲老师 -->
			<!-- <view class="panel lp-flex-column teacher-box">
				<view class="head">主讲老师</view>
				<view class="body lp-flex-column">
					<view v-for="(item,index) in project.author" :key="item.id" class="item lp-flex">
						<view class="avatar-box">
							<image class="avatar" :src="item.avatar"></image>
						</view>
						<view class="lp-flex-column info-box">
							<text class="name">{{item.name}} </text>
							<text class="job">{{item.job}} </text>
							<text class="intro">{{item.intro}}</text>
						</view>
					</view>
				</view>
			</view> -->

			<!-- 课程内容 -->
			<view class="panel content-box" >
				<view class="head">课程详情</view>
				<view class="body" style="text-align: center;">
					<image  v-if="project.class_picture != null" show-menu-by-longpress='1' :src="project.class_picture"  mode="widthFix"></image>
					<!-- <rich-text class="rich-text" :nodes="project.content | formatRichText"></rich-text>
					 -->
					 <u-parse :html="project.content" ></u-parse>
				</view>
			</view>

			<!-- 报名 -->
			<!-- <view class="btn-box lp-flex lp-flex-space-between">
				<view class="info">￥{{project.price}}</view>
				<view class="btn lp-flex-center" v-if="project.is_over === false && project.sign===0" @click="toggleSpec">我要报名</view>
				<view class="btn lp-flex-center" style="background-color: #e5e5e5;" v-else-if="project.is_over === false && project.sign===1">已报名</view>
				<view class="btn lp-flex-center" style="background-color: #e5e5e5;" v-else-if="project.is_over === true">已结束</view>
			</view> -->
			
			<!-- 底部操作菜单 -->
			<view class="page-bottom">
				<view class="p-b-btn">
					<!-- <text class="yticon icon-dianhua-copy"></text> -->
					<image src="/static/kf.png" style="width: 50rpx;height: 50rpx;"></image>
					<text>客服</text>
					<button class='contact-btn' open-type='contact'>a</button> 
				</view>
				<view   class="p-b-btn" style="margin-left: 10rpx;">
					<!-- <text class="yticon icon-share"></text> -->
					<text style="font-size: 35rpx;color:#FD6D24">￥{{project.price}}</text>
				</view>
				
				<view class="buy" v-if="project.is_over === false && project.sign===0 && is_show=== true" @click="toggleSpec">
				     我要报名
				</view>
				<view class="buy" style="background-color: #e5e5e5;" v-else-if="project.is_over === false && project.sign===1">
				     已报名
				</view>
				<view class="buy" style="background-color: #e5e5e5;" v-else-if="project.is_over === true">
				     已结束
				</view>
			</view>
			<!-- 规格-模态层弹窗 -->
			<view 
				class="popup spec" 
				:class="specClass"
				@touchmove.stop.prevent="stopPrevent"
				@click="toggleSpec"
			>
				<!-- 遮罩层 -->
				<view class="mask"></view>
				<view class="layer attr-content" @click.stop="stopPrevent">
					<view class="a-t">
						<image :src="project.picture"></image>
						<view class="right">
							<text class="title">{{project.name}}</text>
							<text class="price">¥{{project.price}}</text>
							<!-- <text class="stock">库存：188件</text>
							<view class="selected">
								已选：
								<text class="selected-text" v-for="(sItem, sIndex) in specSelected" :key="sIndex">
									{{sItem.name}}
								</text>
							</view> -->
						</view>
					</view>
					<!-- <view v-for="(item,index) in specList" :key="index" class="attr-list">
						<text>{{item.name}}</text>
						<view class="item-list">
							<text 
								v-for="(childItem, childIndex) in specChildList" 
								v-if="childItem.pid === item.id"
								:key="childIndex" class="tit"
								:class="{selected: childItem.selected}"
								@click="selectSpec(childIndex, childItem.pid)"
							>
								{{childItem.name}}
							</text>
						</view>
					</view> -->
					<scroll-view style="height: 800rpx;margin: 30rpx 0;" scroll-y="true">
					<form @submit="onFormSubmitHandler" @reset="onFormResetHandler">
						<view class="form lp-flex-column">
							<template v-for="(item,index) in form">
								<!-- 输入性文本 -->
								<view v-if="item.value_type=='text'" class="form-item lp-flex" :key="item.id">
									<view v-if="item.is_show=='1'" style="color: red;">*</view>
									<view v-else style="color: white;">*</view>
									<view class="control-label">{{item.title}}</view>
									<view class="input-box">
										<input :name="item.field_name" v-model="form_data[item.field_name]" />
									</view>
								</view>
								<!-- 单选 -->
								<view v-if="item.value_type=='radio'" class="form-item lp-flex">
									<view v-if="item.is_show=='1'" style="color: red;">*</view>
									<view v-else style="color: white;">*</view>
									<view class="control-label">{{item.title}}</view>
									<view class="input-box">
										<radio-group class="lp-flex-column" :name="item.field_name">
											<label v-for="(option_value,index) in item.option_values.split('|')" :key="option_value">
												<radio v-if="form_data[item.field_name]!=undefined" :value="index" :checked="form_data[item.field_name].includes(index)">
													{{option_value}}
												</radio>
												<radio v-else :value="index" :checked="false">{{option_value}}</radio>
											</label>
										</radio-group>
									</view>
								</view>
								
								<!-- 多选 -->
								<view v-if="item.value_type=='checkbox'" class="form-item lp-flex">
									<view v-if="item.is_show=='1'" style="color: red;">*</view>
									<view class="control-label">{{item.title}}</view>
									<view class="input-box">
										<checkbox-group class="lp-flex-column" :name="item.field_name">
											<label v-for="option_value in item.option_values.split('|')" :key="option_value">
												<checkbox v-if="form_data[item.field_name]!=undefined" :value="option_value" :checked="form_data[item.field_name].includes(option_value)">
													{{option_value}}
												</checkbox>
												<checkbox v-else :value="option_value" :checked="false">{{option_value}}</checkbox>
											</label>
										</checkbox-group>
									</view>
								</view>
							</template>
							<!-- <view class="btn-box"> -->
								<button form-type="submit" class="btn1">提交</button>
							<!-- </view> -->
						</view>
					</form>
					</scroll-view>
					<!-- <button class="btn" @click="toggleSpec">完成</button> -->
				</view>
			</view>
			<!-- <button class="kf_button" open-type='contact'>
			 <image class="kf_image" src="/static/kefu.png"></image>
			</button> -->
		</view>
	</gui-page>
</template>

<script>
	import uParse from "@/components/uview-ui/components/u-parse/u-parse.vue"; 
	export default {
		components: {
		    uParse
		},
		data() {
			return {
				project_id: 1,
				project: null,
				specClass: 'none',
				specSelected:[],
				
				favorite: true,
				shareList: [],
				imgList: [
					{
						src: 'https://gd3.alicdn.com/imgextra/i3/0/O1CN01IiyFQI1UGShoFKt1O_!!0-item_pic.jpg_400x400.jpg'
					},
					{
						src: 'https://gd3.alicdn.com/imgextra/i3/TB1RPFPPFXXXXcNXpXXXXXXXXXX_!!0-item_pic.jpg_400x400.jpg'
					},
					{
						src: 'https://gd2.alicdn.com/imgextra/i2/38832490/O1CN01IYq7gu1UGShvbEFnd_!!38832490.jpg_400x400.jpg'
					}
				],
				desc: `
					<div style="width:100%">
						<img style="width:100%;display:block;" src="https://gd3.alicdn.com/imgextra/i4/479184430/O1CN01nCpuLc1iaz4bcSN17_!!479184430.jpg_400x400.jpg" />
						<img style="width:100%;display:block;" src="https://gd2.alicdn.com/imgextra/i2/479184430/O1CN01gwbN931iaz4TzqzmG_!!479184430.jpg_400x400.jpg" />
						<img style="width:100%;display:block;" src="https://gd3.alicdn.com/imgextra/i3/479184430/O1CN018wVjQh1iaz4aupv1A_!!479184430.jpg_400x400.jpg" />
						<img style="width:100%;display:block;" src="https://gd4.alicdn.com/imgextra/i4/479184430/O1CN01tWg4Us1iaz4auqelt_!!479184430.jpg_400x400.jpg" />
						<img style="width:100%;display:block;" src="https://gd1.alicdn.com/imgextra/i1/479184430/O1CN01Tnm1rU1iaz4aVKcwP_!!479184430.jpg_400x400.jpg" />
					</div>
				`,
				specList: [
					{
						id: 1,
						name: '尺寸',
					},
					{	
						id: 2,
						name: '颜色',
					},
				],
				specChildList: [
					{
						id: 1,
						pid: 1,
						name: 'XS',
					},
					{
						id: 2,
						pid: 1,
						name: 'S',
					},
					{
						id: 3,
						pid: 1,
						name: 'M',
					},
					{
						id: 4,
						pid: 1,
						name: 'L',
					},
					{
						id: 5,
						pid: 1,
						name: 'XL',
					},
					{
						id: 6,
						pid: 1,
						name: 'XXL',
					},
					{
						id: 7,
						pid: 2,
						name: '白色',
					},
					{
						id: 8,
						pid: 2,
						name: '珊瑚粉',
					},
					{
						id: 9,
						pid: 2,
						name: '草木绿',
					},
				],
				// 表单数据
				form: [],
				// 已填写数据
				form_data: {
				
				},
				is_show:true
			};
		},
		onLoad(options) {
			this.project_id = options.id;
			let platform = this.$store.state.systemInfo.platform
			if(platform=='IOS'){
				this.is_show = false;
			}
			this.ready();
			wx.showShareMenu({
					withShareTicket:true,
					menus:["shareAppMessage","shareTimeline"]
				})
		},
		onShareAppMessage() {
					return {
						title: this.project.name,//标题
						imageUrl: '',//封面
						path: `/pages/product/product?id=${this.project_id}`//此处链接为要分享的页面链接	
					};
			},
			// 分享到朋友圈
			onShareTimeline() {
				return {
					title: this.project.name,//标题
					imageUrl: this.project.picture,//封面
					path: `/pages/product/product?id=${this.project_id}`//此处链接为要分享的页面链接	
				};
			},
		filters: {},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			ready: function() {
				this.apiGetDetail(this.project_id).then(data => {
					console.log(data)
					this.project = data;
					this.form = data.pt_config.items;
					this.form_data = data.form ? data.form.json_data : {};
				})
			},
			
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onApplyHandler: function() {
				uni.navigateTo({
					url: '/pages/product/order/order?project_id=' + this.project_id
				})
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 获取项目详情
			 * @param {Object} id
			 */
			apiGetDetail: function(id) {
				return this.$http.get('/v1/project_detail', {
					params: {
						id
					}
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
			//规格弹窗开关
			toggleSpec() {
				if(this.specClass === 'show'){
					this.specClass = 'hide';
					setTimeout(() => {
						this.specClass = 'none';
					}, 250);
				}else if(this.specClass === 'none'){
					this.specClass = 'show';
				}
			},
			//-----------------------------------------------------------------------------------------------
			//
			// hander
			//
			//-----------------------------------------------------------------------------------------------
			onFormSubmitHandler: function(e) {
				let self = this;
				if (!this.$store.state.user.token) {
					uni.showModal({
						title: '请先登录',
						content: '登录后才能进行操作',
						success: function(res) {
							if (res.confirm) {
								self.reload = true;
								uni.navigateTo({
									url: "/pages/login/login?type=wx"
								})
							} else if (res.cancel) {
								uni.navigateBack();
							}
						}
					});
				} else {
					var formdata = JSON.stringify(e.detail.value);
					for (var i in e.detail.value) {
						let v = e.detail.value[i];
						let obj = this.form.find(o => o.field_name === i);
						console.log(obj);
						if(obj['is_show']==1){
							if ((v instanceof Array && v.length == 0) || (!v)) {
								uni.showToast({
									icon:'none',
									title: '请填写完整的报名信息！'
								});
								return;
							}
						}
						
					}
					console.log('formdata', formdata, e.detail.value);
					// uni.redirectTo({
					// 	url: './pay_result?sn_order='
					// })
					uni.showLoading();
					this.apiPay(this.project_id, this.project.price, formdata).then(data => {
						console.log(data);
						// 仅作为示例，非真实参数信息。
						uni.requestPayment({
							provider: 'wxpay',
							...data.pay,
							success: function(res) {
								uni.hideLoading();
								uni.redirectTo({
									url: './pay_result?sn_order=' + data.sn_order
								})
							},
							fail: function(err) {
								uni.hideLoading();
								uni.showToast({
									icon: 'none',
									title: '支付失败',
								})
								console.log('fail:' + JSON.stringify(err));
							}
						});
					});
				}
				
			},
			onFormResetHandler: function() {
			
			},
			
			/**
			 * 创建订单
			 * @param {Object} project_id
			 * @param {Object} money
			 * @param {Object} json_data
			 */
			apiPay: function(project_id, money, json_data) {
				return this.$http.post('/v1/sign_up', {
					project_id,
					money,
					json_data
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
			goService(){
				uni.redirectTo({
					url: '/pages/service/service'
				})
			},
			goShare: function() {
				uni.navigateTo({
					url: '/pages/product/sign-in-share/sign-in-share?project_id=' + this.project_id
				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	.panel {
		background: #fff;
		margin-bottom: 30rpx;

		.head {
			font-size: 34rpx;
			margin-bottom: 30rpx;
			color: #333;
		}
	}

	.root-box {
		color: #666;
		background: #f2f2f2;
		padding-bottom: 120rpx;

		/* 基本信息 */
		.base-box {
			.cover-box {
				.cover {
					width: 100%;
					height: 400rpx;
				}
			}

			.info-box {
				padding: 30rpx;
				font-size: 28rpx;
				color: #666;

				view {
					margin-bottom: 5rpx;
				}

				.title {
					font-size: 36rpx;
					font-weight: bold;
				}

				.label {
					color: #999;
				}

				.course_date {
					font-size: 24rpx;
				}
			}
		}

		/* 讲师 */
		.teacher-box {
			padding: 30rpx;
			font-size: 28rpx;
			color: #666;

			.item {
				padding: 30rpx 0;
				border-bottom: solid 1px #eee;
			}

			.item:last-child {
				border-bottom: unset;
			}

			.info-box {
				.name {
					font-size: 32rpx;
					color: #333;
					margin-bottom: 20rpx;
				}
			}

			.avatar-box {
				margin-right: 30rpx;

				.avatar {
					width: 128rpx;
					height: 128rpx;
					border-radius: 50%;
				}
			}
		}

		/* 课程详情 */
		.content-box {
			padding: 30rpx;
		}

		/* 立即报名 */
		.btn-box {
			position: fixed;
			bottom: 0;
			background: #fff;
			flex: 1;
			left: 0;
			right: 0;
			height: 100rpx;
			border-top: solid 1px #eee;
			align-items: stretch;
			padding-left: 30rpx;
			line-height: 100rpx;

			.info {
				flex: 1;
				text-align: center;
				color: #ff0000;
			}

			.btn {
				background-color: $uni-color-error;
				color: #fff;
				padding: 0 80rpx;
			}

		}
	}
	/* 规格选择弹窗 */
	.attr-content{
		padding: 10upx 30upx;
		.a-t{
			display: flex;
			image{
				width: 250upx;
				height: 130upx;
				flex-shrink: 0;
				//margin-top: -40upx;
				border-radius: 8upx;;
			}
			.right{
				display: flex;
				flex-direction: column;
				padding-left: 24upx;
				font-size: $font-sm + 2upx;
				color: $font-color-base;
				line-height: 42upx;
				.price{
					font-size: $font-lg;
					color: #FD6D24;
					margin-bottom: 10upx;
				}
				.selected-text{
					margin-right: 10upx;
				}
			}
		}
		.attr-list{
			display: flex;
			flex-direction: column;
			font-size: $font-base + 2upx;
			color: $font-color-base;
			padding-top: 30upx;
			padding-left: 10upx;
		}
		.item-list{
			padding: 20upx 0 0;
			display: flex;
			flex-wrap: wrap;
			text{
				display: flex;
				align-items: center;
				justify-content: center;
				background: #eee;
				margin-right: 20upx;
				margin-bottom: 20upx;
				border-radius: 100upx;
				min-width: 60upx;
				height: 60upx;
				padding: 0 20upx;
				font-size: $font-base;
				color: $font-color-dark;
			}
			.selected{
				background: #fbebee;
				color: $uni-color-primary;
			}
		}
	}
	
	/*  弹出层 */
	.popup {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 99;
		
		&.show {
			display: block;
			.mask{
				animation: showPopup 0.2s linear both;
			}
			.layer {
				animation: showLayer 0.2s linear both;
			}
		}
		&.hide {
			.mask{
				animation: hidePopup 0.2s linear both;
			}
			.layer {
				animation: hideLayer 0.2s linear both;
			}
		}
		&.none {
			display: none;
		}
		.mask{
			position: fixed;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			background-color: rgba(0, 0, 0, 0.4);
		}
		.layer {
			position: fixed;
			z-index: 99;
			bottom: 0;
			width: 100%;
			min-height: 40vh;
			border-radius: 10upx 10upx 0 0;
			background-color: #fff;
			.btn{
				height: 66upx;
				line-height: 66upx;
				border-radius: 100upx;
				background: $uni-color-primary;
				font-size: $font-base + 2upx;
				color: #fff;
				margin: 30upx auto 20upx;
			}
		}
		@keyframes showPopup {
			0% {
				opacity: 0;
			}
			100% {
				opacity: 1;
			}
		}
		@keyframes hidePopup {
			0% {
				opacity: 1;
			}
			100% {
				opacity: 0;
			}
		}
		@keyframes showLayer {
			0% {
				transform: translateY(120%);
			}
			100% {
				transform: translateY(0%);
			}
		}
		@keyframes hideLayer {
			0% {
				transform: translateY(0);
			}
			100% {
				transform: translateY(120%);
			}
		}
	}
	.form {
		flex: 1;
		padding: 5rpx 0;
	
		.form-item {
			padding: 5rpx 5rpx;
			line-height: 50rpx;
			font-size: 30rpx;
	
			.control-label {
				width: 250rpx;
			}
	
			.input-box {
				flex: 1;
				border: solid 1px #e5e5e5;
				border-radius: 10rpx;
				padding: 10rpx 10rpx;
			}
		}
	
		.btn-box {
			position: fixed;
			bottom: 0;
			right: 0;
			left: 0;
	
			.btn {
				background-color: $uni-color-error;
				color: #fff;
				border-radius: 0;
			}
		}
		.btn1 {
			// position: fixed;
			// bottom: 0;
			// right: 0;
			// left: 0;
			// margin: 30rpx auto;
			
			background-color: #FD6D24;
			width: 700rpx;
			color: #fff;
			border-radius: 20rpx;
		}
	}
	.buy-left {
		// display: flex;
		// flex-direction: column;
		justify-content: space-evenly;
		align-items: center;
		image {
			width: 40rpx;
			height: 40rpx;
		}
		text {
			margin-top: 5rpx;
			margin-left: 5rpx;
			font-size: 24rpx;
			color: #ff6229;
		}
	}
	.sharebtn {
	  margin: 0;
	  padding: 0;
	  outline: none;
	  border-radius: 0;
	  background-color: transparent;
	  line-height: inherit;
	  width: max-content;
	}
	/* 客服 */
	.kf_button{
	 background-color: rgba(255, 255, 255, 0);
	 border: 0px;
	 height: 80rpx;
	 left: 200;
	 bottom: 5rpx;
	 position: fixed;
	}
	.kf_button::after{
	 border: 0px;
	}
	.kf_image{
	 z-index: 9999;
	 width: 50rpx;
	 height: 50rpx;
	}
	
	/* 底部操作菜单 */
	.page-bottom{
		position:fixed;
		left: 0;
		bottom:0;
		z-index: 95;
		display: flex;
		//justify-content: center;
		align-items: center;
		display: flex;
		justify-content: space-evenly;
		width: 100%;
		height: 100upx;
		background: rgba(255,255,255,.9);
		box-shadow: 0 0 20upx 0 rgba(0,0,0,.5);
		border-radius: 16upx;
		
		.p-b-btn{
			display:flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: $font-sm;
			color: $font-color-base;
			// width: 96upx;
			height: 80upx;
			.yticon{
				font-size: 40upx;
				line-height: 48upx;
				color: $font-color-light;
			}
			&.active, &.active .yticon{
				color: $uni-color-primary;
			}
			.icon-fenxiang2{
				font-size: 42upx;
				transform: translateY(-2upx);
			}
			.icon-shoucang{
				font-size: 46upx;
			}
		}
		.action-btn-group{
			display: flex;
			height: 76upx;
			border-radius: 50px;
			overflow: hidden;
			box-shadow: 0 20upx 40upx -16upx #fa436a;
			box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
			background: #FD6D24;
			margin-left: 100upx;
			position:relative;
			&:after{
				content: '';
				position:absolute;
				top: 50%;
				right: 50%;
				transform: translateY(-50%);
				height: 28upx;
				width: 0;
				border-right: 1px solid rgba(255,255,255,.5);
			}
			.action-btn{
				display:flex;
				align-items: center;
				justify-content: center;
				width: 180upx;
				height: 100%;
				font-size: $font-base ;
				padding: 0;
				border-radius: 0;
				background: transparent;
			}
		}
	}
	.contact-btn {
	  display: inline-block;
	  position: absolute;
	  width: 20%;
	  background: salmon;
	    opacity: 0;
	}
	.buy{
		width: 300upx;
		height: 80upx;
		background-color: #FD6D24;
		border-radius: 30upx;
		font-size: 34upx;
		font-weight: 700;
		color: #fff;
		// border: 3upx solid #fff;
		text-align: center;
		line-height: 80upx;

	}
</style>
