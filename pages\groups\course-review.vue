<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 筛选栏 -->
			<view class="filter-bar">
				<view class="filter-item" 
					v-for="(filter, index) in filterList" 
					:key="index"
					:class="{ active: currentFilter === filter.value }"
					@click="changeFilter(filter.value)"
				>
					{{filter.label}}
				</view>
			</view>

			<!-- 课程列表 -->
			<view class="course-list">
				<view 
					class="course-item" 
					v-for="(course, index) in filteredCourses" 
					:key="index"
					@click="viewCourse(course)"
				>
					<view class="course-cover">
						<image :src="course.cover" mode="aspectFill"></image>
						<view class="course-duration">{{course.duration}}</view>
					</view>
					<view class="course-info">
						<view class="course-title">{{course.title}}</view>
						<view class="course-desc">{{course.description}}</view>
						<view class="course-meta">
							<text class="meta-tag" :class="course.status">{{getStatusText(course.status)}}</text>
							<text class="meta-time">{{course.studyTime}}</text>
						</view>
						<view class="course-progress">
							<view class="progress-bar">
								<view class="progress-fill" :style="{ width: course.progress + '%' }"></view>
							</view>
							<text class="progress-text">{{course.progress}}%</text>
						</view>
					</view>
					<view class="course-action">
						<text class="iconfont icon-play" v-if="course.status === 'learning'"></text>
						<text class="iconfont icon-check" v-else-if="course.status === 'completed'"></text>
						<text class="iconfont icon-lock" v-else></text>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="filteredCourses.length === 0">
				<image src="/static/imgs/empty-course.png" mode="aspectFit"></image>
				<text>暂无课程内容</text>
			</view>
		</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			groupId: '',
			currentFilter: 'all',
			filterList: [
				{ label: '全部', value: 'all' },
				{ label: '学习中', value: 'learning' },
				{ label: '已完成', value: 'completed' },
				{ label: '未开始', value: 'pending' }
			],
			courseList: [
				{
					id: 1,
					title: '第1课：五十音图（平假名）',
					description: '学习日语基础的平假名发音和书写',
					cover: '/static/imgs/course1.jpg',
					duration: '25:30',
					status: 'completed',
					progress: 100,
					studyTime: '2024-01-10'
				},
				{
					id: 2,
					title: '第2课：五十音图（片假名）',
					description: '学习日语基础的片假名发音和书写',
					cover: '/static/imgs/course2.jpg',
					duration: '28:15',
					status: 'completed',
					progress: 100,
					studyTime: '2024-01-12'
				},
				{
					id: 3,
					title: '第3课：基础问候语',
					description: '学习日常生活中的基本问候用语',
					cover: '/static/imgs/course3.jpg',
					duration: '22:45',
					status: 'learning',
					progress: 75,
					studyTime: '2024-01-15'
				},
				{
					id: 4,
					title: '第4课：数字和时间表达',
					description: '学习数字的读法和时间的表达方式',
					cover: '/static/imgs/course4.jpg',
					duration: '30:20',
					status: 'learning',
					progress: 45,
					studyTime: '2024-01-16'
				},
				{
					id: 5,
					title: '第5课：家族称呼',
					description: '学习家庭成员的称呼方式',
					cover: '/static/imgs/course5.jpg',
					duration: '26:10',
					status: 'pending',
					progress: 0,
					studyTime: ''
				}
			]
		}
	},
	computed: {
		filteredCourses() {
			if (this.currentFilter === 'all') {
				return this.courseList;
			}
			return this.courseList.filter(course => course.status === this.currentFilter);
		}
	},
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId;
			this.loadCourses();
		}
	},
	methods: {
		// 加载课程列表
		loadCourses() {
			// 这里应该根据groupId调用API获取课程列表
			// 暂时使用模拟数据
			this.pageLoading = true;
			setTimeout(() => {
				this.pageLoading = false;
			}, 1000);
		},

		// 切换筛选条件
		changeFilter(value) {
			this.currentFilter = value;
		},

		// 查看课程详情
		viewCourse(course) {
			if (course.status === 'pending') {
				uni.showToast({
					title: '课程尚未开放',
					icon: 'none'
				});
				return;
			}

			// 跳转到课程播放页面
			uni.navigateTo({
				url: `/pages/projects/course?courseId=${course.id}&groupId=${this.groupId}`
			});
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'learning': '学习中',
				'pending': '未开始'
			};
			return statusMap[status] || '未知';
		}
	}
}
</script>

<style scoped>
.filter-bar {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.filter-item {
	padding: 16rpx 32rpx;
	background: #f8f8f8;
	border-radius: 30rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
}

.filter-item.active {
	background: #2094CE;
	color: #fff;
}

.course-list {
	padding: 0 30rpx;
}

.course-item {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.course-cover {
	width: 160rpx;
	height: 120rpx;
	border-radius: 16rpx;
	overflow: hidden;
	position: relative;
	margin-right: 20rpx;
}

.course-cover image {
	width: 100%;
	height: 100%;
}

.course-duration {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0,0,0,0.7);
	color: #fff;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
}

.course-info {
	flex: 1;
}

.course-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.course-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
	line-height: 1.4;
}

.course-meta {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 12rpx;
}

.meta-tag {
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.meta-tag.completed {
	background: #e8f5e8;
	color: #52c41a;
}

.meta-tag.learning {
	background: #e6f7ff;
	color: #1890ff;
}

.meta-tag.pending {
	background: #f0f0f0;
	color: #999;
}

.meta-time {
	font-size: 22rpx;
	color: #999;
}

.course-progress {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.progress-bar {
	flex: 1;
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: #2094CE;
	transition: width 0.3s;
}

.progress-text {
	font-size: 22rpx;
	color: #666;
	min-width: 60rpx;
}

.course-action {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 20rpx;
}

.course-action .iconfont {
	font-size: 36rpx;
}

.icon-play {
	color: #2094CE;
}

.icon-check {
	color: #52c41a;
}

.icon-lock {
	color: #ccc;
}

.empty-state {
	text-align: center;
	padding: 100rpx 30rpx;
	color: #999;
}

.empty-state image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}
</style>
