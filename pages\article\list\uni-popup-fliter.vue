<template>
	<view class="uni-popup-fliter">
		<view class="uni-fliter-title"><text class="uni-fliter-title-text">{{title}}</text></view>
		<view class="uni-fliter-content">
			<view class="uni-fliter-content-box">

				<view class="item-group">
					<view class="title">日期</view>
					<view class="item-box date-box">
						<picker mode="date" :value="start_time" @change="onStartDateChangeHandler">
							<view class="input date-input">{{start_time ?start_time: "开始日期"}}</view>
						</picker>
						<view class="line"></view>
						<picker mode="date" :value="end_time" @change="onEndDateChangeHandler">
							<view class="input date-input">{{end_time ?end_time: "结束日期"}}</view>
						</picker>
					</view>
				</view>

				<view class="item-group">
					<view class="title">题材类型</view>
					<view class="item-box type-box">
						<view class="type-item" v-for="(item,index) in fliters.theme" :class="{active:selectThemes.find((id) => item.id == id)}"
						 :key="index" @click.stop="onTypeTapHandler(item,index)">
							<view class="input type-input">{{item.name}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="uni-fliter-button-box">
			<button class="uni-fliter-button reset-btn" @click="reset">重置</button>
			<button class="uni-fliter-button enter-btn" @click="close">确定</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'UniPopupfliter',
		props: {
			title: {
				type: String,
				default: '设置条件'
			},
			fliters: {
				type: Object,
				default: {
					typs: []
				}
			}
		},
		inject: ['popup'],
		data() {
			return {
				start_time: '',
				end_time: '',
				selectThemes: [],
			}
		},
		created() {},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 重置
			 */
			reset() {
				this.start_time = ''
				this.end_time = ''
				this.selectThemes = [];
			},
			/**
			 * 关闭窗口
			 */
			close() {
				this.$emit('select', {
					start_time: this.start_time,
					end_time: this.end_time,
					theme_id: this.selectThemes.join(',')
				}, () => {
					this.popup.close();
				});

			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 开始时间
			 */
			onStartDateChangeHandler: function(event) {
				this.start_time = event.detail.value;
			},

			/**
			 * 结束时间
			 */
			onEndDateChangeHandler: function(event) {
				this.end_time = event.detail.value;
			},


			/**
			 * 选择内容
			 */
			onTypeTapHandler: function(item, index) {
				const findex = this.selectThemes.findIndex((id) => item.id == id);
				if (findex >= 0) {
					this.selectThemes.splice(findex, 1);
				} else {
					this.selectThemes.push(item.id);
				}
			},
		}
	}
</script>
<style lang="scss" scoped>
	.uni-popup-fliter {
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;
	}

	.uni-fliter-title {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 40px;
	}

	.uni-fliter-title-text {
		font-size: 14px;
		color: #666;
	}

	.uni-fliter-content {
		display: flex;
		justify-content: stretch;
		padding-top: 10px;
		padding: 40rpx;
	}

	.uni-fliter-content-box {
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: stretch;

		.item-group {
			display: flex;
			flex-direction: column;
			margin-bottom: 40rpx;

			.title {
				margin-bottom: 40rpx;
				font-weight: bold;
			}

			.input {
				font-size: 28rpx;
			}

			/* 日期 */
			.date-box {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.date-input {
					padding: 20rpx 60rpx;
					border-radius: 40rpx;
					background: #eee;
					color: #666;
				}

				.line {
					flex: 1;
					border-bottom: solid 4rpx #eee;
				}
			}

			/* 题材类型 */
			.type-box {
				display: flex;
				flex-wrap: wrap;
				
				.type-item{
					width: 30%;
					margin-right: 3%;
				}

				.type-input {
					text-align: center;
					padding: 20rpx 60rpx;
					border-radius: 40rpx;
					border: solid 1px #eee;
					background: #eee;
					color: #666;
					margin-bottom: 40rpx;
				}

				.active {
					.input {
						border: solid 1px $uni-color-primary;
						background-color: $uni-color-primary;
						color: #ffffff;
					}
				}
			}
		}
	}

	.uni-fliter-image {
		width: 30px;
		height: 30px;
	}

	.uni-fliter-text {
		margin-top: 10px;
		font-size: 14px;
		color: #3B4144;
	}

	.uni-fliter-button-box {
		display: flex;
		flex-direction: row;
		padding: 10px 15px;

		.reset-btn {
			margin-right: 20rpx;
		}

		.enter-btn {
			background-color: $uni-color-primary;
			color: #fff;
		}
	}

	.uni-fliter-button {
		flex: 1;
		border-radius: 50px;
		color: #666;
		font-size: 16px;
	}

	.uni-fliter-button::after {
		border-radius: 50px;
	}
</style>
