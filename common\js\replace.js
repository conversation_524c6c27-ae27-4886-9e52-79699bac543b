// 拼接图片视频路径
export const isHttpOrHttps = (url) => {
	const reg = /http[s]{0,1}:\/\/([\w.]+\/?)\S*/;
	if (url) {
		if (reg.test(url)) {
			return url
		} else {
			// process.uniEnv.UNI_BASE_OSS_IMAGES 是 oss 的基准路径
			return process.uniEnv.UNI_BASE_OSS_IMAGES + url
		}
	} else {
		return;
	}
};

// 随机生成文件名
export const randomString = (len, suffix) => {
	const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz12345678';
	const maxPos = chars.length;
	let fileName = '';
	for (let i = 0; i < len; i++) {
		fileName += chars.charAt(Math.floor(Math.random() * maxPos));
	}
	return fileName + '_' + new Date().getTime() + '.' + suffix;
};
