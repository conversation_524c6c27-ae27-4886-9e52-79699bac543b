
@charset "UTF-8";
.uni-load-more {
	display: flex;
	flex-direction: row;
	height: 80rpx;
	align-items: center;
	justify-content: center
}
.uni-load-more__text {
	font-size: 28rpx;
	color: #999
}
.uni-load-more__img {
	height: 24px;
	width: 24px;
	margin-right: 10px
}
.uni-load-more__img>view {
	position: absolute
}
.uni-load-more__img>view view {
	width: 6px;
	height: 2px;
	border-top-left-radius: 1px;
	border-bottom-left-radius: 1px;
	background: #999;
	position: absolute;
	opacity: .2;
	-webkit-transform-origin: 50%;
	        transform-origin: 50%;
	-webkit-animation: load 1.56s ease infinite;
	        animation: load 1.56s ease infinite
}
.uni-load-more__img>view view:nth-child(1) {
	-webkit-transform: rotate(90deg);
	        transform: rotate(90deg);
	top: 2px;
	left: 9px
}
.uni-load-more__img>view view:nth-child(2) {
	-webkit-transform: rotate(180deg);
	        transform: rotate(180deg);
	top: 11px;
	right: 0
}
.uni-load-more__img>view view:nth-child(3) {
	-webkit-transform: rotate(270deg);
	        transform: rotate(270deg);
	bottom: 2px;
	left: 9px
}
.uni-load-more__img>view view:nth-child(4) {
	top: 11px;
	left: 0
}
.load1,
.load2,
.load3 {
	height: 24px;
	width: 24px
}
.load2 {
	-webkit-transform: rotate(30deg);
	        transform: rotate(30deg)
}
.load3 {
	-webkit-transform: rotate(60deg);
	        transform: rotate(60deg)
}
.load1 view:nth-child(1) {
	-webkit-animation-delay: 0s;
	        animation-delay: 0s
}
.load2 view:nth-child(1) {
	-webkit-animation-delay: .13s;
	        animation-delay: .13s
}
.load3 view:nth-child(1) {
	-webkit-animation-delay: .26s;
	        animation-delay: .26s
}
.load1 view:nth-child(2) {
	-webkit-animation-delay: .39s;
	        animation-delay: .39s
}
.load2 view:nth-child(2) {
	-webkit-animation-delay: .52s;
	        animation-delay: .52s
}
.load3 view:nth-child(2) {
	-webkit-animation-delay: .65s;
	        animation-delay: .65s
}
.load1 view:nth-child(3) {
	-webkit-animation-delay: .78s;
	        animation-delay: .78s
}
.load2 view:nth-child(3) {
	-webkit-animation-delay: .91s;
	        animation-delay: .91s
}
.load3 view:nth-child(3) {
	-webkit-animation-delay: 1.04s;
	        animation-delay: 1.04s
}
.load1 view:nth-child(4) {
	-webkit-animation-delay: 1.17s;
	        animation-delay: 1.17s
}
.load2 view:nth-child(4) {
	-webkit-animation-delay: 1.3s;
	        animation-delay: 1.3s
}
.load3 view:nth-child(4) {
	-webkit-animation-delay: 1.43s;
	        animation-delay: 1.43s
}
@-webkit-keyframes load {
0% {
		opacity: 1
}
100% {
		opacity: .2
}
}

