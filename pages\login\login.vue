<template>
	<gracePage>
		<view slot="gHeader">
			<ws-head color="black"></ws-head>
		</view>
		<view slot="gBody" style="padding:30rpx 30rpx 120rpx 30rpx;">
			<view class="ws-logo-box">
				<view class="logo-title">日语云课</view>
				<!-- <view class="logo-text">日语国际翻译大赛</view> -->
			</view>
			<view class="ws-btn-box wx-box" v-if="loginType=='wx'">
				<!-- <button class="wx-login-btn ws-mt" 
				open-type="getUserInfo"
				@getuserinfo="getuserinfo">
					微信账号一键登录
				</button> -->
				<!-- <button @tap="phoneLogin" class="phone-login-btn">
					手机号登录
				</button> -->
				<button class="wx-login-btn ws-mt" v-if="canIUseProfile==false" type="primary" open-type="getUserInfo"
					@getuserinfo="getUserInfo" withCredentials="true">登录</button>

				<button class="wx-login-btn ws-mt" v-else type="primary" @tap="getUserInfo">登录</button>
			</view>
			<view v-else class="ws-btn-box">
				<form @submit="loginNow" class="grace-form" style="margin-top:80rpx;">
					<!-- <view class="grace-form-item grace-border-b"> -->
						<!-- <view class="grace-pnper">
							<picker :value="pnpre" @change="changePre" :range="pnpres" name="pn_pre" style="text-align:left;">
								<text class="grace-text">+{{pnpres[pnpre]}}</text><text class="grace-text grace-icons icon-arrow-down" style="margin-left:10rpx;"></text>
							</picker>
						</view> -->
						<!-- <view class="grace-form-body">
							<input type="number" v-model="phoneno" class="grace-form-input" name="pn"
								placeholder="请输入手机号码" />
						</view>
					</view>
					<view class="grace-form-item grace-border-b"> -->
						<!-- <text class="grace-form-label">短信验证码</text> -->
						<!-- <view class="grace-form-body">
							<input type="number" class="grace-form-input" name="yzm" placeholder="请输入验证码" />
						</view>
						<view class="login-sendmsg-btn" @tap="getVCode">{{vcodeBtnName}}</view>
					</view> -->
					<view class="grace-margin-top">
						<!-- <button form-type="submit" type="primary" class="phone-login-btn">
							绑定手机号
						</button> -->
						<button class="phone-login-btn" type="default" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">获取手机号</button>
					</view>
				</form>
			</view>
		</view>
	</gracePage>
</template>


<script>
	var graceChecker = require("@/GraceUI5/js/checker.js");
	export default {
		created() {
			uni.login({
				provider: 'weixin',
				success: (res) => {
					if (res.code) {
						this.code = res.code;
					} else {
						console.log('登录失败！' + res.errMsg)
					}
				}
			})
		},
		onLoad(option) {
			// uni.navigateTo({
			// 	url:"/pages/index/index"
			// })
			if (option.type) {
				this.loginType = option.type;
			}
			if (wx.getUserProfile) {
				this.canIUseProfile = true;
			}
		},
		data() {
			return {
				code: "123",
				loginType: "wx", // 登录方式 -- wx 微信一键登录，其他为 账号密码登录
				userInfo: {}, // 用户信息
				token: "", // token

				// 手机号登录相关变量
				pnpre: 0,
				pnpres: ['86', '01', '11', '26', '520'],
				vcodeBtnName: "获取验证码",
				countNum: 120,
				countDownTimer: null,
				phoneno: '',

				vCodeKey: "", // 验证码key
				user_id: "",
				canIUseProfile: false,
			}
		},
		methods: {
			getLogin: function(iv, encryptedData, userInfo) {
				this.$http.post("auth/wxlogin", {
					type:'class',
					code: this.code,
					iv: iv,
					encryptedData: encryptedData
				}).then(res => {
					console.log(res.data.data)
					this.userInfo = res.data.data;
					// 缓存 token
					this.$store.commit('setUserToken', {
						token: res.data.data || '',
						saveStorage: true
					});
					this.getPhone(this.userInfo);
				});
			},
			getUserInfo(e) {
				// 等待登录弹窗
				uni.showLoading({
					title: "登录中...",
					mask: true
				})
				// 获得用户信息，并异步存储到本地
				this.userInfo = e.target.userInfo;
				console.log(this.userInfo)
				this.$store.commit('setUserInfo', {
					userInfo: this.userInfo,
					saveStorage: true
				});
				// 获得code
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {

						this.code = loginRes.code
						console.log('code' + loginRes.code);
					}
				});
				//旧版本方式
				if (this.canIUseProfile == false) {
					//获取授权信息  
					if (e.target.userInfo) {
						console.log('用户允许了授权', e.target.userInfo); //1.拿到基本的微信信息!!  
						this.getLogin(e.target.iv, e.target.encryptedData, e.target.userInfo);
					}
				} else {
					//新版本方式  
					var that = this;
					uni.getUserProfile({
						desc: '用于完善用户资料',
						lang: 'zh_CN',
						success: function(res) {
							console.log('wx.getUserProfile=>用户允许了授权', res);
							that.getLogin(res.iv, res.encryptedData, res.userInfo);
						},
						fail: function(res) {
							console.log('wx.getUserProfile=>用户拒绝了授权', res);
						},
					});

				}
			},
			getPhone() {
				this.$http.get("auth/me").then(res => {
					uni.hideLoading();
					if (res.data.code == 0) {
						this.userInfo = res.data.data;
						this.$store.commit('setUserInfo', {
							userInfo: this.userInfo,
							saveStorage: true
						});
						uni.setStorage({
							key: "phone",
							data: res.data.data.phone
						})
						if(res.data.data.phone){
							//处理兼容，如果没有上一级界面则返回首页
							const pages = getCurrentPages();
							if (pages.length === 2) {
								uni.navigateBack({
									delta: 1
								});
							} else if (pages.length === 1) {
								uni.switchTab({
									url: '/pages/index/index',
								})
							} else {
								uni.navigateBack({
									delta: 1
								});
							}
						}else{
							uni.showModal({
								title:"是否绑定手机号？",
								content:"必须绑定手机号才可进入课程",
								success: (res) => {
									if (res.confirm) {
										this.loginType = "phone"
									} else if (res.cancel) {
										uni.switchTab({
											url:"/pages/index/index"
										})
									}
								}
							})
						}
					} else {
						
					}
				})
			},
			navTo() {
				uni.switchTab({
					url: "/pages/index/index"
				})
			},
			phoneLogin() {
				this.loginType = '';
				this.pnpre = 0;
				this.countNum = 120;
				this.phoneno = '';
				clearInterval(this.countDownTimer);
				this.countDownTimer = null;
				this.vcodeBtnName = "获取验证码";
			},
			loginWithWx: function() {
				uni.showToast({
					title: "请完善登录功能",
					icon: "none"
				})
			},
			changePre: function(e) {
				this.pnpre = e.detail.value;
			},
			loginNow: function(e) {
				// 表单验证
				var rule = [{
						name: "pn",
						checkType: "phoneno",
						errorMsg: "请填写正确的手机号"
					},
					{
						name: "yzm",
						checkType: "string",
						checkRule: "4,6",
						errorMsg: "请正确填写短信验证码"
					},
				];
				var formData = e.detail.value;
				var checkRes = graceChecker.check(formData, rule);
				// 验证通过
				if (checkRes) {
					this.$http.post("auth/bindphone", {
						mobile: e.mp.detail.value.pn,
						verification_key: this.vCodeKey,
						sms_code: e.mp.detail.value.yzm,
						user_id: this.user_id
					}).then(res => {
						console.log(res.data)
						if (res.data.code == 0) {
							uni.setStorage({
								key: "phone",
								data: e.mp.detail.value.pn
							})
							uni.setStorage({
								key: "user",
								data: res.data.data || ''
							})
							uni.showToast({
								title: "绑定成功！",
								duration: 2000
							})
							// setTimeout(() => {
							// 	uni.navigateBack({
							// 		delta:1
							// 	})
							// },2000)
							uni.navigateTo({
								url: "/pages/index/index"
							})
						} else {
							uni.showToast({
								title: res.data + "，建议退出并重新登录后再次尝试",
								icon: "none"
							})
						}
					})
				} else {
					uni.showToast({
						title: graceChecker.error,
						icon: "none"
					});
				}
			},
			getVCode: function() {
				var myreg = /^[1][0-9]{10}$/;
				if (!myreg.test(this.phoneno)) {
					uni.showToast({
						title: '请正确填写手机号码',
						icon: "none"
					});
					return false;
				}
				// 手机号码为 :  this.phoneno
				// vcodeBtnName 可以阻止按钮被多次点击 多次发送 return 会终止函数继续运行
				if (this.vcodeBtnName != '获取验证码' && this.vcodeBtnName != '重新发送') {
					return;
				}
				this.vcodeBtnName = "发送中...";
				// 与后端 api 交互，发送验证码 【自己写的具体业务代码】
				this.$http.get("auth/sms", {
					params: {
						mobile: this.phoneno
					}
				}).then(res => {
					// console.log(res.data.data.verification_key)
					if (!res.data.data) {
						uni.showToast({
							title: '短信发送失败，请稍后再试',
							icon: "none"
						});
						this.vcodeBtnName = "重新发送"
					} else {
						// 假设发送成功，给用户提示
						uni.showToast({
							title: '短信已发送，请注意查收',
							icon: "none"
						});
						this.vCodeKey = res.data.data.verification_key;
						// 倒计时
						this.countNum = 120;
						this.countDownTimer = setInterval(function() {
							this.countDown();
						}.bind(this), 1000);
					}
				})

			},
			countDown: function() {
				if (this.countNum < 1) {
					clearInterval(this.countDownTimer);
					this.vcodeBtnName = "重新发送";
					return;
				}
				this.countNum--;
				this.vcodeBtnName = this.countNum + '秒重发';
			},
			// reg : function(){
			// 	wx.showToast({ title: "注册页面类似登录，请自行完善 (:", icon: "none" });
			// },
			getPhoneNumber(e) {
				console.log(e)
				// console.log(e.detail.errMsg)
				// console.log(e.detail.iv)
				// console.log(e.detail.encryptedData)
				console.log('userInfo'+uni.getStorageSync('userInfo'))
				uni.login({
					provider: 'weixin',
					success: (res) => {
						if (res.code) {
							this.code = res.code;
							this.$http.post("auth/decrypt_phone", {
								code: this.code,
								iv: e.detail.iv,
								encryptedData: e.detail.encryptedData,
								type:'class',
							}).then(res => {
								if (res.data.code == 0) {
									this.$store.commit('setUserInfo', {
										userInfo: res.data.data,
										saveStorage: true
									});
									//处理兼容，如果没有上一级界面则返回首页
									const pages = getCurrentPages();
									if (pages.length === 2) {
										uni.navigateBack({
											delta: 1
										});
									} else if (pages.length === 1) {
										uni.switchTab({
											url: '/pages/index/index',
										})
									} else {
										uni.navigateBack({
											delta: 1
										});
									}
								} else {
									console.log(res)
									uni.showToast({
										title: res.data.data.message,
										icon: "none"
									});
								}
								})
						} else {
							console.log('登录失败！' + res.errMsg)
						}
					}
				})
				
			},
			//订阅消息
			open() {
				var that = this
				// 获取用户的当前设置，判断是否点击了“总是保持以上，不在询问”
				wx.getSetting({
					withSubscriptions: true, //是否获取用户订阅消息的订阅状态，默认false不返回
					success(res) {
						if (res.authSetting['scope.subscribeMessage']) { //用户点击了“总是保持以上，不再询问”
							uni.openSetting({ // 打开设置页
								success(res) {
									console.log(res.authSetting)
									that.submit();
								}
							});
						} else { // 用户没有点击“总是保持以上，不再询问”则每次都会调起订阅消息
							// var templateid = that.setting.templateid.map(item => item.tempid)
							uni.requestSubscribeMessage({
								tmplIds: ['09VXAeuCPLVlrfdAN5JSCPaSnblK3obw5ynHqtDhsyI',
									'KlMjfhtBCQWDZKxuXxEmWy1Xubp5NIccPnIBh4h3QI4'
								],
								success(res) {
									console.log(res)
									that.submit();
								},
								fail: (res) => {
									console.log(res)
								}
							})
						}

					}
				})
			}
		}
	}
</script>

<style scoped>
	.ws-logo-box {
		margin: 100rpx 0;
		text-align: center;
		/* display: flex;
			justify-content: center;
			align-items: center; */
	}

	.ws-logo-img {
		margin: 0 auto;
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
	}

	.logo-title {
		font-size: 44rpx;
		font-weight: bold;
		letter-spacing: 8rpx;
		margin-top: 20rpx;
	}

	.logo-text {
		margin-top: 14rpx;
		font-size: 24rpx;
		color: #333333;
	}

	.ws-btn-box {
		text-align: center;
		padding: 0 40rpx;
	}

	.wx-box {
		padding-top: 100rpx;
	}

	.wx-login-btn,
	.phone-login-btn {
		height: 90rpx;
		line-height: 90rpx;
		width: 600rpx;
		margin: 0 auto;
		margin-top: 40rpx;
		border-radius: 100rpx;
		background-color: #04BCD6;
		color: #FFFFFF;
		text-align: center;
		letter-spacing: 2px;
		font-size: 32rpx;
	}

	/* .phone-login-btn{
			background-color: #FFFFFF;
			color: #04BCD6;
			border: 1px solid #04BCD6;
		} */
	.noLogin {
		position: absolute;
		bottom: 80rpx;
		left: 0;
		width: 100%;
		text-align: center;
		color: #04BCD6;
	}

	.marginTop {
		margin-top: 100rpx;
	}

	.logo {
		width: 250rpx;
		height: 68rpx
	}

	.login-sendmsg-btn {
		height: 60rpx;
		width: 200rpx;
		flex-shrink: 0;
		margin-left: 30rpx;
		text-align: center;
		color: #04BCD6;
		line-height: 60rpx;
		font-size: 26rpx;
	}

	.grace-pnper {
		width: 168rpx;
		flex-shrink: 0;
	}

	.grace-form-label {
		width: 168rpx;
	}

	.grace-form-input {
		text-align: left;
	}

	.grace-form-item {
		padding: 10rpx 0;
	}
</style>
