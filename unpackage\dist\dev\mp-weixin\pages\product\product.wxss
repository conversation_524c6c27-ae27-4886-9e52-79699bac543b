@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-2fbdbe34 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-2fbdbe34 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-2fbdbe34 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-2fbdbe34 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-2fbdbe34 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-2fbdbe34 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-2fbdbe34::after {
  border: none;
}
.u-hover-class.data-v-2fbdbe34 {
  opacity: 0.7;
}
.u-primary-light.data-v-2fbdbe34 {
  color: #ecf5ff;
}
.u-warning-light.data-v-2fbdbe34 {
  color: #fdf6ec;
}
.u-success-light.data-v-2fbdbe34 {
  color: #f5fff0;
}
.u-error-light.data-v-2fbdbe34 {
  color: #fef0f0;
}
.u-info-light.data-v-2fbdbe34 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-2fbdbe34 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-2fbdbe34 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-2fbdbe34 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-2fbdbe34 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-2fbdbe34 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-2fbdbe34 {
  color: #398ade;
}
.u-warning-dark.data-v-2fbdbe34 {
  color: #f1a532;
}
.u-success-dark.data-v-2fbdbe34 {
  color: #53c21d;
}
.u-error-dark.data-v-2fbdbe34 {
  color: #e45656;
}
.u-info-dark.data-v-2fbdbe34 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-2fbdbe34 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-2fbdbe34 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-2fbdbe34 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-2fbdbe34 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-2fbdbe34 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-2fbdbe34 {
  color: #9acafc;
}
.u-warning-disabled.data-v-2fbdbe34 {
  color: #f9d39b;
}
.u-success-disabled.data-v-2fbdbe34 {
  color: #a9e08f;
}
.u-error-disabled.data-v-2fbdbe34 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-2fbdbe34 {
  color: #c4c6c9;
}
.u-primary.data-v-2fbdbe34 {
  color: #3c9cff;
}
.u-warning.data-v-2fbdbe34 {
  color: #f9ae3d;
}
.u-success.data-v-2fbdbe34 {
  color: #5ac725;
}
.u-error.data-v-2fbdbe34 {
  color: #f56c6c;
}
.u-info.data-v-2fbdbe34 {
  color: #909399;
}
.u-primary-bg.data-v-2fbdbe34 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-2fbdbe34 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-2fbdbe34 {
  background-color: #5ac725;
}
.u-error-bg.data-v-2fbdbe34 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-2fbdbe34 {
  background-color: #909399;
}
.u-main-color.data-v-2fbdbe34 {
  color: #303133;
}
.u-content-color.data-v-2fbdbe34 {
  color: #606266;
}
.u-tips-color.data-v-2fbdbe34 {
  color: #909193;
}
.u-light-color.data-v-2fbdbe34 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-2fbdbe34 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-2fbdbe34 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-2fbdbe34 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-2fbdbe34 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-2fbdbe34::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.panel.data-v-2fbdbe34 {
  background: #fff;
  margin-bottom: 30rpx;
}
.panel .head.data-v-2fbdbe34 {
  font-size: 34rpx;
  margin-bottom: 30rpx;
  color: #333;
}
.root-box.data-v-2fbdbe34 {
  color: #666;
  background: #f2f2f2;
  padding-bottom: 120rpx;
  /* 基本信息 */
  /* 讲师 */
  /* 课程详情 */
  /* 立即报名 */
}
.root-box .base-box .cover-box .cover.data-v-2fbdbe34 {
  width: 100%;
  height: 400rpx;
}
.root-box .base-box .info-box.data-v-2fbdbe34 {
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
}
.root-box .base-box .info-box view.data-v-2fbdbe34 {
  margin-bottom: 5rpx;
}
.root-box .base-box .info-box .title.data-v-2fbdbe34 {
  font-size: 36rpx;
  font-weight: bold;
}
.root-box .base-box .info-box .label.data-v-2fbdbe34 {
  color: #999;
}
.root-box .base-box .info-box .course_date.data-v-2fbdbe34 {
  font-size: 24rpx;
}
.root-box .teacher-box.data-v-2fbdbe34 {
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
}
.root-box .teacher-box .item.data-v-2fbdbe34 {
  padding: 30rpx 0;
  border-bottom: solid 1px #eee;
}
.root-box .teacher-box .item.data-v-2fbdbe34:last-child {
  border-bottom: unset;
}
.root-box .teacher-box .info-box .name.data-v-2fbdbe34 {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.root-box .teacher-box .avatar-box.data-v-2fbdbe34 {
  margin-right: 30rpx;
}
.root-box .teacher-box .avatar-box .avatar.data-v-2fbdbe34 {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
}
.root-box .content-box.data-v-2fbdbe34 {
  padding: 30rpx;
}
.root-box .btn-box.data-v-2fbdbe34 {
  position: fixed;
  bottom: 0;
  background: #fff;
  flex: 1;
  left: 0;
  right: 0;
  height: 100rpx;
  border-top: solid 1px #eee;
  align-items: stretch;
  padding-left: 30rpx;
  line-height: 100rpx;
}
.root-box .btn-box .info.data-v-2fbdbe34 {
  flex: 1;
  text-align: center;
  color: #ff0000;
}
.root-box .btn-box .btn.data-v-2fbdbe34 {
  background-color: #dd524d;
  color: #fff;
  padding: 0 80rpx;
}
/* 规格选择弹窗 */
.attr-content.data-v-2fbdbe34 {
  padding: 10rpx 30rpx;
}
.attr-content .a-t.data-v-2fbdbe34 {
  display: flex;
}
.attr-content .a-t image.data-v-2fbdbe34 {
  width: 250rpx;
  height: 130rpx;
  flex-shrink: 0;
  border-radius: 8rpx;
}
.attr-content .a-t .right.data-v-2fbdbe34 {
  display: flex;
  flex-direction: column;
  padding-left: 24rpx;
  font-size: 26rpx;
  color: #606266;
  line-height: 42rpx;
}
.attr-content .a-t .right .price.data-v-2fbdbe34 {
  font-size: 32rpx;
  color: #FD6D24;
  margin-bottom: 10rpx;
}
.attr-content .a-t .right .selected-text.data-v-2fbdbe34 {
  margin-right: 10rpx;
}
.attr-content .attr-list.data-v-2fbdbe34 {
  display: flex;
  flex-direction: column;
  font-size: 30rpx;
  color: #606266;
  padding-top: 30rpx;
  padding-left: 10rpx;
}
.attr-content .item-list.data-v-2fbdbe34 {
  padding: 20rpx 0 0;
  display: flex;
  flex-wrap: wrap;
}
.attr-content .item-list text.data-v-2fbdbe34 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eee;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 100rpx;
  min-width: 60rpx;
  height: 60rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #303133;
}
.attr-content .item-list .selected.data-v-2fbdbe34 {
  background: #fbebee;
  color: #007aff;
}
/*  弹出层 */
.popup.data-v-2fbdbe34 {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.popup.show.data-v-2fbdbe34 {
  display: block;
}
.popup.show .mask.data-v-2fbdbe34 {
  -webkit-animation: showPopup-data-v-2fbdbe34 0.2s linear both;
          animation: showPopup-data-v-2fbdbe34 0.2s linear both;
}
.popup.show .layer.data-v-2fbdbe34 {
  -webkit-animation: showLayer-data-v-2fbdbe34 0.2s linear both;
          animation: showLayer-data-v-2fbdbe34 0.2s linear both;
}
.popup.hide .mask.data-v-2fbdbe34 {
  -webkit-animation: hidePopup-data-v-2fbdbe34 0.2s linear both;
          animation: hidePopup-data-v-2fbdbe34 0.2s linear both;
}
.popup.hide .layer.data-v-2fbdbe34 {
  -webkit-animation: hideLayer-data-v-2fbdbe34 0.2s linear both;
          animation: hideLayer-data-v-2fbdbe34 0.2s linear both;
}
.popup.none.data-v-2fbdbe34 {
  display: none;
}
.popup .mask.data-v-2fbdbe34 {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.4);
}
.popup .layer.data-v-2fbdbe34 {
  position: fixed;
  z-index: 99;
  bottom: 0;
  width: 100%;
  min-height: 40vh;
  border-radius: 10rpx 10rpx 0 0;
  background-color: #fff;
}
.popup .layer .btn.data-v-2fbdbe34 {
  height: 66rpx;
  line-height: 66rpx;
  border-radius: 100rpx;
  background: #007aff;
  font-size: 30rpx;
  color: #fff;
  margin: 30rpx auto 20rpx;
}
@-webkit-keyframes showPopup-data-v-2fbdbe34 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes showPopup-data-v-2fbdbe34 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@-webkit-keyframes hidePopup-data-v-2fbdbe34 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes hidePopup-data-v-2fbdbe34 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@-webkit-keyframes showLayer-data-v-2fbdbe34 {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@keyframes showLayer-data-v-2fbdbe34 {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@-webkit-keyframes hideLayer-data-v-2fbdbe34 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
@keyframes hideLayer-data-v-2fbdbe34 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
.form.data-v-2fbdbe34 {
  flex: 1;
  padding: 5rpx 0;
}
.form .form-item.data-v-2fbdbe34 {
  padding: 5rpx 5rpx;
  line-height: 50rpx;
  font-size: 30rpx;
}
.form .form-item .control-label.data-v-2fbdbe34 {
  width: 250rpx;
}
.form .form-item .input-box.data-v-2fbdbe34 {
  flex: 1;
  border: solid 1px #e5e5e5;
  border-radius: 10rpx;
  padding: 10rpx 10rpx;
}
.form .btn-box.data-v-2fbdbe34 {
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
}
.form .btn-box .btn.data-v-2fbdbe34 {
  background-color: #dd524d;
  color: #fff;
  border-radius: 0;
}
.form .btn1.data-v-2fbdbe34 {
  background-color: #FD6D24;
  width: 700rpx;
  color: #fff;
  border-radius: 20rpx;
}
.buy-left.data-v-2fbdbe34 {
  justify-content: space-evenly;
  align-items: center;
}
.buy-left image.data-v-2fbdbe34 {
  width: 40rpx;
  height: 40rpx;
}
.buy-left text.data-v-2fbdbe34 {
  margin-top: 5rpx;
  margin-left: 5rpx;
  font-size: 24rpx;
  color: #ff6229;
}
.sharebtn.data-v-2fbdbe34 {
  margin: 0;
  padding: 0;
  outline: none;
  border-radius: 0;
  background-color: transparent;
  line-height: inherit;
  width: -webkit-max-content;
  width: max-content;
}
/* 客服 */
.kf_button.data-v-2fbdbe34 {
  background-color: rgba(255, 255, 255, 0);
  border: 0px;
  height: 80rpx;
  left: 200;
  bottom: 5rpx;
  position: fixed;
}
.kf_button.data-v-2fbdbe34::after {
  border: 0px;
}
.kf_image.data-v-2fbdbe34 {
  z-index: 9999;
  width: 50rpx;
  height: 50rpx;
}
/* 底部操作菜单 */
.page-bottom.data-v-2fbdbe34 {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 95;
  display: flex;
  align-items: center;
  display: flex;
  justify-content: space-evenly;
  width: 100%;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.5);
  border-radius: 16rpx;
}
.page-bottom .p-b-btn.data-v-2fbdbe34 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #606266;
  height: 80rpx;
}
.page-bottom .p-b-btn .yticon.data-v-2fbdbe34 {
  font-size: 40rpx;
  line-height: 48rpx;
  color: #909399;
}
.page-bottom .p-b-btn.active.data-v-2fbdbe34, .page-bottom .p-b-btn.active .yticon.data-v-2fbdbe34 {
  color: #007aff;
}
.page-bottom .p-b-btn .icon-fenxiang2.data-v-2fbdbe34 {
  font-size: 42rpx;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
}
.page-bottom .p-b-btn .icon-shoucang.data-v-2fbdbe34 {
  font-size: 46rpx;
}
.page-bottom .action-btn-group.data-v-2fbdbe34 {
  display: flex;
  height: 76rpx;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 20rpx 40rpx -16rpx #fa436a;
  box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
  background: #FD6D24;
  margin-left: 100rpx;
  position: relative;
}
.page-bottom .action-btn-group.data-v-2fbdbe34:after {
  content: '';
  position: absolute;
  top: 50%;
  right: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  height: 28rpx;
  width: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.5);
}
.page-bottom .action-btn-group .action-btn.data-v-2fbdbe34 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 100%;
  font-size: 28rpx;
  padding: 0;
  border-radius: 0;
  background: transparent;
}
.contact-btn.data-v-2fbdbe34 {
  display: inline-block;
  position: absolute;
  width: 20%;
  background: salmon;
  opacity: 0;
}
.buy.data-v-2fbdbe34 {
  width: 300rpx;
  height: 80rpx;
  background-color: #FD6D24;
  border-radius: 30rpx;
  font-size: 34rpx;
  font-weight: 700;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
}

