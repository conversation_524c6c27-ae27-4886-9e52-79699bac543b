(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/nodata/nodata"],{"0015":function(n,t,e){"use strict";e.r(t);var u=e("e5d4"),a=e("f857");for(var c in a)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(c);e("44c7");var r=e("828b"),f=Object(r["a"])(a["default"],u["b"],u["c"],!1,null,"ef1069a0",null,!1,u["a"],void 0);t["default"]=f.exports},"44c7":function(n,t,e){"use strict";var u=e("d5e9"),a=e.n(u);a.a},d5e9:function(n,t,e){},e5d4:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},a=[]},ecea7:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:["text"],data:function(){return{}}}},f857:function(n,t,e){"use strict";e.r(t);var u=e("ecea7"),a=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);t["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/nodata/nodata-create-component',
    {
        'components/nodata/nodata-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0015"))
        })
    },
    [['components/nodata/nodata-create-component']]
]);
