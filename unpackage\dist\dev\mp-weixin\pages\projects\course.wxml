<view class="container data-v-3b3be832"><view class="data-v-3b3be832"><video style="width:100%;" model="{{menuinfo}}" src="{{videosrc}}" controls="{{video_controls}}" autoplay="{{true}}" show-center-play-btn="{{true}}" enable-play-gesture="{{true}}" data-event-opts="{{[['error',[['videoErrorCallback',['$event']]]],['timeupdate',[['video_timeUpdate',['$event']]]],['fullscreenchange',[['fullscreen',['$event']]]],['play',[['video_onplay',['$event']]]],['pause',[['video_onpause',['$event']]]],['ended',[['video_onend',['$event']]]]]}}" binderror="__e" bindtimeupdate="__e" bindfullscreenchange="__e" bindplay="__e" bindpause="__e" bindended="__e" class="data-v-3b3be832"></video></view><view class="shipinpart-info data-v-3b3be832" style="background-color:#ffffff;"><text class="info-span1 data-v-3b3be832" style="margin-top:0;">{{menuinfo.title}}</text><view class="info-span5 data-v-3b3be832"><text class="info-span3 data-v-3b3be832"><text class="data-v-3b3be832">{{menuinfo.count+"课时"}}</text></text></view></view><view class="kechengpart data-v-3b3be832"><view class="kechengpart-title data-v-3b3be832"><block wx:for="{{kechengList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="kechengpart-title-item data-v-3b3be832"><view data-event-opts="{{[['tap',[['muluchange',[index]]]]]}}" class="{{['data-v-3b3be832',(btnnum==index)?'btna':'']}}" bindtap="__e">{{item}}</view><text class="{{['data-v-3b3be832',(btnnum==index)?'_underline':'']}}"></text></view></block></view><view class="kechengpart-content data-v-3b3be832"><view class="{{['kcjs','data-v-3b3be832',(btnnum==0)?'dis':'']}}" model="{{teacher}}"><view class="kcjs-brief data-v-3b3be832"><view class="kcjs-brief-center data-v-3b3be832"><rich-text nodes="{{menuinfo.content}}" class="data-v-3b3be832"></rich-text></view></view></view><view class="{{['kcml','data-v-3b3be832',(btnnum==1)?'dis':'']}}" style="margin-bottom:122rpx;"><block wx:for="{{menuinfo.li}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['openvideo',['$0'],[[['menuinfo.li','id',item.id]]]]]]]}}" class="kcml-list data-v-3b3be832" bindtap="__e"><view class="data-v-3b3be832"><block wx:if="{{item.id==videoId}}"><text class="fontcolor data-v-3b3be832">{{index+1+' . '+item.title}}</text></block><block wx:else><text class="data-v-3b3be832">{{index+1+' . '+item.title}}</text></block></view></view></block></view></view></view><zaudio vue-id="69dff1ff-1" class="data-v-3b3be832" bind:__l="__l"></zaudio></view>