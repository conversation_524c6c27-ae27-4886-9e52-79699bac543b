<view><view style="height:0;" animation="{{anData}}"></view><scroll-view style="{{'width:750rpx;'+('height:'+(srcollHeight)+';')}}" scroll-with-animation="{{true}}" scroll-y="true" scroll-top="{{go}}" data-event-opts="{{[['touchmove',[['hideKey',['$event']]]],['tap',[['hide',['$event']]]]]}}" bindtouchmove="__e" bindtap="__e"><view id="okk" scroll-with-animation="{{true}}"><block wx:for="{{msgList}}" wx:for-item="x" wx:for-index="i" wx:key="i"><view class="flex-column-start"><block wx:if="{{x.my}}"><view class="flex justify-end padding-right one-show align-start padding-top"><view class="flex justify-end" style="width:400rpx;"><view class="margin-left padding-chat bg-cyan" style="border-radius:35rpx;"><text style="word-break:break-all;">{{x.msg}}</text></view></view></view></block><block wx:if="{{!x.my}}"><view class="margin-left margin-top one-show" style="display:flex;"><view class="chat-img flex-row-center"><image style="height:75rpx;width:75rpx;" src="../../static/robt.png" mode="aspectFit"></image></view><view class="flex" style="width:500rpx;"><view class="margin-left padding-chat flex-column-start" style="border-radius:35rpx;background-color:#f9f9f9;"><rich-text nodes="{{x.msg}}"></rich-text><block wx:if="{{x.type==1}}"><view class="flex-column-start" style="color:#2fa39b;"><text style="color:#838383;font-size:22rpx;margin-top:15rpx;">你可以这样问我:</text><block wx:for="{{x.questionList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['msgKf',['$0'],[[['msgList','',i],['questionList','',index]]]]]]]}}" style="margin-top:30rpx;" bindtap="__e">{{item.title}}</text></block></view></block><block wx:if="{{x.type==2}}"><view class="flex-column-start" style="color:#2fa39b;"><text style="color:#838383;font-size:22rpx;margin-top:15rpx;">猜你想问:</text><block wx:for="{{x.questionList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['answer',['$0'],[[['msgList','',i],['questionList','',index]]]]]]]}}" style="margin-top:30rpx;" bindtap="__e">{{item.title}}</text></block></view></block><block wx:if="{{x.type==0}}"><view class="flex-column-start"><text class="padding-top-sm" style="color:#2fa39b;">提交意见与反馈</text><text style="color:#838383;font-size:22rpx;margin-top:15rpx;">下面是一些常见问题,您可以点击对应的文字快速获取答案:</text><block wx:for="{{x.questionList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['answer',['$0'],[[['msgList','',i],['questionList','',index]]]]]]]}}" style="margin-top:30rpx;color:#2fa39b;" bindtap="__e">{{item.title}}</text></block><view class="flex-row-start padding-top-sm"><text class="my-neirong-sm">没有你要的答案?</text><text class="padding-left" style="color:#1396c5;">换一批</text></view></view></block></view></view></view></block></view></block><view hidden="{{!(msgLoad)}}" class="flex-row-start margin-left margin-top"><view class="chat-img flex-row-center"><image style="height:75rpx;width:75rpx;" src="../../static/robt.png" mode="aspectFit"></image></view><view class="flex" style="width:500rpx;"><view class="margin-left padding-chat flex-column-start" style="border-radius:35rpx;background-color:#f9f9f9;"><view class="cuIcon-loading turn-load" style="font-size:35rpx;color:#3e9982;"></view></view></view></view><view style="height:120rpx;"></view></view></scroll-view><view class="flex-column-center" style="position:fixed;bottom:-180px;" animation="{{animationData}}"><view class="bottom-dh-char flex-row-around" style="font-size:25rpx;"><input class="dh-input" style="background-color:#f0f0f0;" type="text" confirm-type="search" placeholder-class="my-neirong-sm" placeholder="用一句简短的话描述您的问题" data-event-opts="{{[['confirm',[['sendMsg',['$event']]]],['input',[['__set_model',['','msg','$event',[]]]]]]}}" value="{{msg}}" bindconfirm="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendMsg',['$event']]]]]}}" class="cu-tag bg-cyan round" bindtap="__e">发送</view><text data-event-opts="{{[['tap',[['ckAdd',['$event']]]]]}}" class="cuIcon-roundaddfill text-brown" bindtap="__e"></text></view><view class="box-normal flex-row-around flex-wrap"><view class="tb-text"><view class="cuIcon-form"></view><text>人工客服</text><button class="contact-btn" open-type="contact">a</button></view></view></view></view>