<view data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" class="search-result-item data-v-0e7bdf99" bindtap="__e"><common-image class="course-image data-v-0e7bdf99" vue-id="7dfed6e6-1" src="{{item.picture}}" width="160rpx" height="120rpx" lazy-load="{{true}}" border-radius="8rpx" bind:__l="__l"></common-image><view class="item-content data-v-0e7bdf99"><text class="course-title data-v-0e7bdf99"><rich-text nodes="{{highlightedTitle}}"></rich-text></text><text class="course-desc data-v-0e7bdf99">{{item.des||item.description||item.jianjie}}</text><view class="course-meta data-v-0e7bdf99"><text class="price data-v-0e7bdf99">{{"￥"+(item.Cost||item.price||0)}}</text><text class="students data-v-0e7bdf99">{{(item.student_count||item.viewnum||0)+"人学习"}}</text><text class="lessons data-v-0e7bdf99">{{(item.lesson_count||item.count||0)+"课时"}}</text></view><block wx:if="{{$root.g0}}"><view class="course-tags data-v-0e7bdf99"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><text class="tag data-v-0e7bdf99">{{''+tag+''}}</text></block></view></block></view></view>