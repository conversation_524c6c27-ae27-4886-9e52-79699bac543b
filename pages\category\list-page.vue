<template>
	<view class="course-container">
		<!-- 搜索栏 -->
		<view class="search-header">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					placeholder="搜索课程..."
					v-model="keyword"
					@input="onSearchInput"
				/>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 左侧分类导航 -->
			<scroll-view class="category-nav" scroll-y="true">
				<view
					class="category-item"
					:class="{ active: activeIndex === index }"
					v-for="(item, index) in classifyList"
					:key="index"
					@click="postSonflData(item.id, index)"
				>
					<view class="category-indicator" v-if="activeIndex === index"></view>
					<text class="category-name">{{item.name}}</text>
					<view class="category-count" v-if="item.count">{{item.count}}</view>
				</view>
			</scroll-view>

			<!-- 右侧课程列表 -->
			<scroll-view class="course-list" scroll-y="true">
				<!-- 加载状态 -->
				<view class="loading-container" v-if="isLoading">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载中...</text>
				</view>

				<!-- 课程分组 -->
				<view class="course-groups" v-else>
					<uni-collapse ref="collapse" v-model="value">
						<uni-collapse-item
							:title="item.name"
							v-for="(item, index) in son_fls"
							:key="index"
							:open="open == 0 ? false : true"
							class="course-group"
						>
							<!-- 空状态 -->
							<view class="empty-state" v-if="item.class.length === 0">
								<text class="empty-icon">📚</text>
								<text class="empty-text">暂无课程</text>
							</view>

							<!-- 课程列表 -->
							<view class="course-grid" v-else>
								<view
									class="course-card"
									v-for="(course, courseIndex) in item.class"
									:key="courseIndex"
									@tap="navToDetailPage(course)"
								>
									<!-- 课程封面 -->
									<view class="course-cover">
										<image
											class="cover-image"
											:src="course.picture || defaultCover"
											mode="aspectFill"
											@error="handleImageError"
										/>
										<view class="course-badge" v-if="course.is_free">免费</view>
										<view class="course-badge premium" v-else-if="course.price">¥{{course.price}}</view>
									</view>

									<!-- 课程信息 -->
									<view class="course-info">
										<text class="course-title">{{course.title}}</text>
										<view class="course-meta">
											<text class="course-teacher" v-if="course.teacher">{{course.teacher}}</text>
											<text class="course-students" v-if="course.student_count">{{course.student_count}}人学习</text>
										</view>
										<view class="course-tags" v-if="course.tags">
											<text
												class="course-tag"
												v-for="tag in course.tags.slice(0, 2)"
												:key="tag"
											>{{tag}}</text>
										</view>
									</view>
								</view>
							</view>
						</uni-collapse-item>
					</uni-collapse>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import { postClassifylist, postSonflData } from '@/request/index'
	import {postSearchCourse} from '@/request/search'
	import empty1 from "@/components/null";
	export default {
		components: {
			empty1
		},
		data() {
			return {
				keyword: '',
				activeIndex: 0,
				classifyList: [],
				flid: '',
				son_fls: [],
				goodstype: '',
				open: 0,
				// 添加缺失的响应式属性
				cateId: '',
				style: '',
				title: '',
				// 新增优化相关数据
				isLoading: false,
				searchTimer: null,
				defaultCover: '/static/imgs/default-course.png',
				value: []
			};
		},
		onShareAppMessage(res) {
			let path = getCurrentPages()
			let path_share = path[0].$page.fullPath
			let path_title = path[0].data.title
			let userinfo = uni.getStorageSync('userinfo')
			let base_set = uni.getStorageSync('base_set')
			if(userinfo.uid=='' || !userinfo.uid){
				uni.navigateTo({
					url:'../login/login'
				})
				return {
					title: '请先登录后再分享给好友',
					path: ''
				}
			}else{
				if (res.from === 'button') {
					
				}
				return {
					title: base_set.title,
					path: `${path_share}?pid=${userinfo.uid}`
				}
			}
		},
		onLoad(options) {
			this.cateId = options.id;
			this.style = options.style;
			this.title = options.title;
			console.log(this.title);
			// 动态设置标题
			uni.setNavigationBarTitle({
				title: this.title
			});
			this.loadHotData()
		},
		methods: {
			async loadHotData(type = 'add', loading) {
				this.isLoading = true;
				uni.showLoading();
				this.$http.get("v1/course/getCate", {
					params: {
						page: this.page
					}
				}).then(res => {
					console.log(res)
					this.classifyList = res.data.data
					this.postSonflData(this.classifyList[0].id, 0)
					this.isLoading = false;
					uni.hideLoading();
				}).catch(error => {
					console.error('加载分类失败:', error);
					this.isLoading = false;
					uni.hideLoading();

					// 添加测试数据确保页面有内容
					this.addTestData();

					uni.showToast({
						title: '网络请求失败，显示测试数据',
						icon: 'none'
					});
				});
			},

			// 添加测试数据
			addTestData() {
				console.log('添加分类测试数据...');
				this.classifyList = [
					{ id: 1, name: '基础日语', count: 15 },
					{ id: 2, name: '进阶日语', count: 12 },
					{ id: 3, name: '商务日语', count: 8 },
					{ id: 4, name: '考试辅导', count: 10 }
				];

				this.son_fls = [
					{
						name: '基础日语课程',
						class: [
							{
								id: 1,
								title: '五十音图入门',
								picture: 'https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/course1.jpg',
								teacher: '田中老师',
								student_count: 1200,
								price: 99,
								is_free: false
							},
							{
								id: 2,
								title: '基础语法精讲',
								picture: 'https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/course2.jpg',
								teacher: '佐藤老师',
								student_count: 800,
								is_free: true
							}
						]
					}
				];

				this.activeIndex = 0;
			},

			// 搜索输入处理
			onSearchInput() {
				// 防抖处理
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				this.searchTimer = setTimeout(() => {
					this.performSearch();
				}, 500);
			},

			// 执行搜索
			performSearch() {
				if (!this.keyword.trim()) {
					// 如果搜索关键词为空，重新加载当前分类数据
					if (this.classifyList.length > 0) {
						this.postSonflData(this.classifyList[this.activeIndex].id, this.activeIndex);
					}
					return;
				}

				// 实现搜索功能
				console.log('搜索关键词:', this.keyword);
				this.searchCourses(this.keyword);
			},

			// 搜索课程
			searchCourses(keyword) {
				this.isLoading = true;
				this.$http.get("v1/course/search", {
					params: {
						keyword: keyword,
						page: 1,
						limit: 20
					}
				}).then(res => {
					console.log('搜索结果:', res);
					if (res.data.code === 0) {
						// 将搜索结果转换为分类格式显示
						this.son_fls = [{
							name: `"${keyword}" 的搜索结果`,
							class: res.data.data.list || []
						}];
					} else {
						this.son_fls = [{
							name: `"${keyword}" 的搜索结果`,
							class: []
						}];
					}
					this.isLoading = false;
				}).catch(error => {
					console.error('搜索失败:', error);
					this.son_fls = [{
						name: `"${keyword}" 的搜索结果`,
						class: []
					}];
					this.isLoading = false;
				});
			},

			// 图片加载错误处理
			handleImageError(e) {
				console.warn('图片加载失败:', e);
				// 可以设置默认图片
				e.target.src = this.defaultCover;
			},
			// 获取分类列表
			postClassifylist(goodstype) {
				postClassifylist({goodstype: goodstype}).then(res => {
					this.classifyList = res.data.data
					this.postSonflData(this.classifyList[0].id, 0)
				})
			},
			// 获取子分类列表
			postSonflData(id, key) {
				this.$nextTick(() => {
					setTimeout(()=>{
						this.$refs.collapse.resize();
					},500)
				});
				this.activeIndex = key
				this.flid = this.classifyList[key].id
				// postSonflData({goodstype: this.goodstype, flid: id}).then(res => {
				// 	this.son_fls = res.data.data
				// })
				this.$http.get("v1/course/getSubClass", {
					params: {
						id: id
					}
				}).then(res => {
					console.log(res)
					this.son_fls = res.data.data.class
				});
			},
			navigate(id) {
				// console.log(id)
				if(this.goodstype == 'course') {
					uni.navigateTo({
						url: `/pages/course-list/course-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`
					})
				} else {
					uni.navigateTo({
						url: `/pages/commodity-list/commodity-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`
					})
				}
			},
			postSearchCourse(e) {
				console.log(123)
				var keyword
				if(e == 'hot') {
					keyword = e
				} else {
					keyword = this.keyword
					if(keyword == '') {
						uni.showToast({
							title: '搜索内容不能为空',
							icon: 'none'
						});
						return false
					}
				}
				uni.navigateTo({
					url: `/pages/course-list/course-list?keyword=${keyword}`
				});
			},
			navToDetailPage(item){
				let id = item.id
				uni.navigateTo({
					url: `/pages/course/course?id=${id}`
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	/* 新增的现代化样式 */
	.course-container {
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
	}

	.search-header {
		background: rgba(255, 255, 255, 0.95);
		padding: 20rpx;
		backdrop-filter: blur(10rpx);
	}

	.search-box {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 25rpx;
		padding: 15rpx 25rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.search-icon {
		font-size: 32rpx;
		color: #999;
		margin-right: 15rpx;
	}

	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #333;
	}

	.main-content {
		flex: 1;
		display: flex;
		background: #fff;
	}

	.category-nav {
		width: 200rpx;
		background: #f8f9fa;
		border-right: 1rpx solid #e9ecef;
	}

	.category-item {
		position: relative;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #e9ecef;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: all 0.3s ease;

		&.active {
			background: linear-gradient(135deg, #667eea, #764ba2);
			color: #fff;
		}
	}

	.category-indicator {
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 40rpx;
		background: #fff;
		border-radius: 0 3rpx 3rpx 0;
	}

	.category-name {
		font-size: 26rpx;
		font-weight: 500;
		text-align: center;
		line-height: 1.3;
	}

	.category-count {
		font-size: 20rpx;
		opacity: 0.8;
		margin-top: 5rpx;
	}

	.course-list {
		flex: 1;
		background: #fff;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.course-groups {
		padding: 20rpx;
	}

	.course-group {
		margin-bottom: 30rpx;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 0;
	}

	.empty-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	.course-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
		gap: 20rpx;
		padding: 20rpx 0;
	}

	.course-card {
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-5rpx);
			box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
		}
	}

	.course-cover {
		position: relative;
		width: 100%;
		height: 180rpx;
		overflow: hidden;
	}

	.cover-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
		transition: transform 0.3s ease;
	}

	.course-card:hover .cover-image {
		transform: scale(1.05);
	}

	.course-badge {
		position: absolute;
		top: 15rpx;
		right: 15rpx;
		background: #28a745;
		color: #fff;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 20rpx;
		font-weight: 500;

		&.premium {
			background: #ff6b6b;
		}
	}

	.course-info {
		padding: 20rpx;
	}

	.course-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		line-height: 1.4;
		margin-bottom: 15rpx;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.course-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.course-teacher {
		font-size: 24rpx;
		color: #666;
	}

	.course-students {
		font-size: 22rpx;
		color: #999;
	}

	.course-tags {
		display: flex;
		gap: 10rpx;
	}

	.course-tag {
		background: #f8f9fa;
		color: #666;
		padding: 6rpx 12rpx;
		border-radius: 12rpx;
		font-size: 20rpx;
	}

	// search框
	.search {
		height: 90upx;
		background-color: #4b89ff;
		padding: 0 20upx;
		padding-top: 30upx;

		&-input {
			position: relative;
			display: flex;
			text {
				font-size: 31upx;
				color: #fff;
				margin-top: 10upx;
			}
			image {
				width: 25upx;
				height: 14upx;
				margin: 25upx 20upx 0 10upx;
			}
			input {
				width: 710upx;
				height: 63upx;
				border: 0;
				background-color: #fff;
				border-radius: 63upx;
				font-size: 24upx;
				padding-left: 20upx;
				box-sizing: border-box;
				
			}
			.goods-search {
				width: 28upx;
				height: 28upx;
				position: absolute;
				right: 10upx;
				top: -5upx;
				z-index: 99;
			}
		}
	}
	
	
	.scroll {
		height: calc(100vh - 5upx);
		// background-color: pink;
		display: flex;
		&-left {
			flex: 2;
			// background-color: red;
			view {
				height: 120upx;
				background-color: #eee;
				display: flex;
				//flex-direction: column;
				justify-content: center;
				align-items: center;
				// border-bottom: 2upx solid #ddd;
				font-size: 30rpx;
				color: #333;
				letter-spacing: 2upx;
			}
			.active {
				background-color: #fff;
			}
		}
		&-right {
			flex: 5;
			background-color: #fff;
			padding: 0 10upx;
			box-sizing: border-box;
			.item {
				display: inline-block;
				width: 220upx;
				height: 60upx;
				background-color: #eee;
				text-align: center;
				line-height: 60upx;
				border-radius: 60upx;
				margin: 20upx 12upx 0;
				text {
					font-size: 30upx;
					font-weight: 700;
					letter-spacing: 4upx;
					color: #333;
				}
			}
		}
	}
	.list-box {
		// padding-bottom: 20rpx;
	
		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding: 10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			border-bottom: 1rpx solid #ebebeb;
	
	
			.top-box {
				position: relative;
				padding: 20rpx;
	
				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
	
					.cover :after {
						background-color: red;
						border-radius: 10rpx;
						color: #fff;
						content: "hot";
						font-size: 25rpx;
						line-height: 1;
						padding: 2rpx 6rpx;
						position: absolute;
						left: 5rpx;
						top: 5rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-box {
					width: 150rpx;
					height: auto;
					min-height: 150rpx;
					position: relative;
						
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-large-box {
					width: 100%;
					height: auto;
					height: 200rpx;
					position: relative;
				
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
				
					.button {
						position: absolute;
						bottom: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: rgba(0, 0, 0, .5) !important;
						color: white;
						padding: 15rpx 20rpx;
						font-size: 20rpx;
					}
				}
	
				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
	
					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}
	
					.lang-box {
						//color: $uni-text-color-grey;
						font-size: 24rpx;
					}
	
					.title {
						font-weight: bold;
						font-size: 24rpx;
						color: #666666;
					}
	
					.end-date {
						font-size: 20rpx;
						color: #999999;
					}
	
					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
	
					.des {
						font-size: 22rpx;
						color: #8f8f94;
	
	
					}
	
					.price {
						font-size: 24rpx;
						color: red;
						float: right;
					}
	
					.end {
						font-size: 24rpx;
						color: blue;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}
	
			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;
	
				.text-sm {
					font-size: 24upx;
				}
	
				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}
	
				.uni-row {
					flex-direction: row;
				}
	
				.align-center {
					align-items: center;
				}
	
				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
	
		:last-child {
			// border-bottom: 1rpx solid #fff;
		}
	}
</style>
