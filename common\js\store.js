import Vue from 'vue'
import Vuex from 'vuex'
/* 全局引入 请求拦截器，对请求做一些统一的配置 */
import {
	http
} from './request.js'

//挂载Vuex
Vue.use(Vuex)

//创建VueX对象
const store = new Vuex.Store({
    state:{
        //存放的键值对就是所要管理的状态
        statusBar : (() => {
			try {
				// 使用新API获取状态栏高度
				const windowInfo = uni.getWindowInfo();
				return windowInfo.statusBarHeight || 20;
			} catch (error) {
				// 降级到旧API
				try {
					return uni.getSystemInfoSync().statusBarHeight || 20;
				} catch (e) {
					return 20; // 默认值
				}
			}
		})(),
		login : false,		// 存放登录状态，用于判断是否为手机号登录
		fileUrl:"http://skytest.utools.club/api/upload/alioss",
		user: {
			token: null,
			userInfo: null,
			member: null,
		},
		systemInfo:'ios'
    },
	getters: {
		// 添加缺失的hasLogin getter
		hasLogin: state => {
			return !!(state.user && state.user.token);
		},
		// 获取用户信息
		userInfo: state => {
			return state.user ? state.user.userInfo : null;
		},
		// 获取用户会员信息
		userMember: state => {
			return state.user ? state.user.member : null;
		},
		// 获取登录状态
		isLoggedIn: state => {
			return state.login || !!(state.user && state.user.token);
		}
	},
	mutations:{
		// 手机登录
		logging(store){
			store.state.login = true;
		},
		/**
			 * 设置 token
			 * @param {Object} store
			 * @param {Object} data {token,saveStorage}
			 */
			setUserToken(store, data) {
				store.user.token = data.token;
				if (data.saveStorage) {
					uni.setStorage({
						key: "token",
						data: data.token
					})
				}
			},
		
			/**
			 * 设置 用户信息
			 * @param {Object} store
			 * @param {Object} data {token,saveStorage}
			 */
			setUserInfo(store, data) {
				store.user.userInfo =  data.userInfo;
				if (data.saveStorage) {
					uni.setStorage({
						key: "userInfo",
						data: data.userInfo
					})
				}
			},
		
			/**
			 * 设置用户会员信息
			 * @param {Object} store
			 * @param {Object} memer
			 */
			setUserMember(store, member) {
				console.log('setUserMember',member);
				store.user.member = member;
			},
			
			/**
			 * 设置 设备信息
			 * @param {Object} store
			 * @param {Object} data {token,saveStorage}
			 */
			setSystemInfo(store, data) {
				store.systemInfo =  data;
				if (data.saveStorage) {
					uni.setStorage({
						key: "systemInfo",
						data: data
					})
				}
			},
		},
		actions:{
			/**
			 * 刷新会员信息
			 * @param {Object} store
			 */
			refreshUserMember(context) {
				console.log('refreshUserMember',context);
				return http.get('/v1/member').then(res => {
					// 安全地访问嵌套属性
					const memberData = res.data && res.data.data && res.data.data.member ? res.data.data.member : null;
					context.commit('setUserMember', memberData);
					console.log('用户会员信息:', context.state.user ? context.state.user.member : null);
					return Promise.resolve(res.data && res.data.data ? res.data.data : {});
				}).catch(error => {
					console.error('获取用户会员信息失败:', error);
					context.commit('setUserMember', null);
					return Promise.reject(error);
				});
			}
		}
	
})

export default store