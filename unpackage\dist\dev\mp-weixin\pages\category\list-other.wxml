<gui-page class="vue-ref" vue-id="c846f186-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#ffffff;" slot="gBody"><view class="rich-text"><u-parse vue-id="{{('c846f186-2')+','+('c846f186-1')}}" content="{{text}}" selectable="{{true}}" bind:__l="__l"></u-parse></view><view style="text-align:center;"><image style="margin:20rpx 0;width:300rpx;" src="{{image}}" mode="widthFix" show-menu-by-longpress="1"></image></view><view style="margin-top:20rpx;background-color:#f3f3f3;"><view class="h2title" style="padding-top:20rpx;">大家都在学</view><view class="content"><view class="list-box"><block wx:for="{{hotList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['hotList','',index]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><block wx:if="{{item.status==1}}"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view></block><block wx:else><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view></block><view class="info-box lp-flex-column"><view class="title">{{item.name}}</view><view class="des">{{item.des}}</view><view class="end"><text style="text-align:right;float:right;">详情</text></view></view></view></view></block></view></view></view></view></gui-page>