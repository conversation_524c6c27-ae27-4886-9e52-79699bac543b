<template>
    <view style="height: 100vh;">
		<!-- <web-view src="./up.html"></web-view> -->
        <web-view :webview-styles="webviewStyles" :src="data.link"></web-view>
    </view>
</template>

<script>
    export default {
		onLoad(option) {
			this.data = JSON.parse(decodeURIComponent(option.data));
			if(this.data.haveData){
				console.log(this.data,"02");
				this.data.link = this.data.link + "?data=" + encodeURIComponent(this.data.datas);
			}
		},
        data() {
            return {
				data:{},
                webviewStyles: {
                    progress: {
                        color: '#FF3333'
                    }
                }
            }
        }
    }
</script>

<style>

</style>