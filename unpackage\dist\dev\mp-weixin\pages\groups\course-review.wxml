<gui-page vue-id="33db5d3e-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-43bcbcc8 vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1 data-v-43bcbcc8" style="background-color:#F8F8F8;" slot="gBody"><view class="filter-bar data-v-43bcbcc8"><block wx:for="{{filterList}}" wx:for-item="filter" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeFilter',['$0'],[[['filterList','',index,'value']]]]]]]}}" class="{{['filter-item','data-v-43bcbcc8',(currentFilter===filter.value)?'active':'']}}" bindtap="__e">{{''+filter.label+''}}</view></block></view><view class="course-list data-v-43bcbcc8"><block wx:for="{{$root.l0}}" wx:for-item="course" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewCourse',['$0'],[[['filteredCourses','',index]]]]]]]}}" class="course-item data-v-43bcbcc8" bindtap="__e"><view class="course-cover data-v-43bcbcc8"><image src="{{course.$orig.cover}}" mode="aspectFill" class="data-v-43bcbcc8"></image><view class="course-duration data-v-43bcbcc8">{{course.$orig.duration}}</view></view><view class="course-info data-v-43bcbcc8"><view class="course-title data-v-43bcbcc8">{{course.$orig.title}}</view><view class="course-desc data-v-43bcbcc8">{{course.$orig.description}}</view><view class="course-meta data-v-43bcbcc8"><text class="{{['meta-tag','data-v-43bcbcc8',course.$orig.status]}}">{{course.m0}}</text><text class="meta-time data-v-43bcbcc8">{{course.$orig.studyTime}}</text></view><view class="course-progress data-v-43bcbcc8"><view class="progress-bar data-v-43bcbcc8"><view class="progress-fill data-v-43bcbcc8" style="{{'width:'+(course.$orig.progress+'%')+';'}}"></view></view><text class="progress-text data-v-43bcbcc8">{{course.$orig.progress+"%"}}</text></view></view><view class="course-action data-v-43bcbcc8"><block wx:if="{{course.$orig.status==='learning'}}"><text class="iconfont icon-play data-v-43bcbcc8"></text></block><block wx:else><block wx:if="{{course.$orig.status==='completed'}}"><text class="iconfont icon-check data-v-43bcbcc8"></text></block><block wx:else><text class="iconfont icon-lock data-v-43bcbcc8"></text></block></block></view></view></block></view><block wx:if="{{$root.g0===0}}"><view class="empty-state data-v-43bcbcc8"><image src="/static/imgs/empty-course.png" mode="aspectFit" class="data-v-43bcbcc8"></image><text class="data-v-43bcbcc8">暂无课程内容</text></view></block></view></gui-page>