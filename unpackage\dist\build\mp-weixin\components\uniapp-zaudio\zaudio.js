(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uniapp-zaudio/zaudio"],{"2c7b":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return l})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,a=(e._self._c,e.audiolist.length),l=a>0&&"theme3"==e.theme?e.renderData("current_value"):null,u=a>0&&"theme3"==e.theme?e.renderData("duration_value"):null,r=a>0&&"theme3"==e.theme?e.renderData("paused"):null,i=a>0&&"theme3"==e.theme?e.renderData("coverImgUrl"):null,m=a>0&&"theme3"==e.theme&&e.loading?n("3052"):null,d=a>0&&"theme3"==e.theme&&!e.loading?e.renderData("paused"):null,o=a>0&&"theme3"==e.theme&&!e.loading&&d?n("1788"):null,h=a>0&&"theme3"==e.theme&&!e.loading&&!d?n("0e4a"):null,s=a>0&&"theme3"==e.theme?e.renderData("title"):null,c=a>0&&"theme3"==e.theme?e.renderData("singer"):null,f=a>0&&"theme3"==e.theme?e.renderData("current"):null,p=a>0&&"theme3"==e.theme?e.renderData("duration"):null,g=a>0&&"theme2"==e.theme?e.renderData("coverImgUrl"):null,D=a>0&&"theme2"==e.theme&&e.loading?n("3052"):null,v=a>0&&"theme2"==e.theme&&!e.loading?e.renderData("paused"):null,b=a>0&&"theme2"==e.theme&&!e.loading&&v?n("1788"):null,y=a>0&&"theme2"==e.theme&&!e.loading&&!v?n("0e4a"):null,$=a>0&&"theme2"==e.theme?e.renderData("title"):null,z=a>0&&"theme2"==e.theme?e.renderData("current"):null,I=a>0&&"theme2"==e.theme?e.renderData("duration"):null,P=a>0&&"theme2"==e.theme?e.renderData("singer"):null,_=a>0&&"theme1"==e.theme?e.renderData("paused"):null,O=a>0&&"theme1"==e.theme?e.renderData("coverImgUrl"):null,S=a>0&&"theme1"==e.theme?e.renderData("title"):null,k=a>0&&"theme1"==e.theme?e.renderData("singer"):null,x=a>0&&"theme1"==e.theme?e.renderData("current"):null,j=a>0&&"theme1"==e.theme?e.renderData("current_value"):null,U=a>0&&"theme1"==e.theme?e.renderData("duration_value"):null,w=a>0&&"theme1"==e.theme?e.renderData("duration"):null,J=a>0&&"theme1"==e.theme?n("f5cd"):null,C=a>0&&"theme1"==e.theme?n("e1d9"):null,E=a>0&&"theme1"==e.theme&&e.loading?n("be51"):null,M=a>0&&"theme1"==e.theme&&!e.loading?e.renderData("paused"):null,T=a>0&&"theme1"==e.theme&&!e.loading&&M?n("ccb7"):null,q=a>0&&"theme1"==e.theme&&!e.loading&&!M?n("85c8"):null,A=a>0&&"theme1"==e.theme?n("e1d9"):null,B=a>0&&"theme1"==e.theme?n("b51b"):null;e.$mp.data=Object.assign({},{$root:{g0:a,m0:l,m1:u,m2:r,m3:i,m4:m,m5:d,m6:o,m7:h,m8:s,m9:c,m10:f,m11:p,m12:g,m13:D,m14:v,m15:b,m16:y,m17:$,m18:z,m19:I,m20:P,m21:_,m22:O,m23:S,m24:k,m25:x,m26:j,m27:U,m28:w,m29:J,m30:C,m31:E,m32:M,m33:T,m34:q,m35:A,m36:B}})},l=[]},"5bea":function(e,t,n){"use strict";var a=n("d4f8"),l=n.n(a);l.a},"7e5e":function(e,t,n){"use strict";n.r(t);var a=n("2c7b"),l=n("af4f");for(var u in l)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return l[e]}))}(u);n("5bea");var r=n("828b"),i=Object(r["a"])(l["default"],a["b"],a["c"],!1,null,"dcf7ba36",null,!1,a["a"],void 0);t["default"]=i.exports},af4f:function(e,t,n){"use strict";n.r(t);var a=n("fa21"),l=n.n(a);for(var u in a)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(u);t["default"]=l.a},d4f8:function(e,t,n){},fa21:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={props:{theme:{type:String,default:"theme1"},themeColor:{type:String,default:"#42b983"}},data:function(){return{playinfo:this.$zaudio.playinfo,audiolist:this.$zaudio.audiolist,paused:this.$zaudio.paused,renderIsPlay:this.$zaudio.renderIsPlay,audio:this.$zaudio.renderinfo,loading:this.$zaudio.loading,action:Symbol("zaudio")}},computed:{renderData:function(){var e=this;return function(t){return e.renderIsPlay?"paused"==t?e.paused:e.playinfo[t]:"paused"==t||e.audio[t]}}},mounted:function(){var e=this;this.$nextTick((function(){var t=e.action;e.$zaudio.syncStateOn(t,(function(t){var n=t.audiolist,a=t.paused,l=t.playinfo,u=t.renderIsPlay,r=t.renderinfo,i=t.loading;e.audiolist=n,e.paused=a,e.playinfo=l,e.renderIsPlay=u,e.audio=r,e.loading=i}))}))},methods:{operate:function(){this.$zaudio.operate()},change:function(e){this.renderIsPlay&&this.$zaudio.seek(e.detail.value)},stepPlay:function(e){this.$zaudio.stepPlay(e)},changeplay:function(e){this.$zaudio.changeplay(e)}},beforeDestroy:function(){var e=this.action;this.$zaudio.syncStateOff(e)}};t.default=a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uniapp-zaudio/zaudio-create-component',
    {
        'components/uniapp-zaudio/zaudio-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7e5e"))
        })
    },
    [['components/uniapp-zaudio/zaudio-create-component']]
]);
