<template>
	<view class="root-box">
		<view class="content-box">
			<rich-text class="content" :nodes="comment.content" @longpress="onSetClipboardDataHandler(comment.content)"></rich-text>
		</view>

		<view class="file-box" v-if="comment.file != ''">
			<view class="left-box">
				<image :src="comment.author.avatar"></image>
			</view>
			<view class="center-box">
				<view class="name-box">
					<text>老师 ● </text>
					<text class="name">{{comment.author.name}}</text>
				</view>
				<view class="job">
					<text>{{comment.author.job}}</text>
				</view>
				<view class="intro">
					<text>{{comment.author.intro}}</text>
				</view>
			</view>
			<view class="right-box">
				<lp-audio-player :mini="true" :audio="{src:comment.file}"></lp-audio-player>
			</view>
		</view>
	</view>
</template>

<script>
	import lpAudioPlayer from '@/components/audio-player/audio-player.vue';
	import util from '@/common/js/util.js';

	export default {
		components: {
			lpAudioPlayer
		},
		props: {
			comment: {
				type: Object,
				default: null
			}
		},
		methods:{
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onSetClipboardDataHandler: function(text) {
				console.log('text'+text);
				console.log('text2'+util.html.getPureContent(text))
				uni.setClipboardData({
				    data: util.html.getPureContent(text),
				    success: function () {
				        uni.showToast({
				        	icon:'none',
							title:'已复制'
				        })
				    }
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		display: flex;
		flex-direction: column;
		font-size: 28rpx;
		border: solid 1px #eee;
		padding: 30rpx;
		background: #f5f5f5;
		border-radius: 10rpx;

		.content-box {
			margin-bottom: 30rpx;
			min-height: 100rpx;

			.content {
				color: #999;
			}
		}

		.file-box {
			display: flex;
			align-items: center;
			padding: 30rpx;
			border-radius: 10rpx;
			background: #fff;


			.left-box {
				width: 96rpx;
				height: 96rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.center-box {
				flex: 1;
				color: #aaa;
				margin: 0 30rpx;

				.name {
					margin-left: 10rpx;
					color: #666;
					font-weight: bold;
				}
			}

			.right-box {}
		}
	}
</style>
