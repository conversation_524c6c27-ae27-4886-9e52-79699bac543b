-- GST日语培训班 - 课程分类测试数据
-- 这个文件用于测试分类导入功能

-- 插入顶级分类
INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(1, '基础日语', 0, 1, '日语入门基础课程分类', 1, 1);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(2, '进阶日语', 0, 2, '日语进阶提高课程分类', 1, 1);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(3, '商务日语', 0, 3, '商务场景日语课程分类', 1, 1);

-- 插入二级分类
INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(4, '五十音图', 1, 1, '日语假名学习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(5, '基础语法', 1, 2, '日语基础语法学习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(6, '基础词汇', 1, 3, '日语基础词汇学习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(7, '中级语法', 2, 1, '日语中级语法学习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(8, '中级词汇', 2, 2, '日语中级词汇学习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(9, '听力训练', 2, 3, '日语听力专项训练', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(10, '商务会话', 3, 1, '商务场景对话练习', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(11, '商务邮件', 3, 2, '商务邮件写作', 1, 2);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(12, '商务礼仪', 3, 3, '日本商务礼仪文化', 1, 2);

-- 插入三级分类
INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(13, '平假名', 4, 1, '平假名学习', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(14, '片假名', 4, 2, '片假名学习', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(15, '助词用法', 5, 1, '日语助词详解', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(16, '动词变位', 5, 2, '动词变位规则', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(17, '形容词变化', 5, 3, '形容词变化规则', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(18, '常用词汇', 6, 1, '日常生活常用词汇', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(19, '主题词汇', 6, 2, '按主题分类的词汇', 1, 3);

INSERT INTO courses_classify (id, name, pid, sort, description, status, level) VALUES 
(20, '敬语表达', 7, 1, '敬语系统详解', 1, 3);
