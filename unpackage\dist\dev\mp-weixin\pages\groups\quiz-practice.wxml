<gui-page vue-id="26e44275-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-5012569e vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1 data-v-5012569e" style="background-color:#F8F8F8;" slot="gBody"><block wx:if="{{!currentPractice}}"><view class="practice-list data-v-5012569e"><view class="list-header data-v-5012569e"><view class="header-title data-v-5012569e">答题练习</view><view class="header-desc data-v-5012569e">选择一个练习开始训练</view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['startPractice',['$0'],[[['practiceList','',index]]]]]]]}}" class="practice-item data-v-5012569e" bindtap="__e"><view class="{{['item-icon','data-v-5012569e',item.$orig.type]}}"><text class="{{['iconfont','data-v-5012569e',item.m0]}}"></text></view><view class="item-info data-v-5012569e"><view class="item-title data-v-5012569e">{{item.$orig.title}}</view><view class="item-desc data-v-5012569e">{{item.$orig.description}}</view><view class="item-meta data-v-5012569e"><text class="meta-count data-v-5012569e">{{item.$orig.questionCount+"题"}}</text><text class="meta-type data-v-5012569e">{{item.m1}}</text><text class="{{['meta-difficulty','data-v-5012569e',item.$orig.difficulty]}}">{{item.m2}}</text></view></view><view class="item-status data-v-5012569e"><block wx:if="{{item.$orig.bestScore!==null}}"><text class="status-score data-v-5012569e">{{item.$orig.bestScore+"分"}}</text></block><block wx:else><text class="status-new data-v-5012569e">NEW</text></block></view></view></block></view></block><block wx:else><block wx:if="{{!practiceFinished}}"><view class="practice-content data-v-5012569e"><view class="progress-header data-v-5012569e"><view class="progress-info data-v-5012569e"><text class="data-v-5012569e">{{currentQuestionIndex+1+" / "+$root.g0}}</text><block wx:if="{{timeLimit>0}}"><text class="time-left data-v-5012569e">{{"剩余时间: "+$root.m3}}</text></block></view><view class="progress-bar data-v-5012569e"><view class="progress-fill data-v-5012569e" style="{{'width:'+(progressPercent+'%')+';'}}"></view></view></view><view class="question-content data-v-5012569e"><view class="question-header data-v-5012569e"><view class="question-type data-v-5012569e">{{$root.m4}}</view><view class="question-points data-v-5012569e">{{currentQuestion.points+"分"}}</view></view><view class="question-title data-v-5012569e">{{currentQuestion.question}}</view><block wx:if="{{currentQuestion.image}}"><view class="question-image data-v-5012569e"><image src="{{currentQuestion.image}}" mode="aspectFit" class="data-v-5012569e"></image></view></block><block wx:if="{{currentQuestion.type==='choice'}}"><view class="question-options data-v-5012569e"><block wx:for="{{$root.l1}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectAnswer',[index]]]]]}}" class="{{['option-item','data-v-5012569e',(selectedAnswer===index)?'selected':'',(showResult&&index===currentQuestion.correctAnswer)?'correct':'',(showResult&&selectedAnswer===index&&index!==currentQuestion.correctAnswer)?'wrong':'']}}" bindtap="__e"><view class="option-label data-v-5012569e">{{option.g1}}</view><view class="option-text data-v-5012569e">{{option.$orig}}</view></view></block></view></block><block wx:else><block wx:if="{{currentQuestion.type==='fill'}}"><view class="question-input data-v-5012569e"><input class="input-field data-v-5012569e" placeholder="{{currentQuestion.placeholder||'请输入答案'}}" disabled="{{showResult}}" data-event-opts="{{[['input',[['__set_model',['','inputAnswer','$event',[]]]]]]}}" value="{{inputAnswer}}" bindinput="__e"/></view></block><block wx:else><block wx:if="{{currentQuestion.type==='judge'}}"><view class="question-judge data-v-5012569e"><view data-event-opts="{{[['tap',[['selectAnswer',[true]]]]]}}" class="{{['judge-option','data-v-5012569e',(selectedAnswer===true)?'selected':'']}}" bindtap="__e"><text class="judge-icon data-v-5012569e">○</text><text class="judge-text data-v-5012569e">正确</text></view><view data-event-opts="{{[['tap',[['selectAnswer',[false]]]]]}}" class="{{['judge-option','data-v-5012569e',(selectedAnswer===false)?'selected':'']}}" bindtap="__e"><text class="judge-icon data-v-5012569e">×</text><text class="judge-text data-v-5012569e">错误</text></view></view></block></block></block><block wx:if="{{showResult&&currentQuestion.explanation}}"><view class="question-explanation data-v-5012569e"><view class="explanation-title data-v-5012569e">解析</view><view class="explanation-content data-v-5012569e">{{currentQuestion.explanation}}</view></view></block></view><view class="action-buttons data-v-5012569e"><button class="btn-secondary data-v-5012569e" disabled="{{currentQuestionIndex===0}}" data-event-opts="{{[['tap',[['prevQuestion',['$event']]]]]}}" bindtap="__e">上一题</button><block wx:if="{{!showResult}}"><button data-event-opts="{{[['tap',[['checkAnswer',['$event']]]]]}}" class="btn-check data-v-5012569e" bindtap="__e">检查答案</button></block><block wx:if="{{showResult&&!isLastQuestion}}"><button data-event-opts="{{[['tap',[['nextQuestion',['$event']]]]]}}" class="btn-primary data-v-5012569e" bindtap="__e">下一题</button></block><block wx:if="{{showResult&&isLastQuestion}}"><button data-event-opts="{{[['tap',[['finishPractice',['$event']]]]]}}" class="btn-primary data-v-5012569e" bindtap="__e">完成练习</button></block></view></view></block></block><block wx:if="{{practiceFinished}}"><view class="result-page data-v-5012569e"><view class="result-header data-v-5012569e"><view class="{{['result-score','data-v-5012569e',$root.m5]}}">{{finalScore}}</view><view class="result-text data-v-5012569e">分</view><view class="result-grade data-v-5012569e">{{$root.m6}}</view></view><view class="result-details data-v-5012569e"><view class="detail-item data-v-5012569e"><text class="detail-label data-v-5012569e">正确题数</text><text class="detail-value data-v-5012569e">{{correctCount+" / "+totalQuestions}}</text></view><view class="detail-item data-v-5012569e"><text class="detail-label data-v-5012569e">正确率</text><text class="detail-value data-v-5012569e">{{correctRate+"%"}}</text></view><view class="detail-item data-v-5012569e"><text class="detail-label data-v-5012569e">用时</text><text class="detail-value data-v-5012569e">{{practiceTime}}</text></view><view class="detail-item data-v-5012569e"><text class="detail-label data-v-5012569e">平均每题</text><text class="detail-value data-v-5012569e">{{averageTime}}</text></view></view><view class="result-actions data-v-5012569e"><button data-event-opts="{{[['tap',[['reviewAnswers',['$event']]]]]}}" class="btn-secondary data-v-5012569e" bindtap="__e">查看解析</button><button data-event-opts="{{[['tap',[['restartPractice',['$event']]]]]}}" class="btn-primary data-v-5012569e" bindtap="__e">重新练习</button><button data-event-opts="{{[['tap',[['backToList',['$event']]]]]}}" class="btn-secondary data-v-5012569e" bindtap="__e">返回列表</button></view></view></block></view></gui-page>