<gui-page class="vue-ref" vue-id="e8adf606-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#F8F8F8;" slot="gBody"><block wx:if="{{$root.g0===0}}"><view class="empty"><image src="/static/emptyCart.jpg" mode="aspectFit"></image><view class="empty-tips">空空如也<navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator></view></view></block><block wx:else><view class="content"><view class="list-box"><block wx:for="{{goodsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['goodsList','',index]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item.title}}</view><view class="des">{{item.des}}</view></view></view></view></block></view><uni-load-more vue-id="{{('e8adf606-2')+','+('e8adf606-1')}}" status="{{loadingType}}" bind:__l="__l"></uni-load-more><view data-event-opts="{{[['tap',[['toggleCateMask',['$event']]]]]}}" class="{{['cate-mask',cateMaskState===0?'none':cateMaskState===1?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['stopPrevent',['$event']]]],['touchmove',[['stopPrevent',['$event']]]]]}}" class="cate-content" catchtap="__e" catchtouchmove="__e"><scroll-view class="cate-list" scroll-y="{{true}}"><block wx:for="{{cateList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view><view class="cate-item b-b two">{{item.name}}</view><block wx:for="{{item.child}}" wx:for-item="tItem" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['changeCate',['$0'],[[['cateList','id',item.id],['child','id',tItem.id]]]]]]]}}" class="{{['cate-item','b-b',(tItem.id==cateId)?'active':'']}}" bindtap="__e">{{''+tItem.name+''}}</view></block></view></block></scroll-view></view></view></view></block></view></gui-page>