/* 学习小组相关图标样式 */

.group-icon-font {
  font-family: 'iconfont';
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 小组图标 */
.icon-group1:before { content: "\e601"; }
.icon-group2:before { content: "\e602"; }
.icon-group3:before { content: "\e603"; }
.icon-group4:before { content: "\e604"; }
.icon-group5:before { content: "\e605"; }

/* 功能图标 */
.icon-book:before { content: "\e606"; }
.icon-headphone:before { content: "\e607"; }
.icon-edit:before { content: "\e608"; }
.icon-play:before { content: "\e609"; }
.icon-pause:before { content: "\e60a"; }
.icon-check:before { content: "\e60b"; }
.icon-lock:before { content: "\e60c"; }
.icon-arrow-right:before { content: "\e60d"; }
.icon-video:before { content: "\e60e"; }
.icon-practice:before { content: "\e60f"; }
.icon-bulb:before { content: "\e610"; }
.icon-word:before { content: "\e611"; }
.icon-read:before { content: "\e612"; }
.icon-star:before { content: "\e613"; }

/* 小组主题色彩 */
.group-theme-1 {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --bg-color: rgba(102, 126, 234, 0.1);
}

.group-theme-2 {
  --primary-color: #f093fb;
  --secondary-color: #f5576c;
  --bg-color: rgba(240, 147, 251, 0.1);
}

.group-theme-3 {
  --primary-color: #4facfe;
  --secondary-color: #00f2fe;
  --bg-color: rgba(79, 172, 254, 0.1);
}

.group-theme-4 {
  --primary-color: #43e97b;
  --secondary-color: #38f9d7;
  --bg-color: rgba(67, 233, 123, 0.1);
}

.group-theme-5 {
  --primary-color: #fa709a;
  --secondary-color: #fee140;
  --bg-color: rgba(250, 112, 154, 0.1);
}

/* 通用样式类 */
.gradient-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-bg-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-shadow-hover {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
  transition: all 0.3s ease;
}

/* 按钮样式 */
.btn-gradient-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.btn-gradient-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
}

/* 进度条样式 */
.progress-gradient-1 .progress-fill {
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.progress-gradient-2 .progress-fill {
  background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}

.progress-gradient-3 .progress-fill {
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}

/* 状态指示器 */
.status-active {
  background: #e8f5e8;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background: #f0f0f0;
  color: #999;
}

/* 难度等级样式 */
.difficulty-easy {
  background: #e8f5e8;
  color: #52c41a;
}

.difficulty-medium {
  background: #fff7e6;
  color: #fa8c16;
}

.difficulty-hard {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 分数等级样式 */
.score-excellent {
  color: #52c41a;
}

.score-good {
  color: #2094CE;
}

.score-pass {
  color: #fa8c16;
}

.score-fail {
  color: #ff4d4f;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .group-card {
    flex-direction: column;
    text-align: center;
  }
  
  .group-icon {
    margin-bottom: 20rpx;
    margin-right: 0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .card-shadow {
    box-shadow: 0 4rpx 20rpx rgba(255, 255, 255, 0.1);
  }
  
  .group-card {
    background: #1f1f1f;
    color: #fff;
  }
  
  .permission-info {
    background: #2a2a2a;
  }
}
