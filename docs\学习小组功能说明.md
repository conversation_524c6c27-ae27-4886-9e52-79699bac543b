# 学习小组功能说明

## 功能概述

学习小组功能是在原有日语学习小程序基础上新增的高级功能，为特定用户提供更深入的学习体验。该功能包含5个独立的学习小组，每个小组都有完整的课程回顾和练习系统。

## 功能特点

### 1. 权限控制
- 基于白名单的访问控制
- 只有授权用户才能访问学习小组功能
- 支持多种用户标识（手机号、邮箱、用户ID等）

### 2. 学习小组
- 5个独立的学习小组
- 每个小组有独特的主题和难度设置
- 支持学习进度跟踪和统计

### 3. 课程回顾
- 完整的课程列表展示
- 支持按状态筛选（全部、学习中、已完成、未开始）
- 课程进度可视化
- 视频播放和学习记录

### 4. 课后练习
- 听力练习：音频播放、题目作答、成绩统计
- 答题练习：选择题、填空题、判断题等多种题型
- 实时答题反馈和解析
- 学习建议和进度分析

## 技术架构

### 页面结构
```
pages/groups/
├── index.vue              # 学习小组主页
├── group-detail.vue       # 小组详情页
├── course-review.vue      # 课程回顾页
├── practice.vue           # 练习主页
├── listening-practice.vue # 听力练习页
└── quiz-practice.vue      # 答题练习页
```

### 组件结构
```
components/
└── lp-auth-content.vue    # 权限验证组件
```

### 工具模块
```
utils/
└── auth.js               # 权限管理工具
```

### 样式文件
```
static/css/
├── group-icons.css       # 图标和主题样式
└── group-common.css      # 通用样式库
```

## 使用说明

### 1. 权限配置

在 `utils/auth.js` 中配置允许访问的用户：

```javascript
const GROUP_ACCESS_WHITELIST = [
    '13800138000',        // 手机号
    '<EMAIL>',  // 邮箱
    // 添加更多授权用户...
];
```

### 2. 页面导航

用户可以通过底部导航栏的"小组"tab进入学习小组功能：

1. 点击"小组"tab
2. 系统自动验证用户权限
3. 有权限的用户可以看到5个学习小组列表
4. 无权限的用户会看到权限提示页面

### 3. 学习流程

1. **选择小组**：从5个小组中选择适合的小组
2. **查看详情**：了解小组信息、学习进度和统计数据
3. **课程回顾**：查看和学习课程内容
4. **课后练习**：完成听力和答题练习
5. **查看成绩**：查看练习结果和学习建议

## 权限管理

### 用户权限验证

系统支持多种用户标识进行权限验证：

- 手机号码
- 邮箱地址
- 用户ID
- 微信OpenID

### 权限检查流程

1. 用户访问学习小组功能
2. 系统获取当前登录用户信息
3. 调用权限验证工具检查用户是否在白名单中
4. 根据验证结果显示相应内容或权限提示

### 权限管理API

```javascript
import { checkGroupAccess, addToWhitelist, removeFromWhitelist } from '@/utils/auth.js';

// 检查用户权限
const hasPermission = checkGroupAccess(userInfo);

// 添加用户到白名单
addToWhitelist('13900139000');

// 从白名单移除用户
removeFromWhitelist('13900139000');
```

## 样式定制

### 主题色彩

每个学习小组都有独特的主题色彩：

- 小组1：蓝紫渐变 (#667eea → #764ba2)
- 小组2：粉红渐变 (#f093fb → #f5576c)
- 小组3：蓝青渐变 (#4facfe → #00f2fe)
- 小组4：绿青渐变 (#43e97b → #38f9d7)
- 小组5：粉黄渐变 (#fa709a → #fee140)

### 通用样式类

系统提供了丰富的通用样式类，包括：

- 布局类：`.flex`, `.grid`, `.container`
- 间距类：`.m-1`, `.p-2`, `.mt-3`
- 文本类：`.text-center`, `.text-primary`, `.font-bold`
- 按钮类：`.btn`, `.btn-primary`, `.btn-secondary`
- 卡片类：`.card`, `.card-shadow`

## 数据结构

### 学习小组数据
```javascript
{
    id: 1,
    name: "初级日语小组",
    description: "适合日语初学者",
    courseCount: 12,
    memberCount: 25,
    progress: 60,
    status: "active"
}
```

### 课程数据
```javascript
{
    id: 1,
    title: "第1课：五十音图",
    description: "学习日语基础发音",
    cover: "/static/imgs/course1.jpg",
    duration: "25:30",
    status: "completed",
    progress: 100
}
```

### 练习数据
```javascript
{
    id: 1,
    title: "语法基础练习",
    type: "grammar",
    questionCount: 10,
    difficulty: "easy",
    bestScore: 92
}
```

## 注意事项

1. **权限安全**：确保只有授权用户才能访问学习小组功能
2. **数据同步**：定期同步权限配置和学习数据
3. **性能优化**：合理使用缓存和懒加载
4. **用户体验**：提供清晰的权限提示和操作指引
5. **兼容性**：确保在不同设备和微信版本下正常运行

## 扩展功能

### 未来可扩展的功能：

1. **实时聊天**：小组内成员交流
2. **作业提交**：在线提交和批改作业
3. **直播课程**：实时在线授课
4. **学习排行榜**：激励用户学习
5. **证书系统**：完成学习后颁发证书

## 技术支持

如有技术问题或需要功能扩展，请联系开发团队。

---

*最后更新时间：2024年1月*
