<view class="content"><view class="{{['mix-list-cell',border]}}" hover-class="cell-hover" hover-stay-time="{{50}}" data-event-opts="{{[['tap',[['eventClick',['$event']]]]]}}" bindtap="__e"><block wx:if="{{icon}}"><text class="{{['cell-icon','yticon',icon]}}" style="{{'color:'+(iconColor)+';'}}"></text></block><text class="cell-tit clamp">{{title}}</text><block wx:if="{{tips}}"><text class="cell-tip">{{tips}}</text></block><text class="{{['cell-more','yticon',typeList[navigateType]]}}"></text></view></view>