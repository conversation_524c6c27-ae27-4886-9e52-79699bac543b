<template>
	<view class="common-image" :style="containerStyle">
		<!-- 图片 -->
		<image 
			v-if="!error && !loading"
			:src="optimizedSrc" 
			:mode="mode"
			:lazy-load="lazyLoad"
			:show-menu-by-longpress="showMenuByLongpress"
			@load="handleLoad"
			@error="handleError"
			:style="imageStyle"
			class="image-content"
		/>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="image-loading" :style="containerStyle">
			<view class="loading-content">
				<view class="loading-spinner" v-if="showLoadingSpinner"></view>
				<text class="loading-text" v-if="loadingText">{{loadingText}}</text>
			</view>
		</view>
		
		<!-- 错误状态 -->
		<view v-if="error" class="image-error" :style="containerStyle" @click="retry">
			<view class="error-content">
				<text class="error-icon">⚠️</text>
				<text class="error-text">{{errorText}}</text>
				<text class="retry-text" v-if="showRetry">点击重试</text>
			</view>
		</view>
		
		<!-- 占位图 -->
		<view v-if="showPlaceholder && !src" class="image-placeholder" :style="containerStyle">
			<text class="placeholder-icon">🖼️</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CommonImage',
	props: {
		// 图片地址
		src: {
			type: String,
			default: ''
		},
		// 宽度
		width: {
			type: [String, Number],
			default: '100%'
		},
		// 高度
		height: {
			type: [String, Number],
			default: 'auto'
		},
		// 显示模式
		mode: {
			type: String,
			default: 'aspectFill'
		},
		// 是否懒加载
		lazyLoad: {
			type: Boolean,
			default: true
		},
		// 是否显示长按菜单
		showMenuByLongpress: {
			type: Boolean,
			default: false
		},
		// 圆角
		borderRadius: {
			type: [String, Number],
			default: 0
		},
		// 是否显示加载状态
		showLoading: {
			type: Boolean,
			default: true
		},
		// 加载文本
		loadingText: {
			type: String,
			default: ''
		},
		// 是否显示加载动画
		showLoadingSpinner: {
			type: Boolean,
			default: true
		},
		// 错误文本
		errorText: {
			type: String,
			default: '加载失败'
		},
		// 是否显示重试
		showRetry: {
			type: Boolean,
			default: true
		},
		// 是否显示占位图
		showPlaceholder: {
			type: Boolean,
			default: true
		},
		// 最大重试次数
		maxRetries: {
			type: Number,
			default: 3
		},
		// 图片质量优化
		quality: {
			type: String,
			default: 'medium', // low, medium, high
			validator: value => ['low', 'medium', 'high'].includes(value)
		},
		// 是否启用WebP格式
		enableWebp: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			loading: false,
			error: false,
			retryCount: 0,
			actualSrc: ''
		};
	},
	computed: {
		// 容器样式
		containerStyle() {
			return {
				width: this.addUnit(this.width),
				height: this.addUnit(this.height),
				borderRadius: this.addUnit(this.borderRadius),
				overflow: 'hidden'
			};
		},
		
		// 图片样式
		imageStyle() {
			return {
				width: '100%',
				height: '100%',
				borderRadius: this.addUnit(this.borderRadius)
			};
		},
		
		// 优化后的图片地址
		optimizedSrc() {
			if (!this.src) return '';
			
			let url = this.src;
			
			// 如果是网络图片，添加优化参数
			if (url.startsWith('http')) {
				url = this.addImageOptimization(url);
			}
			
			return url;
		}
	},
	watch: {
		src: {
			handler(newSrc) {
				if (newSrc && newSrc !== this.actualSrc) {
					this.loadImage();
				}
			},
			immediate: true
		}
	},
	methods: {
		// 加载图片
		loadImage() {
			if (!this.src) return;
			
			this.loading = this.showLoading;
			this.error = false;
			this.actualSrc = this.src;
		},
		
		// 图片加载成功
		handleLoad(e) {
			this.loading = false;
			this.error = false;
			this.retryCount = 0;
			this.$emit('load', e);
		},
		
		// 图片加载失败
		handleError(e) {
			this.loading = false;
			this.error = true;
			this.$emit('error', e);
			
			// 记录错误日志
			console.error('图片加载失败:', this.src, e);
		},
		
		// 重试加载
		retry() {
			if (this.retryCount >= this.maxRetries) {
				uni.showToast({
					title: '图片加载失败，请稍后重试',
					icon: 'none'
				});
				return;
			}
			
			this.retryCount++;
			this.loadImage();
			
			this.$emit('retry', this.retryCount);
		},
		
		// 添加图片优化参数
		addImageOptimization(url) {
			// 这里可以根据不同的CDN服务商添加相应的优化参数
			// 例如：阿里云OSS、腾讯云COS、七牛云等
			
			const qualityMap = {
				low: 50,
				medium: 75,
				high: 90
			};
			
			const quality = qualityMap[this.quality] || 75;
			
			// 示例：阿里云OSS图片处理
			if (url.includes('aliyuncs.com')) {
				const separator = url.includes('?') ? '&' : '?';
				url += `${separator}x-oss-process=image/quality,q_${quality}`;
				
				if (this.enableWebp) {
					url += '/format,webp';
				}
			}
			
			// 示例：腾讯云COS图片处理
			else if (url.includes('myqcloud.com')) {
				const separator = url.includes('?') ? '&' : '?';
				url += `${separator}imageView2/2/q/${quality}`;
				
				if (this.enableWebp) {
					url += '/format/webp';
				}
			}
			
			return url;
		},
		
		// 添加单位
		addUnit(value) {
			if (typeof value === 'number') {
				return value + 'rpx';
			}
			if (typeof value === 'string') {
				if (/^\d+$/.test(value)) {
					return value + 'rpx';
				}
			}
			return value;
		},
		
		// 预加载图片
		preload() {
			return new Promise((resolve, reject) => {
				if (!this.src) {
					reject(new Error('No image source'));
					return;
				}
				
				const img = new Image();
				img.onload = () => resolve(img);
				img.onerror = reject;
				img.src = this.optimizedSrc;
			});
		}
	}
};
</script>

<style scoped>
.common-image {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
}

.image-content {
	display: block;
}

.image-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f8f8;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #e0e0e0;
	border-top: 4rpx solid #2094CE;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
}

.image-error {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	cursor: pointer;
}

.error-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.error-icon {
	font-size: 40rpx;
}

.error-text {
	font-size: 24rpx;
	color: #999;
}

.retry-text {
	font-size: 22rpx;
	color: #2094CE;
}

.image-placeholder {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f8f8;
}

.placeholder-icon {
	font-size: 60rpx;
	opacity: 0.3;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
