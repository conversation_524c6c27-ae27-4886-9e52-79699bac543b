<gui-page class="root-box data-v-acb34606" vue-id="e9b478ae-1" fullPage="{{true}}" customHeader="{{true}}" bind:__l="__l" vue-slots="{{['gHeader','gBody']}}"><view slot="gHeader" class="data-v-acb34606"><view class="gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding head-box data-v-acb34606"><gui-header-leading vue-id="{{('e9b478ae-2')+','+('e9b478ae-1')}}" class="data-v-acb34606" bind:__l="__l"></gui-header-leading><view class="gui-header-content nav-box data-v-acb34606"></view></view></view><view class="body-box lp-flex-column data-v-acb34606" slot="gBody"><view class="content-box lp-flex-column data-v-acb34606"><block wx:if="{{!isReady}}"><view class="loading data-v-acb34606">海报生成中...</view></block><block wx:else><view class="pop-box lp-flex-column lp-flex-center data-v-acb34606"><view class="poster-box lp-flex-center data-v-acb34606"><image class="posterImage data-v-acb34606" src="{{posterImage||''}}" mode="heightFix"></image></view><view class="lp-flex-center btn-box data-v-acb34606"><view class="lp-flex-column lp-flex-center btn data-v-acb34606"><button type="primary" size="mini" data-event-opts="{{[['tap',[['saveImage']]]]}}" catchtap="__e" class="data-v-acb34606"><view class="lp-flex-center icon-box data-v-acb34606"><text class="gui-icons data-v-acb34606"></text></view></button><text class="data-v-acb34606">保存图片</text></view><view class="lp-flex-column lp-flex-center btn data-v-acb34606"><button type="primary" open-type="share" size="mini" class="data-v-acb34606"><view class="lp-flex-center icon-box data-v-acb34606"><text class="gui-icons data-v-acb34606"></text></view></button><text class="data-v-acb34606">微信分享</text></view></view></view></block><view class="hideCanvasView data-v-acb34606"><canvas class="hideCanvas data-v-acb34606" style="{{'width:'+((poster.width||10)+'px')+';'+('height:'+((poster.height||10)+'px')+';')}}" id="default_PosterCanvasId" canvas-id="default_PosterCanvasId"></canvas></view></view></view></gui-page>