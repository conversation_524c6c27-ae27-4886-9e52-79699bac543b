@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */








/* 规划 H5 端字体 */



/* 图标加载 */
@font-face{
		font-family : "gui-iconfont";
		font-weight : normal;
		font-style  : normal; 
		src         : url(data:font/ttf;base64,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) format('truetype');
}
.gui-icons{font-family:"gui-iconfont"; font-style:normal;}

/* 页面主体结构 */
.gui-body{width:750rpx;}
/* 去除图片空白 */
.gui-img-in{font-size:0;}
.gui-flex{display:flex;}
.gui-border-box{box-sizing:border-box;}
.gui-body{width:750rpx; box-sizing:border-box;}

/* flex 布局 */
.gui-rows{flex-direction:row;}
.gui-row{flex-direction:row;}
.gui-columns{flex-direction:column;}
.gui-column{flex-direction:column;}
.gui-wrap{flex-direction:row; flex-wrap:wrap;}
.gui-nowrap{flex-direction:row; flex-wrap:nowrap;}
.gui-space-around{justify-content:space-around;}
.gui-space-between{justify-content:space-between;}
.gui-justify-content-start{justify-content:flex-start;}
.gui-justify-content-center{justify-content:center;}
.gui-justify-content-end{justify-content:flex-end;}
.gui-align-items-start{align-items:flex-start;}
.gui-align-items-center{align-items:center;}
.gui-align-items-end{align-items:flex-end;}
.gui-flex1{flex:1;}
/* 文本对齐 */
.gui-text-left{text-align:left;}
.gui-text-center{text-align:center;}
.gui-text-right{text-align:right;}
.gui-ellipsis{overflow:hidden;}
.gui-ellipsis{white-space:nowrap; text-overflow:ellipsis;}
.gui-block-text{display:block;}

/* 文本修饰 */
.gui-bold{font-weight:bold;}
.gui-line-through{text-decoration:line-through;}
.gui-underline{text-decoration:underline;}
.gui-italic{font-style:italic;}
/* 定位 */
.gui-relative{position:relative;}
.gui-absolute-lt{position:absolute; z-index:2; left:0; top:0;}
.gui-absolute-rt{position:absolute; z-index:2; right:0; top:0;}
.gui-absolute-lb{position:absolute; z-index:2; left:0; bottom:0;}
.gui-absolute-rb{position:absolute; z-index:2; right:0; bottom:0;}
.gui-fixed-lt{position:fixed; z-index:2; left:0; top:0;}
.gui-fixed-rt{position:fixed; z-index:2; right:0; top:0;}
.gui-fixed-lb{position:fixed; z-index:2; left:0; bottom:0;}
.gui-fixed-rb{position:fixed; z-index:2; right:0; bottom:0;}
/* 背景色 */
.gui-bg-red{background-color:#EE0A25 !important;}
.gui-bg-green{background-color:#07C160 !important;}
.gui-bg-blue{background-color:#008AFF !important;}
.gui-bg-orange{background-color:#ED6A0C !important;}
.gui-bg-yellow{background-color:#FBDE4E !important;}
.gui-bg-purple{background-color:#8A3FD4 !important;}
.gui-bg-white{background-color:#FFFFFF !important;}
.gui-bg-black{background-color:#2B2E3D !important;}
.gui-bg-black{background-color:#2B2E3D !important;}
.gui-bg-black2{background-color:#656565 !important;}
.gui-bg-black3{background-color:#969799 !important;}
.gui-bg-black4{background-color:#C8C9CC !important;}
.gui-bg-black-opacity7{background-color:rgba(0,0,0,0.7);}
.gui-bg-black-opacity5{background-color:rgba(0,0,0,0.5);}
.gui-bg-black-opacity3{background-color:rgba(0,0,0,0.3);}
.gui-gtbg-red{background-image:linear-gradient(45deg, #FF0066 , #D50000) !important;}
.gui-gtbg-blue{background-image:linear-gradient(45deg, #5887DF , #008AFF) !important;}
.gui-gtbg-green{background-image:linear-gradient(45deg, #39B55A , #8DC63E) !important;}
.gui-bg-gray{background-color:#F7F8FA !important;}
.gui-bg-white{background-color:#FFFFFF !important;}
/* 内置颜色 */
.gui-color-black{color:#2B2E3D !important;}
.gui-color-white{color:#FFFFFF !important;}
.gui-color-gray{color:rgba(69, 90, 100, 0.6) !important;}
.gui-color-gray-light{color:rgba(69, 90, 100, 0.3) !important;}
.gui-color-blue{color:#008AFF !important;}
.gui-color-red{color:#EE0A25 !important;}
.gui-color-orange{color:#ED6A0C !important;}
.gui-color-purple{color:#8A3FD4 !important;}
.gui-color-green{color:#39B55A !important;}
.gui-color-yellow{color:#FBDE4E !important;}
/* 边框 */
.gui-border{border:1rpx solid #F1F2F3;}
.gui-border-l{border-left:1rpx solid #F1F2F3;}
.gui-border-r{border-right:1rpx solid #F1F2F3;}
.gui-border-t{border-top:1rpx solid #F1F2F3;}
.gui-border-b{border-bottom:1rpx solid #F1F2F3;}
.gui-noborder{border:none !important;}

/* 自定义头部导航相关 */
.gui-header-content{width:100rpx; flex:1; text-align:center; margin-left:10rpx; margin-right:168rpx;}
.gui-headr-back{width:148rpx; line-height:40px; font-size:32rpx;}
/* 宫格布局 */
.gui-grids{padding:0;}
.gui-grids-items{width:138rpx;}
.gui-grids-items{box-sizing:border-box;}
.gui-grids-icon{height:80rpx; font-size:68rpx; line-height:80rpx; text-align:center;}
.gui-grids-icon{display:block; width:100%;}
.gui-grids-icon-img{width:80rpx; height:80rpx; border-radius:6rpx;}
.gui-grids-text{line-height:50rpx; text-align:center; font-size:24rpx; margin-top:2px;}
.gui-grids-text{display:block; width:100%;}

/* 列表样式 */
.gui-list-items, .gui-list-title{display:flex;}
.gui-list-icon, .gui-list-title-text, .gui-list-title-desc, .gui-list-body-desc, .gui-list-arrow-right{display:block;}
.gui-list-items{flex-direction:row; flex-wrap:nowrap; align-items:center; justify-content:center;}
.gui-list-icon{width:80rpx; height:80rpx; line-height:80rpx; text-align:center; font-size:44rpx;}
.gui-list-image{width:80rpx; height:80rpx; border-radius:80rpx; font-size:0;}
.gui-list-body{padding:25rpx 0; margin-left:25rpx; width:100rpx; flex:1;}
.gui-list-title{flex-direction:row; flex-wrap:nowrap; justify-content:space-between; align-items:center;}
.gui-list-one-line{line-height:60rpx !important;}
.gui-list-title-text{font-size:26rpx; line-height:44rpx;}
.gui-list-title-desc{font-size:22rpx; line-height:30rpx;}
.gui-list-body-desc{font-size:22rpx; line-height:32rpx;}
.gui-list-arrow-right{width:50rpx; height:50rpx; line-height:50rpx; font-size:30rpx; text-align:right;}
/* 徽章 */
.gui-badge{border-radius:38rpx; height:38rpx; line-height:38rpx; padding:0 13rpx; font-size:22rpx;}
.gui-badge-absolute{position:absolute; right:0rpx; top:4rpx; z-index:1;}
.gui-badge-point{width:20rpx; height:20rpx; border-radius:12rpx; position:absolute; right:4rpx; top:4rpx; z-index:1; background-color:#FF0000;}
.gui-badge-gender{width:38rpx; height:38rpx; border-radius:30rpx; text-align:center; font-size:22rpx !important; line-height:38rpx; position:absolute; right:6rpx; top:4rpx; z-index:1;}
/* 滚动区域 */
.gui-scroll-x{display:flex; white-space:nowrap;}
.gui-scroll-x-item, .gui-scroll-x-items{display:inline-flex; vertical-align:top;}
.gui-scroll-x{width:750rpx; flex-direction:row; overflow:hidden;}
/* 卡片列表 */
.gui-card-list{}
.gui-card-item{width:330rpx; margin-bottom:30rpx;}
.gui-card-img{width:330rpx; height:191rpx; overflow:hidden; position:relative;}
.gui-card-title{margin-top:3px;}
.gui-card-desc{margin-top:3px;}
.gui-card-tip{width:68rpx; height:40rpx; line-height:40rpx; text-align:center;}
.gui-card-mask-title{line-height:60rpx; height:60rpx; padding:0 10rpx; width:330rpx;}
/* 底部导航相关 */
.gui-footer-icon-buttons{width:80rpx; height:80rpx; margin:10rpx;}
.gui-footer-icon-buttons-icon{text-align:center; font-size:38rpx; line-height:50rpx;}
.gui-footer-icon-buttons-text{text-align:center; font-size:20rpx; line-height:30rpx;}
.gui-footer-large-buttons{margin-left:25rpx; margin-right:25rpx;}
.gui-footer-large-button{width:218rpx; height:80rpx;}
.gui-footer-large-button-text{line-height:80rpx !important;}
/* 通用标题布局 */
.gui-title-line{width:50rpx; height:1px; background-color:#E1E2E3; flex:1;}
.gui-title-text{line-height:60rpx;}
.gui-title-icon{width:50rpx; font-size:32rpx;}
/* 表单 */
.gui-form{overflow:hidden;}
.gui-form-item{flex-direction:row; flex-wrap:nowrap; align-items:center;}
.gui-form-label{width:130rpx; height:100rpx; font-size:28rpx; line-height:100rpx; overflow:hidden;}
.gui-form-icon{width:60rpx; height:60rpx; line-height:60rpx; font-size:28rpx;}
.gui-form-body{width:200rpx; margin-left:20rpx; overflow:hidden; flex:1;}
.gui-form-input{height:40rpx; line-height:40rpx; margin:20rpx 0; background-color:rgba(255,255,255,0); border-width:0px; font-size:28rpx;}
.gui-check-item{margin:10rpx 10rpx 0 0; padding:0 10rpx; font-size:28rpx; flex-direction:row; flex-wrap:nowrap; align-items:center;}
.gui-check-item-y{margin:10rpx 0; font-size:28rpx;}
.gui-textarea{height:120rpx; padding:15rpx; line-height:38rpx; background-color:rgba(255,255,255,0); border-width:0px; font-size:28rpx;}
@font-face{font-family:"gui-formicons"; src:url('data:application/ttf;charset=utf-8;base64,OLh6+EVGahJS0OU2yaKO26Kiu6Zv+fbC+9P6l/wm8ZwtrOU5zo2XwdDjj7ilb9szx6Pz8hzzU1DUMrbXMHC2NbU15WTlxOxUdK2llbX0DSSdFF0GClXLlRPIOJppGChZi5s6MnpKNvaqMLkwKoD8NsI9B7wqBANQgCNQhGDQgBAwhFKwhHGhBeDCDCOAI2YElRARjiAS2EBlMIQx4iGInC9nJQTHaVXQA60121033F008BO4gBKhATdCAWWEFskIYcQBnigDbEBX2IBwYQHyQhIThBohj9KScAF0gKAuQMEpALsJAHSEG+/kuiIgA4aBqIoCiQhzQx6qgEABMoBTShNDCC5oAC1AQWUAuIQRYwhzaCM7QFGOgo6EHXQAn6DDbQJNjjczuo4gsok+FuApClC9pt9nPwK3ehR05loNUk');}
/* 底部导航 */
.gui-form-item{display:flex;}
.gui-form-label, .gui-form-icon{display:block;}
.gui-form-picker{display:flex;}
.gui-check-item{display:flexbox;}
.gui-check-item-y{display:block;}
.gui-textarea{box-sizing:border-box;}

/* 评论列表 */
.gui-comments{}
.gui-comments-items{margin-top:35rpx;}
.gui-comments-face{width:80rpx; height:80rpx; border-radius:80rpx; margin-right:25rpx;}
.gui-comments-body{width:580rpx; overflow:hidden;}
.gui-comments-header-text{line-height:40rpx;}
.gui-comments-info{margin-top:2px;}
.gui-comments-info-text{font-size:22rpx; line-height:40rpx; margin-top:10rpx;}
.gui-comments-content{line-height:36rpx; font-size:26rpx; padding:8rpx 0;}
.gui-comments-replay{font-size:24rpx; color:#666666; border-radius:3px; margin:3px 0; padding:15rpx; line-height:36rpx;}
.gui-comments-replay-btn{font-size:20rpx; line-height:44rpx; padding:0rpx 20rpx; border-radius:44rpx;}
.gui-comments-imgs{margin:8rpx 0;}
.gui-comments-image{width:180rpx; height:128rpx; margin-right:10rpx; margin-bottom:10rpx; font-size:0; overflow:hidden;}
/* 底部导航相关 */
.gui-footer-input-body{padding:0 20rpx; height:70rpx; border-radius:66rpx; margin:0 30rpx;}
.gui-footer-input-icon{width:66rpx; text-align:center; line-height:66rpx; font-size:30rpx; margin-right:10rpx;}
.gui-footer-input{width:100rpx; flex:1; font-size:26rpx; height:32rpx; line-height:32rpx; padding:0; overflow:hidden;}
.gui-common-line{height:20rpx; background-color:#F7F8FA;}
/* css3 动画 不支持 nvue */
.gui-transition-all{transition:all 0.2s ease-in 0s;}
/* 使用记录 : gui-switch-navigation2 gui-popup */
@-webkit-keyframes gui-fade-in{0%{opacity:0;}
100%{opacity:1;}}
@keyframes gui-fade-in{0%{opacity:0;}
100%{opacity:1;}}
.gui-fade-in{-webkit-animation:gui-fade-in 350ms ease-in forwards;animation:gui-fade-in 350ms ease-in forwards;}
/* 使用记录 : gui-image gui-popup */
@-webkit-keyframes gui-fade-out{0%{opacity:1;}
100%{opacity:0;}}
@keyframes gui-fade-out{0%{opacity:1;}
100%{opacity:0;}}
.gui-fade-out{-webkit-animation:gui-fade-out 350ms ease-out forwards;animation:gui-fade-out 350ms ease-out forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-top-in{0%{-webkit-transform:translateY(-1000px);transform:translateY(-1000px);}
100%{-webkit-transform:translateY(0px);transform:translateY(0px);}}
@keyframes gui-top-in{0%{-webkit-transform:translateY(-1000px);transform:translateY(-1000px);}
100%{-webkit-transform:translateY(0px);transform:translateY(0px);}}
.gui-top-in{-webkit-animation:gui-top-in 350ms linear forwards;animation:gui-top-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-top-out{0%{-webkit-transform:translateY(0px);transform:translateY(0px);}
100%{-webkit-transform:translateY(-1000px);transform:translateY(-1000px);}}
@keyframes gui-top-out{0%{-webkit-transform:translateY(0px);transform:translateY(0px);}
100%{-webkit-transform:translateY(-1000px);transform:translateY(-1000px);}}
.gui-top-out{-webkit-animation:gui-top-out 350ms linear forwards;animation:gui-top-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-bottom-in{0%{-webkit-transform:translateY(600px);transform:translateY(600px);}
100%{-webkit-transform:translateY(0px);transform:translateY(0px);}}
@keyframes gui-bottom-in{0%{-webkit-transform:translateY(600px);transform:translateY(600px);}
100%{-webkit-transform:translateY(0px);transform:translateY(0px);}}
.gui-bottom-in{-webkit-animation:gui-bottom-in 350ms linear forwards;animation:gui-bottom-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-bottom-out{0%{-webkit-transform:translateY(0px);transform:translateY(0px);}
100%{-webkit-transform:translateY(600px);transform:translateY(600px);}}
@keyframes gui-bottom-out{0%{-webkit-transform:translateY(0px);transform:translateY(0px);}
100%{-webkit-transform:translateY(600px);transform:translateY(600px);}}
.gui-bottom-out{-webkit-animation:gui-bottom-out 350ms linear forwards;animation:gui-bottom-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-left-in{0%{-webkit-transform:translateX(-600px);transform:translateX(-600px);}
100%{-webkit-transform:translateX(0px);transform:translateX(0px);}}
@keyframes gui-left-in{0%{-webkit-transform:translateX(-600px);transform:translateX(-600px);}
100%{-webkit-transform:translateX(0px);transform:translateX(0px);}}
.gui-left-in{-webkit-animation:gui-left-in 350ms linear forwards;animation:gui-left-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-left-out{0%{-webkit-transform:translateX(0px);transform:translateX(0px);}
100%{-webkit-transform:translateX(-600px);transform:translateX(-600px);}}
@keyframes gui-left-out{0%{-webkit-transform:translateX(0px);transform:translateX(0px);}
100%{-webkit-transform:translateX(-600px);transform:translateX(-600px);}}
.gui-left-out{-webkit-animation:gui-left-out 350ms linear forwards;animation:gui-left-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-right-in{0%{-webkit-transform:translateX(600px);transform:translateX(600px);}
100%{-webkit-transform:translateX(0px);transform:translateX(0px);}}
@keyframes gui-right-in{0%{-webkit-transform:translateX(600px);transform:translateX(600px);}
100%{-webkit-transform:translateX(0px);transform:translateX(0px);}}
.gui-right-in{-webkit-animation:gui-right-in 350ms linear forwards;animation:gui-right-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-right-out{0%{-webkit-transform:translateX(0px);transform:translateX(0px);}
100%{-webkit-transform:translateX(600px);transform:translateX(600px);}}
@keyframes gui-right-out{0%{-webkit-transform:translateX(0px);transform:translateX(0px);}
100%{-webkit-transform:translateX(600px);transform:translateX(600px);}}
.gui-right-out{-webkit-animation:gui-right-out 350ms linear forwards;animation:gui-right-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-scale-in{0%{-webkit-transform:scale(0.3,0.3);transform:scale(0.3,0.3);}
100%{-webkit-transform:scale(1,1);transform:scale(1,1);}}
@keyframes gui-scale-in{0%{-webkit-transform:scale(0.3,0.3);transform:scale(0.3,0.3);}
100%{-webkit-transform:scale(1,1);transform:scale(1,1);}}
.gui-scale-in{-webkit-animation:gui-scale-in 350ms linear forwards;animation:gui-scale-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@-webkit-keyframes gui-scale-out{0%{-webkit-transform:scale(1,1);transform:scale(1,1);}
100%{-webkit-transform:scale(0.3,0.3);transform:scale(0.3,0.3);}}
@keyframes gui-scale-out{0%{-webkit-transform:scale(1,1);transform:scale(1,1);}
100%{-webkit-transform:scale(0.3,0.3);transform:scale(0.3,0.3);}}
.gui-scale-out{-webkit-animation:gui-scale-out 350ms linear forwards;animation:gui-scale-out 350ms linear forwards;}
/* 使用记录 : gui-page */
@-webkit-keyframes gui-rotate360{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg);}
50%{-webkit-transform:rotate(180deg);transform:rotate(180deg);}
100%{-webkit-transform:rotate(360deg);transform:rotate(360deg);}}
@keyframes gui-rotate360{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg);}
50%{-webkit-transform:rotate(180deg);transform:rotate(180deg);}
100%{-webkit-transform:rotate(360deg);transform:rotate(360deg);}}
.gui-rotate360{-webkit-animation:gui-rotate360 1200ms infinite linear;animation:gui-rotate360 1200ms infinite linear;}

/* 不支持 nvue 的常用样式 */
.gui-box-shadow{box-shadow:0px 0px 16rpx #323232;}
.gui-transition-all{transition:all 0.2s ease-in 0s;}
::-webkit-scrollbar {display: none;}

/* 文本核心色 一般为黑色 */
.gui-primary-color{color:#2B2E3D;}
/* 核心背景色 */
.gui-bg-primary{background-color:#2B2E3D !important;}
/* 点击元素通用样式 */
.gui-tap{opacity:0.85;}
/* 购物按钮渐变背景色 */
.gui-bg-add-card{background-image:linear-gradient(to right, #F1CF53,#F29C39) !important;}
.gui-bg-buy{background-image:linear-gradient(to right, #E86E35,#EB5058) !important;}
/* *** 间距设置 */
.gui-padding{padding-left:30rpx; padding-right:30rpx;}
/* 左右内间距 */
.gui-margin{margin-left:30rpx; margin-right:30rpx;}
/* 左右外间距 */
.gui-margin-top{margin-top:30rpx;}
/* 顶部外间距 */
.gui-margin-top-large{margin-top:58rpx;}
/* 顶部外间距 大 */
/* 间距设置 *** */
/* 文本尺寸 */
.gui-text{font-size:28rpx; line-height:50rpx; color:#2B2E3D;}
.gui-text-small{font-size:22rpx;}
.gui-h1{font-size:80rpx;}
.gui-h2{font-size:60rpx;}
.gui-h3{font-size:45rpx;}
.gui-h4{font-size:32rpx;}
.gui-h5{font-size:30rpx;}
.gui-h6{font-size:28rpx;}
.gui-indent{text-indent:56rpx;}




/* *** gui-page 页面组件相关  */
.gui-page-loading-bg{background-color:rgba(255,255,255,0.88);}
/* 页面内置全屏 Loading 背景颜色 */
.gui-page-loading-color{background-color:#2B2E3D;}
/* 页面内置全屏 Loading 颜色 */
.gui-header-buttons-bg{background-color:rgba(0,0,0,0.8);}
/* 自定义导航左侧按钮背景颜色 */
.gui-header-buttons-color{color:#FFFFFF;}
/* 自定义导航左侧按钮颜色 */
.gui-nav-bottom-color{color:#B6C3D2;}
/* 底部自定义导航默认色 */
.gui-nav-bottom-active-color{color:#2B2E3D;}
/* 底部自定义导航切换色 */
/* gui-page 页面组件相关 *** */
/* 圆角 */
.gui-border-radius-small{border-radius:6rpx;}
.gui-border-radius{border-radius:10rpx;}
.gui-border-radius-large{border-radius:20rpx;}
/* 按钮 基于原生组件的修饰 */
.button-hover{opacity:0.8;}
.gui-button-text{font-size:28rpx; line-height:88rpx; text-align:center;}
.gui-button-text-mini{font-size:22rpx; line-height:58rpx; text-align:center;}
.gui-button-mini{height:58rpx;}
.gui-button{line-height:88rpx; border-radius:5rpx; background:transparent; margin:0; color:transparent; border:0; text-align:center; padding:0;}
.gui-button::after{width:0; height:0; -webkit-transform: scale(1); transform: scale(1); display:none; background:transparent;}
.gui-button[disabled][type=default]{opacity:0.5;}
.gui-button[type=default][size=mini]{font-size:22rpx; height:58rpx; line-height:58rpx; background:none;}




/* 按钮 组件 框架自定义样式 */
.gui-sbutton{width:230rpx; height:80rpx; border-radius:8rpx; padding:0; margin:0;}
.gui-sbutton-text{font-size:30rpx; line-height:80rpx; text-align:center; color:#FFFFFF;}
.gui-sbutton-loading-point{width:8rpx; height:8rpx; border-radius:8rpx; margin:8rpx; background-color:#FFFFFF;}
.gui-sbutton-default{background-color:#3688FF;}
.gui-sbutton-loading{background-color:#3688FF; opacity:0.8;}
.gui-sbutton-success{background-color:#07C160 !important;}
.gui-sbutton-fail{background-color:#FF0036 !important;}
/* *** gui-select-list 可选列表样式 */
.gui-select-list-ring{font-size:32rpx; font-weight:bold;}
/* 左侧选择圆环 */
.gui-select-list-img{width:66rpx; height:66rpx; border-radius:60rpx; margin-right:28rpx;}
/* 图片样式 */
.gui-select-list-title{font-size:28rpx; line-height:50rpx; color:#2B2E3D;}
/* 标题样式 */
.gui-select-list-desc{font-size:22rpx; color:#828282; line-height:33rpx;}
/* 描述小文本 */
.gui-select-list-icon{width:60rpx; line-height:60rpx; font-size:36rpx; text-align:center; color:rgba(69, 90, 100, 0.3);}
/* 选中图标 */
.gui-select-list-current{color:#2B2E3D !important;}
/* 选中颜色 */
/* 可选列表样式 *** */
/* *** gui-slide-list 滑动列表样式 */
.gui-slide-list-img-wrap{width:80rpx; height:80rpx; margin-left:25rpx;}
/* 列表图片外层样式 */
.gui-slide-list-img{width:80rpx; height:80rpx; border-radius:6rpx;}
/* 列表图片外层样式 */
.gui-slide-list-point{border-radius:32rpx; height:32rpx; line-height:32rpx; padding:0 10rpx; font-size:20rpx;}
/* 消息数标签样式 */
.gui-slide-list-point{min-width:12rpx;}
.gui-slide-list-title-text{line-height:38rpx; height:38rpx; font-size:28rpx; color:#2B2E3D; overflow:hidden;}
/* 消息标题样式 */
.gui-slide-list-desc{line-height:32rpx; height:32rpx; font-size:22rpx; color:rgba(69, 90, 100, 0.3); overflow:hidden; margin-right:25rpx; margin-top:2px;}
/* 消息描述样式 */
/* 滑动列表样式 *** */
/* *** gui-tree 树状列表样式 */
.gui-tree-icons{width:50rpx;}
/* 图标样式 */
.gui-tree-icons-text{font-size:32rpx; color:rgba(69, 90, 100, 0.3);}
.gui-tree-title{line-height:80rpx; font-size:28rpx; width:200rpx;}
/* 标题样式 */
.gui-tree-current{color:#2B2E3D;}
/* 选中样式 */
/* 树状列表样式 *** */
/* *** gui-segmented-control 分段器样式 */
.gui-segmented-control{background-color:#F8F8F8; padding:8rpx;}
/* 外层主体 */
.gui-segmented-control-item{color:#2B2E3D; font-size:26rpx; line-height:66rpx;}
/* 项目标题 */
.gui-segmented-current{background-color:#2B2E3D; color:#FFFFFF;}
/* 激活样式 */
/* 分段器样式 *** */
/* *** 为空展示默认图片大小 */
.gui-empty-img{width:320rpx; height:320rpx; margin-top:200rpx;}
/* 演示 : 提交按钮 自定义样式  */
.mygui-sbutton{width:230rpx; height:80rpx; border-radius:80rpx; padding:0; margin:0;}
.mygui-sbutton-text{font-size:28rpx; line-height:80rpx; text-align:center; color:#FFFFFF;}
.mygui-sbutton-loading-point{width:6rpx; height:6rpx; border-radius:10rpx; margin:8rpx; background-color:#FFFFFF;}
.mygui-sbutton-default{background-color:#2B2E3D;}
.mygui-sbutton-loading{background-color:#3688FF; opacity:0.8;}
.mygui-sbutton-success{background-color:#07C160;}
.mygui-sbutton-fail{background-color:#FF0036;}
/*
link   : http://grace.hcoder.net
author : 刘海君 <EMAIL> 
verson : 3.0
last update date : 2020-07-13
GraceUI 的版权约束是不能转售或者将 GraceUI 直接发布到公开渠道！侵权必究，请遵守版权约定！
GraceUI 软著号 2019SR0533188 版权机器人启动中，侵权会追责，请遵守极其简单的版权规定
*/
@font-face{
	font-family : "grace-iconfont";
	font-weight : normal;
	font-style  : normal;

	src         : url(data:font/ttf;base64,AAEAAAALAIAAAwAwR1NVQrD+s+0AAAE4AAAAQk9TLzI8d0vtAAABfAAAAFZjbWFw/VBVRQAAA2QAAAfEZ2x5ZrufGTgAAAv0AABRlGhlYWQesjGvAAAA4AAAADZoaGVhDUUJQQAAALwAAAAkaG10eJiv/+YAAAHUAAABkGxvY2HosdMkAAALKAAAAMptYXhwAYkBHQAAARgAAAAgbmFtZc2mrd4AAF2IAAACkXBvc3SSKG2bAABgHAAAA6AAAQAAA4D/gABcCWX/+P/3CW4AAQAAAAAAAAAAAAAAAAAAAGQAAQAAAAEAAPQULppfDzz1AAsEAAAAAADbFnbZAAAAANsWdtn/+P9/CW4DgQAAAAgAAgAAAAAAAAABAAAAZAERABsAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKAB4ALAABREZMVAAIAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAAAAQQWAZAABQAIAokCzAAAAI8CiQLMAAAB6wAyAQgAAAIABQMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUGZFZABA5gDp5AOA/4AAXAOBAIEAAAABAAAAAAAABAAAAAQA//8EAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABJIAAASe//4EAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAWFAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEQAAABAD//wQAAAAJZf/4BAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQAAAAEAAAABAH//wQAAAAEAAAABAAAAAQE//kEAAAABAAAAAQAAAAEAAAABAAAAAQA//4EAAAABAMAAAQAAAAEAAAABEQAAAQAAAAEAP//BAD//gQAAAAEAAAABAAAAAQAAAAEAAAABAAAAAQA//8EAAAABAAAAAQAAAAEAAAABAAAAAAAAAUAAAADAAAALAAAAAQAAAMQAAEAAAAAAgoAAwABAAAALAADAAoAAAMQAAQB3gAAAEIAQAAFAALmOeZB5kbmTOZU5lbmZuZu5nzmhOaH5pLmm+ae5rjmx+bN5s/m0ebq5v7nBOcn5zbnYueK56Xn+OgA6Vrp2+nk//8AAOYA5jzmROZM5lTmVuZm5m7me+aE5obmkuab5p3muObH5s3mz+bR5urm/ucE5yfnNudi54rnpef46ADpWunb6eT//wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQBCALQAvgDCAMIAwgDCAMIAwgDEAMQAxgDGAMYAyADIAMgAyADIAMgAyADIAMgAyADIAMgAyADIAMgAyADIAMgAAABgAGEAXQBjAFsAYgBVAFgATwBMAEkARAA9AC8AMABHADwAMQAyADMALQBIAE4AQwA6ADsAXAAoAEsANAAqAEAALAAmAEUAJQAuACQAIABCACMAEwAXADgABwAGABEAEAALADUADwANAAwACgACAB0ABQABAFEAKQBSAD4AJwASABgAVwAfAE0AXwA5ABsAPwAiAB4AWQBGAEoACAA3AAQAUABUAA4AUwAhAAkAVgA2ABYAAwAVABoAWgAUABkAQQAcAF4AKwAAAQYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAEtAAAAAAAAABjAADmAAAA5gAAAABgAADmAQAA5gEAAABhAADmAgAA5gIAAABdAADmAwAA5gMAAABjAADmBAAA5gQAAABbAADmBQAA5gUAAABiAADmBgAA5gYAAABVAADmBwAA5gcAAABYAADmCAAA5ggAAABPAADmCQAA5gkAAABMAADmCgAA5goAAABJAADmCwAA5gsAAABEAADmDAAA5gwAAAA9AADmDQAA5g0AAAAvAADmDgAA5g4AAAAwAADmDwAA5g8AAABHAADmEAAA5hAAAAA8AADmEQAA5hEAAAAxAADmEgAA5hIAAAAyAADmEwAA5hMAAAAzAADmFAAA5hQAAAAtAADmFQAA5hUAAABIAADmFgAA5hYAAABOAADmFwAA5hcAAABDAADmGAAA5hgAAAA6AADmGQAA5hkAAAA7AADmGgAA5hoAAABcAADmGwAA5hsAAAAoAADmHAAA5hwAAABLAADmHQAA5h0AAAA0AADmHgAA5h4AAAAqAADmHwAA5h8AAABAAADmIAAA5iAAAAAsAADmIQAA5iEAAAAmAADmIgAA5iIAAABFAADmIwAA5iMAAAAlAADmJAAA5iQAAAAuAADmJQAA5iUAAAAkAADmJgAA5iYAAAAgAADmJwAA5icAAABCAADmKAAA5igAAAAjAADmKQAA5ikAAAATAADmKgAA5ioAAAAXAADmKwAA5isAAAA4AADmLAAA5iwAAAAHAADmLQAA5i0AAAAGAADmLgAA5i4AAAARAADmLwAA5i8AAAAQAADmMAAA5jAAAAALAADmMQAA5jEAAAA1AADmMgAA5jIAAAAPAADmMwAA5jMAAAANAADmNAAA5jQAAAAMAADmNQAA5jUAAAAKAADmNgAA5jYAAAACAADmNwAA5jcAAAAdAADmOAAA5jgAAAAFAADmOQAA5jkAAAABAADmPAAA5jwAAABRAADmPQAA5j0AAAApAADmPgAA5j4AAABSAADmPwAA5j8AAAA+AADmQAAA5kAAAAAnAADmQQAA5kEAAAASAADmRAAA5kQAAAAYAADmRQAA5kUAAABXAADmRgAA5kYAAAAfAADmTAAA5kwAAABNAADmVAAA5lQAAABfAADmVgAA5lYAAAA5AADmZgAA5mYAAAAbAADmbgAA5m4AAAA/AADmewAA5nsAAAAiAADmfAAA5nwAAAAeAADmhAAA5oQAAABZAADmhgAA5oYAAABGAADmhwAA5ocAAABKAADmkgAA5pIAAAAIAADmmwAA5psAAAA3AADmnQAA5p0AAAAEAADmngAA5p4AAABQAADmuAAA5rgAAABUAADmxwAA5scAAAAOAADmzQAA5s0AAABTAADmzwAA5s8AAAAhAADm0QAA5tEAAAAJAADm6gAA5uoAAABWAADm/gAA5v4AAAA2AADnBAAA5wQAAAAWAADnJwAA5ycAAAADAADnNgAA5zYAAAAVAADnYgAA52IAAAAaAADnigAA54oAAABaAADnpQAA56UAAAAUAADn+AAA5/gAAAAZAADoAAAA6AAAAABBAADpWgAA6VoAAAAcAADp2wAA6dsAAABeAADp5AAA6eQAAAArAAAAAAA4AGwAhgCoAP4BDAE8AWQCSgJ+AxoDeAPEA/QEOgSkBPAFSgWiBd4GagbqB24IkAjUCRgJiAnECloKvAteDBYMWAzWDUANlA36Dh4OlA64DvoPdA/SECQQYhC0EUARhhIGEogTbhP0FYAV6BYYFooXEBdcF64X4hiWGR4ZkBpmGrQbsBxMHNQdJh2oHdIeJB6gHwQfbh/2IE4giiDsIUYh+iKsI5Yj+iRwJRIlkCXYJjomlibcJ5In3CgWKEAoYiiGKLYoygAAAAL///+ABAQDgAAIABsAAAkBAzUnLgEnJQEGBwEGFhcFFhcTHgEyNjcBNiYDjv7NYAILKRz+7gM6CQn8Xh0DHgFyEwmBBhccFwYBdAkbAw79BwESAgIaJApNAcABA/5nDzsMaAcU/pMODw8OA6QZJQADAAD/wAPAA0AAAwAPABsAAAEVITUTHgEXDgEHLgEnPgE3DgEHHgEXPgE3LgECoP7AoKPZBATZo6PZBATZo779BQX9vr79BQX9AaBAQAFgBNmjo9kEBNmjo9lEBf2+vv0FBf2+vv0AAAMAAAAAA6MCDAADAAcACwAAEyEVIRUhFSEVIRUhXQNG/LoDRvy6A0b8ugIML10vXS4AAAACAAD/zAO0AzQABQALAAAlARcJATcDNwkBFwECAAFrSP5N/k1ISEgBawFrSP5NXgFtSf5LAbVJAR9J/pQBbEn+SwAAAAIAAP/pA5cDFgAWAC0AAAEmBwEOAR8BHgE3ATYyFwEWNj8BNiYnARY3ATYyFwEWNj8BNiYnASYiBwEOARcCDQ0N/n4GAQUrBBAFAUIFDAUBQgUQBCsFAQb9DwgJAUYFDAUBQgYOBSsFAQb+fgUNBv58BgEFAYIGBv61BBAFLQYBBQEGBQX++gUBBi0FEAQBRQYEAQoFBf76BQIFLQUQBAFLBQX+tQQQBQAAAAABAAAAAAM2AZkAAwAAEyEVIc0CaP2YAZkyAAAAAwAA//8DgQMAAAMAEwAXAAABESERJSEOAQcRHgEXIT4BNxEuAQUhESEDKv2sAlT9rCUwAQEwJQJVJDEBAjD9/AF0/owCqv2rAlVWATAl/askMQEBMSQCVSUwxP6MAAAAAgAA//8DgAMAAAMAEwAAAREhESUhDgEHER4BFyE+ATcRLgEDK/2qAlb9qiQwAQEwJAJWJDABATACq/2qAlZVATAk/aokMAEBMCQCViQwAAALAAD/4AOdAyAADAAcACwAMAA9AE0AXQBtAH0AnQClAAABPgE3NS4BIgYdARQWATMyNj0BNCYrASIGHQEGFhczMjY9ATQmKwEiBh0BBhYTFAYXBz4BPQE0JiIGBxUeAQMzMjY9ATQmKwEiBh0BFBYjMzI2PQE0JisBIgYdAQYWFzMyNj0BNCYrASIGHQEGFjsBMjY9ATQmKwEiBh0BFBYBIxUOASImJzUhFQ4BIiYnNSMOAQcRHgEXIT4BNxEuAQMGByEmJxEhASgUGQEBGScaGgGIXgkNDQleCQ0BDAteCQ0NCV4JDQEMcwEBLxMaGicZAQIa9F4JDQ0JXgkNDcJeCQ0NCV4JDQEOCV4JDQ0JXgkNAQ7UXgkNDQleCQ0NAYkvASo9KQH+4gEpPSoBLyErAQErIQKgICwBASwWAQn9YAkBArQCQQEZE4QTGhkUhBMZ/vsNCT0JDAwJPQkNtwwJPgkMDAk+CQwB6AMDAiUBGROEExoZFIQTGf77DQk9CQwMCT0JDQ0JPQkMDAk9CQ23DAk+CQwMCT4JDAwJPgkMDAk+CQwCSWEeKCgeYWEeKCgeYQEsIP2pICoBASogAlcgLP1dCQEBCQHbAAAAAQAA/8ADwQNAABwAAAUuASczHgEXPgE3LgEnDgEHFyERFz4BNx4BFw4BAgCt8htAGs6So9kEBNmjXZo1bP8AZj+xar79BQX9QATWpouyAwTZo6PZBAFPRGwBAGZMWQEF/b6+/QAAAAAEAAD/0wOwAzIADwAfAF8AYAAAAR4CBgcOAScuAjY3PgEHDgEeARcWNjc+AS4BJyYGPwEuAT4BFzc+ARcWBgcDFgYHBgcnNjc+AS8BEz4BJyYGFQcnJiIGFh8BBycmBgcOAR4BFxYfASYnLgI2Nz4BFwIAFxoBFxUwfjIXGQIXFi9+jg8RARMQJFsjDxEBExAkW7lmEwInNBU6ATYuMwkE9zAbRzZKATgpOxojGPcDEB4gJD0fBxINAQYgnSdQoj0mJwMrKDJBAVM/LjMDLSxKxl8BdxU5PzoXLwMtFjk/ORcvA1AQKi0pECACIhAqLSkQIAKsaRM2JwETNQQQJzY5Af8AXcZMNxkuFio/o00nAQABKB8YEgM/HgYNEgYeoxYgIj0nZG1iJi4TLBM7LHF/dC1LIysAAAADAAD/xwPKAzUAGwAxADgAABciJjcTJyY2PwI2Mh8CHgEPAhMWBi8BBwY3Mh8BJyY/AScmLwEHBg8BFxYPATc2AwcOAR8BN/wPEwEetgwLEvmXCiQKlvoSCwumAQ4BIRHz9Qf7EQ+/CwESgsQaD3p6DxrCjxUDF7sPf0wJBgZ2YDQWDwETxA4lBTzpDw/oPQUjD8MC/usTFQmBgQTICGXaGxWaMAcWvb0WBy+ZFx/UYwgBMxMDEQeEJAAAAAIAAAAAA4wC4QAUACoAABMBFBYXPgE1ATYmDwEOAScmLwEuAQMBFBYXPgE1ATYmDwEOAScmLwEuAQd3ATomJiImAT0CIjPlBGk3Ok1WBEIFATslJyImAT0BITTlA2k3Ok5VBEMMAp7+ywIcAgITAQE/BzsfowQQHyk5PwQD/rL+ywEcAgEUAQE+BzsfowQQICk5PgUCMwAAAgAA/38EAAOBAA4AGgAABSImJxE0PgEyHgEVEQ4BASEuATQ2NyEeARQGAgATGAEMFBgUDAEYAcH8WBMZGRMDqBMZGYAZEwOoDBQMDBQM/FgTGQHUARgmGAEBGCYYAAAAAgAA/4AEAAOAAAsAJwAAAQYABxYAFzYANyYAAwcXFhQGIi8BBwYiJjQ/AScmNDYyHwE3NjIWFAIA2v7fBQUBIdraASEFBf7fDp+dCBIYCp6eChkTCZ2eCRMaCZ+fCRsSA4AF/t/a2v7fBQUBIdraASH+pJ+dCRgSCJueCRMZCp6dChoTCZ6fCRMZAAAFAAD/2QNqAycADwAfACkANABAAAAFLgEnET4BNyEeARcRDgEHAQ4BBxEeARchPgE3ES4BJwEiJjYzITIWBiMFBiY2FyEyFhQGIyUiJjQ2MyEyFhQGIwEMMkIBAUIyAegyQgEBQjL+GBwlAQElHAHoHCUBASUc/ksPEBAPAYIPEBAP/n4QEREQAQcLDw8L/vkLDw8LAYILDw8LJgFCMgJiMkIBAUIy/Z4yQgEDGQElHP2eHCUBASUcAmIcJQH+cxoaGhp6ARsbAg4WD/UOFg8PFg4AAAIAAP+mA9oDWgAMACoAAAEyNjcRLgEiBgcRHgETBhYXHgEXDgEHLgEnPgE3NiYHDgEHFgAXNgA3LgECAAoPAQEPFA8BAQ/qIwYDZ3gBBOy2tuwEAXdhBx4CcIICBQELysoBCwUCiAFgEAoBxgoPDwr+QA8RAcAHJAI2w3qy8AQE8LJ5vjYgDgE81ojK/vUFBQELyojcAAAAAgAA/38DiwNxACkAOQAAASUmNjc+AhYXHgEzPgEnLgEnIgYHDgEXByIGFREUFjMhMjY1ETQmIzEDFAYjISImJxE+ATchHgEVA1P90gMIJxpMknUdCBMRKgwKKZlxS2QsQhkDIBgfHxgCphgfHxgfGhL98RIZAQEZEgIPExkBuAE8lzQjNAFlShUXAj0YanEBJzFRykQBHxf+NBcfHxcBzBcf/kwTGRkTATMSGQEBGRIAAAAABAAA/4ADXQOAAA8AHwAsADUAAAUhLgEnET4BNyEeARcRDgEBIgYHER4BMyEyNjcRLgEjBzQmJyMOARQWFzM+AQMiBhQWMjY0JgLq/iwxQAICQDEB1DBBAgJB/fwXHwEBHxcB1BcfAQEfF3oOC7IKDg4KsgsOcBIZGSQZGYABQTEDGjFBAQFBMfzmMUEDwx8Y/OYYHx8YAxoYH1wLDgEBDhUOAQEO/YYZJBgYJBkAAAABAAD//QODAwMAIwAAARceAT4CJi8BNz4BLgIGDwEnLgEOAhYfAQcOAR4CNjcCAP0PJykcCwoP/f0PCgscKScP/f0PJykcCwoP/f0PCgscKScPARP9DwoLHCknD/39DycpHAsKD/39DwoLHCknD/39DycpHAsKDwAFAAD/mgSSA4AAFwAyAD4ASwBYAAABFiYnIQYHDgEHER4BFyE+ATcRLgEvASYDIiMiBgcXDgEHLgEnNy4BByE3PgE3ITIWHwEBJyY0NjIfARYUBiI3JjUnPgEyFhUXFAYiFyY0PwE2MhYUDwEGIgP6CSMk/RspFhR8CAEtIgPyIi0BCYUKCAb6AQEOEwQBAko4N0oCAQkbAv6mdQEcFwK7EREMd/z9WQcOFAZZBw0U7wcEAQ0UDgMNFOAHBlUHFA4GVQcUAhwNMAUBJh7NLP7kIi0BAS0iARwv2Q4PC/7mDwwHOEoBAUo4CBMIAdgDIQIQFtgBmlQHFQ0GVQcUDiwHCnsKDg0KfAoNGwcUB1kHDRQHWQcAAAAAB//+/58EoANgACAAJgAqAC8ANQA/AEoAAAEnNSYnJSYiBwUGDwEGFh8BERQXBRYyNyUzNjURNzM+AScXBS8BJScXBREHNxEjJQc3BQ8BJRMnNQUWMzI/ARElFQURFxYzMjclFQScfAMF/kIFCwT+QwcEfAQHCG8NAbsFDAYBuAEPbwQHA0EJ/m4DYgGPOBH+jzYQCP6RLwYBlWMD/ml9CwEkBQULBlcBs/5zVwYKBQUBHAHO3QEGA6gDA6gEB94JEgQ3/v4OB70DA70GEAEDOQUQGBHJBbTJIQa7AToCBf7HuSMLzrIFy/6rBOuTAgmY/mK0DaQBm5gJAo/aAAAAAAYAAP/9A4wDAgAJABEAMQA+AEsAWAAAJQ4BIyEiJicRISU0NzMWFxUjISM1LgEnIw4BBxUjIgYUFjsBER4BFyE+ATcRMzI2NCYBMjY1ETQmIgYVERQWIzI2NxEuASIGFREUFiEyNjURNCYiBgcRHgEC3gEbFP6kFBsBAbz+pQnoCQH7Aeu2ASYd6BwmAbcNEBANVgE8LQFcLTwBVg0QEP6FDBERGBERewwQAQEQGRARARoNEBAZEAEBEGgUHBwUAgBWCQEBCR0dHSYBASYdHRAZEf4BLTwBATwtAgAQGRD97hEMATUMEREM/ssMEREMATUMEREM/ssMEREMATUMEREM/ssMEQAAAAkAAP/hA5MDIQArAC0ARwBmAH8AgACKAJ4AyQAAATEnJi8BIy4BPwE2OwEyFhQGKwEHMxcWMxcyPwEzJy4BPgEfARYGKwEHDgElMQMhIiY1ETQ2MhYVERYXITY3ETQ2MhYVERQGASInJjc2NSYnLgE+Azc2HgIHDgMjIicGBwY3MhcWMz4BNzYuAgcOAQcGFhcWFx4BBzY3JzYWBwYHBiY3NhcyFAcjFTMWFAcjJjQ7ATUjJjYzNzIOAQcWFxYXFgYnJicmNhcWFyY2NwY2JyMiHQEGIic1NDsBJjc2FxYXFQJhsRUMG+8QCguMBgjtCAoKCOht3yYBArEEAiPJOgYCCQ4GVwwKENkZBhMBBAj9LRIYCg8KAQYC0wYBCg8KGP7QBgQJAwcHBxUWAxkvOiElRjUcAQEbMj4iKiUEFAUdAwMiJj5WAwEYLzshO08EARMSBwcEAgMR9sMIDAcUIAwECxZ1CAkzQAgJkwkJPTIJAQmCDgIIBwECBwMHCQkWGgkHCg4NAgEDAQMEcgIBEwEPNwYIDAYCBAErAQERKQEeCnMFCg8KWjoCAQQ5LAUODAIFQgseKQkLSf5uGBIBbAcKCgf+lAYBAQYBbAcKCgf+lBIYAdoEBwwYAwcHGj5CPC8bAgIZNEUlIj4wGhIDDgQtARICUz4hOy0WAgVROx41FggGBA4ODH8zBQ4GEQsDEgQHGRMBJgERAQESJgETSBgVAQEBAwIIDgQNCgUQAgUGAwYEAQYBAw0ICBYPDQMFCAMIAgACAAD/gAQAA4AACwAjAAABBgAHFgAXNgA3JgATARQGDwEOAS8BJjY/AT4BFRcBNh8BFhQCANv+4AUFASDb2wEgBQX+4F7+nwUBEQgKBKUEBgkLCQ59AVYREREIA4AF/uDb2/7gBQUBINvbASD+mf6rBAQEEQgEAbAFDQURCQYEgwFQERERCRQAAgAA/4AEAAOAABgAJAAAATIXHgEXFhQHDgEHBiInLgEnJjQ3PgE3NjcGAAcWABc2ADcmAAIAWlFPeiEjIiJ6T1KyUk96ISMjIXpPUlna/t8FBQEh2toBIQUF/t8DOCIiek9SslJPeiEjIyF6T1KzUU96ISNIBf7f2tr+3wUFASHa2gEhAAAAAAQAAP+JA6wDdwAPACUAMwBHAAA3IScmPQEuAScOAQcVFA8BBSEiJic+AT8BNT4BNx4BFxUXFhQHBgUuASczHgEXPgE3Mw4BAyY1PgEyFhcUByc2NzQmIgYVFhe6AokzBgOZcXKYAwIEApb9ABEYAQEDA1ABwZGRwgNQBQUO/mhPaQJKAUAvL0ABSgJpqRMBPV49ARM9BQEUHhQBBUNaCg/wcpgDA5hy8AcFDaAYEQUNBY3cksEEBMGS4IwKFgoTcwJoTy9AAQFAL09oA0EcIS49PS4hHCkKCg4UFA4KCgAAAQAA/8ADwANAACMAACUOAS4BNzY1LgEnDgEHHgEXMjc2HgEGBwYjLgEnPgE3HgEXFAOnBh0gDgYUBNCcnNAEBNCcPDgRHAsOEEVJvv0FBf2+vv0F7RAOCx0QOj+c0AQE0Jyc0AQTBQ8gHAYXBf2+vv0FBf2+TAAABgAA/4AD7QOAABUAHQAxAEUAVgBiAAATATYyFwEeATI2NzYnASYiBwEGFBYyBzMRMxEzNSElIgcOARQWFx4BMzI3PgE0JicuARMGIyImJyY1NDc+ATcyFx4BBxQGAS4BKwERMzUzMjY3PgE1NCYHDgErATUzMhYVFAZNAaYFEAUBpgUKCQoFExP+UxU9Ff5aBQscIm0zbf7zAdNWMBgbGxgYQSdWMBgbGxgTQTQmOh0wEyYmEzAdOiYTDgERAV4TMB1tMzQdNRQTExM5CiUYLTQrLg8BoAGmBQX+WgUCAgUTEwGnExP+WQUXFLb+pgFaJgc6HUtWRR0YGzodSldFHRwX/sYmFRgmTUMwFBgBJxM5Jy86AQMOEv6AkxEPEyodGCyECgqaJicTHQAABAAA/8ADwANAABIAHQBEAEUAAAEyHgIUDgIiLgI0PgIzMRMyNjQmIgYUFjMxNzQ+AjU0LgIjIg4CFBY+ATU0PgIzMhYVFA4CHQEUFj4BPQECAFykeUZGeaS4pHlGRnmkXAkUHBwpGxsVLhogGxImPSoyOx8JHSEcAwwYFSEoHCEcHCEcAz9GeqO5o3lGRnmjuaN6Rv1TGycbGycbuwkZJTAhFzAnGyIwNCIOAhEPCRYUDSQYFCUfGAcyBgcBBwcWAAAAAAkAAP+ABAADgAALABcAIwAvADAATQBOAFoAWwAABSYAJzYANxYAFwYAAwYABxYAFzYANyYAAyYAJzYANxYAFwYAAwYABxYAFzYANyYAJwMnNjMyFhUUBwYVFDMyNxcOASMiJjU0NzY1NCMiBzc0NjcyFhUUBiMiJicCANn+3wYGASHZ2QEhBgb+39nE/vsFBQEFxMQBBQUF/vvE2P7hBQUBH9jYAR8FBf7h2Mb++QUFAQfGxgEHBQX++cZJE0w6EAwcLAkRNBA3NhMPDz8ECg8xVRoRCxEZEQ0PAYAGASHZ2QEhBgb+39nZ/t8DyAX++8TE/vsFBQEFxMQBBfw7BQEf2NgBHwUF/uHY2P7hA8kF/vnGxv75BQUBB8bGAQcF/kgOUwkLD1eWHA0zEDQgFg0vzwoGCTGpExgBFA0RGxMOAAAAAAMAAP+ABAADgAAUAGcAeAAAJT4BNzYmBxYGBwYjLgE1JgYXHgEzASIjLgEnDgEHDgEdAR4BFzI2Nx4BFz4CFy4BJyY2PwE2NzY3NjcyFzceARcWBgcxBhQfAT8BPgE3FhcOAQcuASMOARQWFzI2Nz4BNz4BNzUuAQcuAScOAQcmJz4BNx4BFwYHAiQ6WBICEAQBN0sWFjYyBg0EGE0tAYcBASbqoaXuIR4mAS8jEyELHWlIAgQFAhRSEAQtI9dEHAgGAQcFAgIYPQgJHD0CAwQBBEFcFxQhHcGICSUWHyoqHxglCJ7dHRccAQEwcSq/fX2+KgUEGsiMjMgbBQRfAUE2CQcIAioNAwIYAgQKBycsAdCUugMDxJoGKx6gIi4BEA5HbB4DBgECDW9WJj0HKQw9DioGAQQCIHZHT3g3AgYCAgECI21EHAd2iwwUGAEnOycBGhYOp4wJKRmiIi8ubIUCAoRrBQSDpwMCp4IGBwAAAwAA/8wEAAM0AAMAEwAnAAATIREhJTY3JwYHBiInJicHFhcWMiURIREjFTMxMxEhESEXMychNTM1oAKp/VcB5BkQSAsQI1giEAtJDxk6nQFC/OdxcU4Cff6SAU8BAW12AvX+YyocIh8WEiclEhcdIhw/JwHI/jveAlX9g5tNdN0AAAAABgAA/6MD3QNdAAsAFwBDAEcASwBPAAABDgEHHgEXPgE3LgEDLgEnPgE3HgEXDgEBIy4BJw4BByMiBh0BFBYXMz4BPQE0Jic+ATceARcOAR0BFBYXMz4BPQE0JgUjNTMFIzUzASEVIQIAg68EBK+Dg68EBK+Db5QDA5Rvb5QDA5QBPgMU7Kqq7BQDFBsbFDAUGxkTFNCWltAUExkbFDAUGxv8wjAwAyowMP2UAX7+ggKGA6+Eg68DA6+DhK/9xwOUb3CUAgKUcG+UAXqv4gQE4q8bFI8UGwEBGxSPExsBmsgEBMiaARsTjxQbAQEbFI8UG76Pj4/+Oy8AAAAABAAA/4AEAAOAAAsAFwA2AD8AAAUmACc2ADcWABcGACc+ATcuAScOAQceARM2FgcUBwYHBgcGHQEjNSY2PwE2NTQmIyIHBhUjPgETNhYUBiImNDYCANr+3wUFASHa2gEhBQX+39q9+gUF+r29+gUF+spGWAEbCjMVCgxNAR1EDRQrKDEWFE0BXEMXHyAsHx+ABQEh2toBIQUF/t/a2v7fPwX6vb36BQX6vb36ArEBTkE2IwwtEhQWGgwMKTs7DhkcJiwfGzBQXP5WAR0uHh8tHQAAAAAEAAD/oAPgA2AACwAYACQAMAAAASEiJjQ2MyEyFhQGByImNRE0NjIWFREUBgMmACc2ADcWABcGAAMOAQceARc+ATcuAQKg/sAOEhIOAUAOEhKuDhISHBISDsz+8QUFAQ/MzAEPBQX+8cyx6wQE67Gx6wQE6wFgEhwSEhwSoBIOAUAOEhIO/sAOEv7gBQEPzMwBDwUF/vHMzP7xA3sE67Gx6wQE67Gx6wAEAAD/gAWCA4AAFQApADEAOQAAASIHAQYUFwEWMyEyPgI1ETQuAiMlIR4BFxEOAQchIiYnASY0NwE+AQE2JgcBBhY3AwEWNicBJgYB+xEM/noGBgGGDBECuhgtIxMTIy0Y/UYCuldzAwNzV/1GGS0S/nodHQGGEi0CTxozIP5mGTIhOgGaIDMa/mchMgMuDP5sBhAG/mwMEyItGQJmGS0iE1ICdFf9mld0AhMSAZQfUB8BlBIT/rAgMxn+ZiAzGQGa/mYZMyABmhkzAAADAAD/ngO9A2IABwALABMAACU1MzUjNSERATMRIwEhFSMVMxUhApSUlAEo/fqUlP6OASiUlP7YfZTelP36AuT8PgLklN6UAAADAAD/hwNpA2oAHgA1AEoAAAEeARceAQcUBw4BBwYnISImJxE+ATMhMhceARcWBwYlMxY3Njc+ASc2JicmJyYrAQ4BHQEeARMzFjY3PgEnNiYnLgEHIw4BFREeAQK1KUQZFxcBKBU7JWJn/tIZIgEBIhkBKa1EIiQBASEk/kSwNzYsIBIRAgESEiEvPDyfDBIBEQzPL1onGhkBARsbKmEyvwwSAREBmgsvIiBLKFJHIjQQJAQkGwNgGiROI1oxQTc6EAEICB8UMxwcNBUiBwgBEg3oDRL+NgQXGxhBJCRCGBsXBQESDf7qDhEAAAMAAP/JA5wDNwAHAA8AEwAAASE1MxUhNTMRIzUhFSM1ISUhFSEDm/zKMALWMDD9KjADNvzLAzT8zAJA9sbG/JTGxvbYMAAAAAIAAP/LBAAC/wAPACMAAAEhDgEHER4BFyE+ATcRLgEFITIWFREBJgYXEyMBJgYHAxE+AQO1/JUfKgEBKh8Dax8qAQEq/HYDawsP/tBVEgbPTP7WP1wCygEOAv4BKh/9Yh8qAQEqHwKeHyovDwv9gQGHAXEI/rgBwE5DC/79AfsLDwAAAAMAAP/AA8ADQQAMAC0ATgAAJRYyNwE2NC4BBwEGFDcxJg4CHwEHJzcXFjI2NC8BJiIPAQYUHwEWMj8BNjQnAScmIg8BBhQfARY+ATQvATcXBycmDgEUFzEXFjI/ATY0AWUIFwgBDwgQFwj+8QjCCRUQAQgn48TjKAkWEQgpESwR5BAQxREsEeQQEAFpxREsEeQQECgJFhEIKOPF5CgJFRAHJxIsEeQQ5QgIAQ8JFhABCf7yCRY8CAEQFQko48TkKAgQFgkoEBDkESwRxRAQ5BEtEQFpxBAQ5BEsESgJARAWCSjkxeMnCAEQFQkoEBDkES0AAAACAAD/zgP+AzIAHgA8AAATIREjLgEnJgYHAxQWFzMVIT8BMjY3Ey4BBw4BDwERASEVIy4BJyYGMREUFhczFSM1MxY2NxEiJg4BByM1BAMdJAFUPTxJAQIfHFH+SAJHBC4EAgRbJiVXDCMCdwF/EgEoHh0kDg4n0yECFgICKyQpBhIDMv70CpMUCgUB/VQDKQYvMQIaHgKgAQgHCVtJAgEG/kiABUYKBAL+tgEUAhgYAQ0NAUIECSwiggAAAAIAAAAAA7wDAwAWAC0AAAEuAQcGBz4BNzYmBwQCFzcWFx4BNz4BJS4BBwYHPgE3NiYHBAIXNxYXHgE3PgEBwRaDUAgHCMcgDAsQ/sU6FAECAhaDUE5PAdYWg1AHCAjHIA0MEP7FORMCAQIWg1BPTgEJT1IQAgN+xQ8IEwOm/oEQAQsLT1IPFIFRT1IQAgN+xQ8IEwOm/oEQAgwLT1IPFIEAAAAAAwAAAAADgALAAAsAFwAjAAATIT4BNCYnIQ4BFBYFIQ4BFBYXIT4BNCYHIQ4BFBYXIT4BNCbAAoAbJCQb/YAbJCQCm/2AGyQkGwKAGyQkG/2AGyQkGwKAGyQkAkABJDYkAQEkNiSBASQ2JAEBJDYk/wEkNiQBASQ2JAAAAAQAAP+9A80DcgAkACgALAAwAAABERQGIyEiJicRPgE3ITI2NCYjIQ4BBxEeARchPgE3ES4BIgYHBSEVIRUhFSEBFwEnA10jGv2bGyIBASMZAgUICgoI/fspOAEBOCoCZSo4AQEKEAoB/ZMBC/71AeD+IALCG/6HGwIk/fwaIyMaArUaIwEKEAsBOCr9Syo4AQE4KgJJCAoKCGklsCYCbBr+hxoAAwAA/5wD9wNIADMAQABYAAAFIS4BNSY2NzYWNz4BBzUGJicGLgIXLgE1PgE3HgEXFgYHNgYHFAYnDgEnFTAfAQ4BFRYBDgEHHgEXPgE3LgEnFwcOAS8BJicmNj8BNjIfATc+AR8BHgEGAij+FRIYAyJPLho4Py0BAh4KDwwDAhAEAwRWSFFMBQEFBBACAQ0QCR8CBREeIgEBHlt4AgJ4W1t3AwN3W3dsCRkDIR0KBwIFDwUPBStUBQ8FCwoIAyEBFxMHmDEWARYbFQFVAS07Ax4gNwEeMQ4xUgYGUDMOMR4BNxEPHgM7LQFSAwgnXzVQATMCfF5eewMDe15efAKXkgwBBRwYCgcOBRAFBSJ0BwIDBwcKDgAAAAMAAP/bA0oDJQAPAB8AKwAABSEuAScRPgE3IR4BFxEOAQM0JiMhIgYVERQWMyEyNjUnIyImNDY7ATIWFAYDBv30HSYBASYdAgwdJgEBJgcaE/4iExoaEwHeExrMoAkNDQmgCQ0NJQEmHQLCHSYBASYd/T4dJgLuExoaE/1sExoaExcNEw0NEw0AAAAAAwAAAAADqwLWACMARgBSAAABDgEHFAYiJic+ATcuATU+ATcyFhQGIw4BBx4BFzIWHwEVDgElPgE1LgEnDgEHFBYXDgEHFBYyNjc+ATczHgEXFBYyNjcuAScuASc+ATceARcOAQFeVXMCEhsRAQFUSh8iAmFIDhISDi49AQE9LgwRAgEBEgFyKCsCdFdXdAIsKV1rARIbEQECkm0JbZEDEhsRAQJu2DxRAgJRPD1QAgJQAVcCd1oNERENU4UiGUgoS2MCEhoSAUEwMEEBDgsBBg0RFx1TMFVwAgJwVTFUHSWbYw0REQ1rjwMDj2sNERENY507AU86O08BAU87Ok8AAAgAAP+ABAEDgQAZACMAKAAvAEcATwBTAFQAAAUOAQchLgEnET4BNyE3NjIfATMeARcVHgEXJQ4BFBY7ASMzJRcnBwUhNy4BJyMXMxchMSInER4BFyE+ATc1Iy4BNDY3MzU0JhM1IyIGFBYXNTMVIzUEAAFFNPz0NEUBATcqAYpWChgIF7opNwEqNwH8YhUcHBUjGQUBJc0aVv7yAbnQARsVqDydMPzEGxYBKR8DDB8pAWEqNzcqYRwcYRUcHBUwMAM1RwEBRzUC7yo5ASwEBioBOSpLATkq4QIyLB19BDAsfUsVHAF9Mg79gCAqAQEqIK8BOVU4AsgVHP5wZBwrHAFLMjIABAAA/4AD8gOAAFEAewCHAJUAAAEmPwE0JyY/ATYvASYPAQYnJicmLwEmJyMGDwEGBwYHBi8BJg8BBh8BFg8BFBcWDwEGHwEWPwE2FxYXFh8BHgE7ATI2PwE2NzY3Nh8BFj8BNicHJwcGDwIjLwEmLwEHJzc1Jjc1JzcXNzY/AjMfARYfATcXBxUWBxUXAQ4BBx4BFz4BNy4BAy4BJz4BNx4BFw4DA6oGAQICAQY+CQlaChNrCwkdHwkFNAgTtRIIMwUJIB0JC2sTCloJCT4GAQICAQY8CQlZChNsCwkiJgsFJQMPCrUJDwQkBQslIQkLaxMKWwkJhXoKLDQNK44rDTQsCnxIRwgIR0d7CScuCjmQOQouJwl7SEgICEj+VVFtAgJtUVJtAgJtUjZIAgJINjdIAgETJC8BRQoLJRITCwlXEBCdEAEKAQYUEAQJaBABARBmCAUPFAcBCgEQnBEQVwoLJRITCwpXEBGcEAEKAQcYEAULXAkKCglcCwURFwcBCgEQnBEQkAsHIxQFbW0FFCMHC3tlDDQ0C2V9CwcfEwRxcgQTHwcMfGULNDQMZQFlAm1SUW0CAm1RUm3+wQJINjdIAgJINxkvJBQAAAUAAP9/BAEDgAAVACsAPwBTAGAAAAEzHgEXFRQWMjY9ATQuAisBIgYUFgEVFAYrASIGFBY7ATI+Aj0BNCYiBgEjIiY9ATQmIgYdAR4BFzMyNjQmARQGIiY9AT4BNzMyFhQGKwEiBhUDNDYzITIWFAYjISImApLcKTcBDhUOFio1HdwLDQ0BSDko3AsNDQvcHTUqFg4VDv2f3Cg5DhUOAlI+3AoODv65DhUOAlI+3AoODgrcKDkxDgoD0AoODgr8MAoOA08BNyncCg4OCtwdNSoWDhUO/Z/cKDkOFQ4WKjUd3AoODv65OSjcCg4OCtw+UgIOFQ4CYQoODgrcPlICDhUOOSj+kgoODhQODgAbAAD/hAQjA4AAEgAkADEARABXAGcAeACIAJcAqQC8AMwA0ADUANgA3ADgAOQA6ADsAPAA9AD4APwBAAEMARAAAAEhIgYVHgEXMxUeATM+AScRNjQDIiYnNSMuASc+ATchMhYXEQYnFj4BNREjIgYeARczBTI2NzUzMjY3NCYnIQYVBxEGFhciJicRNDYzIR4BFQ4BByMVDgEDER4BMxY2NzUzPgE0JiMhARE0JiMmBgcVIw4BFRQWFyEHISImNDY3MzU+AhYXERYlDgEVFBYzIREuAQciBxUFFBczITI2LgEnIzUuASMiBgcTIiYnET4BMzIWHwEVMx4BFAYjJSEyNjQmJyM1LgEHIgYVAwEjNTMHMxUjJzMVIyczFSMnMxUjFTMVIzMjNTMXIzUzFyM1MwU1MxURFSM1IRUjNRE1MxUBITIWFAYjIQYmNDYBIzUzBBH+/hATAQ0E2gQRChALAQQaChIH1gcOAQEWEQECAwkBDTUIGwn+DRIBCgff/EsGDATfBxIBEQ7+9gQBAhMOERcEBwYBCxEWARAE2woQJAERDQQKBOMECRAK/voEBAsQCQ8G2wQNEQ0BAgT+/hAXEQTXBhMUFQQF/ucECRAKAQIDDAcOB/wnAgMBBhESAQwE5AYKBg4UAQUECQEBGQ4JDwYF2wcOFhH++gEGDREJA+gECgQKEAUCtBoaZSwscjExVyMjRh8fHx9pIyNlMTFtLCz+YEZGAj04OP1aAxgOEREO/OgMEhECTxoaA3cVDhAUA9IKCAEWCAEGBwP+zQwKzQQVExEXBAcG/vknGg0BDwoBBxIaDgPtCArWExAOFgQBAgL+9RATCBYRAQsECQEaERMRA9INDQEy/vkNEQEECtsDDhsR/BoBAgsYAQgKzgMUEA0TAwQXIRcEzQoMAQ8Z/v4NRgQODQoQAQITCAEN1j4DARYhEAPSCggRDv7tBgcBCxAXCQ0JzQQTIhoNERoPA9YKBAEQCv76Aj2rAaqqqqqqqqp/ycnJycnJycnJAfKqqqqq/g7JyQElERsRARIbEf7byQAAAAAE////gAQBA4AAEAAcACsAPwAAATIeAhQOAiIuAjQ+AjcOAQceARc+ATcuAQEuAScHFhcWFxYXHgEyNgEGAAcUFjI2NzY3PgE3NjsBJy4BAgAtUUAiIkBRWlFAIiJAUS16owMDo3p6owMDowGGCXtkKCokPiIdBQESGxL96cj+8BESGxIBBR0ifFFTWwUGAQwDQCJAUVpSQCEhQFJaUUAiQAOjenuiAwOie3qj/CV9zUEyGyM+UUVLDREUAewN/vbHDhQRDUtFUXwiIy4ICgAABgAAAAADwAJAAAMABwALAA8AEwAXAAATIRUhJSEVISUhFSEVIRUhJSEVISUhFSFAAQD/AAFAAQD/AAFAAQD/AAEA/wD+wAEA/wD+wAEA/wACQICAgICAwICAgICAAAAAB//4/4AJbgOAAAMAEwAXACAAJAAoAD0AAAEhByEBIQ4BBwMGFhchPgE3EzYmBRcBJwEhNwEXASE3FycDNxMBIRMhNwchNyE3IwcnPwEzBzM3MwchByEHBz3+Ax4B/gG9+F9FbQ16C0xFB6FFbA56C0z5rCr+wCsCMP3BDQGCK/6RAeBsKgLCN8MC3f2HNAJ5YAv8/wsBYh3RaSxtDz4Mww0+DQE/C/7BHQEGwwM9Al1F/UpFXwICX0UCtkVdhiT+myT+f0cBrSj+ZX0gzwFlJP6b/n8BJZIxMcOAHnVPMTExMcMAAwAA/8MDwQNCACAAPwBeAAAFLgEnJicOASsBIiY9ATMnNDY7ATY/ATYeAhURFA4BJgEeARQGBw4BIyImNTQ3Iz4DNC4CJyY1NDYzMhYTBiMiJjQ/AT4DNTQmJyY1NDYzMhceAxQOAgHQLWcrMzQJEgkQGB4BAR8XNFJDoxshEwcMGigBCBIXFxIIEAwQFwMBBQwMCAgMDAQDFxAMEDoLDhIaDwUZKyASRDMTGhIPDCE5KRgYKjkhKVomKisBAR0V4gEZIEY3iBMDHSkR/SoRJhcEAiwaNUk5GAkQGBAHBwoSExghGBQSCgYIEBcP/mQIGiYNBBEuNz4hQ28kDRcSGgoXPklSWVJKPgAAAAIAAP/xA48DDwAhAC0AAAEHHgEXDgEHLgEnPgE3HgEXNyMuATQ2MyEyFhURFAYiJicBPgE3LgEnDgEHHgEDVbojJQEEsIWFsQMDsYU5Zim6uwwQEAwBAAwQEBgQAf5WbJEDA5FsbZECApECrborZjeFsQMDsYWFsAQBJiK6ARAYEBAM/wAMEBAM/jkCkW1skQMDkWxtkQAAAgAA/+4DjwMPACQAMAAAJQcXFg4CLwEHBi4BPwEnJj4BMh8BNy4BNT4BNx4BFw4BBy4BNz4BNy4BJw4BBx4BAY1IXggBEBcJXnoLIAgLel0JAREWCV1JIyYEsIWFsQMDsYU5Zp9tkQICkW1skQMDkeVJXQkWEQEJXXoLCCALel4JFhEIXUgrZjeFsQMDsYWFsAQBJhIDkWxtkQICkW1skQAAAAACAAD/+gOcAyEADAAaAAAFIicBJj4BFwEeAQ4BISIuATY3ATYeARQHAQYDewwJ/RoMCSINAuUHAwcP/REKDwcEBgLmCRgSCP0bCQUJAuUNIQkL/RoHERIKChISBgLmCAESFwr9GwkAAAQAAP++A8ADQgAjAE8AdwB4AAAlFQcuASc0PgI3MzIXFjYnJi8BJic0NjczMhYXFgYHDgEnMScWNjc+AScuASMOARUeAR8BFhcWFx4BBw4BJicmJy4BIw4BHgEXMiMzMhcxFyInLgE+ARcWMz4BNy4BJw4BBxQWFxYOASYnLgE1PgE3HgEXDgEHOQEBcRI1RwMRJC0aBCghL1sbCQkDHgJFNgQtRAsNDlpaliIEEH9UTwoJBiIWHCMBCgUBBgYDAxMRMSNBMBgEAwYUDRwjAiMaAQEEBQWTW1MMCgsXDUdOo9gEBNijo9kEHhwHBRcZCCEjBf2+vv0EBP2+RAEBAkY1Gi8mFAEYEHojBwkEIy02SgM3LBqugXNABEAEMGlykBAYHAElGw4UBgEHAwMDEVtHLR8CCQIDBQgBJjQiAQHGIwYYGQoFHgTao6TZBQXZpDdnLwsZDwYLNnlAv/4FBf6/v/4FAAAAAAUAAP+LA/4DeAAjADEAPQBJAFIAAAUiJCc+ATczMh4BBgcOARceARc+ATc2JicuAT4BOwEeARcGBCcmAic+ATceARcGAg8BEw4BBxYSFzYSNy4BAy4BJz4BNx4BFw4BJw4BFBYyNjQmAgDG/s8ICshJAwkOAgwKg3ABAvfW1vcCAnKECgwCDgkDSsoKCP7Q1xr2EQSsgYKsAxH2GhABbZEDC744Ob4LBJFtPlECAlE+PlECAlE+KTc3Ujc3dVBPREMIDBMPAhU4EyRIAwNIJBM4FQEQEwwIQ0ROUaoQATnIgqwDA6yCw/7EERADIQORbZv+8TM1ARCYbZH+egJSPT5RAgJRPj1S8AE3Ujc3UjcABwAA/4AEAAOAAAsAFwAqACsANAA1AD4AAAEGAAcWABc2ADcmAAMmACc2ADcWABcGABMOASImJyYOARceATI2NzYuAQYlIxQWMjY0JiIGBSMUFjI2NCYiBgIA2f7fBgYBIdnZASEGBv7f2cH+/wUFAQHBwQEBBQX+/0UsiqGJLAsgDAo1oLygNQcDEhf+DjggMSAgMSACADkgMSAgMSADgAb+39nZ/t8GBgEh2dkBIfw/BQEBwcEBAQUF/v/Bwf7/ATY3PT03DQUeDUFISEEKFg8C9BghITAgIBgYISEwICAAAAAAFgAAAAAD3gLqAAAACQAVACEALgA7ADwARQBGAE8AUABXAFgAYQBiAGsAbABzAHQAewB8AIMAAAEjHgEyNjQmIgYDLgE0NjcnDgEUFhcBBx4BFAYHFz4BNCYBNDY3Jw4BFBYXNy4BJS4BJwceAQ4BBxc+AQEjFBY2NTQmIgYXIx4BMjY0JiIGBSMUFjY0JgYBIxQWNjU0JiIGNyMUFjI2NCYiBgUjFBY2NCYGFyMUFjY0JgYTIx4BNjQmBgH7TQErQiwsQivGO0JAODhETlBHApI6PUVGPTtIUlL9hyQgOSwxNC44IiYCJgE5MTsnLAEtIzkxOf2TKSkoFyMXmSkBFyIXFyIXAZwoKCkpKP5JKSkoFyMXmCkXIxcXIxcBnSgoKSkomigoKSkoKikBKCgoKAF2ISsrQiws/rw4lauTNztCsc+zQwK/OTeYsZk3OUO107X+4TFUIDwrc4d2KzwgVzJHeiw5IV1sXCA7LHoBhRgXFxgRFxeLEhcXIxcXAxcYGC4XF/3VFxcXFxEXF2oSFxcjFxcYFxcXLhcXjxcXFy4XFwJ0FxgYLhcXAAAEAAD/8wMaAw0ADgAbACcALwAAAQ4BBxc+ATQmLwEHFx4BAzAiMxc2EC8BBxcWEAcUNRc2NC8BBxcWFCc2NC8BBxciAtoBSUYsUFNTUAMtBEZJ9gICLXp6Ay0DZcstT08ELAM+fiEhA1RXBQGAY7RJLVDL48lQAy0DR7P+oyyAAUt+AywEbP7pBgUFLVPaVgMsBEOnASNdIwNTUwALAAAAAANZAm8ACQAZACoAOgBKAFoAawB7AIwApAC0AAATPgEeARQOASYnJTQ2OwEyFh0BFAYHIy4BNR0BFAYHIy4BPQE0NjczHgEVJzQ2OwEyFhcVDgEHIy4BNSc+ATsBMhYdARQGByMuAScTFAYrASImJzU+ATczHgEVNyMuAT0BNDY3Mx4BFxUOAQcFFAYrASImPQE0NjczHgEVPQE+ATczHgEdARQGByMuAScFDgErASImPQE0NjczMjY3NTQ2NzMeARc1DgEHIy4BJzU+ATsBMhYXFAMQEgsLEhADAewRDDkMEREMOQwREQw5DBERDDkMEawQDDoMEAEBEAw6DBCtARAMOgwQEAw6DBABcxAMOgwQAQEQDDoMEB05DRAQDTkMEAEBEAwBAhAMrQwQEAytDBABEAw6DBAQDDoMEAEBIAEQDKwNEBANOQwQARAMOgwQAQEQDHMMEAEBEAxzDBABAYAICAMOEg0ECAjlDRERDTwMEQEBEQx3PA0QAQEQDTwNEAEBEA2zDRERDTwMEQEBEQw8DRERDTwMEQEBEQz+mg0REQ08DBEBAREMWQEQDTwNEAEBEA08DRABlQ0REQ08DBEBAREMdzwNEAEBEA08DRABARANsw0REQ08DBEBEQx4DRABARANdwwRAQERDDwNERENAAAGAAD/wwL/Az0AJwA0AEEAUgBfAGsAACUuAScRPgE3HgEXFRQGIiY9AS4BJw4BBxEeARc+ATc1NDYyFh0BDgETIiY9ATQ2MhYdARQGJyImPQE0NjIWHQEUBiciJjUuASciJjQ2Mx4BFw4BAy4BPQE0NjIWHQEUBhchLgE0NjMhMhYUBgIAWncCAndaWncCCQwJAmZNTWYCAmZNTWYCCQwJAndqBgkJDAkJBgYJCQwJCUcHCAJFNQYICAZBVwIBCIkGCQkMCQnp/iIHCAgHAd4HCAhpAnhZAS1adwMDd1otBwgIBy1NZwICZ03+001mAgJmTV8GCAgGX1l4AUwIBw4GCAgGDgcIOwgHEAYICAYQBwhNCQY0RgEJDAkCVkEGCf2FAQgGpAcICAekBggCAQgNCAgNCAAAAAAFAAAAAAPOAvAACwAXACMAOwBTAAAlLgEnPgE3HgEXDgEDDgEHHgEXPgE3LgE3IyImNDY7ATIWFAYTIS4BJxE+ATczPgE3IR4BFzMeARcRDgEBDgEHER4BFyE+ATcRLgEnIycmJyEGDwECAVBrAgJrUFBrAgJrUD5TAgJTPj9TAQFT7z8JDAwJPwkLCyL9TC9DAgFAM2cLLR8BOR4tC2czQAECQ/0dHSwBASwdArQdLAEBLB2HBAsn/scoCwSiAmtQUGsCAmtQUGsBTgFTPz5TAgJTPj9TNwwSDAwSDP3pAUgyAb4tOQEdIQEBIR0BOS3+QjJIAnUBHh7+QiAwAgIwIAG+Hh4BEC4BAS4QAAAAAAL///9/BAEDgQAwADEAAAEiBgclNjU0JyUeATM+ATcuAScOAQcUFwUuASMOAQceARcyNjcFBhUeARc+ATcuAScxA0EwTxv+0gcIASwbUTFRbQICbVFSbQIC/sIaQydSbAICbFIoRhoBOgICbVJRbQICbVEBACkkmBkcHhuXJSsCbFJSbAICbFILC58ZHAJtUVJsAh4ang0NUmwCAmxSUmwCAAAABgAA//0DvQMFABsAHwAmADYARgBWAAABLgEnIQ4CFhcTERQWHwEeATMWPgE1GwE+AScBJxEzNyIGKwEnIRMuASsBIgYdARQWOwEyNjc1LgErASIGHQEUFjsBMjY3ES4BKwEiBh0BFBY7ATI2NwNVBhEL/S4JEggDBv0LC74DCQMMDwoL9ggDA/6wd4MSAgMCoMkCMtgBEQzuDBISDO4MEQEBEQzuDBISDO4MEQEBEQzuDBISDO4MEQEC6gkRAQERERgI/uj+ywkRBFUDAQEKDgsBjgEXBhQL/Wo4AQROA9/+JAsSEgsPDBISDKMMEhIMDgwSEgz+5QsSEgsPDBISDAAAAAABAAAAAAPRArgAFAAAASYiBwEOASclJiIGFBcFFjY3AT4BA8oIFAj+BA8mEv73CBQPBwEKHlAcAfwKAQKwBwf95g4BDP0HDxQH/R0DHQIaBxQAAAADAAD/vQPZA0MAGwApAC8AAAUhLgEnET4BNyEVIQ4BBxEeARchFjY3ETMRDgElIzQ2Nz4BFwcmBgcOASUnNyc3FwNi/T4kSAICSCQBIP7gFiwBASsXAsIHDwEsAin95SxJSFP9qQWf7ExAQgGwIKysIMpCATIoAsIoLQEsARkQ/T4QHgIBDyIBMP7QNyXHhchASjgTKxM1Qzmvmh+stx7WAAAAAAT/+f+pA+0DRAAQACEATwBTAAAlMh4CFA4CIi4CND4CBTIeAhQOAiIuAjQ+AhMyHgEGDwEOASMhFyEyFRQGJyEiLgIvASYnIyIuAjU0NhczMh4BHwEWHwIFJRcFAU0TIxoPDxojKCIaDw8aIgG/EyQaDw8aJCciGg8PGiLAHCAOAQRNDigd/eYOAgYxFBz95xQcEwsDMBMZTQ8VDQUcF2cVGQ4DBQICBAYCl/1/JAIXaQ4ZIycjGg8PGiMnIxkOAg4aIicjGg8PGiMnIhoOAkYPFxkK3ikgXCoUHwETHSMP/2h/DxYYChUbAQ0SChEIDx4nWwK5AQAAAwAA/74DqQNCABMAIQA9AAABLgEvAQcOAQ8BFQISHwE3NhIDNQEmAhM3PgE3HgEfARICAyM1NCYiBh0BIyIGFBYXMxUUFjI2PQEzPgE0JgNsTsFKERJNwU05BbHyCAfwsAX+XNKZAgZNwlRTw04FBZBVbBAZEWsNEBANaxEYEWwMERECxAktOBAPOC4JCBb+of7DSgIDSQE9AV8W/UBBARQBOAEILTc2LQkB/tn+4QFkbAwREQxsERkQAWsNEBANawEQGREAAAAEAAD/gAO1A4AAEwAmADIAPwAAAR4BFxQGBwYPAQYiLwEmJyYnPgEHBgcWFxYfATc2Nz4BNS4BJwYHFz4BNx4BFw4BBy4BFxYXPgE3LgEnDgEHFgIAufcFGhhIlZAJGgqQlUgwAQX3TmkEAShDjnp5j0MUFQTRn59oRQJtU1NtAgJtU1NtZCQ4OEgBAUg4OEgBAQOABO6zM2Msc5KLCQmLknRaZ7PupmSWVkxsi3Z1jWolUyiZyQQEZftSbgICblJSbgICbgokAgJKNDlJAQFIODgAAAMAAAAAA8ECwgA4AEcAVgAAAS4CJw4DBwYeATY3PgE3PgE3HgEXHgEUBgcOAQcmJyYnLgEnLgEOARceARcWFxYXPgI3NjQFMjY3NjQnLgEjDgEHHgETMhYXFhQHDgEjLgEnPgEDmiOBo1pKiHVVFgUIFxYFEkcyNXpDUZI5Nzk5NzmSUUQ/SzwsPA4FFhcJBBJINUNURktao4EjJv45K0sWGRoXSStGXgICXkYbLw8REA8vHC08AgI8AgE6WC4BAR88UjILFgoICyhEGhsbAQEpJyVeZl4lJykBAQ4SJBpBJgsKCRYMLlAgKBQQAQEuWDo9iOkqJChfKSQoAV5GRl4BDhkYGj0aGBoBPC0tPAAAAwAA/4AEAAOAABAAIQA3AAAFIi4CND4CMhYXFhAHDgEDIg4CFB4CMjY3NhAnLgETIi8BJi8CJicRNDYyFhURFx4BBwYCAGe6kk1NkrrOukmWlkm6Z1ukgkREgaW2pUGEhEGkdQgHtQoCAQMaARIbErgMCgYJgE2Sus66kk1NSZ3+Z55JTQPFRIGltqWBRERBiwFqi0FE/XgEbQYCAwEPDAEtDRISDf7gagcZCxAAAAACAAAAAAN9AskACwAjAAABJyY0PwE2FhcVDgEDIiY0NjchPgE3NS4BJyE1IR4BFxUOAQcBZdoQENoQHAEBHLISGBgSAahQawICa1D+6wEUdJsDA5p1AZ92CiELdQgREusSEf6RGCMYAQJrUA5RawJVA5t1DnSbAwAC//7/ygQAA2wAOwA8AAAJASYiBwEOARcWMzI2MzcRHgEXIT4BNREzER4BFyE+ATURLgEnDgEVESMRLgEnIw4BFREjESUBFjY3NiYnA/L+OAsZCv4SCgYHDRMHCgcuARYOAQ4SFJkBFg8BCREUARYOEhO/ARYO5REUwwFYAbEPHQcOAwsCFQFPBwf+sQgdDg4EIf33DxYBARYPAQT+/A8WAQEWDwGHDhYBARYO/p4BBQ4WAQEWDv77Ahfp/sMKBg4LHQsABQAA/48D5QNzABAAGwAgACsAMQAAJTY3AT4BJyYnLgEHAQYHAzcBNjc2HwEWDwInBxYXAScHNj8BFw8BBi8BJgcUFSE1IQFrBQMCVxkCGCYnGj8b/aoEAoMFApA5ORIRShAPdgJrDTY2/jpsOxcYAmgFbwQEHAKbA7r8Rl8BBAI1GT4aKCcZARj9ygMF/swBAto2NhERTRAPbwJuDDc3/lNvgTg4A2sCKQIDHAPxAgMuAAAABAAA/4AEAwOAAAsAFwBrAG8AAAUmACc2ADcWABcGAAMGAAcWABc2ADcmABMGJyYvAQ4BBzIXMhYXDgEjDgEnJicHJwYHBiYnIiYnPgEzNjcuATcGBwYmMSY2NycmNhcmNjc+AzcyFx4DBzUyHgEGHgEXFAcWHwEeAQcGJTA1FAIB2v7fBgYBIdraASIGBv7e2sj+9QUFAQvIyQEKBgb+9kcEBQsJCwglGAsLAygDAywDBWotBwMQCgMHLWoFAywEAykDCAgvGAESCAYGCSoOBAYMAQEWAwITKks4JhwrNxsIAQERAwcBCwEBBwYGFxIDAf4lgAYBIdnZASEGBv7f2dn+3wPRBf72yMj+9gUFAQrIyAEK/ZACAgYMDCg3EgMVExQSAwYfBgUBAQUGHwYDEhQTFQIBJ0oDGAUDBTNaEgUaEgEYFwEZSUUrAgoONUM9EgoKHRQFCwwDAwgICCNKFQvmDQ4AAAAACQAA/4AEAAOAAAwAGQAxADsARQBXAGIAbQBuAAAFJgAnNgA3FgAXBgAHEQYABxYAFzYANyYAJxMyFy4BJw4BBxQWFwc3HgEzMjcmNT4BNycyFhQGIiY0NjMHIiY0NjIWFAYjBS4BJw4BBx4BFz4BNxcnPgE3JSImNDYzMhYUBiMzIiY0NjMyFhQGIzECANn+3wYGASHZ2QEhBgb+39nR/uoFBQEW0dEBFgUF/urRdQwMEopdaYwCNC4YVhcpFwsMCAJ2WoUOEREcFhYOrA8WFh0QEA4CEwN5Vlp2AgJ2WhIlE0QTJjAB/uoKDw8KDhAQDogKDw8KDhAQDoAGASHZ2QEhBgb+39nZ/t8GA+wF/urR0f7qBQUBFtHRARYF/l8BTGABAnZaM1MhSisECAEZG1NuAkMQHRAQHRA9EB0QEB0QxkxlAgJlTExmAgEHBSU9HEkqHw8TDw8TDw8TDw8TDwAAAAAJAAD/gAQAA4AACwAZACUALwA5AFMAcgB9AI4AAAEGAAcWABc2ADcmAAMmACc2ADcWABcUDgIDDgEXFBYXFjY3NiYHBiY0PgEWFAYHNwYmJyY3NhYXFiUOASImNTQ3NicuAQcGBy4BNDY3MzYXHgEPASY2NzYmJyYGBwYmNzYmBw4BBw4BFx4BFxY2NzYmJwcGJic+AhYXDgETNiYHLgE0Njc2FgcVBgciJgIA2v7fBQUBIdraASEFBf7f2sb++gUFAQbGxgEGBUaEqqg/EwQMCyJKFxYhWQsSDxkSEAwzAwkDBQkDCgMEASgCCxAMBAoQEDchCQoJDAgIAiQkLkoWgw4CAQkDChpKBBQBBQEfNjpQAhMUAQx8Q0WQHhczFLtEWQICWIlZAQFZkwMILgYICAYGTAMCDAYIA4AF/t/a2v7fBQUBIdraASH8NAUBBsbGAQYFBf76xl2qhEYBwQ5RBQ4WBxUOICFYgQMOGBIEDxgQAjAEAQQLCAMBBAt2DQYMCQsLJyUcHgMDAgENEQ0DCAgJX10ZBAwBDyIOFRYDBxESFykSGl4FGTogUT8EBTs/PTkDzwI8MTFCBzUwMUoBAQU4BgEIDQgBBRJKAgwBCQAAAAAFAAD/gARFA4AACAARABoAKAA6AAATHgEyNjQmIgYXHgEyNjQmIgYXHgEyNjQmIgYFJgAnBgAHHgEXByE2AAUhNzYmJy4BNTYkNxYEFw4BB/wBJDYjJDUl/AEkNiMkNST7AiQ1IyQ2IwFQBv7M6On+zAYBRUAxAfPbAR3+Bf6AFQYLEDc5BQEDxcUBBQUF8LgBaxsjJDYjJRobIyQ2IyUaGyMkNiMkBtkBIQYG/t/ZX61FrxUBHd1FFioROI9OtfEFBfG1q+8RAAIAAP+kA9MDMQAkAE4AAAEiBhURDgEjISImNRE0NjczMjY0JisBDgEHER4BFyE+ATcRNCYFBhYzMjY3JjY3Njc2NwcGFBYyPwQ1LwQmDgEWHwEGBwYHDgEDsw0RASYd/XkcJycc0w0REQ3TN0gCAkg3Aoc3SQES/XACEg4LEQIBMD5LZ2J6ZAgSGQqbBQIBAQQFB+YMFgkMDFpiVnNSQzQBZBEN/t8dJiYdAl4dJQESGhEBSTb9ojdIAQFINwEhDRGzDhYOCwWZZG9APQ5mChkRCaAIBQUFBQgHBE0DCxgWBB4VNUd7a6UAAAAABP///38EAQOAADIAOwBxAHIAAAEUBxYVFAcWFRQHFgYrASInJicmKwEiJjURNDY7ATY3Njc+Ajc2MzIeARUUBzMyFhcWATY0JiIGFBYyATQmKwE0NjU0JiMOAQcGBwYPAg4CBwYHIxEzMhcWHwIWOwEyNTQnPgE0JzY1NCcmJzI2NQQAIQYZAigBc15WQD5JSE0PwCIzMiO3GEMnIRAPGR0aIjhZLyB1Ij4YM/yeDBkiGRkiAxozI+pAK0AREA0LHA8lJBcNGhkPCw0VFQkMGBgXK41XUYAEFBgMIwcFCxUdAag3LRUWLykNDj4vVmMOERcZLiABiiEuD1EuIBBKURoXKFVIOT0YFy/+QgshFxYiFwGKIC8kfSQ8OhBIKScdDiorGw4YFQYFAf53AgQHBw4tZxASCi0uEx8qEBIRDTkVAAAAAv/+/5IEBQNsACgAUQAABSIvAQcGIyInLgE/AScuAT8BPgE/Aj4BMhYfAh4CBg8BFxYGBwYlFxYXMj8BPgEvATc+ASc1LgEvAi4BIgYPAgYHBhYfAQcGHgEzMjcDBRYX1tgVGCAaFxQFLKkUDgcBCSod6moNLzkvDGnrHCoQDBWpKwUUFxn+3OMLCQ0IAQoJAi61CAYEBBAL/HEEFBcTBXH8FwkDBgi1LwIJEw0KCG0La2sLExI0HOSeFDUcARwjBCvWGh4fGdYpBCU2NhSg5RwzERTEcgUBBwEIFQv3qwkWCwILDgIs5QoMDQrkLgMZCxcJqfcLFQ8EAAAAAwAA/7gDyANIAAsAGAApAAAFJgAnNgA3FgAXBgA3NjcuAScOAQceARc2AzMyFhQGKwEiJjURNDYyFhUCAML+/wQEAQHCwgEBBAT+/1hwBAThqKvgBATfq6urxwwQEAzjDBEQGRBHBAEBwsIBAQQE/v/Cwv7/qXGoq+AEBOGoq+AEBAGKEBkQEQwBHAwQEAwABwAAAAADJgMtAAkADQAdAC0AMQA1ADkAAAEhIgYHFSE1LgEnFyM/ASMiBg8BBhY7ATI2LwEuARchIgYXEx4BFyE+ATcTNiYBIwMzEyMRMxMjETMC3P5JHykBAkkBKZ8LzQu9ww8YAg8BERDzEBICDgMXdv4xFBoCKAIeFQFtFR4DJwIZ/qJJGGGSYWF6SWICyyofGBgfKjFNTTEVD2YQFBQQZg8V9BwU/kkVGwEBGxUBtxQc/koBVf6rAVX+qwFVAAAAAAMAAP/AA8ADQAAeACoANgAAASc3NjQmIg8BJyYOARQfAQcGHgEyPwEXFjI3MTY0JxMmIAcGEBcWBDc2EAEmEDc2IBcWEAcGIAKLWFkKFR0LWVgMHBQJWVkKARYbC1hZCxwLCwqxiv6aiYODiQFmioP9K3JyeQE4eXNzeP7HASdYWQsdFQpZWQoBFhsLWVgMHBQJWVkKCgsdCwGVg4OJ/pqKggGDigFm/jh4ATl5cnJ5/sh5cwAAAAADAAD/iQPxA3cACwAXACcAAAEOAQceARc+ATcuAQMuASc+ATceARcOAQUnJiIPAQYUHwEWMj8BNjQBpKzkBATkrKvkBATkq4axBASxhoWxBASxAbzHCx8MEwoKxwweDBMMA3YE5Kys4wUF46ys5P02A7KFhrEDA7GGhbLPyAoKEwsfDMcLCxIMHgAAAAcAAP+cBAADZQAgAD8ATABZAGcAdACAAAAlIiY0NjMyNjc+AT0BNC4CIy4BNDY3Mh4CHQEUDgIhIi4CND4COwEyFg4BKwEiDgIUHgIzHgEVFAYTBi8BJjQ2Mh8BFhQGByImND8BNjIWFA8BBgEiLwEmNDYyHwEWFAcGJwYmND8BNjIWFA8BBhcjIiY0NjsBMhYUBgK9DBEQCjViJyYpKExhNQwPEAtBdVwxMVx1/l5Gf2M0NGN/Rl0MDwEQCl06alMsK1RqOgwPD5IKCoUJEhYJhggPkQoTCYUJFxEIhQsBHgoKgwkSFwmFCAgKkAoTCYUJFxEIhQuSWgwPEAtaDA8PIxAXDygnJmE3LzViTigBEBUQATFcdkEvQXVcMTRlfYx/YzQRFRAsU2t0alMsARALDRICggEKhQkXEQiGCBcThxIXCYUJEhcIhgn9gAmECBcSCYUJFwkHiAETFwmFCRIXCYUKARAXDxEVEAADAAD/oAPgA2AACwAoACkAAAEGAAcWABc2ADcmAAMGIi8BBwYiJjQ/AScmNDYyHwE3NjIWFA8BFxYUBwIAzP7xBQUBD8zMAQ8FBf7xAgsbCpqaChsUCZqaCRQbCpqaChsUCZqaDAwDYAX+8czM/vEFBQEPzMwBD/1bCQmamgkUGwqamgobFAmamgkUGwqamgobCwAAAf///4AEAAOBACcAAAUiLgI1NDYyFhUUFx4CMjc+AjQnLgIjIiY0NjMyHgIUDgICAGe6kk0VHhUjIXqgtFFPekQjIXqgWg8VFQ9nupJNTZK6gE2SumcPFRUPWlFPekQjIXqgtFFPekQVHhVNkrrOupJNAAABAAAAAAODAlwAFQAANwkBFjI3MTYmJwEnJicmBgcBBh4BMr4BQAFGCx0MCwIN/qkIBAMKFwv+pAgBFh2/AUX+vgsLCx0LAVgHAwEEBAj+owsdFgABAAAAAAK9AtoAEAAACQE2NCYiBwEGFBcBFjI2NCcBjwEkCRMZCf7FCQkBOwkZEwkBgAEkChkTCv7GChkJ/sUJExkKAAEAAAAAArsCxgARAAAlIiY0NwkBJjQ2MhcBFhQHAQYBaA4UCgEY/vMKFB0KASQKCv7QCiMVGwsBHAERChwVCv7XCxwK/ssKAAEAAP/gA8ADIwAaAAABDgEHLgEnDgEHHgEXHgEXFjI3PgE3PgE3LgECxDtmJCRmO2uOAwEuGlPxFA8iDxPyUxotAgOOAyMBNjAwNgEDnHVEZCZ20wwMDAzTdiZkRHWcAAEAAAAAA4kCawAFAAABJwkBBwEDiDf+rv6yOAGGAi85/rEBUTf+dgAAABIA3gABAAAAAAAAABUAAAABAAAAAAABAAsAFQABAAAAAAACAAcAIAABAAAAAAADAAsAJwABAAAAAAAEAAsAMgABAAAAAAAFAAsAPQABAAAAAAAGAAsASAABAAAAAAAKACsAUwABAAAAAAALABMAfgADAAEECQAAACoAkQADAAEECQABABYAuwADAAEECQACAA4A0QADAAEECQADABYA3wADAAEECQAEABYA9QADAAEECQAFABYBCwADAAEECQAGABYBIQADAAEECQAKAFYBNwADAAEECQALACYBjQpDcmVhdGVkIGJ5IGljb25mb250CmdyYWNldWlmb250UmVndWxhcmdyYWNldWlmb250Z3JhY2V1aWZvbnRWZXJzaW9uIDEuMGdyYWNldWlmb250R2VuZXJhdGVkIGJ5IHN2ZzJ0dGYgZnJvbSBGb250ZWxsbyBwcm9qZWN0Lmh0dHA6Ly9mb250ZWxsby5jb20ACgBDAHIAZQBhAHQAZQBkACAAYgB5ACAAaQBjAG8AbgBmAG8AbgB0AAoAZwByAGEAYwBlAHUAaQBmAG8AbgB0AFIAZQBnAHUAbABhAHIAZwByAGEAYwBlAHUAaQBmAG8AbgB0AGcAcgBhAGMAZQB1AGkAZgBvAG4AdABWAGUAcgBzAGkAbwBuACAAMQAuADAAZwByAGEAYwBlAHUAaQBmAG8AbgB0AEcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAAcwB2AGcAMgB0AHQAZgAgAGYAcgBvAG0AIABGAG8AbgB0AGUAbABsAG8AIABwAHIAbwBqAGUAYwB0AC4AaAB0AHQAcAA6AC8ALwBmAG8AbgB0AGUAbABsAG8ALgBjAG8AbQAAAAACAAAAAAAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGQBAgEDAQQBBQEGAQcBCAEJAQoBCwEMAQ0BDgEPARABEQESARMBFAEVARYBFwEYARkBGgEbARwBHQEeAR8BIAEhASIBIwEkASUBJgEnASgBKQEqASsBLAEtAS4BLwEwATEBMgEzATQBNQE2ATcBOAE5AToBOwE8AT0BPgE/AUABQQFCAUMBRAFFAUYBRwFIAUkBSgFLAUwBTQFOAU8BUAFRAVIBUwFUAVUBVgFXAVgBWQFaAVsBXAFdAV4BXwFgAWEBYgFjAWQBZQALcGF0aGZpbmRpbmcJcmVtb3ZlYnRuBGRyYWcJcGljay1kb3duB3BpY2stdXAEbGluZQ1jaGVjay1ib3gtY2tkCGNoZWNrYm94CGNhbGVuZGFyBXJldHJ5A2tleQVzdGFyMgRkb3duA2FkZA1jbG9zZS13aXRoLWJnBW9yZGVyBmxvZ29mZgNwd2QGcGhvbmUyBmRlbGV0ZQVlbXB0eQZlbXB0eTIHcmVtb3ZlMgZlbXB0eTMOaWNvbi1yYWRpby1ja2QFcmFkaW8DbXNnCGxvYWRpbmcyBXRvdG9wBWhlbHAzBWhlbHAyA2tmMwNrZjIDa2YxBWhlbHAxBGFkZDILYmFjay1kZWxldGUGY2VudGVyBnN0cm9uZwdzcC1saW5lA2ltZwRsaW5rA3R4dAVxdW90ZQRtZW51B2FydGljbGUEdXNlcgVwaG9uZQZmcmllbmQGd2FsbGV0A3NldARzY2FuCHNhb21pYW8xB2FjY291bnQHc3BsaXRlcgdnb25nZ2FvB3NwZWFrZXIDYm95BGdpcmwGY2xvc2UzA3RlbAdhZGRyZXNzBGZhY2UEd2lmaQV2b2ljZQhrZXlib2FyZAptaWNyb3Bob25lCnBob3RvZ3JhcGgGc2hhcmUzBmZpbHRlcgVyaWdodAVzaGFyZQxzaG9wcGluZ2NhcmQEc2FmZQhwb3NpdGlvbgNleWUFdGltZTIEYmFjawRob21lBXdyaXRlAnFxBndlaXhpbgV3ZWlibwhjb21tZW50cwZzaGFyZTIDemFuBHN0YXIEdGltZQZyZW1vdmUGY2xvc2UyBnNlYXJjaAdyZWZyZXNoBWNsb3NlB2xvYWRpbmcIYXJyb3ctdXAKYXJyb3ctbGVmdAthcnJvdy1yaWdodAhzaG91Y2FuZwphcnJvdy1kb3duAAA=) format('truetype');
}
.grace-icons{font-family:"grace-iconfont"; font-style:normal;}
.icon-pathfinding:before{content:"\e639";}
.icon-remove-btn:before{content:"\e636";}
.icon-drag:before{content:"\e727";}
.icon-pick-down:before{content:"\e69d";}
.icon-pick-up:before{content:"\e638";}
.icon-line:before{content:"\e62d";}
.icon-pick-line:before{content:"\e62d";}
.icon-checkbox-ckd:before{content:"\e62c";}
.icon-checkbox:before{content:"\e692";}
.icon-date:before{content:"\e6d1";}
.icon-calendar:before{content:"\e6d1";}
.icon-retry:before{content:"\e635";}
.icon-key:before{content:"\e630";}
.icon-star2:before{content:"\e634";}
.icon-down:before{content:"\e633";}
.icon-add:before{content:"\e6c7";}
.icon-close-with-bg:before{content:"\e632";}
.icon-order:before{content:"\e62f";}
.icon-logoff:before{content:"\e62e";}
.icon-pwd:before{content:"\e641";}
.icon-phone2:before{content:"\e629";}
.icon-delete::before{content:"\e7a5";}
.icon-empty:before{content:"\e736";}
.icon-empty2:before{content:"\e704";}
.icon-remove2:before{content:"\e62a";}
.icon-empty3:before{content:"\e644";}
.icon-radio-ckd:before{content:"\e7f8";}
.icon-radio:before{content:"\e762";}
.icon-msg:before{content:"\e666";}
.icon-loading2:before{content:"\e95a";}
.icon-totop:before{content:"\e637";}
.icon-help3:before{content:"\e67c";}
.icon-help2:before{content:"\e646";}
.icon-kf3:before{content:"\e626";}
.icon-kf2:before{content:"\e6cf";}
.icon-kf1:before{content:"\e67b";}
.icon-help1:before{content:"\e628";}
.icon-add2:before{content:"\e625";}
.icon-back-delete:before{content:"\e623";}
.icon-center:before{content:"\e621";}
.icon-strong:before{content:"\e640";}
.icon-sp-line:before{content:"\e61b";}
.icon-img:before{content:"\e63d";}
.icon-link:before{content:"\e61e";}
.icon-txt:before{content:"\e9e4";}
.icon-quote:before{content:"\e620";}
.icon-menu:before{content:"\e614";}
.icon-article:before{content:"\e624";}
.icon-user:before{content:"\e60d";}
.icon-phone:before{content:"\e60e";}
.icon-friend:before{content:"\e611";}
.icon-wallet:before{content:"\e612";}
.icon-set:before{content:"\e613";}
.icon-scan:before{content:"\e61d";}
.icon-scancode:before{content:"\e631";}
.icon-account:before{content:"\e6fe";}
.icon-spliter:before{content:"\e69b";}
.icon-gonggao:before{content:"\e62b";}
.icon-speaker:before{content:"\e656";}
.icon-boy:before{content:"\e618";}
.icon-girl:before{content:"\e619";}
.icon-close3:before {content:"\e610";}
.icon-tel:before{content:"\e60c";}
.icon-address:before{content:"\e63f";}
.icon-face:before{content:"\e66e";}
.icon-wifi:before{content:"\e61f";}
.icon-voice:before{content:"\e800";}
.icon-keyboard:before{content:"\e627";}
.icon-microphone:before{content:"\e617";}
.icon-photograph:before{content:"\e60b";}
.icon-share3:before{content:"\e622";}
.icon-filter:before{content:"\e686";}
.icon-right:before{content:"\e60f";}
.icon-share:before{content:"\e615";}
.icon-shoppingcard:before{content:"\e60a";}
.icon-safe:before{content:"\e687";}
.icon-position:before{content:"\e61c";}
.icon-eye:before{content:"\e609";}
.icon-time2:before{content:"\e64c";}
.icon-back:before{content:"\e616";}
.icon-home:before{content:"\e608";}
.icon-write:before{content:"\e69e";}
.icon-qq:before{content:"\e63c";}
.icon-weixin:before{content:"\e63e";}
.icon-weibo:before{content:"\e6cd";}
.icon-comments:before{content:"\e6b8";}
.icon-share2:before{content:"\e606";}
.icon-zan:before{content:"\e6ea";}
.icon-star:before{content:"\e645";}
.icon-time:before{content:"\e607";}
.icon-remove:before{content:"\e684";}
.icon-close2:before {content:"\e78a";}
.icon-search:before{content:"\e604";}
.icon-refresh:before{content:"\e61a";}
.icon-close:before {content:"\e602";}
.icon-loading:before{content:"\e9db";}
.icon-arrow-up:before{content:"\e654";}
.icon-arrow-left:before{content:"\e600";}
.icon-arrow-right:before{content:"\e601";}
.icon-arrow-down:before{content:"\e603";}
.icon-shoucang:before{content:"\e605";}
.icon-right-margin:before{margin-right:10rpx;}
.icon-left-margin:before{margin-left:10rpx;}
/* Logo 字体 */
@font-face {
	  font-family: 'iconfont';
	    src: url(data:application/vnd.ms-fontobject;base64,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);
	    src: url(data:application/vnd.ms-fontobject;base64,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?#iefix) format('embedded-opentype'),
	        url(data:font/woff2;base64,d09GMgABAAAAAAPAAAsAAAAAB/QAAAN0AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDHAqDNIMNATYCJAMQCwoABCAFhG0HQRv4BhEVnJ3IfibYtinSe7SVhU9v5DDITPBAeW9vkHQ+uhCZvwCvla4LuICue1KCvAEC2Cb7vdMVUMSoE6ZT9vnWs3zHT7sv73JhCCMQEiH5n2OmSyvQ/EBziWqNCBrtDXAUrQOKrAuQA74TxluG13apDuswBOCliU7EqDGTZmHQ0FECEJvWr12OyajRDbECg8CuWKgRV7Aw8qz8DFwO/l5U0CMDEktBJ05bM3oVw4rt4mzluA6LPASc6YyAuAwU0AloEOsqzStQkbsThVfvrukDDAYJU7WEF2e7LnKPIjbsXx4oJAKEDY32A9ALPQqKbYedSMJBiQRJNnAf4CANgAFs4DjwGoqr5dMlCtGqSjV/s9oLSUqa7UtJmSt8JCbO8uK7GeqEeL3nbgVu1/UJb3LyHLG2XihrmMkPVykpe70hEfW4G9naFx7dUNyPWpBYOCupaHZKydzk4n2J5+Y8TO7wc05yYG58Ssdfc1POz36Q1P7H7CRnVkzi98SD5yKc2qtvOrVW3XAiz9ddGx3osOZWwf1tctmZwBox1Gb4F8pl2/ovloGomKNS9OuaULVeVf9tv1vPTejaTxyVZR1lYPHEdwvlgYNyUWdkoH2hWCRSF7CtY+E2N8h2ty1z7SD37KxUq1kzK5UpmzVzhToRpCEDApS6OK15gt1h2eguXz83n9R8WgKJt7uMrvw4qWUVMablph9Gdqf6B1YZXvfO032RDSITNt6tO7xRhG/4M+FPbko40wBw76uRalXgfamqq0G/chDvn5ypvbTqoN/G9yhFGYc6PMUM19EoB/4HpzPnx3E65miDRzVaa3yHbU7TDKIAkICX6eB3+aGG7CmstZEYBUbiAEiCaAwKQyukxnYCCx+9wMYwFLx0NHa5j5p2oBDaA3RwkoCgmniQLlU8A6WaL1Jjf2Cp4w+2ainwmlPNDX3atN6MFDSKntoPTR3v1tapCWMvGMLaSJoXkB4QP9ko79It76uxQ6aY4p9DoWrJCm9UkeOwrkxOeEanyajqyrTrmqpvSjreImMkAQ0FPdL6IJMO21mvsDKF37+AQbBqSENWnf4BhDe1T3KJrAOyFu+dsm7lGe9pUFDKIpZgG1IRI6zMzYir7jeDjkqMA5xOKcV8tqs4WV66veEqwIvO1kmhhBYW1sJn2s+X3ytWeD3t5xBF) format('woff2'),
	        url(data:font/woff;base64,d09GRgABAAAAAAVUAAsAAAAAB/QAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFY8fEjQY21hcAAAAYAAAABfAAABnLTMHslnbHlmAAAB4AAAAXIAAAG0AxKbKmhlYWQAAANUAAAALwAAADYaF5l5aGhlYQAAA4QAAAAcAAAAJAfeA4VobXR4AAADoAAAAA4AAAAQEAAAAGxvY2EAAAOwAAAACgAAAAoBJgCwbWF4cAAAA7wAAAAfAAAAIAERAEhuYW1lAAAD3AAAAUUAAAJtPlT+fXBvc3QAAAUkAAAAMAAAAEEq5/tOeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2BkYWCcwMDKwMHUyXSGgYGhH0IzvmYwYuRgYGBiYGVmwAoC0lxTGByesT27x9zwv4EhhrmBoQEozAiSAwDuHQzHeJztkLENgDAMBM8koAgxCiViICo2y1Yps0ZwPhQMwVtn2S/LxQMzEJzdiWA3RtflrskPrPIjp+/Ja4K61FxLa99JMl0kfZ36B1v4takf7xZ6agPNeaBcywB7AIesF1YAeJxtT00vA2EQnmeHt2yb6rLbtyjarewefJR+7KJKhDgIEQc3e5BIXByIk+ih6UU4Sxz4ByLhF0ic3J3q4tKfIcu7EU6Sycw8mXmeeYY0oq9bXuUDKtAs0YA1Cllegl8tW0nEClZ5FKaYgO1UPL9cLUzDrUKtGGqoemMJHb48XD685Hm+b6KYMIzETSKVShTRvGds7p0zn+8FUcbd8X5L01r7x0HrgeeQyqRUYI4fWgFeuRGEb0FD0xoBpoIGsfLV7CJuUh8NU40IBXXyz53nsKBYmqRHvkMsbFe5cVyn4qup53slqTbTMm3GkqyddDI5IJfpyKhctcMPIZBvt5EXIvyY2XnccddH6rZtG8Wx+tYPrI1HsL6mTf4RZSSzqxjtSKEb+fd35D9P/6H9woUSQf3xwgYvkql+sIUpoUxBmOmSD6/iwsFg0tLxHI+H27qVjeMplwtX4lzrl73hhT6kh9e67O/FUXYji7MeIvoGGapamgAAeJxjYGRgYADiG3s7ROP5bb4ycLMwgMDtGq19CPp/AwsDcwOQy8HABBIFADK4CnEAeJxjYGRgYG7438AQw8IAAkCSkQEVsAAARwoCbXicY2FgYGBBwgAAsAARAAAAAAAAAEwAsADaAAB4nGNgZGBgYGGwYWBmAAEmIOYCQgaG/2A+AwAO1wFaAHicZY9NTsMwEIVf+gekEqqoYIfkBWIBKP0Rq25YVGr3XXTfpk6bKokjx63UA3AejsAJOALcgDvwSCebNpbH37x5Y08A3OAHHo7fLfeRPVwyO3INF7gXrlN/EG6QX4SbaONVuEX9TdjHM6bCbXRheYPXuGL2hHdhDx18CNdwjU/hOvUv4Qb5W7iJO/wKt9Dx6sI+5l5XuI1HL/bHVi+cXqnlQcWhySKTOb+CmV7vkoWt0uqca1vEJlODoF9JU51pW91T7NdD5yIVWZOqCas6SYzKrdnq0AUb5/JRrxeJHoQm5Vhj/rbGAo5xBYUlDowxQhhkiMro6DtVZvSvsUPCXntWPc3ndFsU1P9zhQEC9M9cU7qy0nk6T4E9XxtSdXQrbsuelDSRXs1JErJCXta2VELqATZlV44RelzRiT8oZ0j/AAlabsgAAAB4nGNgYoAALgbsgIWRiZGZkYWRlYE1p7QyM48zMzk/T7cktbiEuTK/lIEBAFyoB1g=) format('woff'),
	        url(data:font/ttf;base64,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) format('truetype'),
	        url(/static/img/iconfont.09ba57ef.svg#iconfont) format('svg');
}
.ws-icon{
		font-family: "iconfont"!important;
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
}
.ws-flex{
	display: flex;
	align-items: center;
}
.ws-padding{
	padding: 30rpx;
}
.ws-text-df{
	font-size: 28rpx;
}
.ws-text-red{
	color: red;
}
.ws-text-blue{
	color: blue;
}
.ws-text-center{
	text-align: center;
}
.ws-flex-between{
	display: flex;
	align-items: center;
	justify-content: space-between;
}
/**
 * flex
 */
.lp-flex {
	display: flex;
	flex-direction: row;
}
.lp-flex-column {
	display: flex;
	flex-direction: column;
}
.lp-flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.lp-flex-space-between{
	justify-content: space-between;
}
/*
  ColorUi for uniApp  v2.1.6 | by 文晓港 2019-05-31 10:44:24
  仅供学习交流，如作它用所承受的法律责任一概与作者无关  
  
  *使用ColorUi开发扩展与插件时，请注明基于ColorUi开发 
  
  （QQ交流群：240787041）
*/
/* ==================
        初始化
 ==================== */
page {
	background-color: #f1f1f1;
	font-size: 28rpx;
	color: #333333;
	font-family: Helvetica Neue, Helvetica, sans-serif;
}
view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
	box-sizing: border-box;
}
.round {
	border-radius: 5000rpx;
}
.radius-m{
	border-radius: 12rpx;
}
.radius {
	border-radius: 6rpx;
}
/* ==================
          图片
 ==================== */
image {
	max-width: 100%;
	display: inline-block;
	position: relative;
	z-index: 0;
}
image.loading::before {
	content: "";
	background-color: #f5f5f5;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -2;
}
image.loading::after {
	content: "\e7f1";
	font-family: "cuIcon";
	position: absolute;
	top: 0;
	left: 0;
	width: 32rpx;
	height: 32rpx;
	line-height: 32rpx;
	right: 0;
	bottom: 0;
	z-index: -1;
	font-size: 32rpx;
	margin: auto;
	color: #ccc;
	-webkit-animation: cuIcon-spin 2s infinite linear;
	animation: cuIcon-spin 2s infinite linear;
	display: block;
}
.response {
	width: 100%;
}
/* ==================
         开关
 ==================== */
switch,
checkbox,
switch::after,
switch::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: #ffffff !important;
	top: 0%;
	left: 0rpx;
	font-size: 26rpx;
	line-height: 26px;
	width: 50%;
	text-align: center;
	pointer-events: none;
	-webkit-transform: scale(0, 0);
	        transform: scale(0, 0);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
	bottom: 0;
	height: 26px;
	margin: auto;
}
switch::before {
	content: "\e646";
	right: 0;
	-webkit-transform: scale(1, 1);
	        transform: scale(1, 1);
	left: auto;
}
switch[checked]::after,
switch.checked::after {
	-webkit-transform: scale(1, 1);
	        transform: scale(1, 1);
}
switch[checked]::before,
switch.checked::before {
	-webkit-transform: scale(0, 0);
	        transform: scale(0, 0);
}
radio::before,
checkbox::before {
	font-family: "cuIcon";
	content: "\e645";
	position: absolute;
	color: #ffffff !important;
	top: 50%;
	margin-top: -8px;
	right: 5px;
	font-size: 32rpx;
	line-height: 16px;
	pointer-events: none;
	-webkit-transform: scale(1, 1);
	        transform: scale(1, 1);
	transition: all 0.3s ease-in-out 0s;
	z-index: 9;
}
radio .wx-radio-input,
checkbox .wx-checkbox-input,
radio .uni-radio-input,
checkbox .uni-checkbox-input {
	margin: 0;
	width: 24px;
	height: 24px;
}
checkbox.round .wx-checkbox-input,
checkbox.round .uni-checkbox-input {
	border-radius: 100rpx;
}
switch[checked]::before {
	-webkit-transform: scale(0, 0);
	        transform: scale(0, 0);
}
switch .wx-switch-input,
switch .uni-switch-input {
	border: none;
	padding: 0 24px;
	width: 48px;
	height: 26px;
	margin: 0;
	border-radius: 100rpx;
}
switch .wx-switch-input:not([class*="bg-"]),
switch .uni-switch-input:not([class*="bg-"]) {
	background: #8799a3 !important;
}
switch .wx-switch-input::after,
switch .uni-switch-input::after {
	margin: auto;
	width: 26px;
	height: 26px;
	border-radius: 100rpx;
	left: 0rpx;
	top: 0rpx;
	bottom: 0rpx;
	position: absolute;
	-webkit-transform: scale(0.9, 0.9);
	        transform: scale(0.9, 0.9);
	transition: all 0.1s ease-in-out 0s;
}
switch .wx-switch-input.wx-switch-input-checked::after,
switch .uni-switch-input.uni-switch-input-checked::after {
	margin: auto;
	left: 22px;
	box-shadow: none;
	-webkit-transform: scale(0.9, 0.9);
	        transform: scale(0.9, 0.9);
}
radio-group {
	display: inline-block;
}
switch.radius .wx-switch-input::after,
switch.radius .wx-switch-input,
switch.radius .wx-switch-input::before,
switch.radius .uni-switch-input::after,
switch.radius .uni-switch-input,
switch.radius .uni-switch-input::before {
	border-radius: 10rpx;
}
switch .wx-switch-input::before,
radio.radio::before,
checkbox .wx-checkbox-input::before,
radio .wx-radio-input::before,
switch .uni-switch-input::before,
radio.radio::before,
checkbox .uni-checkbox-input::before,
radio .uni-radio-input::before {
	display: none;
}
radio.radio[checked]::after,
radio.radio .uni-radio-input-checked::after {
	content: "";
	background-color: transparent;
	display: block;
	position: absolute;
	width: 8px;
	height: 8px;
	z-index: 999;
	top: 0rpx;
	left: 0rpx;
	right: 0;
	bottom: 0;
	margin: auto;
	border-radius: 200rpx;





	border: 8px solid #ffffff !important;
}
.switch-sex::after {
	content: "\e71c";
}
.switch-sex::before {
	content: "\e71a";
}
.switch-sex .wx-switch-input,
.switch-sex .uni-switch-input {
	background: #e54d42 !important;
	border-color: #e54d42 !important;
}
.switch-sex[checked] .wx-switch-input,
.switch-sex.checked .uni-switch-input {
	background: #0081ff !important;
	border-color: #0081ff !important;
}
switch.red[checked] .wx-switch-input.wx-switch-input-checked,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input,
switch.red.checked .uni-switch-input.uni-switch-input-checked,
checkbox.red.checked .uni-checkbox-input,
radio.red.checked .uni-radio-input {
	background-color: #e54d42 !important;
	border-color: #e54d42 !important;
	color: #ffffff !important;
}
switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input,
switch.orange.checked .uni-switch-input,
checkbox.orange.checked .uni-checkbox-input,
radio.orange.checked .uni-radio-input {
	background-color: #f37b1d !important;
	border-color: #f37b1d !important;
	color: #ffffff !important;
}
switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input,
switch.yellow.checked .uni-switch-input,
checkbox.yellow.checked .uni-checkbox-input,
radio.yellow.checked .uni-radio-input {
	background-color: #fbbd08 !important;
	border-color: #fbbd08 !important;
	color: #333333 !important;
}
switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input,
switch.olive.checked .uni-switch-input,
checkbox.olive.checked .uni-checkbox-input,
radio.olive.checked .uni-radio-input {
	background-color: #8dc63f !important;
	border-color: #8dc63f !important;
	color: #ffffff !important;
}
switch.green[checked] .wx-switch-input,
switch[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input,
radio[checked] .wx-radio-input,
switch.green.checked .uni-switch-input,
switch.checked .uni-switch-input,
checkbox.green.checked .uni-checkbox-input,
checkbox.checked .uni-checkbox-input,
radio.green.checked .uni-radio-input,
radio.checked .uni-radio-input {
	background-color: #39b54a !important;
	border-color: #39b54a !important;
	color: #ffffff !important;
	border-color: #39B54A !important;
}
switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input,
switch.cyan.checked .uni-switch-input,
checkbox.cyan.checked .uni-checkbox-input,
radio.cyan.checked .uni-radio-input {
	background-color: #1cbbb4 !important;
	border-color: #1cbbb4 !important;
	color: #ffffff !important;
}
switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input,
switch.blue.checked .uni-switch-input,
checkbox.blue.checked .uni-checkbox-input,
radio.blue.checked .uni-radio-input {
	background-color: #0081ff !important;
	border-color: #0081ff !important;
	color: #ffffff !important;
}
switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input,
switch.purple.checked .uni-switch-input,
checkbox.purple.checked .uni-checkbox-input,
radio.purple.checked .uni-radio-input {
	background-color: #6739b6 !important;
	border-color: #6739b6 !important;
	color: #ffffff !important;
}
switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input,
switch.mauve.checked .uni-switch-input,
checkbox.mauve.checked .uni-checkbox-input,
radio.mauve.checked .uni-radio-input {
	background-color: #9c26b0 !important;
	border-color: #9c26b0 !important;
	color: #ffffff !important;
}
switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input,
switch.pink.checked .uni-switch-input,
checkbox.pink.checked .uni-checkbox-input,
radio.pink.checked .uni-radio-input {
	background-color: #e03997 !important;
	border-color: #e03997 !important;
	color: #ffffff !important;
}
switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input,
switch.brown.checked .uni-switch-input,
checkbox.brown.checked .uni-checkbox-input,
radio.brown.checked .uni-radio-input {
	background-color: #a5673f !important;
	border-color: #a5673f !important;
	color: #ffffff !important;
}
switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input,
switch.grey.checked .uni-switch-input,
checkbox.grey.checked .uni-checkbox-input,
radio.grey.checked .uni-radio-input {
	background-color: #8799a3 !important;
	border-color: #8799a3 !important;
	color: #ffffff !important;
}
switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input,
switch.gray.checked .uni-switch-input,
checkbox.gray.checked .uni-checkbox-input,
radio.gray.checked .uni-radio-input {
	background-color: #f0f0f0 !important;
	border-color: #f0f0f0 !important;
	color: #333333 !important;
}
switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input,
switch.black.checked .uni-switch-input,
checkbox.black.checked .uni-checkbox-input,
radio.black.checked .uni-radio-input {
	background-color: #333333 !important;
	border-color: #333333 !important;
	color: #ffffff !important;
}
switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input,
switch.white.checked .uni-switch-input,
checkbox.white.checked .uni-checkbox-input,
radio.white.checked .uni-radio-input {
	background-color: #ffffff !important;
	border-color: #ffffff !important;
	color: #333333 !important;
}
/* ==================
          边框
 ==================== */
/* -- 实线 -- */
.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
	position: relative;
}
.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
}
.solid::after {
	border: 1rpx solid rgba(0, 0, 0, 0.1);
}
.solid-top::after {
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}
.solid-right::after {
	border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.solid-bottom::after {
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}
.solid-left::after {
	border-left: 1rpx solid rgba(0, 0, 0, 0.1);
}
.solids::after {
	border: 8rpx solid #eee;
}
.solids-top::after {
	border-top: 8rpx solid #eee;
}
.solids-right::after {
	border-right: 8rpx solid #eee;
}
.solids-bottom::after {
	border-bottom: 8rpx solid #eee;
}
.solids-left::after {
	border-left: 8rpx solid #eee;
}
/* -- 虚线 -- */
.dashed::after {
	border: 1rpx dashed #ddd;
}
.dashed-top::after {
	border-top: 1rpx dashed #ddd;
}
.dashed-right::after {
	border-right: 1rpx dashed #ddd;
}
.dashed-bottom::after {
	border-bottom: 1rpx dashed #ddd;
}
.dashed-left::after {
	border-left: 1rpx dashed #ddd;
}
/* -- 阴影 -- */
.shadow[class*='white'] {
	--ShadowSize: 0 1rpx 6rpx;
}
.shadow-lg {
	--ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}
.shadow-warp {
	position: relative;
	box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}
.shadow-warp:before,
.shadow-warp:after {
	position: absolute;
	content: "";
	top: 20rpx;
	bottom: 30rpx;
	left: 20rpx;
	width: 50%;
	box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
	-webkit-transform: rotate(-3deg);
	        transform: rotate(-3deg);
	z-index: -1;
}
.shadow-warp:after {
	right: 20rpx;
	left: auto;
	-webkit-transform: rotate(3deg);
	        transform: rotate(3deg);
}
.shadow-blur {
	position: relative;
}
.shadow-blur::before {
	content: "";
	display: block;
	background: inherit;
	-webkit-filter: blur(10rpx);
	        filter: blur(10rpx);
	position: absolute;
	width: 100%;
	height: 100%;
	top: 10rpx;
	left: 10rpx;
	z-index: -1;
	opacity: 0.4;
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	border-radius: inherit;
	-webkit-transform: scale(1, 1);
	        transform: scale(1, 1);
}
/* ==================
          按钮
 ==================== */
.cu-btn {
	position: relative;
	border: 0rpx;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0 30rpx;
	font-size: 28rpx;
	height: 64rpx;
	line-height: 1;
	text-align: center;
	text-decoration: none;
	overflow: visible;
	margin-left: initial;
	-webkit-transform: translate(0rpx, 0rpx);
	        transform: translate(0rpx, 0rpx);
	margin-right: initial;
}
.cu-btn::after {
	display: none;
}
.cu-btn:not([class*="bg-"]) {
	background-color: #f0f0f0;
}
.cu-btn[class*="line"] {
	background-color: transparent;
}
.cu-btn[class*="line"]::after {
	content: " ";
	display: block;
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: 12rpx;
	z-index: 1;
	pointer-events: none;
}
.cu-btn.round[class*="line"]::after {
	border-radius: 1000rpx;
}
.cu-btn[class*="lines"]::after {
	border: 6rpx solid currentColor;
}
.cu-btn[class*="bg-"]::after {
	display: none;
}
.cu-btn.sm {
	padding: 0 20rpx;
	font-size: 20rpx;
	height: 48rpx;
}
.cu-btn.smm {
	padding: 0 15rpx;
	font-size: 15rpx;
	height: 30rpx;
}
.cu-btn.lg {
	padding: 0 40rpx;
	font-size: 32rpx;
	height: 80rpx;
}
.cu-btn.cuIcon.sm {
	width: 48rpx;
	height: 48rpx;
}
.cu-btn.cuIcon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 500rpx;
	padding: 0;
}
button.cuIcon.lg {
	width: 80rpx;
	height: 80rpx;
}
.cu-btn.shadow-blur::before {
	top: 4rpx;
	left: 4rpx;
	-webkit-filter: blur(6rpx);
	        filter: blur(6rpx);
	opacity: 0.6;
}
.cu-btn.button-hover {
	-webkit-transform: translate(1rpx, 1rpx);
	        transform: translate(1rpx, 1rpx);
}
.block {
	display: block;
}
.cu-btn.block {
	display: flex;
}
.cu-btn[disabled] {
	opacity: 0.6;
	color: #ffffff;
}
/* ==================
          徽章
 ==================== */
.cu-tag {
	font-size: 24rpx;
	vertical-align: middle;
	position: relative;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0rpx 16rpx;
	height: 48rpx;
	font-family: Helvetica Neue, Helvetica, sans-serif;
	white-space: nowrap;
}
.cu-tag:not([class*="bg"]):not([class*="line"]) {
	background-color: #f1f1f1;
}
.cu-tag[class*="line-"]::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1rpx solid currentColor;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: inherit;
	z-index: 1;
	pointer-events: none;
}
.cu-tag.radius[class*="line"]::after {
	border-radius: 12rpx;
}
.cu-tag.round[class*="line"]::after {
	border-radius: 1000rpx;
}
.cu-tag[class*="line-"]::after {
	border-radius: 0;
}
.cu-tag+.cu-tag {
	margin-left: 10rpx;
}
.cu-tag.sm {
	font-size: 20rpx;
	padding: 0rpx 12rpx;
	height: 32rpx;
}
.cu-capsule {
	display: inline-flex;
	vertical-align: middle;
}
.cu-capsule+.cu-capsule {
	margin-left: 10rpx;
}
.cu-capsule .cu-tag {
	margin: 0;
}
.cu-capsule .cu-tag[class*="line-"]:last-child::after {
	border-left: 0rpx solid transparent;
}
.cu-capsule .cu-tag[class*="line-"]:first-child::after {
	border-right: 0rpx solid transparent;
}
.cu-capsule.radius .cu-tag:first-child {
	border-top-left-radius: 6rpx;
	border-bottom-left-radius: 6rpx;
}
.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*="line-"] {
	border-top-right-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
}
.cu-capsule.round .cu-tag:first-child {
	border-top-left-radius: 200rpx;
	border-bottom-left-radius: 200rpx;
	text-indent: 4rpx;
}
.cu-capsule.round .cu-tag:last-child::after,
.cu-capsule.round .cu-tag:last-child {
	border-top-right-radius: 200rpx;
	border-bottom-right-radius: 200rpx;
	text-indent: -4rpx;
}
.cu-tag.badge {
	border-radius: 200rpx;
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	font-size: 20rpx;
	padding: 0rpx 10rpx;
	height: 28rpx;
	color: #ffffff;
}
.cu-tag.badge:not([class*="bg-"]) {
	background-color: #dd514c;
}
.cu-tag:empty:not([class*="cuIcon-"]) {
	padding: 0rpx;
	width: 16rpx;
	height: 16rpx;
	top: -4rpx;
	right: -4rpx;
}
.cu-tag[class*="cuIcon-"] {
	width: 32rpx;
	height: 32rpx;
	top: -4rpx;
	right: -4rpx;
}
/* ==================
          头像
 ==================== */
.cu-avatar {
	font-variant: small-caps;
	margin: 0;
	padding: 0;
	display: inline-flex;
	text-align: center;
	justify-content: center;
	align-items: center;
	background-color: #ccc;
	color: #ffffff;
	white-space: nowrap;
	position: relative;
	width: 64rpx;
	height: 64rpx;
	background-size: cover;
	background-position: center;
	vertical-align: middle;
	font-size: 1.5em;
}
.cu-avatar.sm {
	width: 48rpx;
	height: 48rpx;
	font-size: 1em;
}
.cu-avatar.lg {
	width: 96rpx;
	height: 96rpx;
	font-size: 2em;
}
.cu-avatar.xl {
	width: 128rpx;
	height: 128rpx;
	font-size: 2.5em;
}
.cu-avatar .avatar-text {
	font-size: 0.4em;
}
.cu-avatar-group {
	direction: rtl;
	unicode-bidi: bidi-override;
	padding: 0 10rpx 0 40rpx;
	display: inline-block;
}
.cu-avatar-group .cu-avatar {
	margin-left: -30rpx;
	border: 4rpx solid #f1f1f1;
	vertical-align: middle;
}
.cu-avatar-group .cu-avatar.sm {
	margin-left: -20rpx;
	border: 1rpx solid #f1f1f1;
}
/* ==================
         进度条
 ==================== */
.cu-progress {
	overflow: hidden;
	height: 28rpx;
	background-color: #ebeef5;
	display: inline-flex;
	align-items: center;
	width: 100%;
}
.cu-progress+view,
.cu-progress+text {
	line-height: 1;
}
.cu-progress.xs {
	height: 10rpx;
}
.cu-progress.sm {
	height: 20rpx;
}
.cu-progress view {
	width: 0;
	height: 100%;
	align-items: center;
	display: flex;
	justify-items: flex-end;
	justify-content: space-around;
	font-size: 20rpx;
	color: #ffffff;
	transition: width 0.6s ease;
}
.cu-progress text {
	align-items: center;
	display: flex;
	font-size: 20rpx;
	color: #333333;
	text-indent: 10rpx;
}
.cu-progress.text-progress {
	padding-right: 60rpx;
}
.cu-progress.striped view {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-size: 72rpx 72rpx;
}
.cu-progress.active view {
	-webkit-animation: progress-stripes 2s linear infinite;
	        animation: progress-stripes 2s linear infinite;
}
@-webkit-keyframes progress-stripes {
from {
		background-position: 72rpx 0;
}
to {
		background-position: 0 0;
}
}
@keyframes progress-stripes {
from {
		background-position: 72rpx 0;
}
to {
		background-position: 0 0;
}
}
/* ==================
          加载
 ==================== */
.cu-load {
	display: block;
	line-height: 3em;
	text-align: center;
}
.cu-load::before {
	font-family: "cuIcon";
	display: inline-block;
	margin-right: 6rpx;
}
.cu-load.loading::before {
	content: "\e67a";
	-webkit-animation: cuIcon-spin 2s infinite linear;
	        animation: cuIcon-spin 2s infinite linear;
}
.cu-load.loading::after {
	content: "加载中...";
}
.cu-load.over::before {
	content: "\e64a";
}
.cu-load.over::after {
	content: "没有更多了";
}
.cu-load.erro::before {
	content: "\e658";
}
.cu-load.erro::after {
	content: "加载失败";
}
.cu-load.load-cuIcon::before {
	font-size: 32rpx;
}
.cu-load.load-cuIcon::after {
	display: none;
}
.cu-load.load-cuIcon.over {
	display: none;
}
.cu-load.load-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 140rpx;
	left: 0;
	margin: auto;
	width: 260rpx;
	height: 260rpx;
	background-color: #ffffff;
	border-radius: 10rpx;
	box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	font-size: 28rpx;
	z-index: 9999;
	line-height: 2.4em;
}
.cu-load.load-modal [class*="cuIcon-"] {
	font-size: 60rpx;
}
.cu-load.load-modal image {
	width: 70rpx;
	height: 70rpx;
}
.cu-load.load-modal::after {
	content: "";
	position: absolute;
	background-color: #ffffff;
	border-radius: 50%;
	width: 200rpx;
	height: 200rpx;
	font-size: 10px;
	border-top: 6rpx solid rgba(0, 0, 0, 0.05);
	border-right: 6rpx solid rgba(0, 0, 0, 0.05);
	border-bottom: 6rpx solid rgba(0, 0, 0, 0.05);
	border-left: 6rpx solid #f37b1d;
	-webkit-animation: cuIcon-spin 1s infinite linear;
	        animation: cuIcon-spin 1s infinite linear;
	z-index: -1;
}
.load-progress {
	pointer-events: none;
	top: 0;
	position: fixed;
	width: 100%;
	left: 0;
	z-index: 2000;
}
.load-progress.hide {
	display: none;
}
.load-progress .load-progress-bar {
	position: relative;
	width: 100%;
	height: 4rpx;
	overflow: hidden;
	transition: all 200ms ease 0s;
}
.load-progress .load-progress-spinner {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	z-index: 2000;
	display: block;
}
.load-progress .load-progress-spinner::after {
	content: "";
	display: block;
	width: 24rpx;
	height: 24rpx;
	box-sizing: border-box;
	border: solid 4rpx transparent;
	border-top-color: inherit;
	border-left-color: inherit;
	border-radius: 50%;
	-webkit-animation: load-progress-spinner 0.4s linear infinite;
	animation: load-progress-spinner 0.4s linear infinite;
}
@-webkit-keyframes load-progress-spinner {
0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
}
100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
}
}
@keyframes load-progress-spinner {
0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
}
100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
}
}
/* ==================
          列表
 ==================== */
.grayscale {
	-webkit-filter: grayscale(1);
	        filter: grayscale(1);
}
.cu-list+.cu-list {
	margin-top: 30rpx
}
.cu-list>.cu-item {
	transition: all .6s ease-in-out 0s;
	-webkit-transform: translateX(0rpx);
	        transform: translateX(0rpx)
}
.cu-list>.cu-item.move-cur {
	-webkit-transform: translateX(-260rpx);
	        transform: translateX(-260rpx)
}
.cu-list>.cu-item .move {
	position: absolute;
	right: 0;
	display: flex;
	width: 260rpx;
	height: 100%;
	-webkit-transform: translateX(100%);
	        transform: translateX(100%)
}
.cu-list>.cu-item .move view {
	display: flex;
	flex: 1;
	justify-content: center;
	align-items: center
}
.cu-list.menu-avatar {
	overflow: hidden;
}
.cu-list.menu-avatar>.cu-item {
	position: relative;
	display: flex;
	padding-right: 10rpx;
	height: 140rpx;
	background-color: #ffffff;
	justify-content: flex-end;
	align-items: center
}
.cu-list.menu-avatar>.cu-item>.cu-avatar {
	position: absolute;
	left: 30rpx
}
.cu-list.menu-avatar>.cu-item .flex .text-cut {
	max-width: 510rpx
}
.cu-list.menu-avatar>.cu-item .content {
	position: absolute;
	left: 146rpx;
	width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx);
	line-height: 1.6em;
}
.cu-list.menu-avatar>.cu-item .content.flex-sub {
	width: calc(100% - 96rpx - 60rpx - 20rpx);
}
.cu-list.menu-avatar>.cu-item .content>view:first-child {
	font-size: 30rpx;
	display: flex;
	align-items: center
}
.cu-list.menu-avatar>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10rpx;
	height: 28rpx;
	font-size: 16rpx;
	line-height: 32rpx
}
.cu-list.menu-avatar>.cu-item .action {
	width: 100rpx;
	text-align: center
}
.cu-list.menu-avatar>.cu-item .action view+view {
	margin-top: 10rpx
}
.cu-list.menu-avatar.comment>.cu-item .content {
	position: relative;
	left: 0;
	width: auto;
	flex: 1;
}
.cu-list.menu-avatar.comment>.cu-item {
	padding: 30rpx 30rpx 30rpx 120rpx;
	height: auto
}
.cu-list.menu-avatar.comment .cu-avatar {
	align-self: flex-start
}
.cu-list.menu>.cu-item {
	position: relative;
	display: flex;
	padding: 0 30rpx;
	min-height: 100rpx;
	background-color: #ffffff;
	justify-content: space-between;
	align-items: center
}
.cu-list.menu>.cu-item:last-child:after {
	border: none
}
.cu-list.menu-avatar>.cu-item:after,
.cu-list.menu>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-bottom: 1rpx solid #ddd;
	border-radius: inherit;
	content: " ";
	-webkit-transform: scale(.5);
	        transform: scale(.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	pointer-events: none
}
.cu-list.menu>.cu-item.grayscale {
	background-color: #f5f5f5
}
.cu-list.menu>.cu-item.cur {
	background-color: #fcf7e9
}
.cu-list.menu>.cu-item.arrow {
	padding-right: 90rpx
}
.cu-list.menu>.cu-item.arrow:before {
	position: absolute;
	top: 0;
	right: 30rpx;
	bottom: 0;
	display: block;
	margin: auto;
	width: 30rpx;
	height: 30rpx;
	color: #8799a3;
	content: "\e6a3";
	text-align: center;
	font-size: 34rpx;
	font-family: cuIcon;
	line-height: 30rpx
}
.cu-list.menu>.cu-item button.content {
	padding: 0;
	background-color: transparent;
	justify-content: flex-start
}
.cu-list.menu>.cu-item button.content:after {
	display: none
}
.cu-list.menu>.cu-item .cu-avatar-group .cu-avatar {
	border-color: #ffffff
}
.cu-list.menu>.cu-item .content>view:first-child {
	display: flex;
	align-items: center
}
.cu-list.menu>.cu-item .content>text[class*=cuIcon] {
	display: inline-block;
	margin-right: 10rpx;
	width: 1.6em;
	text-align: center
}
.cu-list.menu>.cu-item .content>image {
	display: inline-block;
	margin-right: 10rpx;
	width: 1.6em;
	height: 1.6em;
	vertical-align: middle
}
.cu-list.menu>.cu-item .content {
	font-size: 30rpx;
	line-height: 1.6em;
	flex: 1
}
.cu-list.menu>.cu-item .content .cu-tag.sm {
	display: inline-block;
	margin-left: 10rpx;
	height: 28rpx;
	font-size: 16rpx;
	line-height: 32rpx
}
.cu-list.menu>.cu-item .action .cu-tag:empty {
	right: 10rpx
}
.cu-list.menu {
	display: block;
	overflow: hidden
}
.cu-list.menu.sm-border>.cu-item:after {
	left: 30rpx;
	width: calc(200% - 120rpx)
}
.cu-list.grid>.cu-item {
	position: relative;
	display: flex;
	padding: 20rpx 0 30rpx;
	transition-duration: 0s;
	flex-direction: column
}
.cu-list.grid>.cu-item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-right: 1px solid rgba(0, 0, 0, .1);
	border-bottom: 1px solid rgba(0, 0, 0, .1);
	border-radius: inherit;
	content: " ";
	-webkit-transform: scale(.5);
	        transform: scale(.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	pointer-events: none
}
.cu-list.grid>.cu-item text {
	display: block;
	margin-top: 10rpx;
	color: #888;
	font-size: 26rpx;
	line-height: 40rpx
}
.cu-list.grid>.cu-item [class*=cuIcon] {
	position: relative;
	display: block;
	margin-top: 20rpx;
	width: 100%;
	font-size: 48rpx
}
.cu-list.grid>.cu-item .cu-tag {
	right: auto;
	left: 50%;
	margin-left: 20rpx
}
.cu-list.grid {
	background-color: #ffffff;
	text-align: center
}
.cu-list.grid.no-border>.cu-item {
	padding-top: 10rpx;
	padding-bottom: 20rpx
}
.cu-list.grid.no-border>.cu-item:after {
	border: none
}
.cu-list.grid.no-border {
	padding: 20rpx 10rpx
}
.cu-list.grid.col-3>.cu-item:nth-child(3n):after,
.cu-list.grid.col-4>.cu-item:nth-child(4n):after,
.cu-list.grid.col-5>.cu-item:nth-child(5n):after {
	border-right-width: 0
}
.cu-list.card-menu {
	overflow: hidden;
	margin-right: 30rpx;
	margin-left: 30rpx;
	border-radius: 20rpx
}
/* ==================
          操作条
 ==================== */
.cu-bar {
	display: flex;
	position: relative;
	align-items: center;
	min-height: 100rpx;
	justify-content: space-between;
}
.cu-bar .action {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: center;
	max-width: 100%;
}
.cu-bar .action.border-title {
	position: relative;
	top: -10rpx;
}
.cu-bar .action.border-title text[class*="bg-"]:last-child {
	position: absolute;
	bottom: -0.5rem;
	min-width: 2rem;
	height: 6rpx;
	left: 0;
}
.cu-bar .action.sub-title {
	position: relative;
	top: -0.2rem;
}
.cu-bar .action.sub-title text {
	position: relative;
	z-index: 1;
}
.cu-bar .action.sub-title text[class*="bg-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.2rem;
	border-radius: 6rpx;
	width: 100%;
	height: 0.6rem;
	left: 0.6rem;
	opacity: 0.3;
	z-index: 0;
}
.cu-bar .action.sub-title text[class*="text-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.7rem;
	left: 0.5rem;
	opacity: 0.2;
	z-index: 0;
	text-align: right;
	font-weight: 900;
	font-size: 36rpx;
}
.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
	left: 0;
	right: 0;
	margin: auto;
	text-align: center;
}
.cu-bar .action:first-child {
	margin-left: 30rpx;
	font-size: 30rpx;
}
.cu-bar .action text.text-cut {
	text-align: left;
	width: 100%;
}
.cu-bar .cu-avatar:first-child {
	margin-left: 20rpx;
}
.cu-bar .action:first-child>text[class*="cuIcon-"] {
	margin-left: -0.3em;
	margin-right: 0.3em;
}
.cu-bar .action:last-child {
	margin-right: 30rpx;
}
.cu-bar .action>text[class*="cuIcon-"],
.cu-bar .action>view[class*="cuIcon-"] {
	font-size: 36rpx;
}
.cu-bar .action>text[class*="cuIcon-"]+text[class*="cuIcon-"] {
	margin-left: 0.5em;
}
.cu-bar .content {
	position: absolute;
	text-align: center;
	width: calc(100% - 340rpx);
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	margin: auto;
	height: 60rpx;
	font-size: 32rpx;
	line-height: 60rpx;
	cursor: none;
	pointer-events: none;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.cu-bar.ios .content {
	bottom: 7px;
	height: 30px;
	font-size: 32rpx;
	line-height: 30px;
}
.cu-bar.btn-group {
	justify-content: space-around;
}
.cu-bar.btn-group button {
	padding: 20rpx 32rpx;
}
.cu-bar.btn-group button {
	flex: 1;
	margin: 0 20rpx;
	max-width: 50%;
}
.cu-bar .search-form {
	background-color: #f5f5f5;
	line-height: 64rpx;
	height: 64rpx;
	font-size: 24rpx;
	color: #333333;
	flex: 1;
	display: flex;
	align-items: center;
	margin: 0 30rpx;
}
.cu-bar .search-form+.action {
	margin-right: 30rpx;
}
.cu-bar .search-form input {
	flex: 1;
	padding-right: 30rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 26rpx;
	background-color: transparent;
}
.cu-bar .search-form [class*="cuIcon-"] {
	margin: 0 0.5em 0 0.8em;
}
.cu-bar .search-form [class*="cuIcon-"]::before {
	top: 0rpx;
}
.cu-bar.fixed,
.nav.fixed {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.cu-bar.foot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.cu-bar.tabbar {
	padding: 0;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}
.cu-tabbar-height {
	min-height: 100rpx;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}
.cu-bar.tabbar.shadow {
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.cu-bar.tabbar .action {
	font-size: 22rpx;
	position: relative;
	flex: 1;
	text-align: center;
	padding: 0;
	display: block;
	height: auto;
	line-height: 1;
	margin: 0;
	background-color: inherit;
	overflow: initial;
}
.cu-bar.tabbar.shop .action {
	width: 140rpx;
	flex: initial;
}
.cu-bar.tabbar .action.add-action {
	position: relative;
	z-index: 2;
	padding-top: 50rpx;
}
.cu-bar.tabbar .action.add-action [class*="cuIcon-"] {
	position: absolute;
	width: 70rpx;
	z-index: 2;
	height: 70rpx;
	border-radius: 50%;
	line-height: 70rpx;
	font-size: 50rpx;
	top: -35rpx;
	left: 0;
	right: 0;
	margin: auto;
	padding: 0;
}
.cu-bar.tabbar .action.add-action::after {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 100rpx;
	top: -50rpx;
	left: 0;
	right: 0;
	margin: auto;
	box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
	border-radius: 50rpx;
	background-color: inherit;
	z-index: 0;
}
.cu-bar.tabbar .action.add-action::before {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 30rpx;
	bottom: 30rpx;
	left: 0;
	right: 0;
	margin: auto;
	background-color: inherit;
	z-index: 1;
}
.cu-bar.tabbar .btn-group {
	flex: 1;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 0 10rpx;
}
.cu-bar.tabbar button.action::after {
	border: 0;
}
.cu-bar.tabbar .action [class*="cuIcon-"] {
	width: 100rpx;
	position: relative;
	display: block;
	height: auto;
	margin: 0 auto 10rpx;
	text-align: center;
	font-size: 40rpx;
}
.cu-bar.tabbar .action .cuIcon-cu-image {
	margin: 0 auto;
}
.cu-bar.tabbar .action .cuIcon-cu-image image {
	width: 50rpx;
	height: 50rpx;
	display: inline-block;
}
.cu-bar.tabbar .submit {
	align-items: center;
	display: flex;
	justify-content: center;
	text-align: center;
	position: relative;
	flex: 2;
	align-self: stretch;
}
.cu-bar.tabbar .submit:last-child {
	flex: 2.6;
}
.cu-bar.tabbar .submit+.submit {
	flex: 2;
}
.cu-bar.tabbar.border .action::before {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	border-right: 1rpx solid rgba(0, 0, 0, 0.1);
	z-index: 3;
}
.cu-bar.tabbar.border .action:last-child:before {
	display: none;
}
.cu-bar.input {
	padding-right: 20rpx;
	background-color: #ffffff;
}
.cu-bar.input input {
	overflow: initial;
	line-height: 64rpx;
	height: 64rpx;
	min-height: 64rpx;
	flex: 1;
	font-size: 30rpx;
	margin: 0 20rpx;
}
.cu-bar.input .action {
	margin-left: 20rpx;
}
.cu-bar.input .action [class*="cuIcon-"] {
	font-size: 48rpx;
}
.cu-bar.input input+.action {
	margin-right: 20rpx;
	margin-left: 0rpx;
}
.cu-bar.input .action:first-child [class*="cuIcon-"] {
	margin-left: 0rpx;
}
.cu-custom {
	display: block;
	position: relative;
}
.cu-custom .cu-bar .content {
	width: calc(100% - 440rpx);
}
.cu-custom .cu-bar .content image {
	height: 60rpx;
	width: 240rpx;
}
.cu-custom .cu-bar {
	min-height: 0px;

	padding-right: 220rpx;




	box-shadow: 0rpx 0rpx 0rpx;
	z-index: 9999;
}
.cu-custom .cu-bar .border-custom {
	position: relative;
	background: rgba(0, 0, 0, 0.15);
	border-radius: 1000rpx;
	height: 30px;
}
.cu-custom .cu-bar .border-custom::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	border: 1rpx solid #ffffff;
	opacity: 0.5;
}
.cu-custom .cu-bar .border-custom::before {
	content: " ";
	width: 1rpx;
	height: 110%;
	position: absolute;
	top: 22.5%;
	left: 0;
	right: 0;
	margin: auto;
	-webkit-transform: scale(0.5);
	        transform: scale(0.5);
	-webkit-transform-origin: 0 0;
	        transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	opacity: 0.6;
	background-color: #ffffff;
}
.cu-custom .cu-bar .border-custom text {
	display: block;
	flex: 1;
	margin: auto !important;
	text-align: center;
	font-size: 34rpx;
}
/* ==================
         导航栏
 ==================== */
.nav {
	white-space: nowrap;
}
::-webkit-scrollbar {
	display: none;
}
.nav .cu-item {
	height: 90rpx;
	display: inline-block;
	line-height: 90rpx;
	margin: 0 10rpx;
	padding: 0 20rpx;
}
.nav .cu-item.cur {
	border-bottom: 4rpx solid;
}
/* ==================
         时间轴
 ==================== */
.cu-timeline {
	display: block;
	background-color: #ffffff;
}
.cu-timeline .cu-time {
	width: 120rpx;
	text-align: center;
	padding: 20rpx 0;
	font-size: 26rpx;
	color: #888;
	display: block;
}
.cu-timeline>.cu-item {
	padding: 30rpx 30rpx 30rpx 120rpx;
	position: relative;
	display: block;
	z-index: 0;
}
.cu-timeline>.cu-item:not([class*="text-"]) {
	color: #ccc;
}
.cu-timeline>.cu-item::after {
	content: "";
	display: block;
	position: absolute;
	width: 1rpx;
	background-color: #ddd;
	left: 60rpx;
	height: 100%;
	top: 0;
	z-index: 8;
}
.cu-timeline>.cu-item::before {
	font-family: "cuIcon";
	display: block;
	position: absolute;
	top: 36rpx;
	z-index: 9;
	background-color: #ffffff;
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}
.cu-timeline>.cu-item:not([class*="cuIcon-"])::before {
	content: "\e763";
}
.cu-timeline>.cu-item[class*="cuIcon-"]::before {
	background-color: #ffffff;
	width: 50rpx;
	height: 50rpx;
	text-align: center;
	border: none;
	line-height: 50rpx;
	left: 36rpx;
}
.cu-timeline>.cu-item>.content {
	padding: 30rpx;
	border-radius: 6rpx;
	display: block;
	line-height: 1.6;
}
.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
	background-color: #f1f1f1;
	color: #333333;
}
.cu-timeline>.cu-item>.content+.content {
	margin-top: 20rpx;
}
/* ==================
         聊天
 ==================== */
.cu-chat {
	display: flex;
	flex-direction: column;
}
.cu-chat .cu-item {
	display: flex;
	padding: 30rpx 30rpx 70rpx;
	position: relative;
}
.cu-chat .cu-item>.cu-avatar {
	width: 80rpx;
	height: 80rpx;
}
.cu-chat .cu-item>.main {
	max-width: calc(100% - 260rpx);
	margin: 0 40rpx;
	display: flex;
	align-items: center;
}
.cu-chat .cu-item>image {
	height: 320rpx;
}
.cu-chat .cu-item>.main .content {
	padding: 20rpx;
	border-radius: 6rpx;
	display: inline-flex;
	max-width: 100%;
	align-items: center;
	font-size: 30rpx;
	position: relative;
	min-height: 80rpx;
	line-height: 40rpx;
	text-align: left;
}
.cu-chat .cu-item>.main .content:not([class*="bg-"]) {
	background-color: #ffffff;
	color: #333333;
}
.cu-chat .cu-item .date {
	position: absolute;
	font-size: 24rpx;
	color: #8799a3;
	width: calc(100% - 320rpx);
	bottom: 20rpx;
	left: 160rpx;
}
.cu-chat .cu-item .action {
	padding: 0 30rpx;
	display: flex;
	align-items: center;
}
.cu-chat .cu-item>.main .content::after {
	content: "";
	top: 27rpx;
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
	position: absolute;
	z-index: 100;
	display: inline-block;
	overflow: hidden;
	width: 24rpx;
	height: 24rpx;
	left: -12rpx;
	right: initial;
	background-color: inherit;
}
.cu-chat .cu-item.self>.main .content::after {
	left: auto;
	right: -12rpx;
}
.cu-chat .cu-item>.main .content::before {
	content: "";
	top: 30rpx;
	-webkit-transform: rotate(45deg);
	        transform: rotate(45deg);
	position: absolute;
	z-index: -1;
	display: inline-block;
	overflow: hidden;
	width: 24rpx;
	height: 24rpx;
	left: -12rpx;
	right: initial;
	background-color: inherit;
	-webkit-filter: blur(5rpx);
	        filter: blur(5rpx);
	opacity: 0.3;
}
.cu-chat .cu-item>.main .content:not([class*="bg-"])::before {
	background-color: #333333;
	opacity: 0.1;
}
.cu-chat .cu-item.self>.main .content::before {
	left: auto;
	right: -12rpx;
}
.cu-chat .cu-item.self {
	justify-content: flex-end;
	text-align: right;
}
.cu-chat .cu-info {
	display: inline-block;
	margin: 20rpx auto;
	font-size: 24rpx;
	padding: 8rpx 12rpx;
	background-color: rgba(0, 0, 0, 0.2);
	border-radius: 6rpx;
	color: #ffffff;
	max-width: 400rpx;
	line-height: 1.4;
}
/* ==================
         卡片
 ==================== */
.cu-card {
	display: block;
	overflow: hidden;
}
.cu-card>.cu-item {
	display: block;
	background-color: #ffffff;
	overflow: hidden;
	border-radius: 10rpx;
	margin: 30rpx;
}
.cu-card>.cu-item.shadow-blur {
	overflow: initial;
}
.cu-card.no-card>.cu-item {
	margin: 0rpx;
	border-radius: 0rpx;
}
.cu-card .grid.grid-square {
	margin-bottom: -20rpx;
}
.cu-card.case .image {
	position: relative;
}
.cu-card.case .image image {
	width: 100%;
}
.cu-card.case .image .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
}
.cu-card.case .image .cu-bar {
	position: absolute;
	bottom: 0;
	width: 100%;
	background-color: transparent;
	padding: 0rpx 30rpx;
}
.cu-card.case.no-card .image {
	margin: 30rpx 30rpx 0;
	overflow: hidden;
	border-radius: 10rpx;
}
.cu-card.dynamic {
	display: block;
}
.cu-card.dynamic>.cu-item {
	display: block;
	background-color: #ffffff;
	overflow: hidden;
}
.cu-card.dynamic>.cu-item>.text-content {
	padding: 0 30rpx 0;
	max-height: 6.4em;
	overflow: hidden;
	font-size: 30rpx;
	margin-bottom: 20rpx;
}
.cu-card.dynamic>.cu-item .square-img {
	width: 100%;
	height: 200rpx;
	border-radius: 6rpx;
}
.cu-card.dynamic>.cu-item .only-img {
	width: 100%;
	height: 320rpx;
	border-radius: 6rpx;
}
/* card.dynamic>.cu-item .comment {
  padding: 20upx;
  background-color: #f1f1f1;
  margin: 0 30upx 30upx;
  border-radius: 6upx;
} */
.cu-card.article {
	display: block;
}
.cu-card.article>.cu-item {
	padding-bottom: 30rpx;
}
.cu-card.article>.cu-item .title {
	font-size: 30rpx;
	font-weight: 900;
	color: #333333;
	line-height: 100rpx;
	padding: 0 30rpx;
}
.cu-card.article>.cu-item .content {
	display: flex;
	padding: 0 30rpx;
}
.cu-card.article>.cu-item .content>image {
	width: 240rpx;
	height: 6.4em;
	margin-right: 20rpx;
	border-radius: 6rpx;
}
.cu-card.article>.cu-item .content .desc {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.cu-card.article>.cu-item .content .text-content {
	font-size: 28rpx;
	color: #888;
	height: 4.8em;
	overflow: hidden;
}
/* ==================
         表单
 ==================== */
.cu-form-group {
	background-color: #ffffff;
	padding: 1rpx 30rpx;
	display: flex;
	align-items: center;
	min-height: 100rpx;
	justify-content: space-between;
}
.cu-form-group+.cu-form-group {
	border-top: 1rpx solid #eee;
}
.cu-form-group .title {
	text-align: justify;
	padding-right: 30rpx;
	font-size: 30rpx;
	position: relative;
	height: 60rpx;
	line-height: 60rpx;
}
.cu-form-group input {
	flex: 1;
	font-size: 30rpx;
	color: #555;
	padding-right: 20rpx;
}
.cu-form-group>text[class*="cuIcon-"] {
	font-size: 36rpx;
	padding: 0;
	box-sizing: border-box;
}
.cu-form-group textarea {
	margin: 32rpx 0 30rpx;
	height: 4.6em;
	width: 100%;
	line-height: 1.2em;
	flex: 1;
	font-size: 28rpx;
	padding: 0;
}
.cu-form-group.align-start .title {
	height: 1em;
	margin-top: 32rpx;
	line-height: 1em;
}
.cu-form-group picker {
	flex: 1;
	padding-right: 40rpx;
	overflow: hidden;
	position: relative;
}
.cu-form-group picker .picker {
	line-height: 100rpx;
	font-size: 28rpx;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	width: 100%;
	text-align: right;
}
.cu-form-group picker::after {
	font-family: cuIcon;
	display: block;
	content: "\e6a3";
	position: absolute;
	font-size: 34rpx;
	color: #8799a3;
	line-height: 100rpx;
	width: 60rpx;
	text-align: center;
	top: 0;
	bottom: 0;
	right: -20rpx;
	margin: auto;
}
.cu-form-group textarea[disabled],
.cu-form-group textarea[disabled] .placeholder {
	color: transparent;
}
/* ==================
         模态窗口
 ==================== */
.cu-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1110;
	opacity: 0;
	outline: 0;
	text-align: center;
	-webkit-transform: scale(1.185);
	        transform: scale(1.185);
	-webkit-backface-visibility: hidden;
	        backface-visibility: hidden;
	-webkit-perspective: 2000rpx;
	        perspective: 2000rpx;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
}
.cu-modal::before {
	content: "\200B";
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}
.cu-modal.show {
	opacity: 1;
	transition-duration: 0.3s;
	-webkit-transform: scale(1);
	        transform: scale(1);
	overflow-x: hidden;
	overflow-y: auto;
	pointer-events: auto;
}
.cu-dialog {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin-left: auto;
	margin-right: auto;
	width: 680rpx;
	max-width: 100%;
	background-color: #f8f8f8;
	border-radius: 10rpx;
	overflow: hidden;
}
.cu-modal.bottom-modal::before {
	vertical-align: bottom;
}
.cu-modal.bottom-modal .cu-dialog {
	width: 100%;
	border-radius: 0;
}
.cu-modal.bottom-modal {
	margin-bottom: -1000rpx;
}
.cu-modal.bottom-modal.show {
	margin-bottom: 0;
}
.cu-modal.drawer-modal {
	-webkit-transform: scale(1);
	        transform: scale(1);
	display: flex;
}
.cu-modal.drawer-modal .cu-dialog {
	height: 100%;
	min-width: 200rpx;
	border-radius: 0;
	margin: initial;
	transition-duration: 0.3s;
}
.cu-modal.drawer-modal.justify-start .cu-dialog {
	-webkit-transform: translateX(-100%);
	        transform: translateX(-100%);
}
.cu-modal.drawer-modal.justify-end .cu-dialog {
	-webkit-transform: translateX(100%);
	        transform: translateX(100%);
}
.cu-modal.drawer-modal.show .cu-dialog {
	-webkit-transform: translateX(0%);
	        transform: translateX(0%);
}
.cu-modal .cu-dialog>.cu-bar:first-child .action{
  min-width: 100rpx;
  margin-right: 0;
  min-height: 100rpx;
}
/* ==================
         轮播
 ==================== */
swiper .a-swiper-dot {
	display: inline-block;
	width: 16rpx;
	height: 16rpx;
	background: rgba(0, 0, 0, .3);
	border-radius: 50%;
	vertical-align: middle;
}
swiper[class*="-dot"] .wx-swiper-dots,
swiper[class*="-dot"] .a-swiper-dots,
swiper[class*="-dot"] .uni-swiper-dots {
	display: flex;
	align-items: center;
	width: 100%;
	justify-content: center;
}
swiper.square-dot .wx-swiper-dot,
swiper.square-dot .a-swiper-dot,
swiper.square-dot .uni-swiper-dot {
	background-color: #ffffff;
	opacity: 0.4;
	width: 10rpx;
	height: 10rpx;
	border-radius: 20rpx;
	margin: 0 8rpx !important;
}
swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.square-dot .a-swiper-dot.a-swiper-dot-active,
swiper.square-dot .uni-swiper-dot.uni-swiper-dot-active {
	opacity: 1;
	width: 30rpx;
}
swiper.round-dot .wx-swiper-dot,
swiper.round-dot .a-swiper-dot,
swiper.round-dot .uni-swiper-dot {
	width: 10rpx;
	height: 10rpx;
	position: relative;
	margin: 4rpx 8rpx !important;
}
swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active::after,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active::after {
	content: "";
	position: absolute;
	width: 10rpx;
	height: 10rpx;
	top: 0rpx;
	left: 0rpx;
	right: 0;
	bottom: 0;
	margin: auto;
	background-color: #ffffff;
	border-radius: 20rpx;
}
swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active {
	width: 18rpx;
	height: 18rpx;
}
.screen-swiper {
	/* min-height: 375upx; */
}
.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
	width: 100%;
	display: block;
	height: 100%;
	margin: 0;
	pointer-events: none;
}
.card-swiper {
	height: 420rpx !important;
}
.card-swiper swiper-item {
	width: 610rpx !important;
	left: 70rpx;
	box-sizing: border-box;
	padding: 40rpx 0rpx 70rpx;
	overflow: initial;
}
.card-swiper swiper-item .swiper-item {
	width: 100%;
	display: block;
	height: 100%;
	border-radius: 10rpx;
	-webkit-transform: scale(0.9);
	        transform: scale(0.9);
	transition: all 0.2s ease-in 0s;
	overflow: hidden;
}
.card-swiper swiper-item.cur .swiper-item {
	-webkit-transform: none;
	        transform: none;
	transition: all 0.2s ease-in 0s;
}
.tower-swiper {
	height: 420rpx;
	position: relative;
	max-width: 750rpx;
	overflow: hidden;
}
.tower-swiper .tower-item {
	position: absolute;
	width: 300rpx;
	height: 380rpx;
	top: 0;
	bottom: 0;
	left: 50%;
	margin: auto;
	transition: all 0.2s ease-in 0s;
	opacity: 1;
}
.tower-swiper .tower-item.none {
	opacity: 0;
}
.tower-swiper .tower-item .swiper-item {
	width: 100%;
	height: 100%;
	border-radius: 6rpx;
	overflow: hidden;
}
/* ==================
          步骤条
 ==================== */
.cu-steps {
	display: flex;
}
scroll-view.cu-steps {
	display: block;
	white-space: nowrap;
}
scroll-view.cu-steps .cu-item {
	display: inline-block;
}
.cu-steps .cu-item {
	flex: 1;
	text-align: center;
	position: relative;
	min-width: 100rpx;
}
.cu-steps .cu-item:not([class*="text-"]) {
	color: #8799a3;
}
.cu-steps .cu-item [class*="cuIcon-"],
.cu-steps .cu-item .num {
	display: block;
	font-size: 40rpx;
	line-height: 80rpx;
}
.cu-steps .cu-item::before,
.cu-steps .cu-item::after,
.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "";
	display: block;
	position: absolute;
	height: 0px;
	width: calc(100% - 80rpx);
	border-bottom: 1px solid #ccc;
	left: calc(0px - (100% - 80rpx) / 2);
	top: 40rpx;
	z-index: 0;
}
.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
	content: "\e6a3";
	font-family: 'cuIcon';
	height: 30rpx;
	border-bottom-width: 0px;
	line-height: 30rpx;
	top: 0;
	bottom: 0;
	margin: auto;
	color: #ccc;
}
.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	bottom: 40rpx;
	top: initial;
}
.cu-steps .cu-item::after {
	border-bottom: 1px solid currentColor;
	width: 0px;
	transition: all 0.3s ease-in-out 0s;
}
.cu-steps .cu-item[class*="text-"]::after {
	width: calc(100% - 80rpx);
	color: currentColor;
}
.cu-steps .cu-item:first-child::before,
.cu-steps .cu-item:first-child::after {
	display: none;
}
.cu-steps .cu-item .num {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	line-height: 40rpx;
	margin: 20rpx auto;
	font-size: 24rpx;
	border: 1px solid currentColor;
	position: relative;
	overflow: hidden;
}
.cu-steps .cu-item[class*="text-"] .num {
	background-color: currentColor;
}
.cu-steps .cu-item .num::before,
.cu-steps .cu-item .num::after {
	content: attr(data-index);
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	transition: all 0.3s ease-in-out 0s;
	-webkit-transform: translateY(0rpx);
	        transform: translateY(0rpx);
}
.cu-steps .cu-item[class*="text-"] .num::before {
	-webkit-transform: translateY(-40rpx);
	        transform: translateY(-40rpx);
	color: #ffffff;
}
.cu-steps .cu-item .num::after {
	-webkit-transform: translateY(40rpx);
	        transform: translateY(40rpx);
	color: #ffffff;
	transition: all 0.3s ease-in-out 0s;
}
.cu-steps .cu-item[class*="text-"] .num::after {
	content: "\e645";
	font-family: 'cuIcon';
	color: #ffffff;
	-webkit-transform: translateY(0rpx);
	        transform: translateY(0rpx);
}
.cu-steps .cu-item[class*="text-"] .num.err::after {
	content: "\e646";
}
/* ==================
          布局
 ==================== */
/*  -- flex弹性布局 -- */
.flex {
	display: flex;
}
.basis-xs {
	flex-basis: 20%;
}
.basis-sm {
	flex-basis: 40%;
}
.basis-df {
	flex-basis: 50%;
}
.basis-lg {
	flex-basis: 60%;
}
.basis-xl {
	flex-basis: 80%;
}
.flex-sub {
	flex: 1;
}
.flex-twice {
	flex: 2;
}
.flex-treble {
	flex: 3;
}
.flex-direction {
	flex-direction: column;
}
.flex-wrap {
	flex-wrap: wrap;
}
.align-start {
	align-items: flex-start;
}
.align-end {
	align-items: flex-end;
}
.align-center {
	align-items: center;
}
.align-stretch {
	align-items: stretch;
}
.self-start {
	align-self: flex-start;
}
.self-center {
	align-self: flex-center;
}
.self-end {
	align-self: flex-end;
}
.self-stretch {
	align-self: stretch;
}
.align-stretch {
	align-items: stretch;
}
.justify-start {
	justify-content: flex-start;
}
.justify-end {
	justify-content: flex-end;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.justify-around {
	justify-content: space-around;
}
/* grid布局 */
.grid {
	display: flex;
	flex-wrap: wrap;
}
.grid.grid-square {
	overflow: hidden;
}
.grid.grid-square .cu-tag {
	position: absolute;
	right: 0;
	top: 0;
	border-bottom-left-radius: 6rpx;
	padding: 6rpx 12rpx;
	height: auto;
	background-color: rgba(0, 0, 0, 0.5);
}
.grid.grid-square>view>text[class*="cuIcon-"] {
	font-size: 52rpx;
	position: absolute;
	color: #8799a3;
	margin: auto;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}
.grid.grid-square>view {
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 6rpx;
	position: relative;
	overflow: hidden;
}
.grid.grid-square>view.bg-img image {
	width: 100%;
	height: 100%;
	position: absolute;
}
.grid.col-1.grid-square>view {
	padding-bottom: 100%;
	height: 0;
	margin-right: 0;
}
.grid.col-2.grid-square>view {
	padding-bottom: calc((100% - 20rpx)/2);
	height: 0;
	width: calc((100% - 20rpx)/2);
}
.grid.col-3.grid-square>view {
	padding-bottom: calc((100% - 40rpx)/3);
	height: 0;
	width: calc((100% - 40rpx)/3);
}
.grid.col-4.grid-square>view {
	padding-bottom: calc((100% - 60rpx)/4);
	height: 0;
	width: calc((100% - 60rpx)/4);
}
.grid.col-5.grid-square>view {
	padding-bottom: calc((100% - 80rpx)/5);
	height: 0;
	width: calc((100% - 80rpx)/5);
}
.grid.col-2.grid-square>view:nth-child(2n),
.grid.col-3.grid-square>view:nth-child(3n),
.grid.col-4.grid-square>view:nth-child(4n),
.grid.col-5.grid-square>view:nth-child(5n) {
	margin-right: 0;
}
.grid.col-1>view {
	width: 100%;
}
.grid.col-2>view {
	width: 50%;
}
.grid.col-3>view {
	width: 33.33%;
}
.grid.col-4>view {
	width: 25%;
}
.grid.col-5>view {
	width: 20%;
}
/*  -- 内外边距 -- */
.margin-0 {
	margin: 0;
}
.margin-xs {
	margin: 10rpx;
}
.margin-sm {
	margin: 20rpx;
}
.margin {
	margin: 30rpx;
}
.margin-lg {
	margin: 40rpx;
}
.margin-xl {
	margin: 50rpx;
}
.margin-top-xs {
	margin-top: 10rpx;
}
.margin-top-sm {
	margin-top: 20rpx;
}
.margin-top {
	margin-top: 30rpx;
}
.margin-top-lg {
	margin-top: 40rpx;
}
.margin-top-xl {
	margin-top: 50rpx;
}
.margin-right-xs {
	margin-right: 10rpx;
}
.margin-right-sm {
	margin-right: 20rpx;
}
.margin-right {
	margin-right: 30rpx;
}
.margin-right-lg {
	margin-right: 40rpx;
}
.margin-right-xl {
	margin-right: 50rpx;
}
.margin-bottom-xs {
	margin-bottom: 10rpx;
}
.margin-bottom-sm {
	margin-bottom: 20rpx;
}
.margin-bottom {
	margin-bottom: 30rpx;
}
.margin-bottom-lg {
	margin-bottom: 40rpx;
}
.margin-bottom-xl {
	margin-bottom: 50rpx;
}
.margin-left-xs {
	margin-left: 10rpx;
}
.margin-left-sm {
	margin-left: 20rpx;
}
.margin-left {
	margin-left: 30rpx;
}
.margin-left-lg {
	margin-left: 40rpx;
}
.margin-left-xl {
	margin-left: 50rpx;
}
.margin-lr-xs {
	margin-left: 10rpx;
	margin-right: 10rpx;
}
.margin-lr-sm {
	margin-left: 20rpx;
	margin-right: 20rpx;
}
.margin-lr {
	margin-left: 30rpx;
	margin-right: 30rpx;
}
.margin-lr-lg {
	margin-left: 40rpx;
	margin-right: 40rpx;
}
.margin-lr-xl {
	margin-left: 50rpx;
	margin-right: 50rpx;
}
.margin-tb-xs {
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}
.margin-tb-sm {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}
.margin-tb {
	margin-top: 30rpx;
	margin-bottom: 30rpx;
}
.margin-tb-lg {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
}
.margin-tb-xl {
	margin-top: 50rpx;
	margin-bottom: 50rpx;
}
.padding-0 {
	padding: 0;
}
.padding-xs {
	padding: 10rpx;
}
.padding-sm {
	padding: 20rpx;
}
.padding {
	padding: 30rpx;
}
.padding-lg {
	padding: 40rpx;
}
.padding-xl {
	padding: 50rpx;
}
.padding-top-xs {
	padding-top: 10rpx;
}
.padding-top-sm {
	padding-top: 20rpx;
}
.padding-top {
	padding-top: 30rpx;
}
.padding-top-lg {
	padding-top: 40rpx;
}
.padding-top-xl {
	padding-top: 50rpx;
}
.padding-right-xs {
	padding-right: 10rpx;
}
.padding-right-sm {
	padding-right: 20rpx;
}
.padding-right {
	padding-right: 30rpx;
}
.padding-right-lg {
	padding-right: 40rpx;
}
.padding-right-xl {
	padding-right: 50rpx;
}
.padding-bottom-xs {
	padding-bottom: 10rpx;
}
.padding-bottom-sm {
	padding-bottom: 20rpx;
}
.padding-bottom {
	padding-bottom: 30rpx;
}
.padding-bottom-lg {
	padding-bottom: 40rpx;
}
.padding-bottom-xl {
	padding-bottom: 50rpx;
}
.padding-left-xs {
	padding-left: 10rpx;
}
.padding-left-sm {
	padding-left: 20rpx;
}
.padding-left {
	padding-left: 30rpx;
}
.padding-left-lg {
	padding-left: 40rpx;
}
.padding-left-xl {
	padding-left: 50rpx;
}
.padding-lr-xs {
	padding-left: 10rpx;
	padding-right: 10rpx;
}
.padding-lr-sm {
	padding-left: 20rpx;
	padding-right: 20rpx;
}
.padding-lr {
	padding-left: 30rpx;
	padding-right: 30rpx;
}
.padding-lr-lg {
	padding-left: 40rpx;
	padding-right: 40rpx;
}
.padding-lr-xl {
	padding-left: 50rpx;
	padding-right: 50rpx;
}
.padding-tb-xs {
	padding-top: 10rpx;
	padding-bottom: 10rpx;
}
.padding-tb-sm {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}
.padding-tb {
	padding-top: 30rpx;
	padding-bottom: 30rpx;
}
.padding-tb-lg {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
}
.padding-tb-xl {
	padding-top: 50rpx;
	padding-bottom: 50rpx;
}
/* -- 浮动 --  */
.cf::after,
.cf::before {
	content: " ";
	display: table;
}
.cf::after {
	clear: both;
}
.fl {
	float: left;
}
.fr {
	float: right;
}
/* ==================
          背景
 ==================== */
.line-red::after,
.lines-red::after {
	border-color: #e54d42;
}
.line-orange::after,
.lines-orange::after {
	border-color: #f37b1d;
}
.line-yellow::after,
.lines-yellow::after {
	border-color: #fbbd08;
}
.line-olive::after,
.lines-olive::after {
	border-color: #8dc63f;
}
.line-green::after,
.lines-green::after {
	border-color: #39b54a;
}
.line-cyan::after,
.lines-cyan::after {
	border-color: #1cbbb4;
}
.line-blue::after,
.lines-blue::after {
	border-color: #0081ff;
}
.line-purple::after,
.lines-purple::after {
	border-color: #6739b6;
}
.line-mauve::after,
.lines-mauve::after {
	border-color: #9c26b0;
}
.line-pink::after,
.lines-pink::after {
	border-color: #e03997;
}
.line-brown::after,
.lines-brown::after {
	border-color: #a5673f;
}
.line-grey::after,
.lines-grey::after {
	border-color: #8799a3;
}
.line-gray::after,
.lines-gray::after {
	border-color: #aaaaaa;
}
.line-black::after,
.lines-black::after {
	border-color: #333333;
}
.line-white::after,
.lines-white::after {
	border-color: #ffffff;
}
.bg-red {
	background-color: #e54d42;
	color: #ffffff;
}
.bg-orange {
	background-color: #f37b1d;
	color: #ffffff;
}
.bg-yellow {
	background-color: #fbbd08;
	color: #333333;
}
.bg-olive {
	background-color: #8dc63f;
	color: #ffffff;
}
.bg-green {
	background-color: #39b54a;
	color: #ffffff;
}
.bg-cyan {
	background-color: #1cbbb4;
	color: #ffffff;
}
.bg-blue {
	background-color: #0081ff;
	color: #ffffff;
}
.bg-purple {
	background-color: #6739b6;
	color: #ffffff;
}
.bg-mauve {
	background-color: #9c26b0;
	color: #ffffff;
}
.bg-pink {
	background-color: #e03997;
	color: #ffffff;
}
.bg-brown {
	background-color: #a5673f;
	color: #ffffff;
}
.bg-grey {
	background-color: #8799a3;
	color: #ffffff;
}
.bg-gray {
	background-color: #f0f0f0;
	color: #333333;
}
.bg-black {
	background-color: #333333;
	color: #ffffff;
}
.bg-white {
	background-color: #ffffff;
	color: #666666;
}
.bg-shadeTop {
	background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
	color: #ffffff;
}
.bg-shadeBottom {
	background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 1));
	color: #ffffff;
}
.bg-red.light {
	color: #e54d42;
	background-color: #fadbd9;
}
.bg-orange.light {
	color: #f37b1d;
	background-color: #fde6d2;
}
.bg-yellow.light {
	color: #fbbd08;
	background-color: #fef2ced2;
}
.bg-olive.light {
	color: #8dc63f;
	background-color: #e8f4d9;
}
.bg-green.light {
	color: #39b54a;
	background-color: #d7f0dbff;
}
.bg-cyan.light {
	color: #1cbbb4;
	background-color: #d2f1f0;
}
.bg-blue.light {
	color: #0081ff;
	background-color: #cce6ff;
}
.bg-purple.light {
	color: #6739b6;
	background-color: #e1d7f0;
}
.bg-mauve.light {
	color: #9c26b0;
	background-color: #ebd4ef;
}
.bg-pink.light {
	color: #e03997;
	background-color: #f9d7ea;
}
.bg-brown.light {
	color: #a5673f;
	background-color: #ede1d9;
}
.bg-grey.light {
	color: #8799a3;
	background-color: #e7ebed;
}
.bg-gradual-red {
	background-image: linear-gradient(45deg, #f43f3b, #ec008c);
	color: #ffffff;
}
.bg-gradual-orange-sm {
	background-image: linear-gradient(45deg, #ff7903, #ed9e00);
	color: #ffffff;
}
.bg-gradual-orange {
	background-image: linear-gradient(45deg, #ff9700, #ed1c24);
	color: #ffffff;
}
.bg-gradual-green {
	background-image: linear-gradient(45deg, #39b54a, #8dc63f);
	color: #ffffff;
}
.bg-gradual-index-row {
	background-image: linear-gradient(60deg, #23cb8e, #1ea875);
	color: #ffffff;
}
.bg-gradual-purple {
	background-image: linear-gradient(45deg, #9000ff, #5e00ff);
	color: #ffffff;
}
.bg-gradual-pink {
	background-image: linear-gradient(45deg, #ec008c, #6739b6);
	color: #ffffff;
}
.bg-gradual-blue {
	background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
	color: #ffffff;
}
.bg-gradual-blue-sm {
	background-image: linear-gradient(45deg, #1bcaa7, #5ddfa2);
	color: #ffffff;
}
.shadow[class*="-red"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(204, 69, 59, 0.2);
}
.shadow[class*="-orange"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(217, 109, 26, 0.2);
}
.shadow[class*="-yellow"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(224, 170, 7, 0.2);
}
.shadow[class*="-olive"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(124, 173, 55, 0.2);
}
.shadow[class*="-green"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(48, 156, 63, 0.2);
}
.shadow[class*="-cyan"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(28, 187, 180, 0.2);
}
.shadow[class*="-blue"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(0, 102, 204, 0.2);
}
.shadow[class*="-purple"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(88, 48, 156, 0.2);
}
.shadow[class*="-mauve"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(133, 33, 150, 0.2);
}
.shadow[class*="-pink"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(199, 50, 134, 0.2);
}
.shadow[class*="-brown"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(140, 88, 53, 0.2);
}
.shadow[class*="-grey"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}
.shadow[class*="-gray"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}
.shadow[class*="-black"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
}
.shadow[class*="-white"] {
	box-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
}
.text-shadow[class*="-red"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(204, 69, 59, 0.2);
}
.text-shadow[class*="-orange"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(217, 109, 26, 0.2);
}
.text-shadow[class*="-yellow"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(224, 170, 7, 0.2);
}
.text-shadow[class*="-olive"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(124, 173, 55, 0.2);
}
.text-shadow[class*="-green"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(48, 156, 63, 0.2);
}
.text-shadow[class*="-cyan"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(28, 187, 180, 0.2);
}
.text-shadow[class*="-blue"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(0, 102, 204, 0.2);
}
.text-shadow[class*="-purple"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(88, 48, 156, 0.2);
}
.text-shadow[class*="-mauve"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(133, 33, 150, 0.2);
}
.text-shadow[class*="-pink"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(199, 50, 134, 0.2);
}
.text-shadow[class*="-brown"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(140, 88, 53, 0.2);
}
.text-shadow[class*="-grey"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}
.text-shadow[class*="-gray"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(114, 130, 138, 0.2);
}
.text-shadow[class*="-black"] {
	text-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
}
.bg-img {
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}
.bg-mask {
	background-color: #333333;
	position: relative;
}
.bg-mask::after {
	content: "";
	border-radius: inherit;
	width: 100%;
	height: 100%;
	display: block;
	background-color: rgba(0, 0, 0, 0.4);
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
}
.bg-mask view,
.bg-mask cover-view {
	z-index: 5;
	position: relative;
}
.bg-video {
	position: relative;
}
.bg-video video {
	display: block;
	height: 100%;
	width: 100%;
	object-fit: cover;
	position: absolute;
	top: 0;
	z-index: 0;
	pointer-events: none;
}
/* ==================
          文本
 ==================== */
.text-xs {
	font-size: 20rpx;
}
.text-sm {
	font-size: 24rpx;
}
.text-df {
	font-size: 28rpx;
}
.text-lg {
	font-size: 32rpx;
}
.text-xl {
	font-size: 36rpx;
}
.text-xxl {
	font-size: 44rpx;
}
.text-sl {
	font-size: 80rpx;
}
.text-xsl {
	font-size: 120rpx;
}
.text-Abc {
	text-transform: Capitalize;
}
.text-ABC {
	text-transform: Uppercase;
}
.text-abc {
	text-transform: Lowercase;
}
.text-price::before {
	content: "¥";
	font-size: 80%;
	margin-right: 4rpx;
}
.text-cut {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.text-bold {
	font-weight: bold;
}
.text-center {
	text-align: center;
}
.text-content {
	line-height: 1.6;
}
.text-left {
	text-align: left;
}
.text-right {
	text-align: right;
}
.text-red,
.line-red,
.lines-red {
	color: #e54d42;
}
.text-orange,
.line-orange,
.lines-orange {
	color: #f37b1d;
}
.text-yellow,
.line-yellow,
.lines-yellow {
	color: #fbbd08;
}
.text-olive,
.line-olive,
.lines-olive {
	color: #8dc63f;
}
.text-green,
.line-green,
.lines-green {
	color: #39b54a;
}
.text-cyan,
.line-cyan,
.lines-cyan {
	color: #1cbbb4;
}
.text-blue,
.line-blue,
.lines-blue {
	color: #0081ff;
}
.text-hui{
	color:#787878;
}
.text-purple,
.line-purple,
.lines-purple {
	color: #6739b6;
}
.text-mauve,
.line-mauve,
.lines-mauve {
	color: #9c26b0;
}
.text-pink,
.line-pink,
.lines-pink {
	color: #e03997;
}
.text-brown,
.line-brown,
.lines-brown {
	color: #a5673f;
}
.text-grey,
.line-grey,
.lines-grey {
	color: #8799a3;
}
.text-gray,
.line-gray,
.lines-gray {
	color: #aaaaaa;
}
.text-black,
.line-black,
.lines-black {
	color: #333333;
}
.text-white,
.line-white,
.lines-white {
	color: #ffffff;
}
@-webkit-keyframes cuIcon-spin {
0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
}
100% {
		-webkit-transform: rotate(359deg);
		transform: rotate(359deg);
}
}
@keyframes cuIcon-spin {
0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
}
100% {
		-webkit-transform: rotate(359deg);
		transform: rotate(359deg);
}
}
.cuIconfont-spin {
	-webkit-animation: cuIcon-spin 2s infinite linear;
	animation: cuIcon-spin 2s infinite linear;
	display: inline-block;
}
.cuIconfont-pulse {
	-webkit-animation: cuIcon-spin 1s infinite steps(8);
	animation: cuIcon-spin 1s infinite steps(8);
	display: inline-block;
}
[class*="cuIcon-"] {
	font-family: "cuIcon";
	font-size: inherit;
	font-style: normal;
}
@font-face {
	font-family: "cuIcon";
	src: url('//at.alicdn.com/t/font_533566_yfq2d9wdij.eot?t=1545239985831');
	/* IE9*/
	src: url('//at.alicdn.com/t/font_533566_yfq2d9wdij.eot?t=1545239985831#iefix') format('embedded-opentype'),
		/* IE6-IE8 */
		url('data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAAKQcAAsAAAABNKAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFY8dkoiY21hcAAAAYAAAAiaAAATkilZPq9nbHlmAAAKHAAAjqoAAQkUOjYlCmhlYWQAAJjIAAAALwAAADYUMoFgaGhlYQAAmPgAAAAfAAAAJAhwBcpobXR4AACZGAAAABkAAAScnSIAAGxvY2EAAJk0AAACUAAAAlAhX2C+bWF4cAAAm4QAAAAfAAAAIAJAAOpuYW1lAACbpAAAAUUAAAJtPlT+fXBvc3QAAJzsAAAHLQAADMYi8KXJeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2BkYWScwMDKwMHUyXSGgYGhH0IzvmYwYuRgYGBiYGVmwAoC0lxTGByeMbzQZ27438AQw9zA0AAUZgTJAQDhHQwVeJzN1/nf1mMaxvHP9ZQiSUKWbCXZ1+w7Q0NqImNJhSSSZSyTlMQYs9hlLGPKMoRBMyU1tlIiIrKUfeycZyOpkCVLc1zPYbz8BzPdr7fb8/yQ2/29zuM6TmA5oIlsIU31460U6r+O1m9L4++b0KLx902bnq6fL+ICmtE0GqJltIl20TE6R5foHj3jmDgtzoohMSyGx4i4MC6KS+LquD5uiFvizhgb42NCTIwpMS1mxOx4IyJLtsiNc8vcN7vnodkr+2a/HJCD8oK8MkfmdTk6b8oxeUeOzUk5M1/IuTk/F+Ti/CqXztt62TIIfvIp9osDo0ccHv3ijBgcQ3/8FBfHVY2fYlTcFvfEuMZPcX9MjenxVLwYb8ZH2SRb5aa5TXbNHnlY9s5js38OzMF5qT7FNTnqh09xV47LyTkr5zR+ioW55L+f4n/+p+ip/PEnr8u4hr8wlid4mtk8/+PrRV5ufL3DPD7i48bXVywtlBZlnbJV6VMGldFlTJlZZpeXy1vlvfJBmVc+bmhoaKFXq4bWP7zaNnRo2LWhS8MBja9uDT0beupDtC+dSseyHpNKB+aVVfWpGnR2muqENaN52ZDlWUEnaUVashKtWJnWrEIbVmU1Vqcta7Ama7E27ViHdVmP9dmA9nRgQzqyEZ3YmE3YlM34ls11JrdkK7ZmG7Zlu7IandmeHdiRndiZXdiV3didPdizbFDashd7sw/78jP2Y3+68HMO4EC6chDd6M4v6MHBHEJPDuWXHMbhHMGR9OIoetOHvhzNMRxLP46jP8czgBM4kYGcxN8YxMmcwqmcxq84nTM4k7P4NYM5myGcw1CGcS7DOY8RnK+J+YbfcCG/1XP6Hb/nD3pGF3MJl+pJXc4VXMlVjORq/qTndi3XcT1/5gY9wVGM5kZu4mZu4a/cym2M4Xbu4E7u4m7u0RP+O/9gHOO5lwncx0T+yf08wIM8xMNMZgqPMJVpPMp0HuNxZuhEPMlMntK5mMUzPKvT8ZzOxQs6GXOYq9Pwkk7HK7zKa7zOG/yLN3mLt3Vexum/8y7v8T4f8KHGLvm3TtB8PmEhi1jMp3zG5yzhC77UifqapXzH9yzTySqloTQpTctypVlpXpYvK+isrVhalpVKq7JyaV1WKW3K6mWNsmZZq2xU1i7tdBLXLeuzQCeq2f96sP4P/rSs/1hpkX8om9TMs9Je78VKJ703WOmo95amaSTaGJP03s40oURHUxYQnU1TS+xnNf1jf6P+3V2s3hZxoNUbI7pavUniINPEE92M5nrvbkoBoocpD4iDTclAHGL1tomeprQgDrf6TcQRpgQhjjRlCdHLlCrEUaZ8IXqbkoboY9Tvo69R/3+PNuUQcYwpkYh+pmwijjOlFNHflFfE8abkIgaYMow4wajf94mmXCMGmhKOOMmoz2iQKfWIk035R5xi1Gd9qlGf3WlG/T7PMOrzPNOUmMRZRj0bg00pSpxt1LM0xJSsxFBTxhLDTGlLDDflLjHCaluIC01ZTFxkSmXiYlM+E5eYkpq4ypTZxEhjO71fbaV+/9cb9TzeYMp2YpQp5YnRprwnbjQlP3GT6Q4gbjbdBsQtpnuBuM10QxBjTHcFcbvp1iDuMPbU+51W6rO4x0o9D2NNtwsxznTPEONNNw4xwXT3EBNNtxBxv1Hn7AGjztmDRp2zh0y3FfGw6d4iJht1/qYYdf6mGnX+phl1/qYbdf4eM915xONGncUZRp3Fp4w6i08bdRZnmW5J4hnTfUk8a7o5idlGndcXjTqvc4w6r3ONOq8vGXVeXzbqvL5i1Hl91ajz+ppR5/V1o87rG6Z7mnjTqLP7llFn922jzu47Rp3dd406u+8ZdXbfN+rsfmDU2f3QqLMbpi5AfGTUOZ5v1Dn+2KhzvMCoc/yJUed4oalHEItMjYJYbNT5/tSo8/2ZUef7c1PzIJYYdda/MOqsf2nUWf/K1FCIr40690uNOvffmPoL8a1RM+A7U6chvjdqHiwz9RzVAlPjIYup+5BNTC2IbGrqQ+RypmZENjN1JLK5qS2Ry5t6E7mCqUGRLUxdimxlalXkyqZ+RbY2NS1yFVPnItuY2he5qqmHkauZGhm5uqmbkW1NLY1cw9TXyDVNzY1cy9ThyLVNbY5sZ+p15Dqmhkeua+p65Hqm1keub+p/5AamJki2N3VCsoOpHZIbmnoi2dHUGMmNTN2R7GRqkeTGpj5JbmpqluRmpo5Jbm5qm+QWpt5JbmlqoOQ2pi5KbmtqpeR2pn5KdjY1VXJ7U2cldzC1SnJHU8ckdzI1WnJnU7cldzG1XHJXU98ldzM1X3J3Uwcm9zC1YXJPUy8m9zI1ZHJvU1cm9zG1ZnJfU38mu5qaNHmQqVOT3Uztmuxu6tlkD1PjJg82dW/yEFMLJ3ua+jh5qKmZk4eZOjp5uKmtk0eYejt5pKnBk71MXZ7sbWr1ZB9Tvyf7mpo+eayp85P9TO2f7G/aA8jjTRsBOcC0G5ADTVsCeZJpXyAHmTYHcrBphyDPNm0T5BDTXkGeY9owyKGmXYMcZto6yHNN+wc53LSJkOeZdhJyhGk7Ic837SnkBaaNhbzUGs/VZdZ43i437TPkFabNhrzStOOQI03bDnmNae8hr7VawPM6q4GXo0xbETnatB+RN5k2JXKMaWci7zBtT+Rdpj2KvNu0UZH3mHYrcqxpyyLHmfYtcrxp8yLvNe1g5ATTNkbeZ9rLyImmDY2cZNrVyMmmrY2cYtrfyEcM5XtOtRrpOc1KzfhHrWhHyOlWat4/ZqXm/eNWat7PsLrd5RNWat4/aaXm/UwrNe9nWal5/4wV7QX5rBXtBTnbivaCfM5KvROet1LvhBes1DthjpV6J8y1Uu+E+VZq9i+wUvN+oZWa94us1LxfbKVm7RIrNfu/sFKz/0srNfu/slKzf6lp12Xe1saC/wB/IDDcAAB4nLy9CZgcxXkw3FXV93T3TE/PTM+xMzvHzsze1+zO7EraS7u67wMJSSBWiFMgzGGDESCtwICQAQMO2A4YLRK2Hx/gA4MdbGBB+CAE25+dL4njfGFt57Jx8j8h32/HCdP66+ienV20Aiff/4G2u7qnu7rqrar3ft/iEMedeRPNoCYuwy3nNnEcyA2DYicoFkTJAH5AjlIuK4bNUKSUKQf7OwHK5MzSMKgMo8owsFPAjoiSGLEjdqk3YosQsId7y/1mXwEdeEH1i0JPMdlvWraiS0pivXah3zT9MLf3ItB/tzM6viE0mdUChqnBsF9PimIOQcD7/P8sWEA8rzqAH06ZJpjN7h/oHPUrSiC0oliK+psL0PQ7o34zCi5oaS87E+A2vq/fqgwv8UHIw1TTppuQbEp+EDSWO78DT7OHTT+Y8Zsc7ib+49Ad8CLOxhe4s7jHWTFkC5FGEOkdAeUKKPehD6txxTnvV2rcUgFAPBI1kUc8eFmBOxSgOkv+QQnF1CoCCCIIEXhTjXG1usfgi1yC4xRcTyErKYBWrwARg6ai4G+U+4qwA6iKFVed3zm/V2MhFUjO71R8DRSg4G8q4AiQFXx2/h2frZjq/Lvz72oM35ed/5e8hz/D4/GbQafRCJfjurll3GqOEzJ4+Ew8QJneSEjMZbzBoyNS7o2ETQOgbKEP9xA/IAGxDeCr8lJAHrczpFyir6J0daalDEC5BcwYwaDhjJIjJMeGICj/vY5bMkza6byiPkifIIevOVOkCMhxFL8Lp3Ad+IWgUaU/QI7WxeG7Z0hfhykEXlHIIw3BGXbiBNqvl9Ao58Mj1M4Ncitxz3DHcL/wlMM9wPMSF/BlJ+lNsTAMIngy9pbxpEwBiXax2D+MO2WHDZCpvwBnXqwKQvVFdjz1U57/6Sl6PDnxoVYZheNyZs+BCzJyPIzk1hv/PJQAINFMDkCbK4/WKnixipZ6NeBj9chgvy8eQGpre0erDwXivvISABPh0VAiERoNJ+ZK7lw58208fqNcmszDYh4Vij2ihAQDNAIkRkbw8lpKetVXRJUyekG0nH/9sGqFlEPOv1qa/moXTJtvvy3JQA8C2PEdHfwmiFoBMgEwHaeFbzL+1PklXnh33sUHDVEA9mvG3DfHMFQ5IdsFJLFQsYqFMp72KSD68Sf9oFJuxEtiBP91EWh2gopVrvREbEtIYbRgRSQRnpGlt98207DrVV0LPqaHecO46LMqLH7fH/heAfqe/LkpXXKJGI0qwu1KyFI/DPxBXf9OJwzIo/xddyq2BZJ/ajTxcWgkwijwBS3w1jWycs1vAr7PZ5H/f/65pmhRDQRpV6qtKG+8hruiiRwHafufR1sx/LrICsOD2wnLlXITxUYGBiNBYDxuNrluqrhzguIyET3qXLr62LLVu+Jt5RvBxY8Nn2chPRFBgTXlO53/cWlXPrJh+E7QdWlvEEXiBgwvqXxiVwbMVKsd7ZVPPPOF1Y/0XtN1dL0eEXV97APNe9umhh/61O1de9unxjcbuhDRL9q4erfOk7GFdA5P4rENcA0Y7PjrEY4O5wgIkmlbN50h9/D3eAtEU4oBDOXgXwP+ew9P7IZw9wQ9olF8/ajzeEz13Qa0ex/+nsN7P+EjQTe1b5H1gscVLL5W+ipl8vkivhuKMHhB91mRw+PKbTkI4cEt7FheA8CaMjtqIWX9rA+dOnToFLpyv4LCMYU2lDTd+aeUCtK117YcBMO198prqvuCcXUj6LwGv4nfH3zhZl/cRCrtCu91jXP78W1Mj4YwPVrHXcdx+bBEBnMYVkq9dqRMpmOh2FeulBjhMUAxQoYXj3jOAGF8M0xIEcUAGCkUaTfx3e6eSq+dxZeYZEVKFBL1/e8E/R6wwHVmeRUEwVxHnG/Odu6JqzJqhCvLfMe4T9d3736kGJjavtGnihm7IQdUURR5aJk9ubFum+dFS0/mYC6BhE/u2aapvqi2amMNwaSSkmjH5EzOQx3LAQAry7GuQghEA4eykopyHeW1CJTb408dvX50Qui+8roHAtEG2JQwQiLAH+IDe1Z1pIACkSADmO/PAvDdnBCNKXyqhoIql3dqMUPQ+m8e9RAUm4svY3w6gudHjs1Fb0ZYIIzXvIjxAIFtXxlTwEq5N4Wn5AvvCMI7L9Bj/AyHKR+mf5gKHiFU7/JfY0oE0LD3AD46DzpVQIghoYa3Y8IAlAO/wdidq83PGXd+di2Oy61C1k9GUwxhQjxHiwuQWwRp96kx9deXY/KpHJmj0JwKFkXQzn8qym8OKACTndshI9wI8ErcXa+sjcX5MEKYHFJEiVcPwYmYjlIoRUJ+MK9lEqFm9xwnHMPx43VlVN+c6rcItT9+D/n92PG68kI4lc5B8yqEr/AztqWRTHcCKpvxFYvB6sbjhL3AH8NE+9g9CsDjeJy0T1kcWHccI7/fcw/hP+45Rtp67F6X96iHV+MCeM2HVMTuiYjzWtU8TcCCK8RNOMEj/F99E5yOx8kPx2hDp3lRsd49h9rPAZvuHjKVGWAIwzWCl/2iQMFT+gTtFxkv5QkJLQ6Mj4n8NHmIAeJxyaK09AVKS0l7cGv6GWLBTenFaKkTfz9Xa2UIM8qhRhTpHQbo+U919gpvfeWrb/H8W1/dvVVTfFF9xfpHvsvz330E48RSl6Ii+Fn8GaCdGrh7LXvuK28JeRGvdiGNcSZ7dsVtvXgBQP6rapAsNEwez7xIYSRzJpfk9nJXcCc5zhqm3F22kCccIClU6hi9Sn9fF+gjuDKHC+REWP9QGPP9figmycASzFoKMwD3zxXIoRNg6BLusRHkQIhwk/QVwnH1Fd51VRgCuAnl/iKGTimTwlxOOJSC4VnQVG7C/8BMU6UJ/0vXcZFfxXQluDKfA5bUkXo61SGGmppWB0EaYPyLGcw0ozNT7JQmHGuu+h9AlZ+WfSDwW/CfQQOzrKR+QDlUt4TvWQkLNCp5C8yYBV+KMLVcgny8qYGdHmPM6DIBzxAe4XFEaDieASAdG+FRS5swjXje150+3dwPIKN00DuD/ubT6W6wAsqyUKr+rW4GjSyuNJElvfJKpn4aN8Jo+FQoDKLmJ5OYhwsa89dVw4J1lXMBGEmCEhm6ebO68SXdwu09gb8xfzkJln6GfPhNwlovWEfNC75Qv6ZyeMyY+EB40L7FkTCaphz+zMIvv/OduuUDbp0ljTjDUQHCk5M+Akc4cjEnJBEsRsWvQ3hmO990vk7lr30QC2Ngrwr7FcV5FqwhCMI5CRUFXIzFLtKnWbwOG+msL2C+Ac/jLBbrCPXHs3wYFAATfsjk77fJ5KcyzpedL5pd/V2m86UASvRl4clsXwI5GTbyacypNycSR+C+VCaTqp5IDXbFYl2D4E0qwtDezCZaEvgf6YpAZWnWhhTXhjFCP5HGsp2EglHhA7cFMxi4VVhezmCmBRQwO+ZJZRg75LxlirZU95KGBMB22jpwHmmdc1+QtDNEWhkKOF8MBCkkg0Y3EUrwv0y8c0mq1tglnXHEgWT18SRmE7JJeHHSyeIllfYaf22ItDxBYIfHYQal8WzIETwGMgwHSOTPxFMBt7Vi4nVeNzesTuBCcNKZxqtwFK+7SSYtQiY1OjfV8ZFvMkhCT6Ast1AJkDyNz9Wfz2ccWW84hs/ctpG5Os5NcBu4C/HoLoL5gSf70sXRBubJvoWci/Pw00QGrkE7Tx8t9PcwKTi8KAcMWqujrNWTBIj0AJlsPE3RFYPALm88nDeDBsVj+DC9GG/sZFwoMCnZ4WpSMpGyKZxgFwPf35GfyB+V+2fRNB66MJ5rRSz741FzR6tkE4pXqo0ZGyf7XQU0Wp1ivfnJDjWu7vgJvaj+I/vWl+ad8ERyh2ynoux0G+wcdfsJFpy5uvb1c8PcKm4zkzQ9xomgE3dEPPRCx8vTXLARknJYXFu8/ZDT1UnCi6xZo+p0MTINAxsbd3bN9fCFs/UrrUwS/mbtWmVOM+FBHroz1O02mF60t0ymnkWzuL+YCuNp53clEjIzAVVLADpB4Wzv7qburqY9vQcfQKA7AYastt42C4wk2wF6AHFN2e6ubB49cHD4ggbnJSsSCYHl2a2jBx9wv/Em/cYAhqZYdJdjr02wSrGQY/IMIMiTCThZytcTPgzTWrpWMOaBXFu78zL93MEty31CIKb1DOGJmUqCZXaTDYbCTQBP0qbxxF2E+7o7v6ubNLWrwTndngatYJw2B3XJsQgv5fCT7ctyzst2FIyGV3bieuLRuwiTeXcm5/Zips3l3X6J13ESz9duPB/obCCcEZG7SpUy0R3iEa8QEY00t48wcMNEAqDtxv2wMR6tsH65uh7SHxEajYXntrGB2vZcPh1sBCD1MVXx8bIWz6WjpsxHYkog0YpXQkLzXegLAbl3NYSre2UQjqn92yHc3u9ryH8Dv0+Q0zfyiUx1NJN4RZRjvmB6xf6xlO2LBXhfOLN9fGxX1tQPmnG1fOfOnXeW1XgQqksevfzyR5f4XF2c18cit5zbtVgvKU9EJ30jNHHXcuD/TLedE3Tm6+qMosyoOnjgvw8G2ECpujKjwCfxwfnsHw4Wws/gCfAE/AVncS1U2+oHjCuv6YkBEWVMj9nAEjoR+/rAesWSZqgUhVekDy7HWOpKUlJEUVenFfi3CEkzZP0er/4zxZqTasAZUpQD0KLoYFoN8FDBooaLj57AdARxMdyKJbgdpXAOzOfYyxUqQIF+RgiSjJ0tCKGajrSf0mowOTUFKw+1dde4m1WHSw/ihlSnGBNE+czJoEGpwhRuMkxPOTc9WDq8qsY0dbc9hHsGbqgpTrdSvEMxGFfXXj+GWhPBn8Dl/byWFUv9OXKv1ixyE1AkW5kvhxCt3gI5xKb4s/btp6emAFdrLGZDdfVzitLZjZ49duxZhI9LK7qtqvryufZ3teP2kz56lYxOObNeB3BVzqzyOTxenTeMsRrwMcyrsagQqwFtxZE+AjSPd/pbSucDXCuWe5dxB1iP5/VOIDSh1jGypjzCL3hEoVawCDkM+zFqDJspRm5GYJkssn4s71DJx7NTYCo5ySgH7fzmrhW+W30rugbWArB2oHNCO6xNdNILZ2OyUBgsFMDeBnzO5+90urMd4DSfSIJgIpj4MY8gDyFQJPAjl4iAUXyadFmAPWCgvX2AVEpq629r62fl7wBS6WABAFLpYAET247sBRfD0GDOeZHyFcsLoSsRhAISkXCtpFhG9Qk63y9qqXCurvw4Gsd8Z45by13OfZBgHoxSpB4CwEqZarlKDJNgDBIScz0FPCOKOfJQkd7Gs8rGT1Z6ykRcp5OM6dfwY0sJPcHsKn6F6NSo1g2fCDJq9CQ6pll/xFBXPCDjpunaU9sVEHpds4Cy40s+HTdWemCluvIygd96Z0cpkuX9qrpn4+Aqng/4+VUDm/aqqp/Phvs67tzKX7ob7jgQa7HD56/S4mLP4JJuMa6tPC9st8QO7OjCtSeCAASbfOMpRIp8fpsaN4Mx37YmnowDSk2op4Bvz/rdr29X1OzlfQhKCl+6sklVtr++Z90eHxjVzu9a9cQEKkqyvr+nd1JTpDyaeGJV1/namaDxEm6t/pIR9Oblf6IZeMbl51dwa+otLETfSDhIItzWW1qGKL9PBF+U8yRu+la/95YB8uFMP2qsHnUZldsJA5ggEmD1MB3bIxiFkBvlZxqDCdPEJdWZSTQB0JQAo/TsfAaM8uTd5ayOveQ9eqjSaXMxPeDfjuIexYPB6/CrU6wGfHppasrjr1/G5NnHJbgsxozdxNLirTzS8hpf6UoBUjjXjwlZvmQWC35AERJGpBksx5TCIYa67Ui50l8yQ6BxmDSBHODKajzdDkBzCr6dagag3Xrzx4LsjJxcpWnjzsuy8PYZ+PuqIZ0xZFUU91/ubwBvgikmhmHZvj1d/XiqCEAxBQ+m29ff8YAsO59s4PkGsEeQH3ACQABf+H5AFVFzs2gFvu/sEBgOfZPilAZuFEsOV1DOjOARIgjgWVsgV27H8ABaeFJnKM8Utqm+o4yRJTW+kBN+ZggU8hk7I+TwMmAv44VALpiYTC7IEGdwCU36TU2qflbSzJQJurNwd7YbmBsPKKHqlBqA23kAtw+1rilaYy0tLWNWaKCpdWg7BFUD7hivdsNPtAaHEX6TXxNoMVfzwaQJe9JFXAVBDSBi+k9LmiadJgbN0/gu/gAug443/EBXfiTK2ubhbRC0R2yM5iNw2/A2Qz05NQsj7eQFPW9BaOVVMjJNSQC6cps3ZLtd/uU0ehEt55q59Zh7uczj2amqEa99WgZUoUc0WSmiAcVlYkMsujJ7F+Zmsp2w0lch6AcQKxYGH5JCRcqHMo2paNdfgKdzsQlFjbQNRXwxdcKOgW/FJ/AdoJBbmITgW86K2GS3GBDBt0QBA6Kh1BwCYXLDmRCA2J3Bd4phkNMt9WuEHXhG3aaTYwwflKHYSlxJeLg9jKtcGVsRBc/Y0VVqTI0MtYOwQm7FnI3RD/eKIvgarrI3FGnubWjO9OKanY3khgVAuLnUUPxfVhzXZ8XUZ5RJzJR8TaUHypf/P/BHKIDxL8G7oGZbVQAhs9OWH4uHWDj0F5KG8woYNpIBeuUHk0ay4HdecV7BP3GyKzMRmt/IdXEj3CbuIu4D3BGyHj0mkuEOVOMgy2Qe58z3+H3h+8UFv/fnPLnZlY3ntD5UTANTruDOTr/y+AZjkdtg5g98frp2k55G5tiKKrfoT86Mq3hgp5eoUo8epoiOwf3FIW/h3xz2pVGK2GVXB7aJ6knjmG42cR2Ybh6llrMsYU/LRQ9zY3pHrvsKkqc2Emq6A8JP9BWYu0SKUMkSpZo5QnYJs+GalnrtyDAxSLlCGn7CjlQoZiFyOmGAi5TGViLEGJgG5a1l/O8Iw3/XZjs6Jjo6spKiGIoC1ox6ytJKKusTU3uafZIe0/JFETz25S+9lYs0QQglKDQ0YB5r12YtqsnahVe8WBWSCVCKxsx4akPbwOEJfCPvXHrF+Zc8EZk4XOoC/E8hFprJh1uYWukhQL460XER+aqhYNpDPgv+pXN9woyIsURUikYlKaSnf/Hlz52QByoIyXJI6by0H3N3RVGJRsVOofri4DW9YMO+WABkGgpFfL38luppUFrz8cj4/eM7Ljn1U65u3vuoBmpu5nOgTkst1bsmLHL/v7tO0BTT6s0pyd6jXH37D5vo0CVp0+x0hpt3CSb/K8vAtY3gwxSYdeczZy2uN5llo/y7eSfgzTmw4Mx4oFlXB9eIefPVRANXPzLI4xbKnm7aAAKFtMu4u/odRKhuvXKO0GKXFHsCFuOo0PQ7tHeILOhramIK4airv5v2VGVEYPkXg6hqpl2hIwjfnjcCRAijkHWmam8Y0wyKtXeIdMbu1j3jKYGmGXx5ald5BdNGAt8Pct+leILBs8jQBWYgMLUUi4w7JvJ8ocgYZuJZUaAUkboiEJKI71UIY47LNmHKCS/tx4w35dUx4+0nZNV2nRZwrRL1spLEPHkEo44yq4TU4ZX6iLsG+ST5oleSRPYyedcrhYh/B6sHXxItV92ivzKgrgmF1oiW2tcpYw7er9+qmkLcD0X5UgAulUXojwumeqvuDwFF7uxTLbH2vCK/9/OC8xdhe6XPamy0fCvtsAWNmKUFb1LlfRjvQWDsk9WbgpoVM6D1Pp8DC7Clk9YvhfDsLVVD6tmb+p4v1MMC7KTN4Pl3N9ef9r+7ve9+UAviB4Pa3IML7ZshrrLALuORHouItYTyDDGprELtHNSqMedMUm+mYYrOFZEsmd6gsyHcSJc2uWI+JKBtvnVaYCYNsCrcGioTWahcHImHCoGWSn8LuZzYBeGeidwSTz5ibeY4hQtzGSwhcfkadbQXs9B2gsWbL7EeQs5To3ctYnU6ZSzSnwTprGveeHRRR61fgEW61jQYZ11nY+LgdZ/mClwvdz4ek75+YiIlwh6eOGGqrOqhhJxRc2L17e+rp0kWpitZqccAzBkFC4uYPcCCeRcWsubkD/QncJ3am63+a6Zb3QyU3ramruYVsdiKTfiwsrm7qa37tMORJlIt9Q1BQ+CDrWZhKNEwvn6iIbGiEMliUkgAkoO7Me6FGCrCt5KZdPJFIZHo3Rq1MqlUOo3/QvbWngbBoz9GEEoSgJZtx8N21FYkFDS+iN8HXVkyvirF/VMuT9qGZ+UAN8Yt59ZhCeG8BZIw02zOM7jU02k7QxCmR6drdujaXJkrzTkeQsbDVT9R8zw0TjAtJ9iHj5udMVp+SbcsZ6KbzdszeNrML6TrDAHE5AHP1JwR8dE5YiWCwYT1EpG2icD9NJs44XknNtepLYqjc51oEc9j/rIuJ7gQFvPF5iJV8lbYJKecIvlHXTTZlBeptxK7AKMejwfXVg/0jAMw3gMfoefqYCQFQCoCH2Hn6sOCoGkI7r4g3hFO9DX6g6q26gLSuUqHoTR3tE40WPkQ6BpRkQk5xsM5CVJfhNVb/XXPOHyJ1PRrt+YIPldfAkJENx9XgIrZTh5ms737eQwoMFDKTyiipooyEPZnfRqzS8ygOzBcCkT+KRRNLNxl7EjYpJYJLDX2m4h4XuGxJ5pIZOLFPakHgfKj6hs/lksqCsZ8w9rvRST7VfiKGpCg9PvgKB7XWU156y1Fc95sUWJhhJ/0gyZgS8GgqgaDkvMrp51QZ0KbH0On0QbXPngRxkAFo6YrzxaYkksi0EdYFsWkMAUo+e1EBiS+y2X6LOPF8dSfm5LukLkWFvwiutEXM6EvmAGg0hptNfjRht6Dwv7rfWLX5snLdg7HRMEvSdGYFBblzMarbrvxsmFFv+82cVcuOSTY44UVeyDoeudf8OhSN4cfmYaf19G9d4XCcjq0+0Lo/wuFOKAGhqOtFRCxpJ3pLhNG7trWMtEd9Heu2NTS2KBFDUkrtFWu3DUYjAzvqRz8cgPQG9M7xFQG7lnRfD6YYoP8YZ+RD2g7LT7dHOH1shSY80mconaqAvGdLEhFYiafp4+nSnCrnsFb4syqOpI0wakSofcHGHX8BgvayepozQQKzgMZFeMc8kgspP6g+mf0p/5/xi+AD7luvQt8D7rfww/MtQi4Pk7UF6xvUR+EkGsduJJoAKaxfD+tLu7Jc0hRrgAlgk+d168irgRPqNROML99vedoH54ZfrDQkkEht2gLrcclS4E88yG6gjY1Flq8jc9PS5hzgMw76XLnhxTVlQ6oxKOOrLkzxO2ci+ALPJULRUDnvAIMagHEoIK/B0DkNeeEv9iA2zrkvGqAZMEP9uI6wdUAGikf2Iil1oLf+Z+49kJKB1shEFxb5quojxtyrTV17rSExLG1AyhDyte53hZJC/A4LSUwwg0ooC9qUT4WGW9/yPn6B3pbotsnBqeWX/yVkYqFjHgEBbr2Ov9wy5JVoVzrXhC/tW04eI0eVVTtpCgCXg3wS3gfnOJ9+oqe7ZnLuj46/vhn7+ttbTlvy5rz9YigG2uHPtS8o+2m++4cxOf0eb1tvBqzxREIgE99QreZTAQvRpwnEwFvXUvvKoCToLylUtlCaMS8M5w+m7Tk+t2TeRKmnMEwoQTE5kKtDjkiERAi2FeQMj1kCnt0AEv6lNdhPh9WXRlNT4Nys/MSJlPTNdHn/uqMblEHfCKdOA/Nc5KH057ug11PYck07fpXYAmVueuDyXr3BGpcgtTW8guUwfjyw1SO8YPyPCtYmcopxHmNyh91liMJT3sDNEI2zL2VElVy5IdpJe74s+4vnTuTtTFE5g0R8/q9M/prOaYN+vnffPWrbwnCW1+tXNklCIkoJlNxnxVGqOWC7oe/z/Pff/iR76NohxCNqcJqnhehIAqIBzz6lI93bqNunJs3UWfT3Uz7w44YHvWXoNfHyy3lwa/+hmcfbEgAFAhhsgJlvw5ALMZ/75FHiC/yI+NDBzXVZ+tPSQLxDIXwoBL7pYI/oG7YoOLPKTuJk1Ua/42TqsfdC8PFHcSXv4dbgmGL1w5hE8lMoB7JiCieMSgRpfPkBxIy0wgsd3JY5QJ1FSBIT/AK6KlYsfpvNGJGV0W84LsDqhPHhLCcFEr5AvmhoAZQsiT25MA/5HrEElSqazHzkM+Xm8A7HhexP0n00AJSZOcrkgaCKrjh09kOYMUsYGiPOffmuwFoSYNtVr76RUY+EuxEeR2GD4jt1MJYsYj5wKXcasz9XIz7aGbM/AILgbDgHrXwnuU5q975yV70Apw6g3HSGc61fbAz+M6Cm/m8I5zluc/gMUqa1gM0jMh6hF3BWfIkJsKJ+qdHznbTAWe9+4TpBxwB/hlOs8CiF5yEYfc36Ak0wmmYYyR2zSFukruaWCI8bxiMf/L1+nCBOfYWspJL98RwikWA1NSPRVDzYMfQpNFXxOxCHyNFYqwDNXEKi1tTrqcMPrzzv3ULnzGNnFThGnJzymq3qBfMPpUKUuoOpgqwQBeuiH8LLxcejAz0yKJPVky1vf+2e4/0daoBVfYJUnWCBQDQI/w0c6chB8g+Rw43k3tHVXUfvbQiGIe2RKw1mOfGDGXa+dvBPzrvKwQFfGXHwwNrtZgsGOPFtvbmcYM4G4CrvNrxsU7eJPDs4gYJD56vny25eVPnrDg5z/iaJMgwnt19ekGMFJxkYPgBO4G3z4Kfqw9hrDqmB50pMO2MehokEi5FWOXy1NnwLynD9HzUzZBUNe2iboLI6QvM0TDTUvZk7ZeonjSGaU4Z45iVLM6DTQMiQhCMQlB3pUSRsjsBMP4WMkzTyYyTmCzl+kuSi4mzmB1GHDp5yy0nEdg4ccGRMNT9SDNR9Es3irecdBA8PDl5GMLb9ip7D8HDZ+jspnO8a2ZmKk2u8AFYkMMV4Gq23pHPP3yZZiNdv/4BHt8gLx+evPCwIBz+pemfIS9gsjYzNUki+1Kmx5eyOMQI8Q6yRKIgwyuCuUwWyWogrpPUBaITikQ/wLzF3LGzS254VylSN4STfp+CVHBzw/IYuFlFoajq3CNHZOcuQYGv/wi3ua2zGQSNP23qBAQ7PAU3Tm6BX5FljCNQO5gGhpqQQRnLlm/IiRCuqIPnnT/joTNq+h8JxkEs9AixumVBN+mS8yM/uLFn6dKeG4FogA52q6mNq6MLhA/p4rjMu7C8hSnFOagCWojPv4SJwn32ogRgHgaHq5PXnh3V1/Q3p9FyroHLc53UV48DfVTWIXyfa68wqMha5irlYE3tWfEKeSa/9tRsGTUHwydQdCDhy8dKHyKhKJlULsNDXbgJrG8/9sPqJ5hV4ypX//zJvoc2J35wQ/+t4/jRnPNz1njU4sNoRxei/nQWs8jDN/T2b4oLPDBBpOtOoDpjro3iTYB5NcyxXbXu8xsbvrk2V8APj97otLrwcn3nvovXTpFKPVnmGbwUUIdJz2Bvhz2bF2Vy0TPO8fh43LlbFeSAmgadTW/g8W7ubMNz5kf5tjQGuwj+GpTwBHlNCFmq8/F8B0b/Hw/G48GP+832IjioKyE6/i/R8ScyxdYFVo06S3u+tpapsahO8vADamCSykSdTIbEXe0M1+N/cIq6VRuAHNedJkVyANcx6QLs2qbF/IJvxTpQkzAELcSLfU0aL/gsLIwLKKjxvKTokpi+Ofet34NZj6ukp0n20vmPDUpCJCZ3T62uufUA6PMZxXBrWvADENQVyV9JKZakIH1Fm/RX9fYDjRvAEvpm7l68wucc2YmLQb2xoM5dl1oIXFWnp1apAxiqK9vUz5oFJPT3lVJMjZhyZXeqAcCfIA+U8YKzieKOVE41L0zbH4Rfq9aCVeFUzaGUOYMy/VG1Muf5Wztc5zMFXZeuHOjtnPngJgQ3dFeukHRDDBvi4bIeAHrLKgiGjg2BYrtu6uUjIg/Sc3YGYsVspnqsMd39sE8kXi5GF+6Sp7IacZXbrqVonxGNIBiRQq137JtBN628/CNNISkMScgigjEemvpYQE18YM/E0NDE+QczSgDXDfgYBLWYYUJDG7kRbh23k3AjVCHJXA8rRTd6h1n6iQuVlCVKT+pH2kOQUyRE9DqSXfEM+otIyTALdFvJKyAUV/JP966mvrZWf7A3CIJfUewfxEKlILCeUWwdP9ZK2IOWZ0rrCHOyzrprESkacAG1zUf48eZnKuuIKL0uaPWHStafKP4brJ5gv/UtNRBQOtQElglanu2mPM4a643F5GwXHtOUp2jg2gkGzNfPzvdQcrKgFrZ05xTzzI7lunEHQa/nau3No51GbZLhKcTfuHrN9Qg/yX/y4slPC0SU82YXsXF7nvUOMVK9OZ+duH3blRDs3307LX/4TgCPX3/7nM2K9GvM7deKP6xfufxcV9wgSUyepPfbqyrmY/jpyzZ8JCfK0aiUuHTpxpvRuzrmvu+Q8xncMfoqifrBC2Ts5jsB2DyhRTVJ6xu+dDdeIy4ufdnFpZXF9TMgizGlWcMPYbPilVM0AGNRJY1TlSQTjLqN/CfizGbsU01JlJ0Ti8fJVU8iJQSWMw/+X7yIz5plSc6bMh4HieqNvw//iUtyLdwYdz53CXeQu5HyboRTp6idaHBoIVzrAbEdMuc9kcjiPdTBoJyCUg/VX/aUC5i1Z24HPXO3ywWhwBIykDIN3SbRzxWvAH+qmrwP+Oz9EzCCfEKg+OTOkRXi337sGz+BcJnzzHXTKn/vtfQI9nbdPGIEJNvfvnPM1AW9ISaEYndHljZquhDS/ckwFsV90TCvas7nBi6P2cXK0mvika5rtWKTYhea1DzvN5BsGDz4GFS0RMlMKQ2Q92f7zNzI9pHDgwcPAeGxnb1LnB8q29asuVanR9jfldNQpAG/GRvf3mzYss8Y/FDWDoqYgdMgUuwGQwtLqtaw9JTe3t1zvmV29pV2fszUApmMZmRaJQFjY/znrYFZNIlpTw5LXgzXdaKiAamQwLTx1Nma0IWIbYYwwPLuLcwCmET5gcjKxuvEyriMJSXcmTraA3/Ysza0riW/Np30KcJFlYFdAoJLWloGQCAN/HCN893yhQIPl7XEW3Wzze5dba1uSQ2F7MFrKT6nngTO10bIVCMHwMGEzwYgbFgmID7MKAlhCkEQhdCGCn520lRR+jBMIgijUBfBBaLCXjEk55SkObjDdA2mGbWgqlc3bn4KJbkEt5xY6fqZE9tZ1DQScQgiUdaYKFfYCpsnZxA1YKZYQJOjmG+meTW8wpfTJLgtbfoxjl++GbhSxeblF0yFeFUwJNgq8pNDpHFD+I1x8uo4LtyRo2F5SatBMqNS8+2bmSix7XYiSvgJ/yW7seGk/UT+Wf6+ZR9wjo6i9AK5R9SCkMg9Nz+xQO4ZfldXQZU1cstHPHlHu+FjAnry5snbyKt7D/PSYefFea/Qgjcvn0evubLcam6y1hvKbZ+rN4UuWMj6IXGto8t8hCplybNdBJ1IYtgudtIQlEoZ3+ktE3/MRoBU1tNNExceCUHdkKiA9yHJ6+htCN12oXrhIfi8ENpWVPD/20KqbyiAZCkQWrOWlwRFlWSoD0nCEVVMY05REtKS4E8WJYMPBMRQ4f3If87vgry+2bI263xeH9qtmoIitrZCYjcw1d1DktmvWoUAvoaBguFPipqUThuCSHnIM5iH5jC88lhK2cJd+v7GH4u+WTJdl9ZiYiTKExKRhqW5EV3jD3ki76owazcwJOGn0YNXkxCYiYEtHwpBTSOQi5+4HF19vzNeC+raejVw/Ljhloa2HIDwyk1GEIGARoK81n5RbktqMVmSVDMpIFMT/brzRUuPGbwWahvWyR3d4M21kLv6QYQ/tvK6XPYjuykALzsK0QMH6sLRNoX8mildt3XLB5SAjr8hbigPbvjr9PIQrl2LSb7OkGag8J26JERjspbe06/ryNYmPuD6F7yEXkVLaCQdyfXTV6AeqzTUryCGkStyEut10SqFKTHCzEBfod5nau5eySL+zWxR0cX0WUu/J3zH+dau28PH/WZSXNkDj/esQLdVD0UyyL6Mxt7mTT+8YoO18TLoXe6PgzRz9yGqATipBcC2KyC8YhsM+Ks/KY0AMNZTSkWhepecMgl2MVPyvZsuw09seEDy7kjHq7+NpuCUq1JgupLr0EbuSu567hT3Ze5bGOOV6Yogk6SfJJKolGmiEKK4Jp4y5EzFAbKw/IBICI3uVQqSRURCKTBXTIolXItdLLA4L7IUiSxGfxnG0rNAjUOViF2hmrwiJsQkbQVdokRDR2ohk2wEv4bnXyOgTDY+ScXFGOl/FEUfQL0BOYyxvN4al8XQcIvu77FE//6LA6LV49dbhkOijCkMwK2QAr0I+LQdItBDvk29vgDiQ2KLKOTzii4M9eNZYssJQbDjPiEshRAK+Ho3+8K66CyJybYW6kjn7lSjaud4Pw/8+kgS9PsEMZPqH9YiQnT58qgQ0Yb7UxlR8PWD5IjuB3z/+MRessz3suP4Lgh3jdPj01jA9JdkpLfs7jQDSrJT93duSim8v9vPNzTQk5La1OnXO5NKwOzc3aIjueT3KfeqYVNEkUENI4fQPVDIZhXgS60RMOZJG7pPtfWlFg+ANhhBYjCsCElF4oU1Qe1iRWnzt43qFlSHJ/Ky7Rscard4n7YsEFim+XirfWjQZ8v5iWEVWvpom39TrdF7D4NDXqvx0fPJIXHFae4Q9xHuY3gOoU5i0R5yw+Qll5h4YTku62Dlil4Yfc4apoJTpX/uGdvTvOFFVKuHCVoIzzWCeEZcR7lG9vgwFDC/MQJKhD+h0UhdoGRH0EwrFuEFC/Q3Z5oHiORqGRndhB1h3oyj9OuqMNh8W8OQpL4eQglTTxdASE8bJujMXkvW27UIT5b+ljR+NRTQ0x1CHGmxbOh4cYlgIVu8zR+BlrCkeF8oG/NV9x/XDAhfw1InXC1p9xk2QK/zYBw8kV+mAr6dKjQ7st26Zendgi9ojC7rQkBImc7pS4p9AK+KS8CoVVQkczRPmZOhVtrgoDnEZIB0MCeL5ljeudBqSvpBX/OMHgYh/0xzH/AnmwIBI5s0wrIcNpJNmsvXvYx6sVRzHrcbc9TUEwOv6Jov7gjN9SJR5ZSfaA1cNwCRsi82db7BuL9mjxgm+oFCnmkKCpTvbgQ5IZyR+ol+ot/MmESltc6wRaMRwg0n2328P+ZDiQ/3KbzUpLe1B4VdAIKG7f5dn+xDMGWItrFVDwHVxugG3lXsB7YKzOpzZnuHlpN4ue9wXgh3HYbhKs/D09VDmglnMPqDzaHOFgQHBnNyzBZkiAUyjOhTfEAFgIfx9b6hYDtELZ2hZmgZ01isd77XtgSApa1gEAT1acMCAHP4SUvXs90NfLBtdBLscziCUJY43/VHGB/o+ZkX6+KGXasMWiQfzFy4sCvtPbRITpi0q7PwHnW+uHhemPq2NL4Pf6KFbaiXOM/t5uOt5Wka516k/nWL5Jqx3qMV8C8XyTkzeY7Wgd+dPe1M9d/eo9nz8kHYi0u8i0q0iwqtbt2v4LqHuQCN/MeMowFDKYgRDqbnOVefMT8Oj7rvoqHRU18/dWRi4gg7PUaM0oyIuwX4rdHx8SMnv37yCDs5fzfvZ1qgY/Ky+/0M8TcQsp2wbxj2pmDIgGiuMZ3QOgcbD7nddW05cmr3xo8eXLLk4EcfvZeeHnpX44brW3ZkHC1bcvD4Hx8nD9OTc/IsbWX5KkbhDMnrBzKuc4pr4XUdQDJMqKB+3Z5GliYWIWLdND0ZC3+st39kuCCJMLO8lCvERRezDUNAoaGqfQXKbmD8hUdGKpYr9AZFaGF8bdJIBDcpkE2TDM609mMU37rtG5msovpN5wvwzwYbm4YG8eRFanc5Eb3QD7IZOabFrHgDEA6ZfqsjcuC4Gg2pcFZuCMJRjIlP40peyGL0I8fNWbDWiVQqt4ztPDmBKWhMXXL/uv79bbv6+ytXdGq8Goo17WhPRW8ALaGEIPmjB+5SQ1G1OoqPNXpK9PCruG3UU4vSU3GOECYBDaD4w4hjvk4YrxfM0ekeAdNH3odh0NzUjEGBJKD6NvOaR/dsSvcS0BfPhqYp3Qvwk5i2hTDlPBXKxn3VP6YGOXKAwVrRJXvATHt0T1AaVSiF/KMtJQBKmJrllfnUzAjNUbPumlzujj+bW0fhFIkhUsgASvWpItFNzgmS/8Q5SXyVwGqwnqBRG+yFiuqcoDkh1znPuTiVxfT9A/w7bj13BeV/b+Bu5bhKNuc5szF9XqFYUxRR37xIzS2xRig9r3xXDeW6KeIhOddinHP/nUto8oYgbt2jGjdvy5eCMm/H5Gysa5cuj3U3rwoj0wfafSaKrG6JNBumT8vEIl12slEN0KDuv+no23rElPRQeLx1+PLGdxouGiBqDcpDeAXwY89fcswrZHxvfOJTz/N8Z1yLBQS1B8BHjh49KaLdm3267tuyi4fthfZrbj7QnMtBvsPAFQ0Kwp98YuK20uAoL1560e5LwOPzvkELo8wsdannHMG7/nSjnMWluCXcQaJLL+Zd92Y3PlQS8kLeixA9l8kZMbZwfmqvc3vTQB4h5zGf33OW9fucJ53nwARYhqkIxl1wkvrSMpvGqGvN+BVxfOtbr+LVu2EN8S5bW1rgOkMeGIVpMApNzVU+T2L+ZPTQkiUryEPvzC40VbtlGprSECS1KmvWkGC5ta6DTK3ytKv/eAEdxfLZGLeBm+Q+hOH2/kUyGnhM40ypPceT6eopI/X8LNKstCwetVzM02hn+jYV4ag0h6bevzhV2NMr6Eo+r/l79xQ8acx5YN1+CPevo8cvF3f3iEKDFBKxQLXXFxJ13TmEUOnC4lZNlyzfha4k1gh+Krx/USjbLgMlm/UhuT1bE6We8r6Jjw82tirggCVoS2wkyRam0Upb9saQJUvIHtQBH76cY3roMy+iz6BULc5qKcbC1y+eK/IPvj8vm0Kpd54Rk5ra8PBBmmGhxJq+9hIIL1nbjUX8ke6uUQBGwUF2i/3cNQLhSBf92elZdwkAl8x/g/wMly0Phd0fdq7gtSAK6O2DgL0XCatIFkS0gSRSe6EOYkQ+6Ga1dI84P1/sl2pjrZH0l9Eur63Oz1bYS9Lsp4l9qj8ehuJwG+1DV6LDlOOqiIRNNCnbnG9Dhut8PxmW839ICuV3/uL9ZUgG8zIgo7p8kDbNPVsfnVHnllicy7ZTlw7y0/PyY83LAlm93KgFyk3WMuQI874XZZBYjJOdIxvzPMTmteCFk3/F8391kh1rgSLMLlXfHFSpPXXyr77A2utM1Efyuf7rL6PlBA4KIAwWzXmHpyu1qBCxiCUloVnJvulMSZblu/a5sd4igHIwJPM/fpakJDEUMKWAh8ApmZcC6s+l6y7bflRULcwVKLcEnL8juUhU8Gkl6uULIt8cpjYsgpj6TcNNtFug9NiLDKBBAnhBA5cX7yNZYFjQNUyLouJ79sdIxksdgmLvyu/eQnr11W80Dn33I0YQ9Dl/RtKlWJYEpmTFmVJGIREjG81bFQnhlolHt19zHX5Cfm1vcSUMGv8C1oJNbaSK29QAllCdSTWqOPvV+TLI6ILZwqL5FogK3plkrel1JUg/CLuhf+F5wsoQoTb7cDsuIp++iB1vVAEmHldfShgd9cZ99JEFWe1qbxDqgv9CNxL78tVX4VWn3uonNxf4c68/R647l54Sx2ZGe4lC7j1cWRcVuWiav303EWlPuewq1oWLSBcuYkdqwSePnCtbHn7If6saD6pXXU1M2DeG3G7O9ZnSURKTAmdr8Tlc/j2k1/nxsnW88p7q2rZBAAbb4HP0XG0MhMMB+Bw5Lq3O1EJwnGDN8yGNnwa/ZW85atsgPBIOOCp5Afw2EHb9lJ2ZOT7Xy1M8wulYippgmdxMNggmwwImGx6SlaXfy7IgUecNL19DvS9fGwmvhtzWqyG8eutZErbh77KExaTwzHHaC5bOfOb4My/ip4H77hmS9I3kZTvDlUlipDLgymucU1QQn7rlSYSevIWV73s14DpjjARerc/zTPpUxj1y431YV/Lvvw91Wn7w1T+o3bPv2Ure1f2nXdvZzvfvOZjFgmXBfTIcKdEIAJpGh7p80/B2ojwpUwfWcEREyTmT2lSImtSYK2GdpenWvcTStDTU5Ncb0h14+gRVAC9XIqptXeY3wbLA/v2SCOwGJaeGZUvJh6G0iHXpyZtr1iXp1tO6rvoBGGiNZzQAJxXV2u9vCrUO3DqJy5I/BARbQhg3h/yy7q2dV+A0F6IZoUaIVxIVkUjuG4zOqBlNEknqinfdBNQjxr1N9GVFG2OU/03y3Sz9xOceXkpWbM/h+470qid0S9n1i/94cxeJnNn02uzrm1XwoKZMKkC2h1eN2DJUL1aWdvfaWDLEGG9oZGgJQWO9pf6Segrf2LX3gp3EI2bj1u2bFec+5Xwl5osnG5NqTDlP/nBHmzHn03MU47lOjANGiQ4BcxFSvtzfV8x7gU1kECO2UEtMV64IYs3dAKWoq1VfuRYlMefHBxJdpvOnfhH0mG0xd3mthkByfhzsjLPrYiMYE8DqCl07AwnirdhU/Znnfj7GbsyEgl+Kpy3zBX+wlgAxYn3bDLlXoWcCQbb4KqvhmPuyc9QNWnvUDZryfGHPoFmEMC/RgSWIa7h7SNQXC9eiCRlYsrQwZTszWcrGUG8lmsyBjKREdOjkNtH6sRRZ7m8sfXiG+UB59bm5w2t10tSEEjMASQakuoilbBkUEKcqKi8lk/mMirDA3tJRaIK6o+lKe09XJxHXs82FJiU4JmhC95LRsWURn6bFLaTawf6BSiloq0iFOhw0gmrRlNvaSt12g4rwXMhGK8tK3XprQL7f32Q1R+Px2PqM34SaNoknOoo0+yej8inclYSa397ZvSePv4XUzuuXDRxoEwS17QM3X9NOZLL8zgt2NmGe+BQPu1d97ptfmLA1EhEdU4P20oemHxiyg2pMFeRQVG0OqoN3rt7wsSUNUTUaQkoyOXFq19ZHlpvtfhX8WtOgmEynG+W4nivmzZsCFgyZN2U2143PELeDu4r7KPcl6n3UBQqVYWRTnXKlzKLeDepaRl0bvcSJWeIIQ0O+vNT9wv/dsQVVjJsmbQADSQbnaLPV5E/K0Q45agGpVUFKQJV0uHalYEh+nyApk2pBlaIhvLDawf//wz8TNG9KtodyMTYASRFqesPmdLeKzIRa0ht8ApCFXbsEWeVJ+240DBXiX7KYs/2/NDk8e/MMGsMUZy1eo0S3CypWjiXEZZuPYH7Q77p0utGhQMyTABk8UXJFiar9/GQjDMJ+49EseeENFRuMKkGJv/ZtzKkiCczSjUh2/CRgCZvAR37CZBD6U3VWhQdvQ1BEvMAjfOSRAOEkr+qCiHnywK22YsmipjyfKo76wj7Q7wtifnmWbkuyMxH4K3AH4aHxveqs0gk4+jYg/9Eqz3C6LUCf2tYZRFJ076ZNHq09Rfvdi+nK8vfd83rmlMRalYkba1/FJrn7/oDugu8MbYFwy9DQVgC2WuKVhpntOCFcphvZjvfsIUh7Lw4Nbbnf9F8pgY6soV8mgI45ueV2LCslKAdBlFUkEtD1pkYiDYHHqwkdxpLGv1egbIVlJy0Siejta3kpqOgqTEsIaorv9z5LRZKTlqygz3kdN0yFjXKwxtNiXoXwsztINjvgatndEI8MEwuZ10HbgkDrfC2sIRSxqJanwDAEFbv9tKU25mDwz8ANE2a6CY+xYfFwWPKerPezrHougXO5ZVmQevUbjOPCh72yHFRFUcs1N+c0URRD6uOGIQR9CC1tGAQBLaaLWlNLc86HfzPxg49qqhrV24JL4Exwsdy/Xo5kNyV19VU+oEXl8MqtK8NyVFMllEaRmA6A1vPB/WC3KNkxKbxy24qIFNNkFY2INl6rwZbOpZfUxm6MxWm/vxn5/mfde04tMqx6nS844URLmFfZwO2mOQuPcvdzj3KfI1xYnf4jU39RWvBLErjmd/LL3MW8X/Ls5Ma//Hcv7Mwc3+66jYOvsfPb7FR1L6/3nGTn375/3ukHZ7u5sS75DcmwOZe5avHy7DkOM3O5gv7ww2hNeGM85go6do1UezjfnxgUSKRVIwupIGuxUpbIcLHk2mZfF8gU650mPS/iTsWqzlhB9RY3tdEtyksC/bRwEXjtzlpjZudch8EPAwBkAt901rrhrl9/PvBlWXGWMylJle930/648uZHqG93D4nSXdBiUUL1TSwi5s1T14WCUP9GrdGX+2LKyxJtmfiiEosg6Ztu878lI4eFDdQ3Gdoy8p3hFNVrpE8GnA8FYr5/d9a5vXjmd774x+YCA7hazonTcIaLcFnM29OYr/w8PWst5K8+4q+4WJREfVT/8/fkW9EDB5nT2YqB4z6/qvhQ1aHubEyevr0G/o01LPfjOrS49etNeysHH0CsGpB+VhOVGPhwnTj+Yy/TCDvPzukCeDeerYkL4H5dyd1CItk7qULUVbdEyhWWNMVPdXJsRROmzVUpk2Bjb5nPKRMjkqe2O7tHJQWe7WWIqPn5oXFBiUYFfdcE0ZKqY7dd3Kq/+rEHX/VZgkyiwwSZybW60oovdefg+isguGzThssh4KGesBFCAB0/cOVH4VDpvBuCri9p+NFrMX9u/b2a8EMtN86c/fwwsBWU9KiqaMQBxQS57wfufR6hFz+mY3btbsM0jQ9qgl9hEq8aQIGrSZvukv3/A162CX8XXrbRCmm2oPu1hHb5vQgePzB2IJuc2qXbyNAu+SAApuE3l0kwkpDj24d1HYWNDVewWF48n6axzMtsACTrXaeb1QVTWYLVWMyykKmPYZ8rzyXHsM9SAlN1SdRhPT2rL1d7PSPdyLsK0MU30/OmC5hmMuB35p1q/iMkPw3NZwEWZo0g8YPEL29BPouYGleIavTXdNu9RkGTTOWMMlyfzuKPVfV12EMp/xtvEdHdeVMQgOGoMWfz3Bwm+61Mo1E0SfVvzVw7t4zoR9/Tj6UWydvdE6647IzH3uQzZgbOOqPe3ntsNwV7TgM068b3zdRtkuI8BEadGZI/DrlMQxWf0RHcfAp4hI/vzDIBejQ9hXvJPMQxeRgFsy5uT2M8Cbkg5u0aMZbp77EWugZ5za6QJnK4jW5INMtL+5+sXZ9xpsBUOo04/EvVDZpG+PzOy+zzMzBN4cbspn6aU86NQ3ov3WVtEOuMpmBejqGz5wWE0+cA51SdBZOwXc5f1sXS9S5CcEfnshO1EAsrfInZW5mO9B3Gz0HGOU7jn4/Mm9bT3gySXDiQ3HoZvBYHuRXML6JeM2u7BuGa4oaGWeY9moRnz7x8va6dgCaYkMRctrazn11PfUdr+Pzvmwi7lum7e0NNg93i3OOhbWb6Jiuil936o2kFEwoZqdO+mIlur/0O3bX6fI5wiZmewZoye+yDH/UeMjxlMMuhyAB/95SkYXI6JaNw7IH59GEONmuozvI9oeLpjPE8cuUAfNslEszrjxAWAyBqjfQY/veCxmu4SR/8tJ4iD6X0T39w/qU8rSJZ9fsUfDZj54KDs1gV7BL86ZQS82nSFEl3RHmXaXQHXiPEVjvAdOVEiUw1kGE3a5RLxDzS5nIqRP6RrGyhGOmt4M4ekq+Q4N5xGt4/vhdKV8iyqIu37zNXXbDKnLwDl529hFFXI6ovbaZ8ySVJX+oh+bmLbzse9ZNwfX/0+G0XPydpDZIwaPcuW9ZrD/JSA9xNxw+AKrACCAWsujYTu/6Od7eZxhEvBZ4PvsSodp+bTyZ8th5lJdfxjOLNs/RIlpAQ0ROpyM5JgNY3dnx274Wf7UyvQzlRjEbltrP19gbVR/vrO1tnTdFSdR9SwK3XbT/VFemDsD/SeWr73mUk9ZJv3QfOBggIGSiqnAsJz9eJ5Asr4XU9QmYvUcey5HG4ryEyG4n+tXI2e0CFzWehFLE7gVCulHCnp/djHiOoVb+jBwFC+zEjfOUOoXjtxNQcipqauLaZ33ElCL7z56t9odYyvD/kWy2V4WQm25DTAwE915DNBI1Lb4ZgyyW+o2yqHvVdsgXAmy/FtGB8qbx87dLxvjEvdspr/zjRKf/XewAKsNhXydgirPyX+wJuuuohBIAD0ENf+sN75fybAOALur/hBcd5kfWQ6ZFfQGN4vrIsPixCrFAsV6jvmWeml5gXms3IIeljxSzUI6NKXbnoFYhQkZ+XJ1VW8RSpNH9Azvl9jaqeFG/AFMQIxwBY1gaeaV2GOzdVM671eoJA8Ad1os9UHdGHY7IQaSA+NzAV0oAeTCLiSJ2IGB0NTkfbMlzpT1qd4WB9ILcrtD49h2fnYLCMW0+jE69dCIOsBwOa6LS81BU1Siztfy7j7RTlQgYxHQ2h5JSpEepUMnZdwIhUHzxSDxw17QGH0tEbwsWA2Rb5gE7y/uvOlBBtG5gD2YgdcDaYEYBxEPhGwHYuqkHw6RoEN9buzYOZTw+mIHBzn4JE0GwAlCgBsKR9DoAoYNsB8BMzYgc+ycA2Og+kC3x0JxZYmb10t8ShGuY8EzibL6brUku2finObU9FoD3PuNxBA8JHRQEKvHDjprRHrahTGklR1eLxLGxTWH5+Ss878VMQQF74mpdSn9YwOT9xJrcwP9vmxe3lFsmrwhY81Z95W8XVjSjJ9dToJgRj18XSOfZhHMKN8DpBOjTt+d2xfm66EfccCiLFDF3n8RO7z2E7/xvcG8rL4e7RkXe8bAZfE3gMCFKCu2vyw/dQhrOI7RYw3OYngQFk10qiG5MybM84M8OGjBoLiP2C7pXMnKFnruADavVpS7lTABJ4Qg34VfC473N1nr6vT6swGPO98ZovFoTqp79PZqL9W0UN/JtsydV/0wDQoOLPO7S1gPT9GElOpTz9tALDMeVYHU/ktTeCuaL2s7e5KBUl28XHpgJMFylX7EVa+vNf/GjlzA8Y7J3Pg08wR+XTP950ljb+7Lnn7M8TDu528GVnJSCM4uefn/Pln0GI4lLOQ52dntqVcPIjoCZO2BG29U89gvz8L40o1LaNVPYEhbBvVtVt/yEvTPyQ39adf65jweFLo8hvDK8EwuU5VcFCmOk7w/ktFHU+5/L6g1Fk+UHaZ1afdFfqXBtX0+ydbhvJBuKuPoDQrTC+XadoLvhBf4XphRfthUf5CGVk3fDtXGYXTS1miL7IQG7dddEv4R6wEPeoceg1XZNs/d09rN5XL2ywLi5dAwI+snewZGAst22i++ekX64WZor0+OVB3o5r5wbBqwzxM5n1FHoCy6xMB0s4tauI3+rcDuBihpq3h2k0kzhPZyYxhEAIvqsk6/cS+dYrmiySiInumOvuHz7irhqCD0Q0aVhAzZCdopSMUu3T8BEGMdutAguwjZCCxrFnET8k2WliJZ4i5uG0LQ3x6NnVNV59mSCoJgosVePq0gCGgI9Pi1l9zRo9K6ZJ7kC8cFIKDMXUpCwnsagP8WUsPOXKHfgQQc8e234ZH9+eG2B254Hc9jh/2fZjz1YHXUSZhZratUxRlnXpPtnWJ01ZW7tWk81J3XZ9Khks41w/ltwmuYPcIe4uTFRzjOutD+ijGUlqrm5ng6B1DphJovX+RsiaL+bVQe5YHUhvJFq7br6xBXi7wrQ08t0IPWCdA6S68LP3Hrje2vhcWA9RVA9rJMAHDy7fBHMHugaYhmCg60AObh47+KDzyUUBjlH36HuOqRf0Xrf/ehPdH7GmMT2r13obddme55I4ydKOoa/fw3oUdHe3mrrn684ptpM5PYJZlqLsvlf8VH2V9gjzKPS/8nHvKXxkufReQS/TvZpINoh+uvp2cZeSvc5BnUM9U2rW50+uj3Hw2IeFrGdpkTgIa7GYISyFT9ZorJsxkmBY5+2aXP90rfTQWUrO12rFry1C2El2faqPJ1/x5H+XDznLhWvn+iXveMTdQcvqo5bmYsY66E73hT663XMX6O5xecylhOrUawWKngqgD9VkzhRAJwCJxEKCKFFtxEc/2XFgWS3bXG/747gdM3XDhyT8ODH/IuKVdXc2X0t9t+JQ10dvpppy3llWNzNquXbGqO00QXaEzRct2rJGsCCHE1n/EmMUqdqmtv6JCwS449JfkERO52/diYIamkvU9O8YRMmjigkC6gWrVEuSNFncpzSpk5eS8MHrW+BnSNqmRwdW+cvJuaxMT5z6qfPUtw3j/o+aSIpqLwSg/+GHNd4f47y94l9Fy7kl3Pb6deNmpaolaq/PSkVSw7wrK1Xe3Q2KOuETCZ84VhLkFUGna4mpfHG/4Fu5brG8VDwM6vXdrX5Kkix11QW0x0clEkty6aSal/eJMniF1bDr0UF6v3tq9d3P8vyzd5MkVUDV9OYQSVIVNGSSokoNSgo0MDD+EiHz3vsNYLzgiwUE38N/5IeBb+vR978XOwiVaPgg2f4oQzj5XMbVTS3MxV+fZ+YITe0bt5QrAFUzOz84QLwvzrkB+YeBIJwgyujLSbJymun4hBR8F99+jrZadXuju/z7e2+RvgSdJQmxOi3x771VupfmmO6WXtunBJ/YHkdEozdvqyFhwfXC30G6Rl1A8GxFOMm02kzDPVOfLInYUudU/G6cFGuLxeVoTOhSjsvkat4FVB1fLJl0n8X3dW+uddeMjoKpxa8WKOCrs/XpIUdB2pn2thYmLR6FU54+9Ek3VnYLySBUIU5NJRKb1UttWDT1TwqQ5WeT8AtiASszBwiS+aKHbSkaFoPUnYbeTtGNzoapbEZOWcYJY36DCP4scp0FjblOEnhCHSGJyoTLhmks78Y74P9SHt1BI1tXHJIMC5odofHssgZekDf//bV77sjLQR9QBeXin6g+/Kt60bWJLT/czZtqNMSH1+1CujaTzaqmgiQfH5z8yUjFArwl5D/Yf+Hp1clBg9caxmKhylEy42HDsBqMqRuzgpDcSlyjx23eTFhvdm5Ot0+oIWl0E1gyoOTTQnMrCjvTr8mRmHLeU+s2X6EDo7C2EQSBEDMQUCxL1gaaQod3b1sLfC0KKOUAGC71JeWMLzZeQKK7P9SsuydRiVuF5YUt3IXczYtLxPYiXilUuTFvt0kmOM/tIVXvsXKuZDVgdpF9qVudmnrDc06hSUo3UkmCuZJQo1aqtjP1RXMLhhrL2btuAabrNqt2XqnbrPqJd7mnEO3BqLurO5XcyZ3NLNDiVZeWT8+rnRbm5aEj+50sozH89VEgtfySuTnPaRYrQwBDQ+siLHNjhYHnfar+IVcHurK7q9WdwP/nj+F2PfbnGGuTnsy7dK4n+sSvGG6Kpq8cnX8JuToQveRaMi86e1XepXN0kcrYZU2n9ApqxHzDKLHHDYNaRKxIFW9SKMK8mjC2Z7IG5nAYJ0FzBbtiR5idoDTagMA1l4iTlwCUWXvhMf7Jz/zoXkF8COwygvxN67SA1tIP0PZeEqKw9wAAS7rXPiSCoP621PvgSmP/QQCuurTymaWitmbp1i0AXbJ0eCWmQ3p4XANBbdyvZm8e3VyBdHfOKy5Yc19HzL9j0DCBp2N8nK6nFN3fdYTbc7Z95jFOIsgmwjZlna9umtv+Zi5O6Bzx6aO13eG8FXHSsBB/8np/7Ox70zcwzRk98u+KMF24c304oV9zR5S3AqBtsf3rnapXHT5+e15ttEDgIrv7/Gbe155/kiswLraX2bzf82ff6+xc78/7Hdwx01whCll3DzOmfKUkadEfwAvz9z0jyUDYG2e/DaZr1bSQSsmuZrXqqtw5fpz6r77I1tWreC5ejKG9nmq6qdsAi5gn7GrITX/B4oD8YG7zCRJp2mv3uK6C7Looki0fMS4nUVloFiSce5Ibk8caGsBNDZuSubgqT6ox9ffJDSllWImrjzc0XIfLjyvKPpXcN5qChYbJhobEQOJWLHQ7L9Ic82BcAR8tJsFNicQx/LRzTyLRlFBj8lZV/X1DgzqsKCeSG5LXNzScwFXuU/Bdw0hsxU/GKw10j0BMmlXnG2rMxbMncX9HueV0dl31fvrc3SMt7Hb/vG7TJ2gSc/x6XqJAoDlDCRgACZ9iCQiKC0CyueFdIIkcOxtMLkoSmFQ/OoHvXKcoxx4H/3Q3AdBxVSVncKPqTNG0/GA54YPBlecEl33Mg1cCf0RRwX/MAcz5l3FVvQ5/5tiJN4/hn24iRUVxjilxcCXmdBUSWh9TuRr/OkN5xijhsxdmTxFqYRQhMSdkC+/e8Cdso3UL9/R50k3VvBSze68ELB6cv6ehKxwvpwxL9ZHdfCDi3K16gLt1zwkvPGIMo9hYIPBptX6nnqBxxM0pMAZn6d4XZ/OM6S3TiMYKBuevMEL6FYVjWtA0TQBpBdykKL+GNDK8+savqUvnLC8IPEircQ+n/wP6YxTnwhirF7luKo17+Jk41rNwIhYxvCBp9Lu3JYTc0/8oCP/4dLKYBaCY3LxvCgn/6JyfLBaXFApXJQuFJcXi9+ZdoTh+HL+En07kE8kCgEf3/fEPnAOA/Lik8Kx7Bu75G+55To9OeI8AF+OyXJvXcjbl5zf6bG3FUg86fWJMTatjJ04joepcfDYPJTSKpaF732jco+t7Gt+4F8tFE97enQvONVpA2kT28W6n8BziVnJr2T6889JBi65MxwIp5jeX+BQJ9RdS/QXkAm6TX/T6EMBSG3rqXl3u6pL1e59CWDi9zXUxAu6unwnP5yjtdoT3OobS6NljNz1lQ9/YmA/aT9107FnnDs+rK50+S8mLA/w57muJm+DO4/a9Z/Ymmj+tLnkTcwcs1Rae6+rrJm0q5NwsTsy4UKEmKjS93m+Legqi9afafELATd0kSDm9vS0ong/RyhY3c5Mu2v6tlD71FeGdzWXCt1XjpSN5IdR9GKFge7uWkwQ45aXp0YnYqaWDXc0IDgw0ybGIIMFIX0Y3rKRA8jYhNFbwLSN5m5q7gmmN5mkK0rxNcLANDAZJHqeDGZquyc3eZDgn2Tbnibr8IKMsfzlVbc3fFYmubpeW1+QMuES8+VOQSd9kPyQqj8MPXSjuupqy7Q+gNHzwBmcbk+YxSaEyPvjizoMQXL3LESkE/uODD9RyitTvfTZE99Oek2EW7u2BL+uduSo1Y+Fc+5DrwtIJiyTWmsV4VEja0bpcJNQ0SnfgYP6Baj0SxGd+4c5l66rP0lFZh8tEThn/2d4BJPj0WDTc1HjhCvxVnUe+IGwtQzOkmJ3FrkbENw7gMfQm+89w7Y6LoQHG0NXfsurB/1fbe8BJVpV5w/ecc3PdWLdy6gpdVZ1TdVVN6OnumelJPREGZ5hIzwzDBMlRkNCAKCC4AyiLCNKElWUBBVSMSCMKKIuifvIu/kTHsLvvuosJdX+Gunwn3FtdPUF593s/6Ln33FD33pOe88T/46Vc+z15bCbiXkIb6IODy91ZtL49bkFeNHF9bjCMMAJGQNohymJAE9WFiba815GA+rxei/sxSfMRnQBWNUIxMODNc+ipNJCSV5Emw1lTDfDh64BYet+m1nhIU5VEYKjmWR/x426u8WI9F7zzSM/jXWLfKToqeJLAy2sLVuswSP1bza3vBA30BYpSWTo4SjArjbVX+3qsGZTigtxi7gDx12ZmDoZSQ4O36oTlL/f5LtCYc/FD48eYXwIxiVCAa8LdioWyWPafUPNx+8JNAYo6E+L23pMIxnULhfSlN4ekWEwR09f/3Ah2KxrT5eok6Y/uqF+/7e++pvUoWtD9bTinRqJbHT2ZFTuS9f1xAC7cH9p/Pmpbsfdq6BjwYiMOLjsKIXSSFpCCWV3WYlollwsa51rICjA1sa0YF5NhdIOl6ke+zPNfuNXkLfUGI3hEtQoRHgDId9WzSFDUSKTjwEUIXXxg+aMjqjlZNUIhozrZ9KN+Ca3jItw53H3c637edoLfXi/7WWbIojEwWKsOLARMXU7+RBP5RCTKFJiUAxyDBAZUpAnO6MRksB34KsW/rNG8T7QAmJ6aZbolXRT18QtobF+0CRxUyJclWijTnqT5Pfxuxb8uDHq8ZJ7hhNCQIg8R208zjwZ19TXCic3mniW07DVF2aj+EpIkTTxCCG59cjmED6jqXszjLZggzMwONaEsqH4QwrbJDtHQQDosYX5RgTxcSS5PYHbGiul9I1AQIMn2BN3/p6dsCoHTc6drWSke7i4dHP6lFS+lVpQ7S6YY2JbbpuWkRLg7uaLclnnTjpVTK3qTQ6EUFqB5CQQkRy1uTIccuFrVdXWDoqxKDAbTho0vur/DF9s3pB2HpKPHlzqV1wi9fTb3LOHVv4+/dKOCOvECRz4FjxqQLyzD1cH88V6FVAfT6B24UL0ZL1AFXlA1mG7HK0mnw/NoJWmV5aqipKNaSQDE1QPw/F++GpSz2um5rZpoLri4uxS3fjV8oJxM21JO25bbHhCNhZf0YPb4l8MHO5LpceA4mQ0lxZFxPRBvG6nQUHINbmL8BaucYGYduYRrgXgLXxpIrFSUDbgmPk/8HOYz09wwRYfAc6ybGinp4k1ccfFU8xOalD27OmKOvHQ0YXpfbHE+R89hAe6LpFN4XjclXrXdUzppimqGlDfOEPKymPp+qtAvqYj/Ryzf/eVtlpmHKsMYoh6ZPlpfxhACJF+ju5fKhGVoBB0TfNwI5ttKRoAJ48E5fAIyl9Zi/r7OHSLWmvkSICgNUgtGc9IsBp5IxKYGriAFXhdodHzdN43gIS2VPAXqWDNlEx37da+A7vw+XqQ3qnhYkPHh3gdOf3L5w4qyFx8umFB0oCt41EwgXpD1UHQkp1oCr4AzpVxgOx6VolnqKq9IlmO0j7vCMdzHW3On4z7u6Kbn7Tcz2dLKZHdox2us48jsUZLw+6BQWPYJ1RtlZEYl1OVyQNbtWDSJQEDRYxcYYmB7/nQ88u10snxg+JdmvNR98QK8Gmyl88RJJzsOVt9U08meS7i5uPqfejqNFRzn2F6cOcuXIAotx4QcH3vstCQEyVX9nOLjTMumq9/EvT3vYCkNGcct9LJu725gXpXyN6RfQTt80T0q11cBsKoOulXd0N2fKLVVEK6qgR7cqkA/7kRjPWhPMk0l2ybbfV//Z9Bn4BOYzhJff+ITuR6P9qFoM85EYimAiRKrzPii4Voza9fcMkzSdGFmvkiNu9Ru2yzBu00z+tjF130KLV3UdnZqOGWYKrqjFgyH25PJrwdTqUI4DG9Af3/2+XdAeMf5sb7oadGBxe7DmuNodjh8lxYMasFQCLwM918D0T2XTZzXvXehqIJc+7m374yUIvjvVLZz/3TmByD8wJn7PwBVcfDU4tSeUDzU/GP6R9yPR/G8LnKDLCsQHuXtZZGnK0NFCoWjg8TwxVP0fBLCPVibZ3c6SqJkV7zNfeQjb3MryGQkqbsBXAHImRWQnnCzLXo3MK1AURA//EkIP3kHJoJyACETIZ6euB3xQAb837do1byxxr5xAc3++g6/sxwaDFNTcD/wswAUT6R8fkd1WDr64+uu+zGJwGJ7d6qlThNegqN3UDUJgGs/CuFd1/E82X/0WuH+lsq6Xp7zOTpF7Moyll6XUd8BLwn9yY3LZED2AykSDhmQeDwNs3XaS+ICfpQolbAMJZ3AzJz/MjEzx4kOoFy1nWLfcF+wVAr2JYqZG8lC2gG+UKqUitUi+IBnbbaqx1ibP0swLDqG0/lEX9FxnPJZHUHHuZHAGXbMq88ibge1BLwjq3OZwAQca3VGFHSbUF0xRPzIR2F1uFz32Jt6bRiJ3oxEs3NGaGL5bTFCi4EWI7TDQ2eeyf3nmEbemCkmWCMM4wrZ1TJthw7l+85wqYQbYvZ/mjAJbFTVGx0n2HFWGbdTsS+RTw93EHano0ONu/87SBt6zt/uOdx0MZqzxsOd8QWxCklOXomMAZrgjdkouwFLqZQmuHqeQYSY52sUY5Q9AFLtbrWr8QbbF3RFNQPXg5+RHG9xx9Gzpo0mhcCDJCTt7osUVeSRpBGY0fqDREF+L/uZu6+8AMyotgCMT4Ojdjpom+6DZLUlHhRLFvEk49p2AU8fwVDPAYNlsKuj7vvMszotouvvyWqFO98L2mwGTkk5qQuIBRPkw1IVC43/V+p9B+LFcd0hcGtk6z6IAA8R7sNNOjznf94kSyDA3Mu99JH7NAfQ6MGLdmkm+Mf/s7YisdS2j51b8OGUhIyfg5zGTwksCWfBofHeRWZKx1w3PWK3SmAWQvenBCMVf3Ge7t2nDRt/ZY5s7yfIegbAvJNtNPQQsnSACDtV7chmYa0DEisLKdBop7fxsG5gZiyL9yQIqtFuJUIgTSKi8GqdAlYSH5HIqZmOGvSxCVkOJhaXuMbzpZsXkxhtKTstNtOi7zOFZbpc9WS4AMj358yVWwO6c60HuImpHfO4wMVXmp7k4F6WmwuzlI3xoM4Sd3W0oD732Yw7hbOeq737SbYHHiCTn7536ZwvuW1SToNaVVsxpBs5qmI4OnNsyjGymVsHnkfLqS+Z53ledmg0TYBC2UUdqYXvoMlCjkdxFCgyS5PEomDttPDq34hSLC7+8GUsDcvCT04Jv2sBw0isvSty8X5n22J61PgwwzykuIjgN6l+yxSbh1mwoPcIeFGLa5Lm7gX3akQCdhf+/cBiwDAeF/a/8Up1GaAgi+5PfUhH8ut4pM0K+kecZ49/zsv7yWI1Jrkt3HmE//I6kFi/HLZjp5ymaowMGF9dVhsuA1/UxQuE0OKxLswfVASCNwPqoBJmWLyAPpWOCqqa69WZgi74OV3dTNZGvMmSZeAMsml8j+VUjTsKfI2oCHWiLfzLU9QBhQCswt6ndNW9k6Cwgr03uP9EINTBGQoWXTx/PLxpzOJ76Q+MIPizupk8DW9C7uVk5TyDLAvgu0T4o7lV/52NKE+emVHce5mBZNv73XvwL1VwjqJ/2gjO6RPhPzHbgEmUKZJnDqrX6tUo3dkl1G9b3wI5y502DDAtByULfItuAXxAVm+5wAmq7p/VvOL+SUCqc+GtZAtVp/n8/yCIDwpZsW3ipELNDYMuZ2UBsCRbhpwJPgYmlGCw8Z6gygtgQs0zvhPOwmna1/Ozu+bmZXedMuZBLEz7EZ0tjoy0zNbKH6IHUBu1VTQzQEbDYoQGswCqZWwyfTe4f8xszrhf6MwAfvLi941s7Qd5wzQbTzJeDkvXXDLpzpZGqkf27QJLhkCnUewsupd6WSh9+8IDmDaTnJ9lQp2LTS18k1UriKV6dS7RaYgqPRzR/7I6hbwBZMCWwHL2ahaqEtz4vosnEWjrBKsym9NAwt9muD/qP32HpbpfaLcB6t78vtJ4fxJIquL+Ea8Z7LuuIYM1GXR/B3bvu7W6uAzGE4m3OaO9q6i7rw8uwWRbcWfz7YVbNw3B3oEE0NQ2FdCYccZn/wzOUl/a02je8GO1l03Fom/vwlzbvEQ8fT5ALFUFZ3xM2JCndCSW52LN5/UoqT9B9P5QDZ5TGQNM+wiWVCd2BT2MOeeKzZuvcFDY0E1o73Y/BbetWEFSeZDt1erIQCKFy2SFxgtzR14zeEOrTqhEYWlajSRv6G1lNNxp2o6+YgtMxvpGVe/B6kRVM0A6fWCM6S7HDqST562hofanEFDaU/ALUdhcc96Pmu+D224bmIzElpZX7YIkwH9hT7kqo4iuWUBd3KdhKTN0uxER5Gq5ZyFZ3cHONeWlscjkAH1q32LVZmPobeqf5mOlcPOGf6X1oH7yWTLNhsxbbPcdtmt4c6bVy4yUiWmelGe8ELOWlHyszNacN9BPUIEzMPUgeRREjrDaEc5zisKyV63d89toAbL2/AznGHE4+ln3qZAkhcCGzz75Js+/+eTl7q/WrgX25XeSxO8FNa4ePkg9JA8S7dch6u94+LCC8lH3sXY5ohTcx6L4V0++2eACf9iz5w8B/qU773wJ/ErBvyBEEf8uHlIOUr/Kw4eUBOflgZ3GcsYklTGYqrEP+LD6tAiJHhwzEyEKlb6YJd8mvjUl4i3HNJZ09DKYCaI9/r2EKSFJcrHyc6bsWApAYk5NWaUzwraMJH4AAXHHOlkGxKEVIahYOTOQlGO8vOoDCKrBkFRdyF8OPy8ixVYzi2IH7lUEoNiK9osLQkUtYgICobP/Eh6dfl8fHzRkUS/ofG82kNJlXuu4ttb7vjVKkHjQVa5Y/cpLnp3h8+ghNMV9gNB3plONYhpfMmA0Inm2tJYQYprwtuRhGmLSH4oQRjtSpz5EGejNa/yb2rzfhjz4eO9yOBQm/6JhPKnDWCJrA0PhSoSJn/A1NSRLEq/wqz4WkCwdC1XvV6JyUIkDlHbsjBx7962CxMu6IAkaunkyJMdNR0W6GjIfUTsPtSPVtkQnBLsnoHpLfPd5ePkwAaplU90izYSFCtFk1do6MIyILhiz6BA4gvDe6wX0D/BpvLZJYbxkfvgAgLxqSdc+XeqSJSjE2le0ty1vv/CpdRDIghaX+A23bmhb2JZK48erFuKNbz4Ynb5c1gResHtjlbvedfOha/+8gQd4kVu2q5xb06uFEAzqbQtSuS0Lt/zuEGHjdQjYNwCI5QTAL//UgX/4d9+f63kazz3QihFBoX5z86AOfGwDAj3pwTOJKNvwnZBaVrkmqLv7Od1RwAPU8WO3Ou7zo9Tx3jNUevwsSWFOeI2PU5s+gfc9Bg+68FdwclujB04KyNyi/pgHDv2Xb7SgMcNEqybnWB/m3r/iw+zl3aL8HPVIIXzeSb2Xw0Rav5FZQXWRZKuZOXkiT/fLKlA+eBP1Zp1R8RjiH1ATrXlq4qTvCEp0gaqBCUXzDJqUsDlEkMhVm9hRnniB6u5PPJQRZw56ZAwzeDSUlMJzBMHvQc7DGAmpLzeorzWsEPAR9/uYG5z2RRAPHIjhit+PaVkIy3+clzRCQiLNVFakvh3MqWeYhBFEQujOPxAHmqoElyBN0REP2lUR/FBxNUxpnyaoyU+rcMVvFcXtlBT3s5YuA7AUieCXasLNqcqjCpbhlMcIGfXe/QB9d3b+uyveu0tNuu+AKLrrv5WkQl49ijRV4xEoKhJ+NXDt9xKe9oLvVMAnv9HycltTwFIo4XfH3XHK7J7XD2zwha/78Qn+WD3pSJ0/Ok82IhsPzxuEIq3XjOf324fljM3cTualfqKgZeCHu3vpqr34Vydn50jKVpbOPRJ2cg4hkoyhQczRsU7M49V6LhpqAZ+Y27hPjbNZnmXLLvEaPJdAvMAsKEeBYVs6TDYmkwBpVtBIFbCs1ZGBX4wXwfLGWigC+BUAp+dF19BgVJ9ykOJRdwqYPSUswdiQN90K+DamyTaWbHryjZ+194PO3ghQJUMzm74pX/V8z7M0j+027hCT0E8iZ3uKGTSHDkRgOhnAUzjkK+zKVBL1PctbFHmYeZxPE0uoYFfgJ92HCBpiXwHspHtbld2HZFWVwU5ZnW36N38qk6IxILn2QkG1FTkgSpkMbMgJzHQliRU/jVcZGa+2+QIABfenLZAvfbOzKItf0DiTvQjeU+hrOOQV8B6ybTzAHEZBoWd7J1UcDpxbHb+iZgpyPNB3CKjUuaRze0/9UF8gLgtm7Yrx6rkfbxVL3HEw7clI04BgMc3LCY+mGsuJvAif0SkAnGedQtT+QHAlI15Em+T7gMwlrxouiShfEHkpyEVrnFNqRyUOsSkBOfjSf9CsVVc383YBgOnRK4Kwzf2OZYHBnTtBzTbcH14w4v7K4l/+0JFvCbb7nzD5X4eJlHodW1bxusaTfAogpU1tc/+Xe5GsgNtA+2l7/vJKAOzF3Oz6RHJ92v2V+3F/zduLx007y7gleUp3JjkQ9VSGGu0R1c3jXgY5u4/C/hjNmFp0imXBBZ2diwAvbKsv2C0qAZDKThY71zmTQ/XVyHCPujNEENftoA7uI9a/v8gKjEIYwytwBsI04rFgNGU7RhjASCAJYRZzE2Am2GCE12hwVI5v5uLB3/xj/M2Lj/GdyJeOyYRbbs2Ni4e044cQ1+rOKEA/ohoNAPpLhcl4bHN/vgOA1dXaKgg685UNTn5jG+a42D3ZRATq8HMvvfg5zH2GEm1wKcs00bFYWbEPXj9tLinXiA2rVl5i3ngxQPtGlMUd7JZsxXCXWYq0hOdHRcUGz5gVL//lUZTBdZjgTuV20Jl6XF2qfXMIaeU+MO/M/LqUmeyaZ7BDRHLrIg7Kgm/l8gDOCXEbcoLkZ+jHgOXu6C/l18Zjpw7kO2nlcd2HNgbzm9pKA+yGbDICdPj5F/2q35fsTOI/94ZCtQDa4khE8Tb3W3jOdSYS8PuJh26//aGEmdnQu2f/wf0dkxk4Tpp4rL9zkTqxD10/bS0pV4k1r9oxYt14MUR7R9TFHeyWbMV0l9uKOIpbSlIs8BVzyEthcVGyUEjG8gjlY4yANF40ypD4JfX1TgCguf8F4KpBP2bhLtSN+YACV6OYKBTpwM9URcKV/DyqwHeuzuGmIZmUPUsmhRkWjj+FrtPlaX56KnjGNJqWdZsf6Yabu0b4xiPw4Prg+oPQnQ4H45qiOZoaiIG7grGAisuKFofdsq5MXYPQNVOKpjT+u4v4Z3fB8oYDJEuq8p8gFgyEhJ1qIKDuFEKBYAwcd4bz8ivPoiU0x+4gW1kJxt7xpqTWSO96K84W4cG2n3YacgKIl1RtLkTxiufJPCOb/hZSi5ZQE8mi4eDSnBKU5DlzUXk+wgb7NpYnMEmRJ3PzGSyp5Ysk6tVeP3ayev5V+Oun3+ZoJhS8dW7NkiLeOK+A9mQF5cvz0lZfE+YDUJfACzx8hiWoNTH9vpelmV1OcM9QzGjmq55zxpJMbw76uep78Ir5rpPztIiBzBM0ajwiGCatWGZw9OxkpHmSpoX3QKvZuZPyvmfqjtrV09NFyPdwrTasnE0Q6hOpVJJoGwulYkE5h4J5hYBDwKsC4Wg0rCzFD3m2wfONZ33u+F8E4V9ImvsbCJz1gQsPdJJlZOiaW68eUpZivjG5auOqJI0GK+4+uKtdPZXgeVl9FsGxku2+4T5b8vn752g89nISvVb04XUIofHzc5bz3okci0OggzBaYRqiSLRcGoXUtyhKaZVE+9sDVZmLB+kDLAkJ23suUJ6dEz3W/b86nVxAEUQUMQpnLorWEoVV7amoaZptT5xFgJxUd+s9r/IK7NtUjlScsSqviKmumCSH9ixs7+Bf7aEKWaaWdZJeYiu6rUzSjFfriLJ13ceDp6nQtIy0IWccI6IOpToWgZBWG9jyGYN4gKoW/AT/6j1dHWC8JzagREU11NsZxXMr0nfh2D2vukTJnCUblo2LrFBZspkkapDJSdKBk9w8uanCXcbdTen8/Oxh0UrY3zPdOHWqJPgsbE9QtvBKNLeSRcmiXC612Fxbu0r0u0qc31VSTiJ0kIxOr78yoE69qSkEGKGE8C4loa4j0QnKGhpnND5XuaWktJRuK2sV4gdb3tI/BHAT3fsqZjtCSQuzH49de+2jPBjsB7mhQEsnLozhTlxZTEVMw27/xkHwI9yJVXcZ2PBYVgJAhHYtnhnLj19QzgadoBYIl6XIA6fAOxWgsiRla5qNzQw6zcZejWWfoGTlK9Mr7v02z3/73lhMN1HIcELXPobw14xf0IN0CyLL0jO63BYEZlitJDsWkUzgw707vyiznr47m5UeWBsi4cyVRG6REbMAhHzeiA9qQBjNvdv3p38W89icZ+GgyOGewYJB488TN4u+KYyQwFZS0kQOrzHkcKQSedL9V8UJWOjPvvw5Pxh243zEcNPK980AnkKGAwqIB9IW0NQ/Ee3Cy43v0p8NvOrZt4wTQYZr+wlkuEzp9o/gn7gRbhN3kJvm7uUe4Z7ivki0hhkquDN9Esv4RgaUn0iB+k6x9Bv9JL6G5nukHgu4alFRIt6g0Vp1TndXpDBVVJtMlFnDXl6A4aIH7uLj+zPaUSt5CQIIsIuXHoC8uhKhLz7GGaSM2zIv1stUHSbCLRIIxaSumNhmzk8P4KdhkICqRmXVkWxhSkEhU9LhqpVCQDKxSPUwyCtWKCilAabLJGNAvujqWALv6+/rEbBwLKhqrlhqV+CiVE5NmkBJxQYqpm1E5ViMX9goVuoLqiUVhWJqLLZofHzRAI+fG1CQGoNB1o2jpwBwyijuOiAckMzlh40gKYNNBPBfGc5uSunz0wZcZgdhyMafIAHFwPLZpXoqxNIHhFK6uHoMES+XsdVjF/XRjJ+du55QlL7zLj+vT8D/qTG1ePqe09vV+L58jCJzRFOLPrS2e2NJS9iVsxdsfajxnyO3zdy+uETROkLxiU98/uGJAR4CQ03KzpHm9y455Uegp2CqZ6HKYYHk1PSwambRz/GcGMGr5zncB7h/5L7MNJonHBh0jvzVUTXqB6c0E6lS5iZaH64V6XA5fhQJzYCW1pEUweODMXEsk4SvFg2TcURVp2QYtei//egpuFKNSaojW8cPjU4pFJM7Y0LWKDrleCIeL4fwsJJiU/iYDCsL/DiW7O0kaZalfPtCGWqqkpUbn8WjBfdLz2DLAIIvCBIfiY7UySCykZqKDlRATnMafdUFC6oO5vuQgns8FhtZioeQrtARFIUfw+duJqu7Oi5ogqHMKECfNyq2b6ejooK/AaqV3KaUpkMxk81mRKhrqU25S+lY0uLVzq0DZMCQBBilXZWdG9SELIKsIH5+kIyVkNpz3nsv6KEDKK62b/+IoAgDB6vbHpzIxfBvkjIdRFvWC4HDi2/bsOn2xaGAhG80kByKrXxk9048gvAYISMIr4fqTZ0kyew4ftaMGpvDDn226U9QP0ZPRPP2hA2SZLbYUhbo1ssvMsQ8zsHbLbzVLqJfAHTjHp0rg4e6Lr3xki4ZReJKdsfhnTk5EUbzs5U9hQWN0Hg4mQyPN0tfrS1aWA0kLIR5tN6uALISx377AJbeDs7/dkA8BUmYxFCEOE1SxgdfjjJUGOrgni+dqCL1ubsosh/zOWRPinpPmldZd7kipfK48xXQEZdkWYoVBQK2Kcl8ISYp4OcnqLRI7lFEhC/Tm9gTdLyclxOiosxvkwdEtZAWVVVM5SC5B+ZjWGpQJT6RBcp/Htc4/zLvuqEU0vT59LdNneQVWEaYpAi6wx7oKEkHU6ZKBSww0H7GU5ldy7DQAf/YBCGKeuDRiyhz1RwlVIXA6I6RQGM8gyMU9g1dCrLdOVzXAGAFTYG0AIAcwMVcdxaTzUSxaDqFcnJiIJMLX7hm88e6M9YX3y8oiA+A6DLMv1ynYLK9TFXA2D33JpLJxHdUFYSJYTaRuGpwbQDP07WHJsmFjZ/YoCqqump6VTQaifzkssPl0TYNgEUd+1eW+traweKJ2nuUZyUgGucE5a8EVP0cj34yfOwgF+bKHtKyhxnXqrcbLkcpujJT24WJgpPYDlscIk6GCI4umxU00cdXppjIsxddNNXwAnyDxkyw8VsWBEx03BtOAqgMt87yQqv7C6efdRGBxSBZ0KnKzAhCbp5U2JJXTvCwMcbxbK9j6WIHzRpC8pP4Iea4t325nAYmOZUW+IA5MIGKY4C5WhO5hNBv8gRK1Ydqx6Q+o4sPCxmsUL0IuhVzfrdKl51ubbtMOoKlLUdc1ge39i0TL288Fkkj5xxi7t2y3BrfCoNp+xwLpd0pJlcSb7IvdxMlBE0kmj8/FNfC2kW6A8bN88/HMyoZUm0hgRfchBSUQkkgwXHdYTZp22y82b8EgCX9vfg28Osp8sQjk3sg3DN5BylPuU4kAbNMcJ1NI5TG93bnz44DVTvfKKT6l9xyzjmyLYXiohRR1YgkYgnP8PVhb6D3IYHEdxYx51kmPJFA5ogYtkuFPFXkURsitR0uAbWyXTzuArqGeNKGdTdrGJj5zZRzSwbtYEDWVwxksz0jAZJWZ6atnB2dOzuy9CoI4BQSdBNugmGD5wX3VDOUj8SrifZu1aznJUXQdfDHFW547nToVEZD38CypsBpXIJmGeGKzSQv9VodVIt21KsIHhjhE9eiCmhUM4tpFuEhWfK/zNTdq8DMuFchXRYz8z6cVQdymtrIErsKPKo6/yDL7PsEEV6prHDbR+ESr2aq+5dXj6/Wv7nvVeAsEbQb43jr5YJ4Cv6cUziHI+hXi6j2ifpGhnPhnAfAWm1FCivUW0IgCwwfjIx3fICebIs2VFxjtPcvHwepMHTD6cb3/0UzTY1u6u5vyA6YAdMMvFIj5VrAsgLw8WgbAG3Rs2vu2nA6HT7fwqJz1DrHjJoAmKTM9s24Rfg18D3cD5hrIwKLp6uGs7zs3iXL4qcFjf+MCF6WLem7PP9dqfmbt6lenMVfRMjEhV9h98oyOIj/dXxXEL4rkXfNx19tO2atZ27PhFdkOQpD5nykI+qEfB9PjLbSDBFwbpoPnvoM8Vye4XmoONLHyb03MnvI79AtSKNx4DmuyC3FK/UO7vDx9hDJV5EW/AI1DxXywzSdVagbSJU65WULJFwGVurRgkDsQxWS/KKK7yrQGBJMoEjONEJlCDwYXrTQhsaZCWK+SMB76H4C91TENE8LkD4wb2lcCm9u/LcQM+PvkVBKhO9GgqkKfeadjuwgdMrB+DnAiI/EgpOID8l8WymkCMPbwhnVDKa1WEBfUsTrtYaf3vqWlayD2R9+geNeaEbL5WBI04CR+PVbaSxua7/5wHnDXdvw4oREzZrhwdnjsfh7CuGEIE7sNoyUH1sAX4NbOY6OjDLxHxki7HYpD+Gdo6NLH1k2OrrsEXDdnV5p6SjXjEmYhbNcCre577lWbm3ypu9aMwYafcqyziNLy1FvaSHov+dT/wHQWOqF3l8pKyu62HV/LSsvE3g1CGadTzeDtFHz/UNcjWJ6l0xIs5SFJXue4Yt6qp7os1C5StxzyQ15ET1hWTMIQeIs0IpbRcrHf+zY1FSjGQiLP3gK0xiBpDTzMK5mm8g8x9Qg6J618I2F5WbajGbM1oyHQjg3aitsiRvyEhqyMTzPV7RVg3l2gwBEg/7Ci4lOdRFvhyx+kdoZf7F9AICBxoOtvqHntWTzhveB/nZ3dXs/SMVuIzro22IpfAZ8vr3fvc7PBd7fkhecOIGKLd+8ENO+5V68x1/9ckQYXurXQhUoUqHFMjmXZ7rYLP31Gpma8mJAWKQNjAxoiwT9RTmgyvB1RfvUJtA70dc30es+Tkq9+O+vVLHxeyUAArelcrnUbQGgyeDzshZQvpQnP+vNsx3XyruZdLT30TqfzN7K6lT24SeaBQKy0zQs+qFIc64kXg6Lf8S82H10DO0xgg+Eif0l+aUQ3YGvuVQnBp7VHSfNzqHsMY7K7hS+mAwG38LiUCrFxCKyd3OA+RyCa1LErpI6zs/jqr/i50HMVLj3ylIGYpkbc+KoH2LBHRJvg0IVz6ayAUmPlqO1yiisV8IF0Q9arRbClWqhRijmGJ6bleoY5uUr9RqT3Yew9H5ypXmA1yUeyWYIybFsWMHcvBlUSCguQHxmwYA9aPMXVYYyC865cJGVqMZ10w4PLUiLQjEjK44sKHBqijcjlpbKicIK09Q1LRA3HRERfyB4cs+TNB5LUG3D0jsinJIQactbbbqsmJkED2G7Isir7aiJeFChYUgVWUEQX+BB19FbJEHA4jx4C7g0IkkiNmhBCRYMz7f+bdzegMbuq5h3yHlSwAnGP8hFaTRFlEEwSX5mLKJGZ9ZaNs9w24uI4YhQDSV81R/47qeaU+AWFy4HX1LUugL63MgiQXtJ1jRJqQbDYKDwEplfgtYf+jPRlmMOiTgo3zFvEoD+cU1xt1WtEJ42A+5VR7QAmSz6UKAYdVcX6NTShF4TPE+U4Y1xsm3lBcokLoZw6Z5Vs8BQQUNU3A8z6a7CsuMOlwSqS8xL1Qg9LldoZoOhepi5oUbRdCYPLz29e236c+n1PadfCvMZeqJnffoz3gl3yCJ3FIvd+MjaY7ccgNKT6XW9uASyqWN/5j/nG+zWKfaQcs+2S6C1ix348Yd+vZgc927usndeP+T74ZLIw5ZyKzZs/a+3QVvrOvAO2uOTnlaZbN1dvoq4eYopja8/aZvNttz7TtoP/K5FES20lBuw5WD05K083nLbXI5h4OmQllKckqjXRMRZlOYpZU0EWuZCkUYVEuoEmhGINPxwiMyaufhEKUrU9MQxVvIjE8uDNbhrVIDbJ6LhJenObvxPZIfuPQTvEB5ViH/fOTTasG9dX9dEnMUkAoFEJFbPGhiaBLf5IYuH9wxNbpy7NcaiFcFHFjvOxHYoLRbKL+N/aXYIo3OTqJPGIO6Z6C3tqvmxisYdj8N4dLANZP1ARtA30EaCFBG9scpiGBl9Z+2W4BbQ6F9cdVJzgsoyPK9VosVjGiMaam0K1Cp+lUgFD++dCUfxxwqfn6s5enauvh+P+Fe9yk5TEcyJUxEMTOE6gP6PSrhBneATpZ3NygXn6nQXuRoKPolrxCOIa+TeNE8M83inn8CjXIjGoGCZGFPMkMgQtOgMGcKdSq1nQ7hW+J9foROFptaHw/VaZDjKGql1gq0JjXRqylmarZ0l6wB0joQi97TD5ZXOtmxHKhYPet15XHwqzU4LSHNtPfWRFse3HzodbXY0cEDXD0iJYFuHE7mo3FeZALGY1t7J6ho8PkaV50lYFACk6bL3z3fZpHPkKI2/ZdzJDhKRUyxhrmewPFyt53G12+sRnirEqarN8/zBK3SE9zIzt9a5bAAWGwGaEUk0pQF1tyZsNl7x21geaAbHj2+CHKk6T91taVgu4FaQFZQG6fiRuauAcLse5k29vXiC2FzBCMtntYHFPV2Zts6exSAYOCiHt9gRoJNE9NFcIKIklWggCz/5YdVEKCBLd5A2+jBuLKhg5kgXWLwuEm6/OwzNZBiKsyeI3HWhrhzIkReAVArg1yVz2iFF/xWI5Iwzz1Q0Bb8RvwqoEdXTu9wNL0FnkRgaP5jNi1XkBpdBuGyQbtF+sGywkRlcBvAe/nRwWTO+h8QOJPH8Y61LNZ1zsWBEOdAHphkHUlhUQLedzTJBpguF9IOvg2nGmJAjdh5v8W38676O+scUtzCF5/i7KHo5lurJgwdx59SJZqXsOUoSv39hkGhfPZ9d2smVKM0PROI0yU+GSbpn8mlYzK0MEk0cdactm9QmPUjkq6jEmA/PYo0FxWt09ZskgtYgUwHvf0K64q5v4YluGMFvkCn79SN60DZ+BKEeBMHGBD36MaBH9BYs2fee6BHE/xccpT8nZ70HpOhDjwaNv6c30Jcn534Ijs4/Zt+SUN8+4WNaZFmTeDsfx9c3ZUkgkrlgACrYEMw2LGmiHY3J7oUALOyGT7N9Y9IKhy34uPvPgYz+ezVhQ/W3ZqncjiXkMJzFN7hd7EbwWvfCxv1hC7xmhd3/jQWQb8skxcgrpmkaII55mLBvi57xMIR8rfE7xBGaSwCTF1vz5c5L94PmQQsZhqjbMP7opeJlDx4DLfQl25whCswZzXl2zm/HNhtUSjZt5yRIQw9d3kQakq7+uknUnvbZdjoYTNvTbNfUG8+gCbzCt3E9mF/cfHK9MaiwAmrNtiAQFllsSdhQr1ECRXwfWjLxoZuBox2Wbt4fOvOD0mGiFuaX9sHT+paJ7pbQmrMkd1o661b6kQ44sl0I8aZ6/rgYjSvCVmhJjnr+ciGmG8oI09/C5VTvy19D9L6/HiTIA4PwVRp65D5gm+OkfcY159xZPBPuuFVT1Jj+jKQgYNx5RJN5FJ2mN5BN095EYm+J19cGYm+isQmYbPjZBvwWmPP7imLhbF5iWc/0xBJl0Xo3FesiOkH7UFuthHobj/cvE3FzaPSbphyUicDNkTSK7CPH07ilIvz4H5n9AHc2yaZ6cF1o3UESVoVuORA6dDOy/8HCjUWgpPityJRCyvnLxVhMEbar5jhY0g8juoM73LUimOYS3ThpQ9pscC8eBfjSdNDwVOVHyHuO7H8/hO/ff0Rz3C+z9gtEW9pPjeqzeAzSsTODptAezB92cTVuGW47DvjCK54pRRJJOVyulWi2tDTwfOkyXlIC1JLAsTWZYytDDqZbkIXBoc0CULSvu8skHaoA7uobBvwLd975Aj/2HBsX7lFPv98Cbwta4Y5fPSKqdxskYZ4gG3fzkvCJvitX4gfgx2x6P/5mXPtnSLs/47W3beLZOIdtS2XJe9BeXOcM5oi7m3G4HKj7PkAnqGsNi/DlakUQpWolTDK0E+iNMjiJ2D/Pif/NzRDkYo0vCJowr8ZwLLs+su9tbtno0diA+9IUlNFmkGWEgGwfupx9M8tEzJK70BaA4hFB4u+OqqDZBAPvXe01wU0/uF7/t1kQ/8Ergjz7ByTAI40B74FkC944GS62xwthev41zAsRH/luikdyPD4omzCkq6lkLbR4T4KTJo7b11hC0ASqXfB5um/U5voJ7mrQjoJkPrvfwXOGThzTBtkWcgmPgnqHy3lP4TrqDzT72hszIMto5Hns0McVm4KNZu7pudoM1Sr1KJMsvaXK/9byScqIcxHTFwkKfPPXgijQb7nZpR8PKDE6SRk2CCzD9fh+dMcdkFbgqq6qy7MPnZ63a/pRse/uob2w6eRfHhKJFFegeUTLTcXgSUyd88yeJ66Pamh/wGVVfEs1CcIDXqCm/8dVbLRrNroTAf5OZGvwKtJju05caWne2Oufy6j7t6IzgJfd3kPIiBAlWrG1ynMG4EqrBfi4IalikrqEjsPnTbsx1aQifVddBdMtA/HvvNFJDlsG7nHjs3E/vUZ/iMkao0j4qc9cNevRuHk77q/bgATiLVQule0aQTIWiKF2nvqPpmbH/UasSVJQwl8KxPm+CsV7iQYQs5bjjioIDyuOPLd2knc63iwh8erzXyQJohOunyyIDszMf60ivc2JkQf3nUQ3OXvMNyD8WeV/3ucuP5la0Y9du5/myF7FLGrHZf+Aw5VaSWIgKa3jw0+6fqyNBQ+AO2fUWEg95L5C+7JySA2m5BmAJEBNz42jtPsYTSFj+6jXtVm8twH+cSimHsbXDpOnPAigHP2Vx5LAOe5knP2oMc32+Jynz3wOXYuGOYsb4VbgVXcrd4C7guPqJKhILLDYc6KxKkpiqRoRJVyF+uBQlXpkUY41UqsTrOoxWKvWSHogUSr6CtB6s+BHPBBnMMzPkTh9ql8rknBnYvUnQn0QP7RQOoqlzz4e7ajVh5bnY6VesD5b7rGDWmnzZAEBE4l4JVu/OBJIy0SShgLmfRZVVqxzd4NUTzrdkzoDRAuxWCF6kxky8Z/7dKk9vkVXohAsaV9XevA1eHnZ/VzU0pJKaoGlVF8LC5qmTilFudjBx1L5CwYcMHxGpR2gylJNxRwS0GNyMNa1AEvEMQJrCKGJQHVs443V9394TwS/MZ2+Jxgl79ytkhfiGbb0koEeB3YsAuATeOn4wdu97oq26KKgaOtp5yxwV49p817bE7lgOeYUN3HbuL3c33F3co80Y6nDIampgxQl6kDBYgb8pvfihYn75SjwYZ0gg3jyAk9oMLLjW8jqTB+QxzwijR3DtzDQAC9XcChSxcdV0csbzDD1amUFX6yWiUaBvZzFNePhgbbLyRAvLUortrJsG+TRwlEsuTla35ZOieR3gpjpGu5wHC0ix2Iw1xjRJMlx+OduCxbjUcHU7e7QY//Gh2OCJfNStxdudgbzRHkFdMqBcpfpXoHHMw9RbqUmqiJCuoPJjSTH13e1LUmUZZi5Gky5f+DhJpRZlcHT35JEAUGx09gGNHiRyptJ9dT3rB6FAG5arEaUOAEZAjAcQQFBMgTHyfYAIPKy1rNnYOUV7rQcU0uTpduf4zGdM+NRAiUk/Ovj/Vt7JQXTFRbgcqXnq3sq7A11xgTHtfBA1JIaFCDs7M+VA/hXvKVi+Vy1hWAWJFLDhxX1bPC8q/IADryZBYagCLGiIkUsLGUOhEcoXXkCPYT2cinuFIYHQZBrpULJQ2kmTjN8PdJe94zg3cBPfnpM3gZ/P99RPwyfyAwCkMseGFsysjubA7A/9e5p3D/ZCy8EOR2870uURDBCgbn4Fj/88W2EvGzaRPwMp+DyzCl91VxGJxXOLRzYlF744kUvbVWUrS9d8vVXsASQxf+Wk6csx0/J4n/OFvLD9euJsX+n3vRjQDPwDUznuSIoFQYG81IAEKRqTCZHwTC6050J5fOhzzyiAhMLaGRZmoHZdNp9033LzJM1Kt+0X99PdYdRGsVCIJiSxHtoYJCCgEiiAnJoqjFO5Bv18U/LKTMfAlMqHvfuFe4MnMUXMLeiA8e005jK5s08PKNx1Fv/Pofp4kHMkV9zov7wkNTKdEZGKzStoIfBQedORCyLhOzVqUu0AcKs9/DCwFB0vIUj7KemOWEWvaq/h0dS/ZD03vjIol249/gNmQBRSnQOAKCg4Jr+5ZXTggIPBnskAfHmrtt5+NBXvvIQXDpCnYxXrtdBjnX09R8jHYF7E68U21p7GffbeMsYgJNp3NH5jA6hmsktXHYgqqBQUiqua4s50BABkKyAEkmvbRctyQzkH7/64n0A7Lt4zY016hg9NnM6GxWXfm0fiUlev5441W51vEHBRog3XPSmHXMar/1Brr3Ja5HIViFSLxJxiISqS0KRJBNt+tkJ72QmEM/NlogVZq01A+BMUQQ7Ayao9Wim+wn3E2bUN+G6R1uiKQiq1LygFODE1DXraVQKIsgAU5oNxyH+s7Wpzpr7AwWNjpKUolkPX+Co+SsPR8hraHYwqcYcmQRMJNQ1jPdx8VrAUbtinsgH9YJPtp08hXGv1yo854QkCpWBpWHIgeYFqu1nF6p94C3fargzX9BN5OhtbXit3CpBYCcdTKsk933uz9oLuiGEjHTmxs9i4cxIhpHIv76JGQvhhxByEjbmQNxHXLcjbDjI1i0nC9LgSvffJRROGFBAn70xndbDgmHkC4Q3YvP6ecq/cSzIJRTxZgP1nEWzgR/cZUUdoz68ZCoR4UE4HQHPffR1DYQzYcBHEmeMVuqGE23Gjd6DnzWOD2oEnFCiXqZ0ZYvmyRysG17YKIldyhOFHqyHIwaM5mp9kHrl0sAHdHo0zq/cgieAdmEObFnJx2PiqJ346EqYAWAJZi/xBvHlaCi5fwyIGSSDc2WUEQFqgys/mrCXwIerUzxf5Q1JMvCOn6pecsUFmxHoKsOwHqxAYciIOIXsojbjEkCSYdDNC+UugDZfcMUlTb0Y4WtjmL/awX2Ee4K1T95D/mDWReplgdfbEMsWJtLQXkxAqEPxKOoHc4s/8cWnfBk5ifueNBAu4CbA1Akxu4AnhhFrCfOqr9WjQ5FKlKSa9xguEn5h0ojEKKVRxeE6w2TxA45IUlUCN42ZfgLvOHLBQPuC0ILFuGikTXFoY9tZl4/19cS3W1p7BC/3hyAvt2miZQdUJYaEkXjnYkFsw6RGnhwYVxUkhC7TM+aDr7v/LUFBtgQ9VXfKuX8VkJyrJlRJ0M2iaSga4mHWCsUyNTwsewuWJFysRNRJOykFwqXeCczg20U7abjfSxv9WS2qGB1GshwNLCtAxdbCGtG2Y+a9LekMxkY/upnkZUC5yOJvXr3y6iG9SxFEskwfkCwYsyVBUMx1WaSX9GhvSh1aJCFpU7Yg2GpIjAsC/3rj19mFIUEW9UQs5gDMqWq1MQHy1r7xEJ5WmBNM6LquZJHqqJZkCZMSr6zX4rKqRWLVAiazfCC0cP2GseouQ0CCtjDWPQhgSLfQ5i4ImO6frDPfRg+gQeo7REYFiQgr4NVDEgkIDGGWK6VatF5rgj55Ys/9gyFo/LDvRuHsJ0Y1GDx85ZrAP4/eLJz36OIAtPe535vYDsD2iQkCnwTTpxZRQLvPfjJ/IMSjR296jw4ftZ/InxlE/BeeIXexe7fPYZnNUiwzlnqRYplVaMgFzcpIRdZs483/IHyB+zPiYpEm8Q1B5RfQMm0HzPyCrC7uURVfPioI+K5fEKmS6RJIzr0sN8xNcKtpRkxviftb6nBPwvK04scFrFYrUoujMeKuJwvMTbGPHmqGnkJTp4j7j14svvcBqhZfd1HUfZx64+yCV+zWdl8Br26RrftnZ6f9cFbgQA7XYO+VsKkaN8KtIPzg9FX4OsXh/xl1PsYb3ZmdnvIFara+3YjG6Pq2EVN3ys/XmiEFfsJBiuw2Opf0hUFe4ymdJ1SO+ORIRMRiDLrvdo2baYZ8pSbfNiFoqi5gjv20T8LPBh7booCAklRW/p2sO3Z/1ckrTmVH58IJACVDRgK/esHmPY6SD1f6rj81jb80feoN1xMGGaWXk/Q0alzdXEB8+2ZcUJTlWQRsQ7cf2/HyaY7lHBwGxNkOiMP784APnvbyjrU7VGZeUHc0/eJ+Rv0LxykO7QljiCMM9qn4/xeiGa8KhgQ5M8BDezfo/78PXmapgow5el1ec578/xWojM2/a+E/cWEP64/kTye6gjGyTFDMvwvdaSsHvoX/gRwFFATTBrgbiGrQueAWvGn8wQkyOEEgKnnf9+1reN71YBl/CbfG0zJTDosNKZoUdNiPUcUNX/GKmLVizkueipUsQHiISk6hRTcvtB6gKXcg2w9eIIo3EDT2IS8mDe5jBcz6oVrjaaq3Eg63qybuZOf8Vg/F/zof79nRwBjKgt789n0Alte4zxhBXhKYI4sg8RYrWbYJ93XU3WtJjw6M6zwUAqa7yX/AMVtPz3QfXY8zeC4u4pZi2QGvtF6eGy/QRZTKc14ozOwE6GKdQf6UreMTIFIX8+UIenobllIPyHD3clA9rQq687sICYRH5VTQJaTDfQNf5SGedddP2at2rrKLK9KNBh43KJ2OTcTSta0AKk/vufqDUaurWu2yjPwWQl0b43gLP0P1dt/b87SCl0hVXRCpjIxUpLAKfhGruX2Az+d53458K1qI1nk+NutxvYZxbeh8G6o5LWWBlWkEc52hjxl0tTcAFaJsEg8/RDwKbC8jEo3eOtdKZVMmAPlKDrQUj5CyRYpLQDYpP75lcjFoz4THNo9F2gqFtgguhDPt/YNbzwCldNua02uClbLwXzyfh//eLNbtpGUlbVyK52rWQ1eGw13ddnjNpoGBTWvCkPNL7jfDRmz97ujG07rMpR425DSmq8PcKm4vReYoz5nLif9qgTirFjCfV6hTI5wHZ4tlYkp+RgGJIyRa937iQs5wIisSiVwJ+8nDfUA3loYYT8MoyZlEsouW0VUVuZLXSQ/pmEL03i0ZQm2mFjDv7kW2xs7nK3JtAN8F3sKlLLzG1fFC1HUzvul5cvfNCWS7g8vpurFEtQUoKl+UQvLzmio6r2xR9GldAV3/kS8DLCsZ2guysGmTCV7QDEJBuvJvam8WO/D+11B2/4gvbzKALGx+RdGhFwmjK5idmeb5aTHk6JcuRJY6O4u/lNGF19HDqIz50kFuBc3JeoggH9N0TzT3JUVPiEZCFNoO5emOIfmxwI5heuuxuSOpDdPDC2BYAnOREtI8hIstG9deks3lspes3fjYhnWsuG7DlrUTS89KptPJs5ZOfGRi2f5UOp3av2zigsVmzFxs4P8exf9++YhmWRrZqM1S0r2EFMEH8bZEH/XYsS9AoVRq/mM/gl+WSn33q7ZlW1/VQhr+OwVYMQv/XY7/AVL+EDtmcsnt6NfoYppvWWTYfcTvhnrelPz/66U6dZclK3lTBIqgXwGeT4ROWdw/tGPn0mV9O3pXD5YsTZTwIgpkKdxxQaU2Nj1eGTpt44JVwaAYANeNjW3dPHJKxJIMiBdbw4gs6F/U2b14cX//+nXLxy/sdmSFx7yklIlsWlCpn8GZTWw1k9rgF3IrPfTnq7hbuXu5x7lnuFe4N3Avnzhj5WIP7rcAWu3yldaDcuuBdNKD+Yb9AjvyzXXO33i7dMz9f+trj70fTM9PkulHzBk+zO2FbGew3Yfm7byT7nd9sODg0EmeNt68A/z2b96SbV6luzHvBhKOd3QOmHgOkrrl5PgcmNnMXBFwQYMlQKWnm9DG4yd9UQsA8vQ7ucnHin6KyvQhPJ56MR+3n7uSeJpT/RrBj68z4pgn0dz1DKL6fBKegaVw76xDrIIS9S8v96FyyUMKKPfBQr6JmukZEmssnryMaBgZvtDyQFgGvyt2SbxjSVrA4PX1qyQzKgbtzq6JPktBQVvU8elAeOnuhZIZkYIW5jGUbFENajImiqWVSwZsKEpLYzkzqqkxJxBJ2WdLfNf2+uWTpcnC0rVCe0rLjfAreEQea40fXh3Tvaeitk8/DH4uj5esFA8k1Vp9sQ2CbSl0tdy/8pROO4lPKtai8/aOa8DOJnn3XFVsV8KENzpt974hSUdJtf2UNSnHETT+jMOJ79+++T3dsQjqlfJt0ZKW64bwDPo8Y9W5Vy21Ugizc9Y/AbPsyYhv0fgomyKVDWVguDU+xlvSMJ/WEmG6GNgsS3MFjVjpiNaTa9zQ3tPdDh6xTNOqgwWWrls/tDTNwr+3DMNCmhZO243353v7C/A9bf2NXWbcggg8a0Ut/OcuFay4SfGLOXQIATweipibXM/t4c7mLuFu4m7GXxaiK8MoHCa0ME8pYygD6QlIDx1yWGNLBz7FqGe05R5YD3nZfGoMI8BAntGCprvyNWLl+XfR/BRhUHKy0fBAT97y9rL0sJkulosp00yVyu1pSdluxHLFrCGqSNRjeVqC4m8C4XRbOhygu5D2z6ocSupxxVEVvI8F1d35/ny+31ZkCcSIZSn2LpIxStR4xd/DD8a687ISzHX3s3049qdQ87WGkXYs981gLq7pWMSQrPZ8TDdIyT7bSkUCeBji15PmXzf3WjUIgBqM3RPL5wfywFLCdwSj0fZY7IzW9/KCZDK+74/87Sjo8X1kZSplJdGibqGQ8HS+55RD1mkClOqXvfP8rt2NvqlKZQq+OjUMGu8HTjoUSoWfSnenAcCb20P4OB2CP6pUTm387tRhUN0MNfcWkjbtdbJxfx9JpyNgCdm6vzcdx3ydbLj/F1knyIsAAHicY2BkYGAAYrv7dnrx/DZfGbhZGEDghsO8jQj6fy/LJOYSIJeDgQkkCgAjQAqrAHicY2BkYGBu+N/AEMOqxAAELJMYGBlQAKM6AFVxA0YAeJxjYWBgYBnFo3gUj+JBhFmVGBgArlwEwAAAAAAAAAAAfACqAOABTAHAAfoCWgKuAuQDSAP0BDQEhgTIBR4FVgWgBegGygb6Bz4HZAemCAIIUAjcCSwJpAnWCjQKpgsyC3QLzAxEDOINkA4ADm4PBg+iD8YQfBFCEeQSEhKUE8YUIBSQFRAVlhYiFmIW+Bc4F4gX3BgKGG4YnBj6GaYaEhqwG1gb1hxEHLIdAB10HbIeMh76H4If7iBYILIhcCH2IlYivCNUI/YkbCWQJlwm+idAJ3Yn0igAKEAolijEKTgpxCnqKqArPCv2LLIs/C00LYItvC4ULnAu4C84L6Iv9DB+MOQxXDIsMy4zqjQYNEo09jU4NhY2cDbQNz43+DhgOKA5BDk8OcA6TjrOOyg7rjwOPIA9Aj2kPgg+gD7YPyY/eD/6QKBBbkG4QlpCsEMKQ45D5EQ4RH5E1kWMRj5Gzkc0R8BIekjySZhJ7koeSnxKxks8S9RMFEy4TOpNSE3iTyJPiFAqUJZRDlFgUdxSRFLeU0hT3lREVOBVVFX8VixWSlZ0VqxXFFfOWBpYeFjsWbZaBFpGWpRa3lscW1pbiFwUXL5c1l0wXYpd7F6YXwZfVF+uYDZg4mHGYjBjUGRsZMplZmXwZmRnEmdsZ9ZoMGhKaGRonGk8aVhpmGn8alZqzms6a/JsamzWbY5uKm6abyBvzm/scBxwvnEMcYByAnKecxhzpnQOdGp05HVmdaB18nZadxh4HniUeLh45nmeeh56gHqmewx8GnxifJB9Dn2IfiJ+TH7Uf0B/uoBYgPKBQoJqgyyDcoQ8hIp4nGNgZGBgVGe4x8DPAAJMQMwFhAwM/8F8BgAjigIsAHicZY9NTsMwEIVf+gekEqqoYIfkBWIBKP0Rq25YVGr3XXTfpk6bKokjx63UA3AejsAJOALcgDvwSCebNpbH37x5Y08A3OAHHo7fLfeRPVwyO3INF7gXrlN/EG6QX4SbaONVuEX9TdjHM6bCbXRheYPXuGL2hHdhDx18CNdwjU/hOvUv4Qb5W7iJO/wKt9Dx6sI+5l5XuI1HL/bHVi+cXqnlQcWhySKTOb+CmV7vkoWt0uqca1vEJlODoF9JU51pW91T7NdD5yIVWZOqCas6SYzKrdnq0AUb5/JRrxeJHoQm5Vhj/rbGAo5xBYUlDowxQhhkiMro6DtVZvSvsUPCXntWPc3ndFsU1P9zhQEC9M9cU7qy0nk6T4E9XxtSdXQrbsuelDSRXs1JErJCXta2VELqATZlV44RelzRiT8oZ0j/AAlabsgAAAB4nG1WBZTruBWdqxiTzMyH3b/MWNi2u2VmZuZOZVtJtLEtjyQnM1tmZmZmZmZmZmZm5grsyd+ezjkT3SfJ0tN99z1pjaz5v+Ha//3DWSAYIECICDESpBhihDHWsYFN7MN+HMBBHIEjcQhH4Wgcg2NxHI7HCTgRJ+FknIJTcRpOxxk406x1Ni6Ci+JiuDjOwSVwSVwK5+I8XBqXwWVxOVweV8AVcSVcGVfBVXE1XB3XwDVxLVwb18F1cT1cHzfADXEj3Bg3wU1xM9wct8AtcSvcGrfBbXE73B53wB1xJ9wZd8FdcTds4e6gyJCjAMMEU8zAcT7mKFGhhkCDbUgoaLRYYIkd7OIC3AP3xL1wb9wH98X9cH88AA/Eg/BgPAQPxcPwcDwCj8Sj8Gg8Bo/F4/B4PAFPxJPwZDwFT8XT8HQ8A8/Es/BsPAfPxfPwfLwAL8SL8GK8BC/Fy/ByvAKvxKvwarwGr8Xr8Hq8AW/Em/BmvAVvxdvwdrwD78S78G68B+/F+/B+fAAfxIfwYXwEH8XH8HF8Ap/Ep/BpfAafxefweXwBX8SX8GV8BV/F1/B1fAPfxLfwbXwH38X38H38AD/Ej/Bj/AQ/xc/wc/wCv8Sv8Gv8Br/F7/B7/AF/xJ/wZ/wFf8Xf8Hf8A//Ev/Bv/IesERBCBiQgIYlITBKSkiEZkTFZJxtkk+wj+8kBcpAcQY4kh8hR5GhyDDmWHEeOJyeQE8lJ5GRyCjmVnEZOJ2eQM8lZ5Oy1IW0ayXJONQvzGcvnYV4KxQJWcB2ySpzP0wldCDnhZRk6FJeCFryejkuRU81FbYeS3gibmajZhhRtXbj17OhwZXYjdo/DRqzpRySfzvRqxJmRYlTms0DTHZ5oXrkvAwuitp6IskiWVDo3AguGOa2YpNaOPBzloqpY7daNO5yUfO4XsmBfLTSf8NWBxod3hEIWTCaKdltbEBes5AvTyxa0bA19g4buBorVRaBmook0z+dMBxnN50lOVU4LppKCq1yYj8yeSgeVkCwwI3WimNaGUjXebpna47Q3Erug23giZDVoeB4ZSzOZToTQjeS1HmjRJE1bloVY1pEFbRM68mLJJpKp2cjuRg2jghdD4zvT7iyRGTY8BzmVOtqWuSiY6ap4XUR+UtxIYSayYCYqlthpjp7+JM5RO+S4rZhSdMpGtCjMnioTYm6OWpsfkc9NsGwzWPAmXDKeiYTmmi+43l2fSG6IM1/ZVdI9a+zRhFaiVZE3wqkQhUqVcS635MRspynN0YyfzLCvN9V2S42ie+1F3h4d1h06aY3db7dn0hsD83/oQmIQMuNuzqjbqYtEWQRTo4NUsqKhNtbrez45LhSveEnlxirB3EbcrOhWsGBkVjeSdcvHHR5bL6mc+um9ERvWDPlFuBA8Z6n7dU71FJnMDJbG61CZ+SxaulGyZGlpVUBbLUYO+fP4XhdJnyJSaFsCXHecUSeEzUlJ1cx1+Qxd2aJh9dCnpZVyrJhcGI8CJaQOnAYrkRnVDH3jDpyLZnc9NzxrO8FFes8aWsr9iSIPR22jNPUsxB1OMprturUsSDNp9OwKk0Mb+cyyUhvhuQKyMkfGfT1jyue/x+PcpIORn6e5N6IJq2jJkjnbzYShO7BWXLOlnTUwrUsycyCdWuAyLDGbO6kFFgwyWqSeUyOlcCLyVg27IJk563tD7gsjDpU2lPvaFDoUmwR3kekyl0oploYqo72S1SqpqPTbWTDqZN/lcsNoGdIya6thw0TjmY88HHVB6qdSLgOb2UOPXUA0FTuciqY1AuI7vF6nWpvVO02ne5arqB37cYfXbdvWJp+72HZWYLgtTOUobVLLQd7qsKJTno9tbezVnzQl9aFVRlyxibZj3LTh1ORmM6AmovaDrirNhDvywLRBI5QNQsFFJnZSl8lOgm1jr6p0KbnPvdChcT/TM97W+czmzJyZerwwCqYTNu4Lkz+I7OQaOpS6AuRyryt3Dndl0s1T1oWRakSt/M0Zd9gIObM1MF4y16ZL1tYeubvWzt3wyKaaU4FDWevJ0WxHD70DNuPTqlVeLJse7RUrW9CLfVpyWk9L1ifcRt/RuvvkgOPKqtla59gENYWt1qHm2ukiFz46kYfrdlGXF56Y3krsvdTlOK83V7OcO8Ocy7xTooebK1W5GQf/x3a+rfr698fGhbsi56VKed69SIJJ67KCl534bWkaO7a6DE56I61YQUsXLIcS0+djakEnrrjDgW3TBS+Yq9yhQwHb4TpRc+4fHhaMK/P02c28dEeteeEYf3z98jjpJ2zsXRpbLsaqzVQueeNu++4050ZTrmdtFk1LkVEzp3sjuA9sJmz1t7m5l+xta3JwvX+MuGWHLnMc3G/Ta6u7Yfye3fvFGQd8zd3y9G/1b415YErR3FzW9QU8ZmXJG8XibbllL4e4MEqatTTg+crn8waZrtfW/gthnmJTAAAA') format('woff'),
		url('//at.alicdn.com/t/font_533566_yfq2d9wdij.ttf?t=1545239985831') format('truetype'),
		/* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
		url('//at.alicdn.com/t/font_533566_yfq2d9wdij.svg?t=1545239985831#cuIconfont') format('svg');
	/* iOS 4.1- */
}
.cuIcon-appreciate:before {
	content: "\e644";
}
.cuIcon-check:before {
	content: "\e645";
}
.cuIcon-close:before {
	content: "\e646";
}
.cuIcon-edit:before {
	content: "\e649";
}
.cuIcon-emoji:before {
	content: "\e64a";
}
.cuIcon-favorfill:before {
	content: "\e64b";
}
.cuIcon-favor:before {
	content: "\e64c";
}
.cuIcon-loading:before {
	content: "\e64f";
}
.cuIcon-locationfill:before {
	content: "\e650";
}
.cuIcon-location:before {
	content: "\e651";
}
.cuIcon-phone:before {
	content: "\e652";
}
.cuIcon-roundcheckfill:before {
	content: "\e656";
}
.cuIcon-roundcheck:before {
	content: "\e657";
}
.cuIcon-roundclosefill:before {
	content: "\e658";
}
.cuIcon-roundclose:before {
	content: "\e659";
}
.cuIcon-roundrightfill:before {
	content: "\e65a";
}
.cuIcon-roundright:before {
	content: "\e65b";
}
.cuIcon-search:before {
	content: "\e65c";
}
.cuIcon-taxi:before {
	content: "\e65d";
}
.cuIcon-timefill:before {
	content: "\e65e";
}
.cuIcon-time:before {
	content: "\e65f";
}
.cuIcon-unfold:before {
	content: "\e661";
}
.cuIcon-warnfill:before {
	content: "\e662";
}
.cuIcon-warn:before {
	content: "\e663";
}
.cuIcon-camerafill:before {
	content: "\e664";
}
.cuIcon-camera:before {
	content: "\e665";
}
.cuIcon-commentfill:before {
	content: "\e666";
}
.cuIcon-comment:before {
	content: "\e667";
}
.cuIcon-likefill:before {
	content: "\e668";
}
.cuIcon-like:before {
	content: "\e669";
}
.cuIcon-notificationfill:before {
	content: "\e66a";
}
.cuIcon-notification:before {
	content: "\e66b";
}
.cuIcon-order:before {
	content: "\e66c";
}
.cuIcon-samefill:before {
	content: "\e66d";
}
.cuIcon-same:before {
	content: "\e66e";
}
.cuIcon-deliver:before {
	content: "\e671";
}
.cuIcon-evaluate:before {
	content: "\e672";
}
.cuIcon-pay:before {
	content: "\e673";
}
.cuIcon-send:before {
	content: "\e675";
}
.cuIcon-shop:before {
	content: "\e676";
}
.cuIcon-ticket:before {
	content: "\e677";
}
.cuIcon-back:before {
	content: "\e679";
}
.cuIcon-cascades:before {
	content: "\e67c";
}
.cuIcon-discover:before {
	content: "\e67e";
}
.cuIcon-list:before {
	content: "\e682";
}
.cuIcon-more:before {
	content: "\e684";
}
.cuIcon-scan:before {
	content: "\e689";
}
.cuIcon-settings:before {
	content: "\e68a";
}
.cuIcon-questionfill:before {
	content: "\e690";
}
.cuIcon-question:before {
	content: "\e691";
}
.cuIcon-shopfill:before {
	content: "\e697";
}
.cuIcon-form:before {
	content: "\e699";
}
.cuIcon-pic:before {
	content: "\e69b";
}
.cuIcon-filter:before {
	content: "\e69c";
}
.cuIcon-footprint:before {
	content: "\e69d";
}
.cuIcon-top:before {
	content: "\e69e";
}
.cuIcon-pulldown:before {
	content: "\e69f";
}
.cuIcon-pullup:before {
	content: "\e6a0";
}
.cuIcon-right:before {
	content: "\e6a3";
}
.cuIcon-refresh:before {
	content: "\e6a4";
}
.cuIcon-moreandroid:before {
	content: "\e6a5";
}
.cuIcon-deletefill:before {
	content: "\e6a6";
}
.cuIcon-refund:before {
	content: "\e6ac";
}
.cuIcon-cart:before {
	content: "\e6af";
}
.cuIcon-qrcode:before {
	content: "\e6b0";
}
.cuIcon-remind:before {
	content: "\e6b2";
}
.cuIcon-delete:before {
	content: "\e6b4";
}
.cuIcon-profile:before {
	content: "\e6b7";
}
.cuIcon-home:before {
	content: "\e6b8";
}
.cuIcon-cartfill:before {
	content: "\e6b9";
}
.cuIcon-discoverfill:before {
	content: "\e6ba";
}
.cuIcon-homefill:before {
	content: "\e6bb";
}
.cuIcon-message:before {
	content: "\e6bc";
}
.cuIcon-addressbook:before {
	content: "\e6bd";
}
.cuIcon-link:before {
	content: "\e6bf";
}
.cuIcon-lock:before {
	content: "\e6c0";
}
.cuIcon-unlock:before {
	content: "\e6c2";
}
.cuIcon-vip:before {
	content: "\e6c3";
}
.cuIcon-weibo:before {
	content: "\e6c4";
}
.cuIcon-activity:before {
	content: "\e6c5";
}
.cuIcon-friendaddfill:before {
	content: "\e6c9";
}
.cuIcon-friendadd:before {
	content: "\e6ca";
}
.cuIcon-friendfamous:before {
	content: "\e6cb";
}
.cuIcon-friend:before {
	content: "\e6cc";
}
.cuIcon-goods:before {
	content: "\e6cd";
}
.cuIcon-selection:before {
	content: "\e6ce";
}
.cuIcon-explore:before {
	content: "\e6d2";
}
.cuIcon-present:before {
	content: "\e6d3";
}
.cuIcon-squarecheckfill:before {
	content: "\e6d4";
}
.cuIcon-square:before {
	content: "\e6d5";
}
.cuIcon-squarecheck:before {
	content: "\e6d6";
}
.cuIcon-round:before {
	content: "\e6d7";
}
.cuIcon-roundaddfill:before {
	content: "\e6d8";
}
.cuIcon-roundadd:before {
	content: "\e6d9";
}
.cuIcon-add:before {
	content: "\e6da";
}
.cuIcon-notificationforbidfill:before {
	content: "\e6db";
}
.cuIcon-explorefill:before {
	content: "\e6dd";
}
.cuIcon-fold:before {
	content: "\e6de";
}
.cuIcon-game:before {
	content: "\e6df";
}
.cuIcon-redpacket:before {
	content: "\e6e0";
}
.cuIcon-selectionfill:before {
	content: "\e6e1";
}
.cuIcon-similar:before {
	content: "\e6e2";
}
.cuIcon-appreciatefill:before {
	content: "\e6e3";
}
.cuIcon-infofill:before {
	content: "\e6e4";
}
.cuIcon-info:before {
	content: "\e6e5";
}
.cuIcon-forwardfill:before {
	content: "\e6ea";
}
.cuIcon-forward:before {
	content: "\e6eb";
}
.cuIcon-rechargefill:before {
	content: "\e6ec";
}
.cuIcon-recharge:before {
	content: "\e6ed";
}
.cuIcon-vipcard:before {
	content: "\e6ee";
}
.cuIcon-voice:before {
	content: "\e6ef";
}
.cuIcon-voicefill:before {
	content: "\e6f0";
}
.cuIcon-friendfavor:before {
	content: "\e6f1";
}
.cuIcon-wifi:before {
	content: "\e6f2";
}
.cuIcon-share:before {
	content: "\e6f3";
}
.cuIcon-wefill:before {
	content: "\e6f4";
}
.cuIcon-we:before {
	content: "\e6f5";
}
.cuIcon-lightauto:before {
	content: "\e6f6";
}
.cuIcon-lightforbid:before {
	content: "\e6f7";
}
.cuIcon-lightfill:before {
	content: "\e6f8";
}
.cuIcon-camerarotate:before {
	content: "\e6f9";
}
.cuIcon-light:before {
	content: "\e6fa";
}
.cuIcon-barcode:before {
	content: "\e6fb";
}
.cuIcon-flashlightclose:before {
	content: "\e6fc";
}
.cuIcon-flashlightopen:before {
	content: "\e6fd";
}
.cuIcon-searchlist:before {
	content: "\e6fe";
}
.cuIcon-service:before {
	content: "\e6ff";
}
.cuIcon-sort:before {
	content: "\e700";
}
.cuIcon-down:before {
	content: "\e703";
}
.cuIcon-mobile:before {
	content: "\e704";
}
.cuIcon-mobilefill:before {
	content: "\e705";
}
.cuIcon-copy:before {
	content: "\e706";
}
.cuIcon-countdownfill:before {
	content: "\e707";
}
.cuIcon-countdown:before {
	content: "\e708";
}
.cuIcon-noticefill:before {
	content: "\e709";
}
.cuIcon-notice:before {
	content: "\e70a";
}
.cuIcon-upstagefill:before {
	content: "\e70e";
}
.cuIcon-upstage:before {
	content: "\e70f";
}
.cuIcon-babyfill:before {
	content: "\e710";
}
.cuIcon-baby:before {
	content: "\e711";
}
.cuIcon-brandfill:before {
	content: "\e712";
}
.cuIcon-brand:before {
	content: "\e713";
}
.cuIcon-choicenessfill:before {
	content: "\e714";
}
.cuIcon-choiceness:before {
	content: "\e715";
}
.cuIcon-clothesfill:before {
	content: "\e716";
}
.cuIcon-clothes:before {
	content: "\e717";
}
.cuIcon-creativefill:before {
	content: "\e718";
}
.cuIcon-creative:before {
	content: "\e719";
}
.cuIcon-female:before {
	content: "\e71a";
}
.cuIcon-keyboard:before {
	content: "\e71b";
}
.cuIcon-male:before {
	content: "\e71c";
}
.cuIcon-newfill:before {
	content: "\e71d";
}
.cuIcon-new:before {
	content: "\e71e";
}
.cuIcon-pullleft:before {
	content: "\e71f";
}
.cuIcon-pullright:before {
	content: "\e720";
}
.cuIcon-rankfill:before {
	content: "\e721";
}
.cuIcon-rank:before {
	content: "\e722";
}
.cuIcon-bad:before {
	content: "\e723";
}
.cuIcon-cameraadd:before {
	content: "\e724";
}
.cuIcon-focus:before {
	content: "\e725";
}
.cuIcon-friendfill:before {
	content: "\e726";
}
.cuIcon-cameraaddfill:before {
	content: "\e727";
}
.cuIcon-apps:before {
	content: "\e729";
}
.cuIcon-paintfill:before {
	content: "\e72a";
}
.cuIcon-paint:before {
	content: "\e72b";
}
.cuIcon-picfill:before {
	content: "\e72c";
}
.cuIcon-refresharrow:before {
	content: "\e72d";
}
.cuIcon-colorlens:before {
	content: "\e6e6";
}
.cuIcon-markfill:before {
	content: "\e730";
}
.cuIcon-mark:before {
	content: "\e731";
}
.cuIcon-presentfill:before {
	content: "\e732";
}
.cuIcon-repeal:before {
	content: "\e733";
}
.cuIcon-album:before {
	content: "\e734";
}
.cuIcon-peoplefill:before {
	content: "\e735";
}
.cuIcon-people:before {
	content: "\e736";
}
.cuIcon-servicefill:before {
	content: "\e737";
}
.cuIcon-repair:before {
	content: "\e738";
}
.cuIcon-file:before {
	content: "\e739";
}
.cuIcon-repairfill:before {
	content: "\e73a";
}
.cuIcon-taoxiaopu:before {
	content: "\e73b";
}
.cuIcon-weixin:before {
	content: "\e612";
}
.cuIcon-attentionfill:before {
	content: "\e73c";
}
.cuIcon-attention:before {
	content: "\e73d";
}
.cuIcon-commandfill:before {
	content: "\e73e";
}
.cuIcon-command:before {
	content: "\e73f";
}
.cuIcon-communityfill:before {
	content: "\e740";
}
.cuIcon-community:before {
	content: "\e741";
}
.cuIcon-read:before {
	content: "\e742";
}
.cuIcon-calendar:before {
	content: "\e74a";
}
.cuIcon-cut:before {
	content: "\e74b";
}
.cuIcon-magic:before {
	content: "\e74c";
}
.cuIcon-backwardfill:before {
	content: "\e74d";
}
.cuIcon-playfill:before {
	content: "\e74f";
}
.cuIcon-stop:before {
	content: "\e750";
}
.cuIcon-tagfill:before {
	content: "\e751";
}
.cuIcon-tag:before {
	content: "\e752";
}
.cuIcon-group:before {
	content: "\e753";
}
.cuIcon-all:before {
	content: "\e755";
}
.cuIcon-backdelete:before {
	content: "\e756";
}
.cuIcon-hotfill:before {
	content: "\e757";
}
.cuIcon-hot:before {
	content: "\e758";
}
.cuIcon-post:before {
	content: "\e759";
}
.cuIcon-radiobox:before {
	content: "\e75b";
}
.cuIcon-rounddown:before {
	content: "\e75c";
}
.cuIcon-upload:before {
	content: "\e75d";
}
.cuIcon-writefill:before {
	content: "\e760";
}
.cuIcon-write:before {
	content: "\e761";
}
.cuIcon-radioboxfill:before {
	content: "\e763";
}
.cuIcon-punch:before {
	content: "\e764";
}
.cuIcon-shake:before {
	content: "\e765";
}
.cuIcon-move:before {
	content: "\e768";
}
.cuIcon-safe:before {
	content: "\e769";
}
.cuIcon-activityfill:before {
	content: "\e775";
}
.cuIcon-crownfill:before {
	content: "\e776";
}
.cuIcon-crown:before {
	content: "\e777";
}
.cuIcon-goodsfill:before {
	content: "\e778";
}
.cuIcon-messagefill:before {
	content: "\e779";
}
.cuIcon-profilefill:before {
	content: "\e77a";
}
.cuIcon-sound:before {
	content: "\e77b";
}
.cuIcon-sponsorfill:before {
	content: "\e77c";
}
.cuIcon-sponsor:before {
	content: "\e77d";
}
.cuIcon-upblock:before {
	content: "\e77e";
}
.cuIcon-weblock:before {
	content: "\e77f";
}
.cuIcon-weunblock:before {
	content: "\e780";
}
.cuIcon-my:before {
	content: "\e78b";
}
.cuIcon-myfill:before {
	content: "\e78c";
}
.cuIcon-emojifill:before {
	content: "\e78d";
}
.cuIcon-emojiflashfill:before {
	content: "\e78e";
}
.cuIcon-flashbuyfill:before {
	content: "\e78f";
}
.cuIcon-text:before {
	content: "\e791";
}
.cuIcon-goodsfavor:before {
	content: "\e794";
}
.cuIcon-musicfill:before {
	content: "\e795";
}
.cuIcon-musicforbidfill:before {
	content: "\e796";
}
.cuIcon-card:before {
	content: "\e624";
}
.cuIcon-triangledownfill:before {
	content: "\e79b";
}
.cuIcon-triangleupfill:before {
	content: "\e79c";
}
.cuIcon-roundleftfill-copy:before {
	content: "\e79e";
}
.cuIcon-font:before {
	content: "\e76a";
}
.cuIcon-title:before {
	content: "\e82f";
}
.cuIcon-recordfill:before {
	content: "\e7a4";
}
.cuIcon-record:before {
	content: "\e7a6";
}
.cuIcon-cardboardfill:before {
	content: "\e7a9";
}
.cuIcon-cardboard:before {
	content: "\e7aa";
}
.cuIcon-formfill:before {
	content: "\e7ab";
}
.cuIcon-coin:before {
	content: "\e7ac";
}
.cuIcon-cardboardforbid:before {
	content: "\e7af";
}
.cuIcon-circlefill:before {
	content: "\e7b0";
}
.cuIcon-circle:before {
	content: "\e7b1";
}
.cuIcon-attentionforbid:before {
	content: "\e7b2";
}
.cuIcon-attentionforbidfill:before {
	content: "\e7b3";
}
.cuIcon-attentionfavorfill:before {
	content: "\e7b4";
}
.cuIcon-attentionfavor:before {
	content: "\e7b5";
}
.cuIcon-titles:before {
	content: "\e701";
}
.cuIcon-icloading:before {
	content: "\e67a";
}
.cuIcon-full:before {
	content: "\e7bc";
}
.cuIcon-mail:before {
	content: "\e7bd";
}
.cuIcon-peoplelist:before {
	content: "\e7be";
}
.cuIcon-goodsnewfill:before {
	content: "\e7bf";
}
.cuIcon-goodsnew:before {
	content: "\e7c0";
}
.cuIcon-medalfill:before {
	content: "\e7c1";
}
.cuIcon-medal:before {
	content: "\e7c2";
}
.cuIcon-newsfill:before {
	content: "\e7c3";
}
.cuIcon-newshotfill:before {
	content: "\e7c4";
}
.cuIcon-newshot:before {
	content: "\e7c5";
}
.cuIcon-news:before {
	content: "\e7c6";
}
.cuIcon-videofill:before {
	content: "\e7c7";
}
.cuIcon-video:before {
	content: "\e7c8";
}
.cuIcon-exit:before {
	content: "\e7cb";
}
.cuIcon-skinfill:before {
	content: "\e7cc";
}
.cuIcon-skin:before {
	content: "\e7cd";
}
.cuIcon-moneybagfill:before {
	content: "\e7ce";
}
.cuIcon-usefullfill:before {
	content: "\e7cf";
}
.cuIcon-usefull:before {
	content: "\e7d0";
}
.cuIcon-moneybag:before {
	content: "\e7d1";
}
.cuIcon-redpacket_fill:before {
	content: "\e7d3";
}
.cuIcon-subscription:before {
	content: "\e7d4";
}
.cuIcon-loading1:before {
	content: "\e633";
}
.cuIcon-github:before {
	content: "\e692";
}
.cuIcon-global:before {
	content: "\e7eb";
}
.cuIcon-settingsfill:before {
	content: "\e6ab";
}
.cuIcon-back_android:before {
	content: "\e7ed";
}
.cuIcon-expressman:before {
	content: "\e7ef";
}
.cuIcon-evaluate_fill:before {
	content: "\e7f0";
}
.cuIcon-group_fill:before {
	content: "\e7f5";
}
.cuIcon-play_forward_fill:before {
	content: "\e7f6";
}
.cuIcon-deliver_fill:before {
	content: "\e7f7";
}
.cuIcon-notice_forbid_fill:before {
	content: "\e7f8";
}
.cuIcon-fork:before {
	content: "\e60c";
}
.cuIcon-pick:before {
	content: "\e7fa";
}
.cuIcon-wenzi:before {
	content: "\e6a7";
}
.cuIcon-ellipse:before {
	content: "\e600";
}
.cuIcon-qr_code:before {
	content: "\e61b";
}
.cuIcon-dianhua:before {
	content: "\e64d";
}
.cuIcon-cuIcon:before {
	content: "\e602";
}
.cuIcon-loading2:before {
	content: "\e7f1";
}
.cuIcon-btn:before {
	content: "\e601";
}
.status_bar {
	     height: 25px;
		 background-color: #f1f1f1;
	     width: 100%;
}
.status_bar-nobg {
	     height: 25px;
	     width: 100%;
}
/* 转圈动画 */
.turn-load{
  -webkit-animation:turnmy 1s linear infinite;
          animation:turnmy 1s linear infinite;
}
@-webkit-keyframes turnmy{
0%{-webkit-transform:rotate(0deg);}
25%{-webkit-transform:rotate(90deg);}
50%{-webkit-transform:rotate(180deg);}
75%{-webkit-transform:rotate(270deg);}
100%{-webkit-transform:rotate(360deg);}
}
@keyframes turnmy{
0%{-webkit-transform:rotate(0deg);}
25%{-webkit-transform:rotate(90deg);}
50%{-webkit-transform:rotate(180deg);}
75%{-webkit-transform:rotate(270deg);}
100%{-webkit-transform:rotate(360deg);}
}
.one-show{
	-webkit-animation: oneshow 0.8s ease 1;
	        animation: oneshow 0.8s ease 1;
}
@-webkit-keyframes oneshow{
from{opacity: 0;}
to{opacity: 1;}
}
@keyframes oneshow{
from{opacity: 0;}
to{opacity: 1;}
}
.status_bar-fixed{
	height: 25px;
	width: 100%;
	position: fixed;
	background-color: #f1f1f1;
	z-index: 20;
}
.head-dh-my{
	display: flex;
	position: fixed;
	justify-content: space-around;
	align-items: flex-end;
	padding-bottom: 10rpx;
	z-index: 15;
	background-color: #e3e3e3;
	width: 750rpx;
}
.border-bom{
		 border-bottom:0.5rpx solid #DDDDDD ;
}
.border-red{
	 		 border-bottom:1rpx solid #d33e18;
}
.border-bom-big{
	 		 border-bottom:8rpx solid #DDDDDD ;
}
.border-bom-white{
	 		 border-bottom:2rpx solid #FFFFFF ;
}
.border-bom-green{
	 		 border-bottom:4rpx solid #f8f9bd;
}
.border-bom-index{
	 		 border-bottom:4rpx solid #27d9b3;
}
.padding-left{
		 padding-left: 20rpx;
}
.padding-left-top{
	 		 padding-left: 20rpx;
			 padding-top: 20rpx;
}
.padding-right{
	 		 padding-right: 20rpx;
}
.input-my{
		padding-left: 20rpx;
	 	border-radius: 40rpx;
	 	height: 50rpx;
	 	margin: 10rpx;
}
.tb-tag-absolute{
	 	position: absolute;
	 	z-index: 5;
	 	border-radius: 25rpx;
	 	font-size: 16rpx;
	 	margin-left: 25rpx;
	 	margin-top:-35rpx;
}
.lk-tag{
		 height: 50rpx;
		 padding: 0 10rpx;
		 display: flex;
		 justify-content: center;
		 align-items: center;
		 border: 2rpx solid #24bd9f;
		 border-radius: 6rpx;
		 color: #1c947a;
		 font-weight: 500;
}
.tb-tag-my{
	 	border-radius: 15rpx;
	 	font-size: 16rpx;
	 	margin-left: 5rpx;
}
.my-green{
		 color: #29c7a5;
}
.my-hui{
		 color: #585858;
		 font-size: 22rpx;
}
.flex-column-center{
		 display: flex;
		 flex-direction: column;
		 justify-content: center;
		 align-items: center;
}
.flex-column-between{
	 		 display: flex;
	 		 flex-direction: column;
	 		 justify-content: space-between;
	 		 align-items: center;
}
.flex-column-start{
	 		 display: flex;
	 		 flex-direction: column;
			 justify-content: center;
}
.flex-column-around{
	 		 display: flex;
	 		 flex-direction: column;
			 justify-content: space-around;
			 align-items: center;
}
.flex-row-start{
	 		 display: flex;
	 		 flex-direction: row;
	 		 align-items: center;
}
.flex-row-around{
	 		 display: flex;
	 		 flex-direction: row;
	 		 justify-content: space-around;
	 		 align-items: center;
}
.flex-row-center{
	 		 display: flex;
	 		 flex-direction: row;
	 		 justify-content: center;
	 		 align-items: center;
}
.flex-row-between{
	 		 display: flex;
	 		 flex-direction: row;
	 		 justify-content: space-between;
	 		 align-items: center;
}
.my-title{
		 font-size: 35rpx;
		 font-weight: bold;
}
.my-neirong{
		 font-size: 26rpx;
		 color: #6d6d6d;
}
.my-neirong-sm{
	 		 font-size: 23rpx;
	 		 color: #616161;
}
.my-tag-text{
		 font-size: 22rpx;
		 padding-top: 20rpx;
		 color: #bababa;
}
.padding-top{
		 padding-top: 35rpx;
}
.padding-top-sm{
	 	 padding-top: 20rpx;
}
.bottom-dh{
	 	background-color: #f1f1f1;
	 	position: fixed;
		z-index: 10;
	 	bottom: 0;
	 	width: 750rpx;
	 	height: 110rpx;
}
.tb-text{
	 	display: flex;
	 	flex-direction: column;
	 	justify-content: center;
	 	align-items: center;
}
.bottom-text{
		 width: 750rpx;
		 position: fixed;
		 text-align: center;
		 font-size: 26rpx;
		 color: #9d9d9d;
		 bottom: 70rpx;
}
.white-box{
	 		padding: 0 20rpx;
	 		margin-bottom: 15rpx;
	 		margin-top: 5rpx;
	 		width: 715rpx;
	 		background-color: #FFFFFF;
	 		border-radius: 30rpx;
}
.green-box{
			padding: 0 20rpx;
			margin-bottom: 15rpx;
			margin-top: 5rpx;
			width: 715rpx;
			background-color: #FFFFFF;
			border-radius: 30rpx;
			background-image: linear-gradient(#1faf97, #29c7a5);
}
.yuan-sm{
		width: 13rpx;
		height: 13rpx;
		border-radius: 50%;
		background-color: #1fc189;
		margin-left: 10rpx;
}
.yuan-normal{
		width: 14rpx;
		height: 14rpx;
		border-radius: 50%;
		background-color: #159f3c;
		margin-left: 10rpx;
}
.yuan-normal-red{
		width: 14rpx;
		height: 14rpx;
		border-radius: 50%;
		background-color: #bc3c11;
		margin-left: 10rpx;
}
.yuan-sm-red{
		width: 13rpx;
		height: 13rpx;
		border-radius: 50%;
		background-color: #de410d;
		margin-left: 10rpx;
}
.white-box-all{
		margin-top: 5rpx;
		width: 750rpx;
		background-color: #FFFFFF;
		border-radius: 13px;
}
.moneycolor{
	 	color: #ea5002;
}
.text-bold-sm{
		 font-weight: 425;
}
.sm-moneycolor{
		 color: #e3793b;
}
.margin-top{
		 margin-top: 20rpx;
}
.margin-top-sm{
	 		 margin-top: 12rpx;
}
.margin{
		 margin: 20rpx;
}
.margin-left{
		 margin-left: 20rpx;
}
.margin-left-top{
		 margin-left: 20rpx;
		 margin-top: 20rpx;
}
.margin-right{
		 margin-right: 20rpx;
}
.my-absolute{
		 position: absolute;
}
.my-fixed{
	  		 position: fixed;
}
.my-seach{
		 width: 450rpx;
		 height: 55rpx;
		 background-color: #f8f8f8;
		 border-radius: 30rpx;
		 padding-left: 20rpx;
}
.move-view{
		 width: 48rpx;
		 height: 10rpx;
		 background-color: #28ba91;
		 border-radius: 4rpx;
		 margin-left: 100rpx;
}
.move-view-p{
		 width: 45rpx;
		 height: 10rpx;
		 background-color: #28ba91;
		 border-radius: 4rpx;
}
.header-dh{
	 	position: fixed;
	 	padding-top: 20rpx;
		padding-bottom: 15rpx;
	 	height: 70rpx;
	 	width: 750rpx;
	 	background-color: #f1f1f1;
	 	z-index: 20;
}
.tp-normal{
		 width: 60rpx;
		 height: 60rpx;
}
.tp-sm{
		 width: 45rpx;
		 height: 45rpx;
}
.tp-big{
		 width: 70rpx;
		 height: 70rpx;
		 border-radius: 50%;
}
.main-color{
		 color: #07D188;
}
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*每个页面公共css */
/* 加载框架核心样式 */
/* 加载主题样式 */
/* 自定义css样式文件 - ws */
/* 加载图标字体 - 条件编译模式 */
/*
	全局公共样式和字体图标
*/
@font-face {
  font-family: yticon;
  font-weight: normal;
  font-style: normal;
  src: url("https://at.alicdn.com/t/font_1078604_w4kpxh0rafi.ttf") format("truetype");
}
.yticon {
  font-family: "yticon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-yiguoqi1:before {
  content: "\e700";
}
.icon-iconfontshanchu1:before {
  content: "\e619";
}
.icon-iconfontweixin:before {
  content: "\e611";
}
.icon-alipay:before {
  content: "\e636";
}
.icon-shang:before {
  content: "\e624";
}
.icon-shouye:before {
  content: "\e626";
}
.icon-shanchu4:before {
  content: "\e622";
}
.icon-xiaoxi:before {
  content: "\e618";
}
.icon-jiantour-copy:before {
  content: "\e600";
}
.icon-fenxiang2:before {
  content: "\e61e";
}
.icon-pingjia:before {
  content: "\e67b";
}
.icon-daifukuan:before {
  content: "\e68f";
}
.icon-pinglun-copy:before {
  content: "\e612";
}
.icon-dianhua-copy:before {
  content: "\e621";
}
.icon-shoucang:before {
  content: "\e645";
}
.icon-xuanzhong2:before {
  content: "\e62f";
}
.icon-gouwuche_:before {
  content: "\e630";
}
.icon-icon-test:before {
  content: "\e60c";
}
.icon-icon-test1:before {
  content: "\e632";
}
.icon-bianji:before {
  content: "\e646";
}
.icon-jiazailoading-A:before {
  content: "\e8fc";
}
.icon-zuoshang:before {
  content: "\e613";
}
.icon-jia2:before {
  content: "\e60a";
}
.icon-huifu:before {
  content: "\e68b";
}
.icon-sousuo:before {
  content: "\e7ce";
}
.icon-arrow-fine-up:before {
  content: "\e601";
}
.icon-hot:before {
  content: "\e60e";
}
.icon-lishijilu:before {
  content: "\e6b9";
}
.icon-zhengxinchaxun-zhifubaoceping-:before {
  content: "\e616";
}
.icon-naozhong:before {
  content: "\e64a";
}
.icon-xiatubiao--copy:before {
  content: "\e608";
}
.icon-shoucang_xuanzhongzhuangtai:before {
  content: "\e6a9";
}
.icon-jia1:before {
  content: "\e61c";
}
.icon-bangzhu1:before {
  content: "\e63d";
}
.icon-arrow-left-bottom:before {
  content: "\e602";
}
.icon-arrow-right-bottom:before {
  content: "\e603";
}
.icon-arrow-left-top:before {
  content: "\e604";
}
.icon-icon--:before {
  content: "\e744";
}
.icon-zuojiantou-up:before {
  content: "\e605";
}
.icon-xia:before {
  content: "\e62d";
}
.icon--jianhao:before {
  content: "\e60b";
}
.icon-weixinzhifu:before {
  content: "\e61a";
}
.icon-comment:before {
  content: "\e64f";
}
.icon-weixin:before {
  content: "\e61f";
}
.icon-fenlei1:before {
  content: "\e620";
}
.icon-erjiye-yucunkuan:before {
  content: "\e623";
}
.icon-Group-:before {
  content: "\e688";
}
.icon-you:before {
  content: "\e606";
}
.icon-forward:before {
  content: "\e607";
}
.icon-tuijian:before {
  content: "\e610";
}
.icon-bangzhu:before {
  content: "\e679";
}
.icon-share:before {
  content: "\e656";
}
.icon-yiguoqi:before {
  content: "\e997";
}
.icon-shezhi1:before {
  content: "\e61d";
}
.icon-fork:before {
  content: "\e61b";
}
.icon-kafei:before {
  content: "\e66a";
}
.icon-iLinkapp-:before {
  content: "\e654";
}
.icon-saomiao:before {
  content: "\e60d";
}
.icon-shezhi:before {
  content: "\e60f";
}
.icon-shouhoutuikuan:before {
  content: "\e631";
}
.icon-gouwuche:before {
  content: "\e609";
}
.icon-dizhi:before {
  content: "\e614";
}
.icon-fenlei:before {
  content: "\e706";
}
.icon-xingxing:before {
  content: "\e70b";
}
.icon-tuandui:before {
  content: "\e633";
}
.icon-zuanshi:before {
  content: "\e615";
}
.icon-zuo:before {
  content: "\e63c";
}
.icon-shoucang2:before {
  content: "\e62e";
}
.icon-shouhuodizhi:before {
  content: "\e712";
}
.icon-yishouhuo:before {
  content: "\e71a";
}
.icon-dianzan-ash:before {
  content: "\e617";
}
view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
  box-sizing: border-box;
}
/* 骨架屏替代方案 */
.Skeleton {
  background: #f3f3f3;
  padding: 20rpx 0;
  border-radius: 8rpx;
}
/* 图片载入替代方案 */
.image-wrapper {
  font-size: 0;
  background: #f3f3f3;
  border-radius: 4px;
}
.image-wrapper image {
  width: 100%;
  height: 100%;
  transition: .6s;
  opacity: 0;
}
.image-wrapper image.loaded {
  opacity: 1;
}
.clamp {
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap;//溢出不换行 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.common-hover {
  background: #f5f5f5;
}
/*边框*/
.b-b:after,
.b-t:after {
  position: absolute;
  z-index: 3;
  left: 0;
  right: 0;
  height: 0;
  content: '';
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
  border-bottom: 1px solid #E4E7ED;
}
.b-b:after {
  bottom: 0;
}
.b-t:after {
  top: 0;
}
/* button样式改写 */
uni-button,
button {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 34rpx;
  font-weight: normal;
}
uni-button.no-border:before, uni-button.no-border:after, button.no-border:before, button.no-border:after {
  border: 0;
}
uni-button[type=default],
button[type=default] {
  color: #303133;
}
/* input 样式 */
.input-placeholder {
  color: #999999;
}
.placeholder {
  color: #999999;
}

