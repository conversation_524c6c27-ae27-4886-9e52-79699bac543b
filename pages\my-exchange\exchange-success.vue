<template>
	<gui-page :fullPage="true"  class="root-box">
			<!-- 页面主体 -->
			<view slot="gBody"  class="root-box lp-flex-column">
			<!-- <view slot="gBody"  class="root-box lp-flex-column"> -->
				<view class="head lp-flex-column lp-flex-center" style="margin-top: 50rpx;">
					<!-- <text class="gui-icons" style="100rpx">{{order.status ? '&#xe7f8;' : '&#xe632;'}}</text> -->
					<image src="/static/success.png" mode="aspectFit" style="width: 200rpx;height: 200rpx;"></image>
					<text class="success-text">兑换成功</text>
				</view>
				<button  @tap="up()" type="primary" class="phone-login-btn" style="margin-top: 100rpx ;margin-bottom: 100rpx ;margin-left: 10% ;margin-right: 10% ;width: 80%;background-color: rgb(18, 150, 219);">
					去学习
				</button>
			</view>
	</gui-page>
</template>

<script>
	import nodata from '@/components/nodata/nodata.vue';

	export default {
		components: {
			nodata
		},
		data() {
			return {
				sn: '',
				list: []
			}
		},
		onShow: function() {},
		onHide: function() {},
		created: function() {},
		computed: {
			valided: function() {
				return this.list.length > 0;
			},
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			exchangeValid: function() {
				if (this.sn == '') {
					uni.showToast({
						icon: 'none',
						title: '请填写有效兑换码'
					});
					return;
				}
				this.apiExchangeValid(this.sn).then(data => {
					if (data) {
						this.list = [data]
					} else {
						this.list = [];
						uni.showToast({
							icon: 'none',
							title: '找不到数据'
						})
					}
				});
			},
			//-----------------------------------------------------------------------------------------------
			//
			// hander
			//
			//-----------------------------------------------------------------------------------------------			
			/**
			 * 核实
			 */
			onExchangeValidHandler: function() {
				this.exchangeValid();
			},
			/**
			 * 兑换
			 */
			onExchangeHandler: function() {
				if (this.valided) {
					this.apiExchange(this.list[0].id).then(data => {
						if (data.status) {
							uni.showToast({
								title: '兑换成功',
							});
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					});
				}
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiExchangeValid: function(sn) {
				return this.$http.get('/v1/member/course_detail', {
					params: {
						id: sn,
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
			apiExchange: function(id) {
				return this.$http.post('/v1/member/doExchange', {
					id
				}).then(res => {
					return Promise.resolve(res.data);
				}).catch(res => {
					uni.showModal({
						showCancel: false,
						title: res.data.code
					})
					return Promise.resolve(res.data);
				})
			},
			//详情
			navToDetailPage(item) {
				//测试数据没有写id，用title代替
				let id = item.id;
				uni.navigateTo({
					url: `/pages/product/product?id=${id}`
				})
			},
			up(){
				uni.switchTab({
					url: '/pages/study/study',
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		color: #111;
	
		.head {
			padding: 50rpx 0;
	
			.gui-icons {
				font-size: 100rpx;
				color: #28b28b;
			}
	
			.success-text {
				margin: 20rpx 0;
				font-size: 36rpx;
				font-weight: bold;
			}
		}
	
		.content-box {
			font-size: 28rpx;
			padding: 0 60rpx;
			text-align: center;
		}
	}
	.lp-flex-column {
		display: flex;
		flex-direction: column;
	}
	.lp-flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
