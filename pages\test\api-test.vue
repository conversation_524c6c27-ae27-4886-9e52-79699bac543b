<template>
	<view class="api-test-page">
		<view class="header">
			<text class="title">API接口测试</text>
			<text class="subtitle">测试前端与后端的数据连接</text>
		</view>

		<view class="test-section">
			<view class="section-title">🔗 连接状态</view>
			<view class="status-card" :class="{ 'connected': apiConnected, 'disconnected': !apiConnected }">
				<text class="status-text">{{ apiConnected ? '✅ 后端连接正常' : '❌ 后端连接失败' }}</text>
				<text class="status-url">{{ apiBaseUrl }}</text>
			</view>
			<button class="test-btn" @click="testConnection">测试连接</button>
		</view>

		<view class="test-section">
			<view class="section-title">👤 用户认证测试</view>
			<view class="auth-info">
				<text class="auth-label">登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</text>
				<text class="auth-label" v-if="userInfo">用户: {{ userInfo.username }}</text>
			</view>
			<view class="button-group">
				<button class="test-btn" @click="testLogin" v-if="!isLoggedIn">测试登录</button>
				<button class="test-btn" @click="testLogout" v-if="isLoggedIn">退出登录</button>
				<button class="test-btn" @click="getCurrentUser" v-if="isLoggedIn">获取用户信息</button>
			</view>
		</view>

		<view class="test-section">
			<view class="section-title">🎓 小组数据测试</view>
			<view class="data-info">
				<text class="data-label">小组数量: {{ groups.length }}</text>
				<text class="data-label">加载状态: {{ groupsLoading ? '加载中...' : '已完成' }}</text>
			</view>
			<button class="test-btn" @click="testGetGroups">获取小组列表</button>
			
			<view class="data-list" v-if="groups.length > 0">
				<view class="data-item" v-for="group in groups" :key="group.id">
					<text class="item-title">{{ group.name }}</text>
					<text class="item-desc">{{ group.description }}</text>
					<text class="item-info">等级: {{ group.level }} | 成员: {{ group.currentMembers }}</text>
				</view>
			</view>
		</view>

		<view class="test-section">
			<view class="section-title">📚 课程数据测试</view>
			<view class="data-info">
				<text class="data-label">课程数量: {{ courses.length }}</text>
				<text class="data-label">加载状态: {{ coursesLoading ? '加载中...' : '已完成' }}</text>
			</view>
			<button class="test-btn" @click="testGetCourses">获取课程列表</button>
			
			<view class="data-list" v-if="courses.length > 0">
				<view class="data-item" v-for="course in courses" :key="course.id">
					<text class="item-title">{{ course.title }}</text>
					<text class="item-desc">{{ course.description }}</text>
					<text class="item-info">分类: {{ course.category }} | 等级: {{ course.level }}</text>
				</view>
			</view>
		</view>

		<view class="test-section">
			<view class="section-title">📊 学习记录测试</view>
			<view class="data-info">
				<text class="data-label">记录数量: {{ learningRecords.length }}</text>
			</view>
			<button class="test-btn" @click="testGetLearningRecords" v-if="isLoggedIn">获取学习记录</button>
			<button class="test-btn" @click="testSubmitLearningRecord" v-if="isLoggedIn">提交测试记录</button>
		</view>

		<view class="test-section">
			<view class="section-title">📝 测试日志</view>
			<scroll-view class="log-container" scroll-y="true">
				<view class="log-item" v-for="(log, index) in logs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message" :class="log.type">{{ log.message }}</text>
				</view>
			</scroll-view>
			<button class="test-btn secondary" @click="clearLogs">清空日志</button>
		</view>
	</view>
</template>

<script>
import apiService from '@/common/js/apiService.js'

export default {
	data() {
		return {
			apiConnected: false,
			apiBaseUrl: 'http://localhost:8005/api',
			isLoggedIn: false,
			userInfo: null,
			groups: [],
			groupsLoading: false,
			courses: [],
			coursesLoading: false,
			learningRecords: [],
			logs: []
		}
	},
	
	onLoad() {
		this.addLog('页面加载完成', 'info');
		this.checkLoginStatus();
		this.testConnection();
	},
	
	methods: {
		// 添加日志
		addLog(message, type = 'info') {
			const time = new Date().toLocaleTimeString();
			this.logs.unshift({ time, message, type });
			if (this.logs.length > 50) {
				this.logs = this.logs.slice(0, 50);
			}
		},
		
		// 清空日志
		clearLogs() {
			this.logs = [];
			this.addLog('日志已清空', 'info');
		},
		
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token');
			const userInfo = uni.getStorageSync('userInfo');
			this.isLoggedIn = !!(token && userInfo);
			this.userInfo = userInfo;
			this.addLog(`登录状态检查: ${this.isLoggedIn ? '已登录' : '未登录'}`, 'info');
		},
		
		// 测试连接
		async testConnection() {
			try {
				this.addLog('正在测试后端连接...', 'info');
				
				const response = await uni.request({
					url: 'http://localhost:8005/health',
					method: 'GET',
					timeout: 5000
				});
				
				if (response.statusCode === 200) {
					this.apiConnected = true;
					this.addLog('✅ 后端连接成功', 'success');
					this.addLog(`服务器信息: ${response.data.message}`, 'info');
				} else {
					this.apiConnected = false;
					this.addLog('❌ 后端连接失败', 'error');
				}
			} catch (error) {
				this.apiConnected = false;
				this.addLog(`❌ 连接错误: ${error.message || '网络请求失败'}`, 'error');
			}
		},
		
		// 测试登录
		async testLogin() {
			try {
				this.addLog('正在测试登录...', 'info');
				
				const response = await apiService.login('pengwei', '123456');
				
				// 保存登录信息
				uni.setStorageSync('token', response.data.token);
				uni.setStorageSync('userInfo', response.data.user);
				
				this.isLoggedIn = true;
				this.userInfo = response.data.user;
				
				this.addLog('✅ 登录成功', 'success');
				this.addLog(`用户: ${response.data.user.username}`, 'info');
				
			} catch (error) {
				this.addLog(`❌ 登录失败: ${error.message}`, 'error');
			}
		},
		
		// 测试登出
		testLogout() {
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			this.isLoggedIn = false;
			this.userInfo = null;
			this.addLog('✅ 已退出登录', 'info');
		},
		
		// 获取当前用户信息
		async getCurrentUser() {
			try {
				this.addLog('正在获取用户信息...', 'info');
				
				const response = await apiService.getCurrentUser();
				this.userInfo = response.data.user;
				
				this.addLog('✅ 用户信息获取成功', 'success');
				this.addLog(`用户详情: ${JSON.stringify(response.data.user)}`, 'info');
				
			} catch (error) {
				this.addLog(`❌ 获取用户信息失败: ${error.message}`, 'error');
			}
		},
		
		// 测试获取小组列表
		async testGetGroups() {
			try {
				this.groupsLoading = true;
				this.addLog('正在获取小组列表...', 'info');
				
				const response = await apiService.getGroups();
				this.groups = response.data.groups;
				
				this.addLog(`✅ 小组列表获取成功，共${this.groups.length}个小组`, 'success');
				
			} catch (error) {
				this.addLog(`❌ 获取小组列表失败: ${error.message}`, 'error');
			} finally {
				this.groupsLoading = false;
			}
		},
		
		// 测试获取课程列表
		async testGetCourses() {
			try {
				this.coursesLoading = true;
				this.addLog('正在获取课程列表...', 'info');
				
				const response = await apiService.getCourses();
				this.courses = response.data.courses;
				
				this.addLog(`✅ 课程列表获取成功，共${this.courses.length}门课程`, 'success');
				
			} catch (error) {
				this.addLog(`❌ 获取课程列表失败: ${error.message}`, 'error');
			} finally {
				this.coursesLoading = false;
			}
		},
		
		// 测试获取学习记录
		async testGetLearningRecords() {
			try {
				this.addLog('正在获取学习记录...', 'info');
				
				const response = await apiService.getLearningRecords();
				this.learningRecords = response.data.records;
				
				this.addLog(`✅ 学习记录获取成功，共${this.learningRecords.length}条记录`, 'success');
				
			} catch (error) {
				this.addLog(`❌ 获取学习记录失败: ${error.message}`, 'error');
			}
		},
		
		// 测试提交学习记录
		async testSubmitLearningRecord() {
			try {
				this.addLog('正在提交测试学习记录...', 'info');
				
				const response = await apiService.submitLearningRecord({
					courseId: 1,
					startTime: new Date().toISOString(),
					progress: 50,
					status: 'started',
					notes: '这是一条测试学习记录'
				});
				
				this.addLog('✅ 学习记录提交成功', 'success');
				
			} catch (error) {
				this.addLog(`❌ 提交学习记录失败: ${error.message}`, 'error');
			}
		}
	}
}
</script>

<style scoped>
.api-test-page {
	padding: 20rpx;
	background: #f5f7fa;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 24rpx;
	color: #666;
}

.test-section {
	background: white;
	border-radius: 15rpx;
	padding: 25rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.status-card {
	padding: 20rpx;
	border-radius: 10rpx;
	margin-bottom: 15rpx;
}

.status-card.connected {
	background: #e8f5e8;
	border: 1rpx solid #52c41a;
}

.status-card.disconnected {
	background: #fff2f0;
	border: 1rpx solid #ff4d4f;
}

.status-text {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}

.status-url {
	font-size: 22rpx;
	color: #666;
}

.test-btn {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 10rpx;
	padding: 15rpx 25rpx;
	font-size: 24rpx;
	margin: 5rpx 10rpx 5rpx 0;
}

.test-btn.secondary {
	background: #999;
}

.test-btn:active {
	opacity: 0.8;
}

.auth-info, .data-info {
	margin-bottom: 15rpx;
}

.auth-label, .data-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.button-group {
	display: flex;
	flex-wrap: wrap;
}

.data-list {
	margin-top: 15rpx;
}

.data-item {
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 15rpx;
	margin-bottom: 10rpx;
}

.item-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.item-desc {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.item-info {
	font-size: 20rpx;
	color: #999;
}

.log-container {
	height: 300rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 10rpx;
	margin-bottom: 15rpx;
}

.log-item {
	margin-bottom: 10rpx;
	padding: 8rpx;
	background: white;
	border-radius: 5rpx;
}

.log-time {
	font-size: 18rpx;
	color: #999;
	display: block;
	margin-bottom: 3rpx;
}

.log-message {
	font-size: 22rpx;
	color: #333;
}

.log-message.success {
	color: #52c41a;
}

.log-message.error {
	color: #ff4d4f;
}

.log-message.info {
	color: #1890ff;
}
</style>
