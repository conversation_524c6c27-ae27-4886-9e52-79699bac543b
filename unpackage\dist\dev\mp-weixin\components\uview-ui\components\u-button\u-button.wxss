@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-8ef4aa1a {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-8ef4aa1a {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-8ef4aa1a {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-8ef4aa1a::after {
  border: none;
}
.u-hover-class.data-v-8ef4aa1a {
  opacity: 0.7;
}
.u-primary-light.data-v-8ef4aa1a {
  color: #ecf5ff;
}
.u-warning-light.data-v-8ef4aa1a {
  color: #fdf6ec;
}
.u-success-light.data-v-8ef4aa1a {
  color: #f5fff0;
}
.u-error-light.data-v-8ef4aa1a {
  color: #fef0f0;
}
.u-info-light.data-v-8ef4aa1a {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-8ef4aa1a {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-8ef4aa1a {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-8ef4aa1a {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-8ef4aa1a {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-8ef4aa1a {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-8ef4aa1a {
  color: #398ade;
}
.u-warning-dark.data-v-8ef4aa1a {
  color: #f1a532;
}
.u-success-dark.data-v-8ef4aa1a {
  color: #53c21d;
}
.u-error-dark.data-v-8ef4aa1a {
  color: #e45656;
}
.u-info-dark.data-v-8ef4aa1a {
  color: #767a82;
}
.u-primary-dark-bg.data-v-8ef4aa1a {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-8ef4aa1a {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-8ef4aa1a {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-8ef4aa1a {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-8ef4aa1a {
  background-color: #767a82;
}
.u-primary-disabled.data-v-8ef4aa1a {
  color: #9acafc;
}
.u-warning-disabled.data-v-8ef4aa1a {
  color: #f9d39b;
}
.u-success-disabled.data-v-8ef4aa1a {
  color: #a9e08f;
}
.u-error-disabled.data-v-8ef4aa1a {
  color: #f7b2b2;
}
.u-info-disabled.data-v-8ef4aa1a {
  color: #c4c6c9;
}
.u-primary.data-v-8ef4aa1a {
  color: #3c9cff;
}
.u-warning.data-v-8ef4aa1a {
  color: #f9ae3d;
}
.u-success.data-v-8ef4aa1a {
  color: #5ac725;
}
.u-error.data-v-8ef4aa1a {
  color: #f56c6c;
}
.u-info.data-v-8ef4aa1a {
  color: #909399;
}
.u-primary-bg.data-v-8ef4aa1a {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-8ef4aa1a {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-8ef4aa1a {
  background-color: #5ac725;
}
.u-error-bg.data-v-8ef4aa1a {
  background-color: #f56c6c;
}
.u-info-bg.data-v-8ef4aa1a {
  background-color: #909399;
}
.u-main-color.data-v-8ef4aa1a {
  color: #303133;
}
.u-content-color.data-v-8ef4aa1a {
  color: #606266;
}
.u-tips-color.data-v-8ef4aa1a {
  color: #909193;
}
.u-light-color.data-v-8ef4aa1a {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-8ef4aa1a {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-8ef4aa1a {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-8ef4aa1a {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-8ef4aa1a {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-8ef4aa1a::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-btn.data-v-8ef4aa1a::after {
  border: none;
}
.u-btn.data-v-8ef4aa1a {
  position: relative;
  border: 0;
  display: inline-flex;
  overflow: visible;
  line-height: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 40rpx;
  z-index: 1;
  box-sizing: border-box;
  transition: all 0.15s;
}
.u-btn--bold-border.data-v-8ef4aa1a {
  border: 1px solid #ffffff;
}
.u-btn--default.data-v-8ef4aa1a {
  color: #606266;
  border-color: #c0c4cc;
  background-color: #ffffff;
}
.u-btn--primary.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #2979ff;
  background-color: #2979ff;
}
.u-btn--success.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #19be6b;
  background-color: #19be6b;
}
.u-btn--error.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #fa3534;
  background-color: #fa3534;
}
.u-btn--warning.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #ff9900;
  background-color: #ff9900;
}
.u-btn--default--disabled.data-v-8ef4aa1a {
  color: #ffffff;
  border-color: #e4e7ed;
  background-color: #ffffff;
}
.u-btn--primary--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #a0cfff !important;
  background-color: #a0cfff !important;
}
.u-btn--success--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #71d5a1 !important;
  background-color: #71d5a1 !important;
}
.u-btn--error--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #fab6b6 !important;
  background-color: #fab6b6 !important;
}
.u-btn--warning--disabled.data-v-8ef4aa1a {
  color: #ffffff !important;
  border-color: #fcbd71 !important;
  background-color: #fcbd71 !important;
}
.u-btn--primary--plain.data-v-8ef4aa1a {
  color: #2979ff !important;
  border-color: #a0cfff !important;
  background-color: #ecf5ff !important;
}
.u-btn--success--plain.data-v-8ef4aa1a {
  color: #19be6b !important;
  border-color: #71d5a1 !important;
  background-color: #dbf1e1 !important;
}
.u-btn--error--plain.data-v-8ef4aa1a {
  color: #fa3534 !important;
  border-color: #fab6b6 !important;
  background-color: #fef0f0 !important;
}
.u-btn--warning--plain.data-v-8ef4aa1a {
  color: #ff9900 !important;
  border-color: #fcbd71 !important;
  background-color: #fdf6ec !important;
}
.u-hairline-border.data-v-8ef4aa1a:after {
  content: ' ';
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  left: 0;
  top: 0;
  width: 199.8%;
  height: 199.7%;
  -webkit-transform: scale(0.5, 0.5);
  transform: scale(0.5, 0.5);
  border: 1px solid currentColor;
  z-index: 1;
}
.u-wave-ripple.data-v-8ef4aa1a {
  z-index: 0;
  position: absolute;
  border-radius: 100%;
  background-clip: padding-box;
  pointer-events: none;
  -webkit-user-select: none;
          user-select: none;
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 1;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.u-wave-ripple.u-wave-active.data-v-8ef4aa1a {
  opacity: 0;
  -webkit-transform: scale(2);
          transform: scale(2);
  transition: opacity 1s linear, -webkit-transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear, -webkit-transform 0.4s linear;
}
.u-round-circle.data-v-8ef4aa1a {
  border-radius: 100rpx;
}
.u-round-circle.data-v-8ef4aa1a::after {
  border-radius: 100rpx;
}
.u-loading.data-v-8ef4aa1a::after {
  background-color: rgba(255, 255, 255, 0.35);
}
.u-size-default.data-v-8ef4aa1a {
  font-size: 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.u-size-medium.data-v-8ef4aa1a {
  display: inline-flex;
  width: auto;
  font-size: 26rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 80rpx;
}
.u-size-mini.data-v-8ef4aa1a {
  display: inline-flex;
  width: auto;
  font-size: 22rpx;
  padding-top: 1px;
  height: 50rpx;
  line-height: 50rpx;
  padding: 0 20rpx;
}
.u-primary-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #2b85e4 !important;
}
.u-default-plain-hover.data-v-8ef4aa1a {
  color: #2b85e4 !important;
  background: #ecf5ff !important;
}
.u-success-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #18b566 !important;
}
.u-warning-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #f29100 !important;
}
.u-error-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #dd6161 !important;
}
.u-info-plain-hover.data-v-8ef4aa1a {
  color: #ffffff !important;
  background: #82848a !important;
}
.u-default-hover.data-v-8ef4aa1a {
  color: #2b85e4 !important;
  border-color: #2b85e4 !important;
  background-color: #ecf5ff !important;
}
.u-primary-hover.data-v-8ef4aa1a {
  background: #2b85e4 !important;
  color: #fff;
}
.u-success-hover.data-v-8ef4aa1a {
  background: #18b566 !important;
  color: #fff;
}
.u-info-hover.data-v-8ef4aa1a {
  background: #82848a !important;
  color: #fff;
}
.u-warning-hover.data-v-8ef4aa1a {
  background: #f29100 !important;
  color: #fff;
}
.u-error-hover.data-v-8ef4aa1a {
  background: #dd6161 !important;
  color: #fff;
}

