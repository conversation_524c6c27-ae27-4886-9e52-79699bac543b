@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-95426c3c {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-95426c3c {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-95426c3c {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-95426c3c {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-95426c3c {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-95426c3c {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-95426c3c::after {
  border: none;
}
.u-hover-class.data-v-95426c3c {
  opacity: 0.7;
}
.u-primary-light.data-v-95426c3c {
  color: #ecf5ff;
}
.u-warning-light.data-v-95426c3c {
  color: #fdf6ec;
}
.u-success-light.data-v-95426c3c {
  color: #f5fff0;
}
.u-error-light.data-v-95426c3c {
  color: #fef0f0;
}
.u-info-light.data-v-95426c3c {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-95426c3c {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-95426c3c {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-95426c3c {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-95426c3c {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-95426c3c {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-95426c3c {
  color: #398ade;
}
.u-warning-dark.data-v-95426c3c {
  color: #f1a532;
}
.u-success-dark.data-v-95426c3c {
  color: #53c21d;
}
.u-error-dark.data-v-95426c3c {
  color: #e45656;
}
.u-info-dark.data-v-95426c3c {
  color: #767a82;
}
.u-primary-dark-bg.data-v-95426c3c {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-95426c3c {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-95426c3c {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-95426c3c {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-95426c3c {
  background-color: #767a82;
}
.u-primary-disabled.data-v-95426c3c {
  color: #9acafc;
}
.u-warning-disabled.data-v-95426c3c {
  color: #f9d39b;
}
.u-success-disabled.data-v-95426c3c {
  color: #a9e08f;
}
.u-error-disabled.data-v-95426c3c {
  color: #f7b2b2;
}
.u-info-disabled.data-v-95426c3c {
  color: #c4c6c9;
}
.u-primary.data-v-95426c3c {
  color: #3c9cff;
}
.u-warning.data-v-95426c3c {
  color: #f9ae3d;
}
.u-success.data-v-95426c3c {
  color: #5ac725;
}
.u-error.data-v-95426c3c {
  color: #f56c6c;
}
.u-info.data-v-95426c3c {
  color: #909399;
}
.u-primary-bg.data-v-95426c3c {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-95426c3c {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-95426c3c {
  background-color: #5ac725;
}
.u-error-bg.data-v-95426c3c {
  background-color: #f56c6c;
}
.u-info-bg.data-v-95426c3c {
  background-color: #909399;
}
.u-main-color.data-v-95426c3c {
  color: #303133;
}
.u-content-color.data-v-95426c3c {
  color: #606266;
}
.u-tips-color.data-v-95426c3c {
  color: #909193;
}
.u-light-color.data-v-95426c3c {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-95426c3c {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-95426c3c {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-95426c3c {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-95426c3c {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-95426c3c::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@font-face {
  font-family: 'iconfont';
  src: url(data:font/ttf;base64,AAEAAAALAIAAAwAwR1NVQiCLJXoAAAE4AAAAVE9TLzI8UEkLAAABjAAAAGBjbWFw52O2WwAAAfQAAAFwZ2x5Zrep7IAAAANsAAAATGhlYWQjOSmNAAAA4AAAADZoaGVhB94DgwAAALwAAAAkaG10eAgAAAAAAAHsAAAACGxvY2EAJgAAAAADZAAAAAZtYXhwAQ0AHwAAARgAAAAgbmFtZRCjPLAAAAO4AAACZ3Bvc3RRHtfqAAAGIAAAADQAAQAAA4D/gABcBAAAAAAABAAAAQAAAAAAAAAAAAAAAAAAAAIAAQAAAAEAADpZisBfDzz1AAsEAAAAAADgDXMqAAAAAOANcyoAAAAABAACPAAAAAgAAgAAAAAAAAABAAAAAgATAAEAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKADAAPgACREZMVAAObGF0bgAaAAQAAAAAAAAAAQAAAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAQEAAGQAAUAAAKJAswAAACPAokCzAAAAesAMgEIAAACAAUDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFBmRWQAwOY75jsDgP+AAAAD3ACAAAAAAQAAAAAAAAAAAAAAAAACBAAAAAQAAAAAAAAFAAAAAwAAACwAAAAEAAABVAABAAAAAABOAAMAAQAAACwAAwAKAAABVAAEACIAAAAEAAQAAQAA5jv//wAA5jv//wAAAAEABAAAAAEAAAEGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAABwAAAAAAAAAAQAA5jsAAOY7AAAAAQAAAAAAJgAAAAEAAAAAA4UCPAASAAAlJicBJjQ2MhcJATYyFhQHAQ4BAfoMCf6rDRkjDQE4ATgMIxkM/qoIFZEDCAFWDSMZDP7IATgMGSMN/qoIBgAAABIA3gABAAAAAAAAABMAAAABAAAAAAABAAgAEwABAAAAAAACAAcAGwABAAAAAAADAAgAIgABAAAAAAAEAAgAKgABAAAAAAAFAAsAMgABAAAAAAAGAAgAPQABAAAAAAAKACsARQABAAAAAAALABMAcAADAAEECQAAACYAgwADAAEECQABABAAqQADAAEECQACAA4AuQADAAEECQADABAAxwADAAEECQAEABAA1wADAAEECQAFABYA5wADAAEECQAGABAA/QADAAEECQAKAFYBDQADAAEECQALACYBY0NyZWF0ZWQgYnkgaWNvbmZvbnRpY29uZm9udFJlZ3VsYXJpY29uZm9udGljb25mb250VmVyc2lvbiAxLjBpY29uZm9udEdlbmVyYXRlZCBieSBzdmcydHRmIGZyb20gRm9udGVsbG8gcHJvamVjdC5odHRwOi8vZm9udGVsbG8uY29tAEMAcgBlAGEAdABlAGQAIABiAHkAIABpAGMAbwBuAGYAbwBuAHQAaQBjAG8AbgBmAG8AbgB0AFIAZQBnAHUAbABhAHIAaQBjAG8AbgBmAG8AbgB0AGkAYwBvAG4AZgBvAG4AdABWAGUAcgBzAGkAbwBuACAAMQAuADAAaQBjAG8AbgBmAG8AbgB0AEcAZQBuAGUAcgBhAHQAZQBkACAAYgB5ACAAcwB2AGcAMgB0AHQAZgAgAGYAcgBvAG0AIABGAG8AbgB0AGUAbABsAG8AIABwAHIAbwBqAGUAYwB0AC4AaAB0AHQAcAA6AC8ALwBmAG8AbgB0AGUAbABsAG8ALgBjAG8AbQAAAgAAAAAAAAAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAQIBAwAKYXJyb3ctZG93bgAA) format("truetype");
}
.iconfont.data-v-95426c3c {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

