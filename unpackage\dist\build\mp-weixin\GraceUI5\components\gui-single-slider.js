(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["GraceUI5/components/gui-single-slider"],{3304:function(t,e,i){"use strict";var n=i("a0b5"),a=i.n(n);a.a},"961b":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={name:"gui-single-slider",props:{barHeight:{type:Number,default:32},barWidth:{type:Number,default:168},barColor:{type:String,default:"#FFFFFF"},barBgColor:{type:String,default:"linear-gradient(to right, #3688FF,#3688FF)"},bglineSize:{type:Number,default:2},bglineColor:{type:String,default:"rgba(54,136,255,0.5)"},bglineAColor:{type:String,default:"#3688FF"},barText:{type:String,default:""},barTextSize:{type:String,default:"20rpx"},borderRadius:{type:String,default:"32rpx"},canSlide:{type:Boolean,default:!0}},data:function(){return{left:0,startLeft:0,width:0,barWidthPX:30}},mounted:function(){this.init()},methods:{init:function(){var e=this;t.createSelectorQuery().in(this).select("#gracesgslider").fields({size:!0,rect:!0},(function(i){null!=i?(e.startLeft=i.left,e.width=i.width,e.barWidthPX=t.upx2px(e.barWidth)):setTimeout((function(){e.init()}),100)})).exec()},touchstart:function(t){if(this.canSlide){var e=t.touches[0]||t.changedTouches[0];this.changeBar(e.pageX)}},touchmove:function(t){if(this.canSlide){var e=t.touches[0]||t.changedTouches[0];this.changeBar(e.pageX)}},touchend:function(t){if(this.canSlide){var e=t.touches[0]||t.changedTouches[0];this.changeBar(e.pageX,!0)}},changeBar:function(t){var e=t-this.startLeft;if(e<=0)this.left=0,this.$emit("change",0);else if(e+this.barWidthPX>this.width)e=this.width-this.barWidthPX,this.left=e,this.$emit("change",100);else{this.left=e;var i=this.left/(this.width-this.barWidthPX);this.$emit("change",Math.round(100*i))}},setProgress:function(t){var e=this;this.width<1?setTimeout((function(){e.setProgress(t)})):(t<0&&(t=0),t>100&&(t=100),this.left=t/100*(this.width-this.barWidthPX))}}};e.default=i}).call(this,i("df3c")["default"])},a0b5:function(t,e,i){},a88a:function(t,e,i){"use strict";i.r(e);var n=i("bc3a"),a=i("d18d");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("3304");var u=i("828b"),s=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,"2675abb0",null,!1,n["a"],void 0);e["default"]=s.exports},bc3a:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]},d18d:function(t,e,i){"use strict";i.r(e);var n=i("961b"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'GraceUI5/components/gui-single-slider-create-component',
    {
        'GraceUI5/components/gui-single-slider-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("a88a"))
        })
    },
    [['GraceUI5/components/gui-single-slider-create-component']]
]);
