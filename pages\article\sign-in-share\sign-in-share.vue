<template>
	<gui-page :fullPage="true" :customHeader="true" class="root-box">
		<!-- 自定义头部导航 -->
		<view slot="gHeader">
			<view class="gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding head-box">
				<!-- 使用组件实现返回按钮及返回首页按钮 -->
				<gui-header-leading></gui-header-leading>
				<!-- 导航文本此处也可以是其他自定义内容 -->
				<view class="gui-header-content nav-box">

				</view>
				<!-- 如果右侧有其他内容可以利用条件编译和定位来实现-->
			</view>
		</view>
		<!-- 页面主体 -->
		<view slot="gBody" class="body-box lp-flex-column">
			<view class="content-box lp-flex-column">
				<!-- 说明 -->
				<!-- 生成海报 -->
				<view class="loading" v-if="!isReady">海报生成中...</view>
				<!-- 图片展示由自己实现 -->
				<view class="pop-box lp-flex-column lp-flex-center" v-else>
					<view class="poster-box lp-flex-center">
						<image :src="posterImage || ''" mode="heightFix" class="posterImage"></image>
					</view>
					<view class="lp-flex-center btn-box">
						<view class="lp-flex-column lp-flex-center btn">
							<button type="primary" size="mini" @tap.prevent.stop="saveImage()">
								<view class="lp-flex-center icon-box">
									<text class="gui-icons">&#xe63d;</text>
								</view>
							</button>
							<text>保存图片</text>
						</view>
						<view class="lp-flex-column lp-flex-center btn">
							<button type="primary" open-type="share" size="mini">
								<view class="lp-flex-center icon-box"> 
									<text class="gui-icons">&#xe63e;</text>
								</view>
							</button>
							<text>微信分享</text>
						</view>
					</view>
				</view>
				<!-- 画布 -->
				<view class="hideCanvasView">
					<canvas class="hideCanvas" id="default_PosterCanvasId" canvas-id="default_PosterCanvasId" :style="{width: (poster.width||10) + 'px', height: (poster.height||10) + 'px'}"></canvas>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import _app from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/app.js';
	import {
		getSharePoster
	} from '@/js_sdk/QuShe-SharerPoster/QS-SharePoster/QS-SharePoster.js';
	export default {
		components: {},
		data() {
			return {
				isReady: false,
				article_id: 4,
				poster: {},
				posterImage: '',
				canvasId: 'default_PosterCanvasId',
				course: null,
				article: null,
				code: null
			}
		},
		onLoad(options) {
			this.article_id = options.article_id;
			// 获取分享数据
			this.ready();
		},
		created() {
			this.userInfo = this.$store.state.user.userInfo;

			const monthEnglish = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Spt", "Oct", "Nov", "Dec"];
			this.month = monthEnglish[new Date().getMonth()];
			this.date = new Date().getDate();
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			ready: function() {
				uni.showLoading({
					title:'加载数据...'
				});
				this.apiGetShareDate(this.article_id).then(data => {
					uni.hideLoading();
					this.article = data;
					this.course = this.article.course;
					this.code = this.article.code;
					
					this.shareFc();
				});
			},
			async shareFc() {
				try {
					_app.log('准备生成:' + new Date())
					const d = await getSharePoster({
						_this: this, //若在组件中使用 必传
						type: 'testShareType',
						formData: {
							//访问接口获取背景图携带自定义数据

						},
						posterCanvasId: this.canvasId, //canvasId
						delayTimeScale: 20, //延时系数
						/* background: {
							width: 1080,
							height: 1920,
							backgroundColor: '#666'
						}, */
						backgroundImage: '/static/imgs/sign_in_bg.jpg',
						drawArray: ({
							bgObj,
							type,
							bgScale
						}) => {
							const dx = bgObj.width * 0.3;
							const fontSize = bgObj.width * 0.040;
							const lineHeight = bgObj.height * 0.04;

							//可直接return数组，也可以return一个promise对象, 但最终resolve一个数组, 这样就可以方便实现后台可控绘制海报
							return new Promise((rs, rj) => {
								rs([
									// 标题
									/* {
										type: 'text',
										text: '坚持打卡天数',
										fontWeight: 'normal',
										size: fontSize,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dx: 410,
										dy: 200
									}, */
									// 天数
									{
										type: 'text',
										text: this.article.clock_num,
										fontWeight: 'blod',
										size: fontSize * 3,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dy: 350,
										infoCallBack(textLength) {
											console.log(textLength,bgObj.width);
											return {
												dx: (bgObj.width - textLength) / 2,
											}
										}
									},
									// 内容框

									// 用户信息
									{
										type: 'image',
										url: this.userInfo.avatarUrl,
										alpha: 1,
										dx: 100,
										dy: 550,
										infoCallBack(imageInfo) {
											let scale = bgObj.width * 0.2 / imageInfo.height;
											return {
												circleSet: {}, // 圆形图片 , 若circleSet与roundRectSet一同设置 优先circleSet设置
												dWidth: 100, // 因为设置了圆形图片 所以要乘以2
												dHeight: 100,
												/* roundRectSet: { // 圆角矩形
													r: imageInfo.width * .1
												} */
											}
										}
									},
									{
										type: 'text',
										text: this.userInfo.nickName,
										fontWeight: 'normal',
										size: fontSize,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dx: 240,
										dy: 580
									},
									{
										type: 'text',
										text: this.course.title,
										fontWeight: 'normal',
										size: fontSize * 0.8,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dx: 240,
										dy: 640
									},
									// 打卡日期
									{
										type: 'text',
										text: this.month,
										fontWeight: 'normal',
										size: fontSize * 0.6,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dx: 830,
										dy: 555
									},
									{
										type: 'text',
										text: this.date,
										fontWeight: 'normal',
										size: fontSize * 0.6,
										color: 'white',
										alpha: 1,
										textAlign: 'left',
										textBaseline: 'middle',
										serialNum: 1,
										dx: this.date > 9 ? 837 : 845,
										dy: 610
									},
									// 课程封面
									{
										type: 'image',
										url: this.article.picture,
										alpha: 1,
										dx: 70,
										dy: 720,
										dWidth: bgObj.width - 140,
										dHeight: 1000,
										mode: 'aspectFill'
									},
									// 课程信息
									{
										type: 'text',
										text: this.article.publish_date,
										fontWeight: 'blod',
										size: fontSize * 1.2,
										color: '#333',
										alpha: 1,
										textAlign: 'center',
										textBaseline: 'middle',
										serialNum: 1,
										infoCallBack(textLength) {
											return {
												dx: bgObj.width / 2,
												dy: 1780
											}
										}
									},
									{
										type: 'text',
										text: this.article.title,
										fontWeight: 'normal',
										size: fontSize,
										color: '#666',
										alpha: 1,
										textAlign: 'center',
										textBaseline: 'middle',
										serialNum: 1,
										infoCallBack(textLength) {
											return {
												dx: bgObj.width / 2,
												dy: 1840
											}
										}
									},
									// 海报出处
									// 二维码
									{
										type: 'image',
										url: this.code.url,
										alpha: 1,
										dx: bgObj.width - 285,
										dy: bgObj.height - 340,
										dWidth: 220,
										dHeight: 220,
									},
								]);
							})
						},
						setCanvasWH: ({
							bgObj,
							type,
							bgScale
						}) => { // 为动态设置画布宽高的方法，
							this.poster = bgObj;
						}
					});
					_app.log('海报生成成功, 时间:' + new Date() + '， 临时路径: ' + d.poster.tempFilePath)
					this.posterImage = d.poster.tempFilePath;
					this.isReady = true;
					//this.$refs.popup.show()
				} catch (e) {
					_app.hideLoading();
					_app.showToast(JSON.stringify(e));
					console.log(JSON.stringify(e));
				}
			},
			saveImage() {
				console.log('saveImage', this.posterImage);
				uni.saveImageToPhotosAlbum({
					filePath: this.posterImage,
					success(res) {
						_app.showToast('保存成功');
					}
				});
			},
			share() {
				// #ifdef APP-PLUS
				_app.getShare(false, false, 2, '', '', '', this.poster.finalPath, false, false);
				// #endif

				// #ifndef APP-PLUS
				//_app.showToast('分享了');
				// #endif
			},
			hideQr() {
				this.$refs.popup.hide()
			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onShareAppMessage(res) {
				return {
					path: "/pages/article/detail/index?article_id=" + this.article_id,
					title: '我正在参加人民中国“' + this.course.title + '"你也一起来学习吧！',
					imageUrl: this.posterImage
				}
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiGetShareDate: function(article_id) {
				return this.$http.post('/v1/share/code', {
					path: '/pages/article/detail/index?article_id=' + article_id,
					article_id
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.body-box {
		flex: 1;

		.content-box {
			flex: 1;

			.pop-box {
				flex: 1;

				.poster-box {
					margin: 30rpx;

					.posterImage {
						border-radius: 20rpx;
						height: 70vh;
					}

				}

				.btn-box {
					color: #666;
					font-size: 28rpx;

					.btn {
						margin: 20rpx 40rpx;

						.gui-icons {
							font-size: 42rpx;
						}
					}

					button {
						width: 100rpx;
						height: 100rpx;
						border-radius: 50%;
						margin-bottom: 10rpx;

					}
				}
			}
		}
	}

	.loading {
		padding: 50rpx;
		color: $uni-text-color-grey;
	}

	.hideCanvasView {
		position: relative;
	}

	.hideCanvas {
		position: fixed;
		top: -99999rpx;
		left: -99999rpx;
		z-index: -99999;
	}
</style>
