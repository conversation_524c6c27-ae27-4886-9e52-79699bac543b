<template>
	<view class="null-box">
		<image class="null-img" src="/static/bgimg/null.png"></image>
		<view class="null-text">{{ text }}</view>
	</view>
</template>

<script>
	export default {
		props:["text"],
		data(){
			return {
				
			}
		}
	}
</script>

<style scoped>
	.null-box{
		text-align: center;
		height: 100vh;
		background-color: #F9F9F9;
	}
	.null-img{
		margin-top: 200rpx;
		width: 287rpx;
		height: 182rpx;
	}
	.null-text{
		text-align: center;
		color: #C0BDC0;
	}
</style>
