@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page,
.content {
  background: #f8f8f8;
  height: 100%;
}
.swiper-box {
  height: calc(100% - 40px);
}
.list-scroll-content {
  height: 100%;
}
.navbar {
  display: flex;
  height: 40px;
  padding: 0 5px;
  background: #fff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}
.navbar .nav-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 15px;
  color: #303133;
  position: relative;
}
.navbar .nav-item.current {
  color: #fa436a;
}
.navbar .nav-item.current:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 44px;
  height: 0;
  border-bottom: 2px solid #fa436a;
}
.uni-swiper-item {
  height: auto;
}
.order-item {
  display: flex;
  flex-direction: column;
  padding-left: 30rpx;
  background: #fff;
  margin-top: 16rpx;
  /* 多条商品 */
  /* 单条商品 */
}
.order-item .i-top {
  display: flex;
  align-items: center;
  height: 80rpx;
  padding-right: 30rpx;
  font-size: 28rpx;
  color: #303133;
  position: relative;
}
.order-item .i-top .time {
  flex: 1;
}
.order-item .i-top .state {
  color: #fa436a;
}
.order-item .i-top .del-btn {
  padding: 10rpx 0 10rpx 36rpx;
  font-size: 32rpx;
  color: #909399;
  position: relative;
}
.order-item .i-top .del-btn:after {
  content: '';
  width: 0;
  height: 30rpx;
  border-left: 1px solid #DCDFE6;
  position: absolute;
  left: 20rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.order-item .goods-box {
  height: 160rpx;
  padding: 20rpx 0;
  white-space: nowrap;
}
.order-item .goods-box .goods-item {
  width: 120rpx;
  height: 120rpx;
  display: inline-block;
  margin-right: 24rpx;
}
.order-item .goods-box .goods-img {
  display: block;
  width: 100%;
  height: 100%;
}
.order-item .goods-box-single {
  display: flex;
  padding: 20rpx 0;
}
.order-item .goods-box-single .goods-img {
  display: block;
  width: 120rpx;
  height: 120rpx;
}
.order-item .goods-box-single .right {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx 0 24rpx;
  overflow: hidden;
}
.order-item .goods-box-single .right .title {
  font-size: 30rpx;
  color: #303133;
  line-height: 1;
}
.order-item .goods-box-single .right .attr-box {
  font-size: 26rpx;
  color: #909399;
  padding: 10rpx 12rpx;
}
.order-item .goods-box-single .right .price {
  font-size: 30rpx;
  color: #303133;
}
.order-item .goods-box-single .right .price:before {
  content: '￥';
  font-size: 24rpx;
  margin: 0 2rpx 0 8rpx;
}
.order-item .price-box {
  display: flex;
  justify-content: flex-end;
  align-items: baseline;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  color: #909399;
}
.order-item .price-box .num {
  margin: 0 8rpx;
  color: #303133;
}
.order-item .price-box .price {
  font-size: 32rpx;
  color: #303133;
}
.order-item .price-box .price:before {
  content: '￥';
  font-size: 24rpx;
  margin: 0 2rpx 0 8rpx;
}
.order-item .action-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100rpx;
  position: relative;
  padding-right: 30rpx;
}
.order-item .action-btn {
  width: 160rpx;
  height: 60rpx;
  margin: 0;
  margin-left: 24rpx;
  padding: 0;
  text-align: center;
  line-height: 60rpx;
  font-size: 26rpx;
  color: #303133;
  background: #fff;
  border-radius: 100px;
}
.order-item .action-btn:after {
  border-radius: 100px;
}
.order-item .action-btn.recom {
  background: #fff9f9;
  color: #fa436a;
}
.order-item .action-btn.recom:after {
  border-color: #f7bcc8;
}
/* load-more */
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 80rpx;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text {
  font-size: 28rpx;
  color: #999;
}
.uni-load-more__img {
  height: 24px;
  width: 24px;
  margin-right: 10px;
}
.uni-load-more__img > view {
  position: absolute;
}
.uni-load-more__img > view view {
  width: 6px;
  height: 2px;
  border-top-left-radius: 1px;
  border-bottom-left-radius: 1px;
  background: #999;
  position: absolute;
  opacity: .2;
  -webkit-transform-origin: 50%;
          transform-origin: 50%;
  -webkit-animation: load 1.56s ease infinite;
          animation: load 1.56s ease infinite;
}
.uni-load-more__img > view view:nth-child(1) {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  top: 2px;
  left: 9px;
}
.uni-load-more__img > view view:nth-child(2) {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  top: 11px;
  right: 0;
}
.uni-load-more__img > view view:nth-child(3) {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
  bottom: 2px;
  left: 9px;
}
.uni-load-more__img > view view:nth-child(4) {
  top: 11px;
  left: 0;
}
.load1,
.load2,
.load3 {
  height: 24px;
  width: 24px;
}
.load2 {
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg);
}
.load3 {
  -webkit-transform: rotate(60deg);
          transform: rotate(60deg);
}
.load1 view:nth-child(1) {
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.load2 view:nth-child(1) {
  -webkit-animation-delay: .13s;
          animation-delay: .13s;
}
.load3 view:nth-child(1) {
  -webkit-animation-delay: .26s;
          animation-delay: .26s;
}
.load1 view:nth-child(2) {
  -webkit-animation-delay: .39s;
          animation-delay: .39s;
}
.load2 view:nth-child(2) {
  -webkit-animation-delay: .52s;
          animation-delay: .52s;
}
.load3 view:nth-child(2) {
  -webkit-animation-delay: .65s;
          animation-delay: .65s;
}
.load1 view:nth-child(3) {
  -webkit-animation-delay: .78s;
          animation-delay: .78s;
}
.load2 view:nth-child(3) {
  -webkit-animation-delay: .91s;
          animation-delay: .91s;
}
.load3 view:nth-child(3) {
  -webkit-animation-delay: 1.04s;
          animation-delay: 1.04s;
}
.load1 view:nth-child(4) {
  -webkit-animation-delay: 1.17s;
          animation-delay: 1.17s;
}
.load2 view:nth-child(4) {
  -webkit-animation-delay: 1.3s;
          animation-delay: 1.3s;
}
.load3 view:nth-child(4) {
  -webkit-animation-delay: 1.43s;
          animation-delay: 1.43s;
}
@-webkit-keyframes load {
0% {
    opacity: 1;
}
100% {
    opacity: .2;
}
}
/* 历史列表 */
.item-box {
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  padding: 30rpx 0;
  border-bottom: solid 1px #eeeeee;
}
.item-box .top-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.item-box .top-box .publish-date {
  color: #999;
}
.item-box .center-box {
  display: flex;
  justify-content: space-between;
}
.item-box .center-box .left-box {
  display: flex;
  flex-direction: column;
}
.item-box .center-box .left-box .title {
  flex: 1;
  font-size: 32rpx;
}
.item-box .center-box .left-box .info-box {
  font-size: 24rpx;
}
.item-box .center-box .left-box .info-box .gui-icons {
  color: #dd524d;
}
.item-box .center-box .left-box .info-box .txt {
  color: #999;
}
.item-box .center-box .cover-box {
  margin-left: 30rpx;
}
.item-box .center-box .cover-box .cover {
  width: 260rpx;
  height: 150rpx;
  border-radius: 5px;
}

