<template>
	<view class="root-box">
		<scroll-view scroll-y="true" refresher-enabled="true" :refresher-triggered="triggered" @refresherrefresh="onRefreshHandler"
		 @scroll="onScrollHandler" @scrolltolower="onScrolltolowerHandler" @refresherrestore="onRestoreHandler">
			<navigator :url="'../article/detail/index?article_id='+newest.id" hover-class="none">
				<!-- 最新文章 -->
				<view class="newest-box">
					<view class="publish-date">{{newest.publish_date}}</view>
					<view class="newest-item">
						<!-- 文章封面 -->
						<image class="cover" mode="aspectFill" :src="newest.picture+'?x-oss-process=image/resize,m_lfit,h_300,w_300'"></image>
						<view class="content-box lp-flex-column lp-flex-space-between">
							<view class="head-box lp-flex lp-flex-space-between">
								<text class="type-box tc-type">{{newest.theme_text}}</text>
								<view class="type-box">
									<text class="gui-icons">{{newest.type == 1 ? '&#xe62f;' : '&#xe656;'}} </text>
									<text class="lang">{{newest.trans_text}}</text>
								</view>
							</view>
							<view class="foot-box lp-flex lp-flex-space-between">
								<image class="bg" src="@/static/imgs/mask.png"></image>
								<view>
									<!-- 标题 -->
									<view class="title-box"><text>{{newest.title}}</text></view>
									<text class="sign-in">{{newest ? newest.clocks_num : 0}}人 已打卡</text>
								</view>
								<view class="lp-flex-center sign-btn">
									<text>进入打卡</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</navigator>
			<view class="history-box" :class="{fixed:articleScrollFixed}">
				<!-- 历史头部 -->
				<view class="head-box">
					<text>历史</text>
					<view @tap="onFilterTapHandler">
						<text>筛选</text>
						<text class="gui-icons">&#xe686;</text>
					</view>
				</view>
				<!-- 历史列表 -->
				<view class="list-box">
					<navigator v-for="(item,index) in list" :key="item.id" :url="'../article/detail/index?article_id='+item.id" hover-class="none">
						<view class="item-box">
							<view class="top-box">
								<text class="publish-date">{{item.publish_date}}</text>
								<view class="type-box">
									<text class="gui-icons">{{item.type == 1 ? '&#xe62f;' : '&#xe656;'}}</text>
									<text class="lang">{{item.trans}}</text>
								</view>
							</view>
							<view class="center-box">
								<view class="left-box">
									<view class="title">{{item.title}}</view>
									<view class="info-box">
										<!-- <text class="gui-icons">{{item.type == 2 ? '&#xe605;' : '&#xe656;'}}</text> -->
										<text class="txt">{{item.clocks_num}} 人已打卡</text>
									</view>
								</view>
								<view class="cover-box">
									<image class="cover" :src="item.picture+'?x-oss-process=image/resize,m_lfit,h_150,w_150'"></image>
								</view>
							</view>
						</view>
					</navigator>
				</view>
			</view>
		</scroll-view>
		<uni-popup ref="popup" type="share">
			<uni-popup-fliter :fliters="fliters" @select="onFliterSelectHandler"></uni-popup-fliter>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import uniPopupFliter from './uni-popup-fliter.vue';
	export default {
		components: {
			uniPopup,
			uniPopupFliter
		},
		data() {
			return {
				// 是否在刷新
				triggered: false,
				// 加载中
				_freshing: false,
				// 最新消息
				newest: null,
				// 过滤条件
				fliters: {
					theme: []
				},
				fliter_params: {

				},
				// 历史列表
				list: [],
				// 列表参数
				list_params: {
					// 加载中
					loading: false,
					current_page: 1,
					// 总页数
					total_page: 1,
					// 总数量
					total: 0,
				},
				// 文章头部是否浮动
				articleScrollFixed: 0,
			}
		},
		created() {
			this.getNewestData();
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 获取最新
			 */
			getNewestData: function() {
				this.fliter_params = [];
				return this.apiGetNewestData().then((data) => {
					this.newest = data.newest;
					this.fliters = data.fliters;
					this.updateList(data.list, true);
					return Promise.resolve(data);
				})
			},
			/**
			 * 获取历史分页
			 * @param {Object} page
			 * @param {Object} force
			 */
			getHistoryList: function(page, force) {
				if (this.list_params.loading) {
					return
				}

				if (!force && page > this.list_params.total_page) {
					return
				}
				this.list_params.loading = true;
				this.apiGetHistoryList(page, this.fliter_params).then(pagination => {
					this.updateList(pagination, page == 1);
				})
			},
			/**
			 * 更新列表
			 * @param {Object} pagination 分页数据
			 * @param {Object} empty 是否清空
			 */
			updateList: function(pagination, empty) {
				this.list = empty ? pagination.data : this.list.concat(pagination.data);
				this.list_params.current_page = pagination.current_page;
				this.list_params.total_page = pagination.last_page;
				this.list_params.total = pagination.total;
				this.list_params.loading = false;
			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			//------------------------------------------------------
			// scroll event
			//------------------------------------------------------
			/**
			 * 触底更新
			 * @param {Object} value
			 */
			onScrolltolowerHandler: function(value) {
				console.log('onScrolltolowerHandler',value);
				this.getHistoryList(this.list_params.current_page + 1);
			},

			/**
			 * 下拉刷新 
			 */
			onRefreshHandler: function(e) {
				if (this._freshing) return;
				this._freshing = true;
				this.triggered = true;

				this.getNewestData().then(() => {
					this.triggered = false;
					this._freshing = false;
				});
			},

			/**
			 * 重置刷新
			 */
			onRestoreHandler: function() {
				this.triggered = 'restore'; // 需要重置
			},

			/**
			 * 文章滚动侦听
			 * event.detail = {scrollLeft, scrollTop, scrollHeight, scrollWidth, deltaX, deltaY}
			 */
			onScrollHandler: function(event) {
				const scrollTop = event.detail.scrollTop;
				// 设置历史头部浮动
				this.articleScrollFixed = scrollTop >= 402 ? 1 : 0;
			},

			/**
			 * 显示过滤条件
			 */
			onFilterTapHandler: function() {
				this.$refs.popup.open();
			},


			/**
			 * 条件更换事件
			 * @param {Object} data 选择后条件
			 * @param {Object} callback
			 */
			onFliterSelectHandler: function(data, callback) {
				// ...文章历史查询
				this.fliter_params = data;
				this.list = [];
				this.getHistoryList(1, true);
				callback();
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiGetNewestData: function() {
				return this.$http.get('/v1/index',{
					params:{
						ver: this.$store.state.ver
					}
				}).then(res => {
					return Promise.resolve({
						list: res.data.data,
						newest: res.data.data.news,
						fliters: {
							theme: res.data.data.theme
						}
					});
				});
			},
			apiGetHistoryList: function(page, params) {
				params['ver'] = this.$store.state.ver;
				console.log(params);
				return this.$http.post('/v1/history', {
					page,
					...params
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		height: 100%;
	}

	.root-box {
		scroll-view {
			width: unset;
			height: 100%;
			padding: 0 30rpx;
		}

		/* 全局图标 */
		.gui-icons {
			margin-right: 10rpx;
		}

		/* 最新文章 */
		.newest-box {
			.publish-date {
				padding: 30rpx 0;
				font-weight: bold;
			}

			.newest-item {
				position: relative;
				display: flex;
				height: 700rpx;

				.gui-icons {
					width: 32rpx;
					height: 32rpx;
				}

				.cover {
					position: absolute;
					width: 100%;
					height: 700rpx;
					z-index: 0;
				}

				/* 封面遮罩 */
				.cover-mask {
					z-index: 1;
				}

				.content-box {
					position: relative;
					flex: 1;
					z-index: 2;
					color: #fff;
					font-size: 28rpx;

					.head-box {
						padding: 30rpx;

						.type-box {
							color: #333;
							border-radius: 20rpx;
							background-color: #fff;
							padding: 5rpx 15rpx;
						}
					}

					.title-box {
						position: relative;
						z-index: 2;
						font-size: 35rpx;
					}

					.foot-box {
						position: relative;
						padding: 20rpx;
						align-items: center;

						.sign-in {
							position: relative;
							z-index: 2;
							font-size: 24rpx;
							color: #eee;
						}

						.bg {
							position: absolute;
							left: 0;
							top: 0;
							width: 100%;
							height: 100%;
						}

						.sign-btn {
							position: relative;
							z-index: 2;
							background: $uni-color-primary;
							border-radius: 50rpx;
							height: 60rpx;
							padding: 0 30rpx;
							min-width: 130rpx
						}
					}
				}
			}
		}

		/* 头部浮动 */
		/* .fixed {
			margin-top: 124rpx;

			.head-box {
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				padding: 50rpx 30rpx 30rpx 30rpx !important;
				z-index: 99;

			}
		} */

		/* 历史列表 */
		.history-box {
			display: flex;
			flex-direction: column;
			font-size: 32rpx;

			.head-box {
				display: flex;
				justify-content: space-between;
				padding: 50rpx 0 30rpx 0;
				border-bottom: solid 2rpx #CCCCCC;
				background-color: #ffffff;
			}

			.list-box {
				display: flex;
				flex-direction: column;

				.item-box {
					display: flex;
					flex-direction: column;
					font-size: 28rpx;
					padding: 30rpx 0;
					border-bottom: solid 1px #eeeeee;

					.top-box {
						display: flex;
						justify-content: space-between;
						margin-bottom: 20rpx;

						.publish-date {
							color: $uni-text-color-grey;
						}
					}

					.center-box {
						display: flex;
						justify-content: space-between;

						.left-box {
							display: flex;
							flex-direction: column;

							.title {
								flex: 1;
								font-size: 32rpx;
							}

							.info-box {
								font-size: 24rpx;

								.gui-icons {
									color: $uni-color-error;
								}

								.txt {
									color: $uni-text-color-grey;
								}
							}
						}

						.cover-box {
							margin-left: 30rpx;

							.cover {
								width: 260rpx;
								height: 150rpx;
								border-radius: 5px;
							}
						}
					}
				}
			}
		}
	}
</style>
