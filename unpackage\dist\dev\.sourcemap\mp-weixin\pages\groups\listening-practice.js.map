{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?2d30", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?182d", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?7f53", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?89bf", "uni-app:///pages/groups/listening-practice.vue", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?e830", "webpack:///D:/日语云课/pages/groups/listening-practice.vue?29f3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "groupId", "practiceId", "currentPractice", "currentQuestionIndex", "<PERSON><PERSON><PERSON><PERSON>", "showResult", "practiceFinished", "answers", "startTime", "audioContext", "isPlaying", "currentTime", "duration", "playSpeed", "practiceList", "id", "title", "description", "difficulty", "bestScore", "computed", "currentQuestion", "progressPercent", "audioProgressPercent", "isLastQuestion", "finalScore", "correctRate", "practiceTime", "onLoad", "methods", "startPractice", "practice", "questions", "generateQuestions", "audioUrl", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "selectAnswer", "nextQuestion", "uni", "icon", "questionId", "isCorrect", "prevQuestion", "finishPractice", "restartPractice", "reviewAnswers", "togglePlay", "changeSpeed", "seekTo", "formatTime", "getDifficultyText", "loadSpecificPractice"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACqC;;;AAGtG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgIpnB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC,eACA;QACAC;QACAC;QACAC;QACAL;QACAM;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAL;QACAM;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAL;QACAM;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA,uDACAC;QACAC;MAAA,EACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA,QACA;QACAlB;QACAC;QACAkB;QACAC;QACAC;QACAC;MACA,GACA;QACAtB;QACAC;QACAkB;QACAC;QACAC;QACAC;MACA,EACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;UACAxB;UACAyB;QACA;QACA;MACA;;MAEA;MACA;QACAC;QACAtC;QACAiC;QACAM;MACA;MAEA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACAH;UACAtC;UACAiC;UACAM;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAG;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAP;QACAxB;QACAyB;MACA;IACA;IAEA;IACAO;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpXA;AAAA;AAAA;AAAA;AAAq4B,CAAgB,y4BAAG,EAAC,C;;;;;;;;;;;ACAz5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/listening-practice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/listening-practice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./listening-practice.vue?vue&type=template&id=e1193002&scoped=true&\"\nvar renderjs\nimport script from \"./listening-practice.vue?vue&type=script&lang=js&\"\nexport * from \"./listening-practice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./listening-practice.vue?vue&type=style&index=0&id=e1193002&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e1193002\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/listening-practice.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./listening-practice.vue?vue&type=template&id=e1193002&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.currentPractice\n    ? _vm.__map(_vm.practiceList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.getDifficultyText(item.difficulty)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g0 = !!_vm.currentPractice ? _vm.currentPractice.questions.length : null\n  var m1 = !!_vm.currentPractice ? _vm.formatTime(_vm.currentTime) : null\n  var m2 = !!_vm.currentPractice ? _vm.formatTime(_vm.duration) : null\n  var l1 = !!_vm.currentPractice\n    ? _vm.__map(_vm.currentQuestion.options, function (option, index) {\n        var $orig = _vm.__get_orig(option)\n        var g1 = String.fromCharCode(65 + index)\n        return {\n          $orig: $orig,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        m1: m1,\n        m2: m2,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./listening-practice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./listening-practice.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\n\t\t\t<!-- 练习列表 -->\n\t\t\t<view class=\"practice-list\" v-if=\"!currentPractice\">\n\t\t\t\t<view class=\"list-header\">\n\t\t\t\t\t<view class=\"header-title\">听力练习</view>\n\t\t\t\t\t<view class=\"header-desc\">选择一个练习开始训练</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view \n\t\t\t\t\tclass=\"practice-item\" \n\t\t\t\t\tv-for=\"(item, index) in practiceList\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"startPractice(item)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"item-icon\">\n\t\t\t\t\t\t<text class=\"iconfont icon-headphone\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t<view class=\"item-title\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"item-desc\">{{item.description}}</view>\n\t\t\t\t\t\t<view class=\"item-meta\">\n\t\t\t\t\t\t\t<text class=\"meta-duration\">{{item.duration}}</text>\n\t\t\t\t\t\t\t<text class=\"meta-difficulty\" :class=\"item.difficulty\">{{getDifficultyText(item.difficulty)}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-status\">\n\t\t\t\t\t\t<text class=\"status-score\" v-if=\"item.bestScore !== null\">{{item.bestScore}}分</text>\n\t\t\t\t\t\t<text class=\"status-new\" v-else>NEW</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 练习界面 -->\n\t\t\t<view class=\"practice-content\" v-else>\n\t\t\t\t<!-- 进度条 -->\n\t\t\t\t<view class=\"progress-header\">\n\t\t\t\t\t<view class=\"progress-info\">\n\t\t\t\t\t\t<text>{{currentQuestionIndex + 1}} / {{currentPractice.questions.length}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: progressPercent + '%' }\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 音频播放器 -->\n\t\t\t\t<view class=\"audio-player\">\n\t\t\t\t\t<view class=\"audio-controls\">\n\t\t\t\t\t\t<view class=\"play-btn\" @click=\"togglePlay\">\n\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"isPlaying ? 'icon-pause' : 'icon-play'\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"audio-info\">\n\t\t\t\t\t\t\t<view class=\"audio-title\">{{currentQuestion.title}}</view>\n\t\t\t\t\t\t\t<view class=\"audio-time\">{{formatTime(currentTime)}} / {{formatTime(duration)}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"speed-btn\" @click=\"changeSpeed\">\n\t\t\t\t\t\t\t<text>{{playSpeed}}x</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"audio-progress\">\n\t\t\t\t\t\t<view class=\"progress-track\" @click=\"seekTo\">\n\t\t\t\t\t\t\t<view class=\"progress-played\" :style=\"{ width: audioProgressPercent + '%' }\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 题目 -->\n\t\t\t\t<view class=\"question-content\">\n\t\t\t\t\t<view class=\"question-title\">{{currentQuestion.question}}</view>\n\t\t\t\t\t<view class=\"question-options\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\tv-for=\"(option, index) in currentQuestion.options\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:class=\"{ \n\t\t\t\t\t\t\t\tselected: selectedAnswer === index,\n\t\t\t\t\t\t\t\tcorrect: showResult && index === currentQuestion.correctAnswer,\n\t\t\t\t\t\t\t\twrong: showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t@click=\"selectAnswer(index)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"option-label\">{{String.fromCharCode(65 + index)}}</view>\n\t\t\t\t\t\t\t<view class=\"option-text\">{{option}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t<button class=\"btn-secondary\" @click=\"prevQuestion\" :disabled=\"currentQuestionIndex === 0\">\n\t\t\t\t\t\t上一题\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"nextQuestion\" v-if=\"!isLastQuestion\">\n\t\t\t\t\t\t下一题\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"finishPractice\" v-else>\n\t\t\t\t\t\t完成练习\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 结果页面 -->\n\t\t\t<view class=\"result-page\" v-if=\"showResult && practiceFinished\">\n\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t<view class=\"result-score\">{{finalScore}}</view>\n\t\t\t\t\t<view class=\"result-text\">分</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"result-details\">\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">正确率</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{correctRate}}%</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">用时</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{practiceTime}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"result-actions\">\n\t\t\t\t\t<button class=\"btn-secondary\" @click=\"reviewAnswers\">查看解析</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"restartPractice\">重新练习</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\tgroupId: '',\n\t\t\tpracticeId: '',\n\t\t\tcurrentPractice: null,\n\t\t\tcurrentQuestionIndex: 0,\n\t\t\tselectedAnswer: null,\n\t\t\tshowResult: false,\n\t\t\tpracticeFinished: false,\n\t\t\tanswers: [],\n\t\t\tstartTime: null,\n\t\t\t\n\t\t\t// 音频相关\n\t\t\taudioContext: null,\n\t\t\tisPlaying: false,\n\t\t\tcurrentTime: 0,\n\t\t\tduration: 0,\n\t\t\tplaySpeed: 1.0,\n\t\t\t\n\t\t\tpracticeList: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '日常对话练习',\n\t\t\t\t\tdescription: '基础日常对话听力训练',\n\t\t\t\t\tduration: '10分钟',\n\t\t\t\t\tdifficulty: 'easy',\n\t\t\t\t\tbestScore: 85\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '购物场景对话',\n\t\t\t\t\tdescription: '商店购物相关对话练习',\n\t\t\t\t\tduration: '15分钟',\n\t\t\t\t\tdifficulty: 'medium',\n\t\t\t\t\tbestScore: null\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '新闻听力练习',\n\t\t\t\t\tdescription: '简单新闻内容听力理解',\n\t\t\t\t\tduration: '20分钟',\n\t\t\t\t\tdifficulty: 'hard',\n\t\t\t\t\tbestScore: 78\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\tcurrentQuestion() {\n\t\t\tif (!this.currentPractice || !this.currentPractice.questions) return null;\n\t\t\treturn this.currentPractice.questions[this.currentQuestionIndex];\n\t\t},\n\t\tprogressPercent() {\n\t\t\tif (!this.currentPractice) return 0;\n\t\t\treturn ((this.currentQuestionIndex + 1) / this.currentPractice.questions.length) * 100;\n\t\t},\n\t\taudioProgressPercent() {\n\t\t\tif (this.duration === 0) return 0;\n\t\t\treturn (this.currentTime / this.duration) * 100;\n\t\t},\n\t\tisLastQuestion() {\n\t\t\tif (!this.currentPractice) return false;\n\t\t\treturn this.currentQuestionIndex === this.currentPractice.questions.length - 1;\n\t\t},\n\t\tfinalScore() {\n\t\t\tif (this.answers.length === 0) return 0;\n\t\t\tconst correct = this.answers.filter(answer => answer.isCorrect).length;\n\t\t\treturn Math.round((correct / this.answers.length) * 100);\n\t\t},\n\t\tcorrectRate() {\n\t\t\treturn this.finalScore;\n\t\t},\n\t\tpracticeTime() {\n\t\t\tif (!this.startTime) return '0分0秒';\n\t\t\tconst elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n\t\t\tconst minutes = Math.floor(elapsed / 60);\n\t\t\tconst seconds = elapsed % 60;\n\t\t\treturn `${minutes}分${seconds}秒`;\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.groupId) {\n\t\t\tthis.groupId = options.groupId;\n\t\t}\n\t\tif (options.practiceId) {\n\t\t\tthis.practiceId = options.practiceId;\n\t\t\tthis.loadSpecificPractice(options.practiceId);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 开始练习\n\t\tstartPractice(practice) {\n\t\t\tthis.currentPractice = {\n\t\t\t\t...practice,\n\t\t\t\tquestions: this.generateQuestions(practice.id)\n\t\t\t};\n\t\t\tthis.currentQuestionIndex = 0;\n\t\t\tthis.selectedAnswer = null;\n\t\t\tthis.showResult = false;\n\t\t\tthis.practiceFinished = false;\n\t\t\tthis.answers = [];\n\t\t\tthis.startTime = Date.now();\n\t\t},\n\n\t\t// 生成题目（模拟数据）\n\t\tgenerateQuestions(practiceId) {\n\t\t\t// 这里应该从API获取真实的题目数据\n\t\t\treturn [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '对话1',\n\t\t\t\t\taudioUrl: '/static/audio/dialog1.mp3',\n\t\t\t\t\tquestion: '对话中提到的时间是？',\n\t\t\t\t\toptions: ['上午9点', '下午2点', '晚上7点', '中午12点'],\n\t\t\t\t\tcorrectAnswer: 1\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '对话2',\n\t\t\t\t\taudioUrl: '/static/audio/dialog2.mp3',\n\t\t\t\t\tquestion: '说话者要去哪里？',\n\t\t\t\t\toptions: ['学校', '图书馆', '超市', '医院'],\n\t\t\t\t\tcorrectAnswer: 2\n\t\t\t\t}\n\t\t\t];\n\t\t},\n\n\t\t// 选择答案\n\t\tselectAnswer(index) {\n\t\t\tif (this.showResult) return;\n\t\t\tthis.selectedAnswer = index;\n\t\t},\n\n\t\t// 下一题\n\t\tnextQuestion() {\n\t\t\tif (this.selectedAnswer === null) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择答案',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 记录答案\n\t\t\tthis.answers.push({\n\t\t\t\tquestionId: this.currentQuestion.id,\n\t\t\t\tselectedAnswer: this.selectedAnswer,\n\t\t\t\tcorrectAnswer: this.currentQuestion.correctAnswer,\n\t\t\t\tisCorrect: this.selectedAnswer === this.currentQuestion.correctAnswer\n\t\t\t});\n\n\t\t\tif (this.isLastQuestion) {\n\t\t\t\tthis.finishPractice();\n\t\t\t} else {\n\t\t\t\tthis.currentQuestionIndex++;\n\t\t\t\tthis.selectedAnswer = null;\n\t\t\t\tthis.showResult = false;\n\t\t\t}\n\t\t},\n\n\t\t// 上一题\n\t\tprevQuestion() {\n\t\t\tif (this.currentQuestionIndex > 0) {\n\t\t\t\tthis.currentQuestionIndex--;\n\t\t\t\t// 恢复之前的答案\n\t\t\t\tconst prevAnswer = this.answers[this.currentQuestionIndex];\n\t\t\t\tthis.selectedAnswer = prevAnswer ? prevAnswer.selectedAnswer : null;\n\t\t\t}\n\t\t},\n\n\t\t// 完成练习\n\t\tfinishPractice() {\n\t\t\tif (this.selectedAnswer !== null) {\n\t\t\t\tthis.answers.push({\n\t\t\t\t\tquestionId: this.currentQuestion.id,\n\t\t\t\t\tselectedAnswer: this.selectedAnswer,\n\t\t\t\t\tcorrectAnswer: this.currentQuestion.correctAnswer,\n\t\t\t\t\tisCorrect: this.selectedAnswer === this.currentQuestion.correctAnswer\n\t\t\t\t});\n\t\t\t}\n\t\t\t\n\t\t\tthis.practiceFinished = true;\n\t\t\tthis.showResult = true;\n\t\t},\n\n\t\t// 重新练习\n\t\trestartPractice() {\n\t\t\tthis.currentQuestionIndex = 0;\n\t\t\tthis.selectedAnswer = null;\n\t\t\tthis.showResult = false;\n\t\t\tthis.practiceFinished = false;\n\t\t\tthis.answers = [];\n\t\t\tthis.startTime = Date.now();\n\t\t},\n\n\t\t// 查看解析\n\t\treviewAnswers() {\n\t\t\t// 跳转到答案解析页面\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 音频控制\n\t\ttogglePlay() {\n\t\t\tthis.isPlaying = !this.isPlaying;\n\t\t\t// 这里应该控制实际的音频播放\n\t\t},\n\n\t\tchangeSpeed() {\n\t\t\tconst speeds = [1.0, 1.25, 1.5, 0.75];\n\t\t\tconst currentIndex = speeds.indexOf(this.playSpeed);\n\t\t\tthis.playSpeed = speeds[(currentIndex + 1) % speeds.length];\n\t\t},\n\n\t\tseekTo(e) {\n\t\t\t// 音频进度控制\n\t\t},\n\n\t\tformatTime(seconds) {\n\t\t\tconst mins = Math.floor(seconds / 60);\n\t\t\tconst secs = Math.floor(seconds % 60);\n\t\t\treturn `${mins}:${secs.toString().padStart(2, '0')}`;\n\t\t},\n\n\t\tgetDifficultyText(difficulty) {\n\t\t\tconst map = {\n\t\t\t\t'easy': '简单',\n\t\t\t\t'medium': '中等',\n\t\t\t\t'hard': '困难'\n\t\t\t};\n\t\t\treturn map[difficulty] || '未知';\n\t\t},\n\n\t\tloadSpecificPractice(practiceId) {\n\t\t\tconst practice = this.practiceList.find(p => p.id == practiceId);\n\t\t\tif (practice) {\n\t\t\t\tthis.startPractice(practice);\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.practice-list {\n\tpadding: 30rpx;\n}\n\n.list-header {\n\ttext-align: center;\n\tmargin-bottom: 40rpx;\n}\n\n.header-title {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.header-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.practice-item {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.item-icon {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tborder-radius: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.item-icon .iconfont {\n\tcolor: #fff;\n\tfont-size: 36rpx;\n}\n\n.item-info {\n\tflex: 1;\n}\n\n.item-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.item-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 12rpx;\n}\n\n.item-meta {\n\tdisplay: flex;\n\tgap: 15rpx;\n}\n\n.meta-duration {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.meta-difficulty {\n\tfont-size: 24rpx;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.meta-difficulty.easy {\n\tbackground: #e8f5e8;\n\tcolor: #52c41a;\n}\n\n.meta-difficulty.medium {\n\tbackground: #fff7e6;\n\tcolor: #fa8c16;\n}\n\n.meta-difficulty.hard {\n\tbackground: #fff2f0;\n\tcolor: #ff4d4f;\n}\n\n.item-status {\n\tmargin-left: 20rpx;\n}\n\n.status-score {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #2094CE;\n}\n\n.status-new {\n\tfont-size: 24rpx;\n\tcolor: #fff;\n\tbackground: #ff4d4f;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n\n.practice-content {\n\tpadding: 30rpx;\n}\n\n.progress-header {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.progress-info {\n\ttext-align: center;\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-bar {\n\theight: 8rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 4rpx;\n\toverflow: hidden;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: #2094CE;\n\ttransition: width 0.3s;\n}\n\n.audio-player {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.audio-controls {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.play-btn {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tbackground: #2094CE;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n}\n\n.play-btn .iconfont {\n\tcolor: #fff;\n\tfont-size: 36rpx;\n}\n\n.audio-info {\n\tflex: 1;\n}\n\n.audio-title {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.audio-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.speed-btn {\n\tpadding: 10rpx 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.audio-progress {\n\theight: 6rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 3rpx;\n\toverflow: hidden;\n}\n\n.progress-track {\n\theight: 100%;\n\tposition: relative;\n}\n\n.progress-played {\n\theight: 100%;\n\tbackground: #2094CE;\n}\n\n.question-content {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.question-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.option-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tmargin-bottom: 15rpx;\n\tborder: 2rpx solid #f0f0f0;\n\tborder-radius: 16rpx;\n\ttransition: all 0.3s;\n}\n\n.option-item.selected {\n\tborder-color: #2094CE;\n\tbackground: #f0f9ff;\n}\n\n.option-item.correct {\n\tborder-color: #52c41a;\n\tbackground: #f6ffed;\n}\n\n.option-item.wrong {\n\tborder-color: #ff4d4f;\n\tbackground: #fff2f0;\n}\n\n.option-label {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tmargin-right: 20rpx;\n}\n\n.option-item.selected .option-label {\n\tbackground: #2094CE;\n\tcolor: #fff;\n}\n\n.option-text {\n\tflex: 1;\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.btn-secondary, .btn-primary {\n\tflex: 1;\n\theight: 88rpx;\n\tborder-radius: 44rpx;\n\tfont-size: 32rpx;\n\tborder: none;\n}\n\n.btn-secondary {\n\tbackground: #f0f0f0;\n\tcolor: #666;\n}\n\n.btn-primary {\n\tbackground: #2094CE;\n\tcolor: #fff;\n}\n\n.result-page {\n\ttext-align: center;\n\tpadding: 60rpx 30rpx;\n}\n\n.result-header {\n\tmargin-bottom: 60rpx;\n}\n\n.result-score {\n\tfont-size: 120rpx;\n\tfont-weight: bold;\n\tcolor: #2094CE;\n}\n\n.result-text {\n\tfont-size: 48rpx;\n\tcolor: #666;\n}\n\n.result-details {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\tmargin-bottom: 40rpx;\n}\n\n.detail-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.detail-item:last-child {\n\tborder-bottom: none;\n}\n\n.detail-label {\n\tfont-size: 32rpx;\n\tcolor: #666;\n}\n\n.detail-value {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.result-actions {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./listening-practice.vue?vue&type=style&index=0&id=e1193002&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./listening-practice.vue?vue&type=style&index=0&id=e1193002&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662925940\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}