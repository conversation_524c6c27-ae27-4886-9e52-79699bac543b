(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/article/detail"],{"221b":function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;a(n("7b1a"));var r={components:{uniLoadMore:function(){n.e("components/uni-load-more/uni-load-more").then(function(){return resolve(n("8f79"))}.bind(null,n)).catch(n.oe)},empty:function(){n.e("components/empty").then(function(){return resolve(n("4873"))}.bind(null,n)).catch(n.oe)}},data:function(){return{tabCurrentIndex:0,navList:[{state:0,text:"全部",loadingType:"more",orderList:[],current_page:1},{state:2,text:"已完成",loadingType:"more",orderList:[],current_page:1}],project_id:1}},created:function(){this.$store.state.user.token||t.showModal({title:"请先登录",content:"登录后才能进行操作",success:function(e){e.confirm?t.navigateTo({url:"/pages/login/login"}):e.cancel}})},onLoad:function(t){this.project_id=t.project_id,this.tabCurrentIndex=+t.state,0==t.state&&this.loadData()},methods:{loadData:function(t){var e=this,n=this.tabCurrentIndex,a=this.navList[n],r=a.state,i=a.current_page;"tabChange"===t&&!0===a.loaded||"loading"!==a.loadingType&&(a.loadingType="loading",this.apiGetCourseList(this.project_id,i).then((function(t){e.empty=!1,e.current_page=t.current_page,e.total_page=t.last_page,e.total=t.total,e.loading=!1;var o=t.data.filter((function(t){return t=Object.assign(t,e.orderStateExp(t.state)),0===r?t:t.state===r}));console.log(o),o.forEach((function(t){a.orderList.push(t)})),e.$set(a,"loaded",!0),a.loadingType="noMore",i++,e.navList[n].current_page=i})))},changeTab:function(t){this.tabCurrentIndex=t.target.current,this.loadData("tabChange")},tabClick:function(t){this.tabCurrentIndex=t},deleteOrder:function(e){var n=this;t.showLoading({title:"请稍后"}),setTimeout((function(){n.navList[n.tabCurrentIndex].orderList.splice(e,1),t.hideLoading()}),600),t.navigateTo({url:"/pages/projects/course"})},cancelOrder:function(e){var n=this;t.showLoading({title:"请稍后"}),setTimeout((function(){var a=n.orderStateExp(9),r=a.stateTip,i=a.stateTipColor;e=Object.assign(e,{state:9,stateTip:r,stateTipColor:i});var o=n.navList[1].orderList,c=o.findIndex((function(t){return t.id===e.id}));-1!==c&&o.splice(c,1),t.hideLoading()}),600)},orderStateExp:function(t){var e="",n="#fa436a";switch(+t){case 1:e="已完成";break;case 2:e="待发货";break;case 9:e="订单已关闭",n="#909399";break}return{stateTip:e,stateTipColor:n}},apiGetCourseList:function(t,e){return this.$http.get("/v1/article/index",{params:{id:t,page:e}}).then((function(t){return Promise.resolve(t.data.data)}))},navToDetailPage:function(e){var n=e.id;t.navigateTo({url:"/pages/article/detail/index?article_id=".concat(n)})}}};e.default=r}).call(this,n("df3c")["default"])},"2e6a":function(t,e,n){},9579:function(t,e,n){"use strict";n.r(e);var a=n("e547"),r=n("ecea");for(var i in r)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(i);n("b65e");var o=n("828b"),c=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},b65e:function(t,e,n){"use strict";var a=n("2e6a"),r=n.n(a);r.a},dac6:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("5788");a(n("3240"));var r=a(n("9579"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},e547:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return a}));var a={uniLoadMore:function(){return n.e("components/uni-load-more/uni-load-more").then(n.bind(null,"8f79"))}},r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.__map(t.navList,(function(e,n){var a=t.__get_orig(e),r=!0===e.loaded&&0===e.orderList.length;return{$orig:a,g0:r}})));t.$mp.data=Object.assign({},{$root:{l0:n}})},i=[]},ecea:function(t,e,n){"use strict";n.r(e);var a=n("221b"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);e["default"]=r.a}},[["dac6","common/runtime","common/vendor"]]]);