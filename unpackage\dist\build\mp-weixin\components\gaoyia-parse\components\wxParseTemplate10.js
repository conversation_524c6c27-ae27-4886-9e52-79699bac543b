(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/gaoyia-parse/components/wxParseTemplate10"],{"190a":function(e,n,t){"use strict";t.r(n);var o=t("f688"),a=t("d0f4");for(var r in a)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(r);var c=t("828b"),i=Object(c["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=i.exports},d0f4:function(e,n,t){"use strict";t.r(n);var o=t("f41c"),a=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=a.a},f41c:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={name:"wxParseTemplate10",props:{node:{}},components:{wxParseTemplate:function(){t.e("components/gaoyia-parse/components/wxParseTemplate11").then(function(){return resolve(t("ad51"))}.bind(null,t)).catch(t.oe)},wxParseImg:function(){t.e("components/gaoyia-parse/components/wxParseImg").then(function(){return resolve(t("7a34"))}.bind(null,t)).catch(t.oe)},wxParseVideo:function(){t.e("components/gaoyia-parse/components/wxParseVideo").then(function(){return resolve(t("af01"))}.bind(null,t)).catch(t.oe)},wxParseAudio:function(){t.e("components/gaoyia-parse/components/wxParseAudio").then(function(){return resolve(t("82c1"))}.bind(null,t)).catch(t.oe)},wxParseTable:function(){t.e("components/gaoyia-parse/components/wxParseTable").then(function(){return resolve(t("01d6"))}.bind(null,t)).catch(t.oe)}},methods:{wxParseATap:function(e,n){var t=n.currentTarget.dataset.href;if(t){var o=this.$parent;while(!o.preview||"function"!==typeof o.preview)o=o.$parent;o.navigate(t,n,e)}}}};n.default=o},f688:function(e,n,t){"use strict";t.d(n,"b",(function(){return o})),t.d(n,"c",(function(){return a})),t.d(n,"a",(function(){}));var o=function(){var e=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/gaoyia-parse/components/wxParseTemplate10-create-component',
    {
        'components/gaoyia-parse/components/wxParseTemplate10-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("190a"))
        })
    },
    [['components/gaoyia-parse/components/wxParseTemplate10-create-component']]
]);
