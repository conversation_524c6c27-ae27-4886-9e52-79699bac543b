<template>
	<view class="null-box">
		<view class="content-box">
			<view><text class="gui-icons">&#xe704;</text></view>
			<view class="null-text">{{ text }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: ["text"],
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.null-box {
		text-align: center;
		color: #C0BDC0;

		.content-box {
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 300rpx;

		}

		.gui-icons {
			font-size: 96rpx;
		}
	}

	.null-img {
		margin-top: 200rpx;
		width: 287rpx;
		height: 182rpx;
	}

	.null-text {
		text-align: center;
	}
</style>
