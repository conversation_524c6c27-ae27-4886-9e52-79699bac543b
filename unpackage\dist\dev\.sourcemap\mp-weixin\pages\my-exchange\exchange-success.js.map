{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?71f8", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?16b6", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?e33e", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?cd4f", "uni-app:///pages/my-exchange/exchange-success.vue", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?1b0b", "webpack:///D:/日语云课/pages/my-exchange/exchange-success.vue?1392"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "nodata", "data", "sn", "list", "onShow", "onHide", "created", "computed", "valided", "methods", "exchangeValid", "uni", "icon", "title", "onExchangeValidHandler", "onExchangeHandler", "setTimeout", "apiExchangeValid", "params", "id", "apiExchange", "showCancel", "navToDetailPage", "url", "up"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCoBlnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;EACAC;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;UACAF;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;UACA;YACAJ;cACAE;YACA;YACAG;cACAL;YACA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAM;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAD;MACA;QACA;MACA;QACAR;UACAU;UACAR;QACA;QACA;MACA;IACA;IACA;IACAS;MACA;MACA;MACAX;QACAY;MACA;IACA;IACAC;MACAb;QACAY;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAqpC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACAzqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my-exchange/exchange-success.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my-exchange/exchange-success.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./exchange-success.vue?vue&type=template&id=6231ee46&scoped=true&\"\nvar renderjs\nimport script from \"./exchange-success.vue?vue&type=script&lang=js&\"\nexport * from \"./exchange-success.vue?vue&type=script&lang=js&\"\nimport style0 from \"./exchange-success.vue?vue&type=style&index=0&id=6231ee46&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6231ee46\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my-exchange/exchange-success.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange-success.vue?vue&type=template&id=6231ee46&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange-success.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange-success.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\"  class=\"root-box\">\n\t\t\t<!-- 页面主体 -->\r\n\t\t\t<view slot=\"gBody\"  class=\"root-box lp-flex-column\">\r\n\t\t\t<!-- <view slot=\"gBody\"  class=\"root-box lp-flex-column\"> -->\r\n\t\t\t\t<view class=\"head lp-flex-column lp-flex-center\" style=\"margin-top: 50rpx;\">\r\n\t\t\t\t\t<!-- <text class=\"gui-icons\" style=\"100rpx\">{{order.status ? '&#xe7f8;' : '&#xe632;'}}</text> -->\r\n\t\t\t\t\t<image src=\"/static/success.png\" mode=\"aspectFit\" style=\"width: 200rpx;height: 200rpx;\"></image>\r\n\t\t\t\t\t<text class=\"success-text\">兑换成功</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button  @tap=\"up()\" type=\"primary\" class=\"phone-login-btn\" style=\"margin-top: 100rpx ;margin-bottom: 100rpx ;margin-left: 10% ;margin-right: 10% ;width: 80%;background-color: rgb(18, 150, 219);\">\r\n\t\t\t\t\t去学习\r\n\t\t\t\t</button>\r\n\t\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\n\timport nodata from '@/components/nodata/nodata.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tnodata\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsn: '',\n\t\t\t\tlist: []\n\t\t\t}\n\t\t},\n\t\tonShow: function() {},\n\t\tonHide: function() {},\n\t\tcreated: function() {},\n\t\tcomputed: {\n\t\t\tvalided: function() {\n\t\t\t\treturn this.list.length > 0;\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\texchangeValid: function() {\n\t\t\t\tif (this.sn == '') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写有效兑换码'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.apiExchangeValid(this.sn).then(data => {\n\t\t\t\t\tif (data) {\n\t\t\t\t\t\tthis.list = [data]\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.list = [];\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '找不到数据'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// hander\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\t\t\t\n\t\t\t/**\n\t\t\t * 核实\n\t\t\t */\n\t\t\tonExchangeValidHandler: function() {\n\t\t\t\tthis.exchangeValid();\n\t\t\t},\n\t\t\t/**\n\t\t\t * 兑换\n\t\t\t */\n\t\t\tonExchangeHandler: function() {\n\t\t\t\tif (this.valided) {\n\t\t\t\t\tthis.apiExchange(this.list[0].id).then(data => {\n\t\t\t\t\t\tif (data.status) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '兑换成功',\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tapiExchangeValid: function(sn) {\n\t\t\t\treturn this.$http.get('/v1/member/course_detail', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tid: sn,\n\t\t\t\t\t}\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t})\n\t\t\t},\n\t\t\tapiExchange: function(id) {\n\t\t\t\treturn this.$http.post('/v1/member/doExchange', {\n\t\t\t\t\tid\n\t\t\t\t}).then(res => {\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t}).catch(res => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\ttitle: res.data.code\n\t\t\t\t\t})\n\t\t\t\t\treturn Promise.resolve(res.data);\n\t\t\t\t})\n\t\t\t},\r\n\t\t\t//详情\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/product/product?id=${id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tup(){\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/study/study',\r\n\t\t\t\t})\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.root-box {\r\n\t\tcolor: #111;\r\n\t\r\n\t\t.head {\r\n\t\t\tpadding: 50rpx 0;\r\n\t\r\n\t\t\t.gui-icons {\r\n\t\t\t\tfont-size: 100rpx;\r\n\t\t\t\tcolor: #28b28b;\r\n\t\t\t}\r\n\t\r\n\t\t\t.success-text {\r\n\t\t\t\tmargin: 20rpx 0;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.content-box {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tpadding: 0 60rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\t.lp-flex-column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t.lp-flex-center {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange-success.vue?vue&type=style&index=0&id=6231ee46&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange-success.vue?vue&type=style&index=0&id=6231ee46&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753663861839\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}