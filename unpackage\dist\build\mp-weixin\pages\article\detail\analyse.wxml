<view class="root-box data-v-7aa0b7e0"><view class="content-box data-v-7aa0b7e0"><rich-text class="content data-v-7aa0b7e0" nodes="{{comment.content}}" data-event-opts="{{[['longpress',[['onSetClipboardDataHandler',['$0'],['comment.content']]]]]}}" bindlongpress="__e"></rich-text></view><block wx:if="{{comment.file!=''}}"><view class="file-box data-v-7aa0b7e0"><view class="left-box data-v-7aa0b7e0"><image src="{{comment.author.avatar}}" class="data-v-7aa0b7e0"></image></view><view class="center-box data-v-7aa0b7e0"><view class="name-box data-v-7aa0b7e0"><text class="data-v-7aa0b7e0">老师 ●</text><text class="name data-v-7aa0b7e0">{{comment.author.name}}</text></view><view class="job data-v-7aa0b7e0"><text class="data-v-7aa0b7e0">{{comment.author.job}}</text></view><view class="intro data-v-7aa0b7e0"><text class="data-v-7aa0b7e0">{{comment.author.intro}}</text></view></view><view class="right-box data-v-7aa0b7e0"><lp-audio-player vue-id="0bb4923f-1" mini="{{true}}" audio="{{$root.a0}}" class="data-v-7aa0b7e0" bind:__l="__l"></lp-audio-player></view></view></block></view>