<template>
	<view class="auth-container">
		<!-- 有权限时显示内容 -->
		<view v-if="permission">
			<slot></slot>
		</view>
		
		<!-- 无权限时显示提示 -->
		<view class="no-permission" v-else>
			<view class="permission-icon">
				<text class="iconfont icon-lock"></text>
			</view>
			<view class="permission-title">访问受限</view>
			<view class="permission-desc">
				<text>您暂时没有访问学习小组的权限</text>
				<text>请联系管理员开通权限或使用授权账号登录</text>
			</view>
			<view class="permission-actions">
				<button class="btn-login" @click="goToLogin" v-if="!isLoggedIn">
					重新登录
				</button>
				<button class="btn-contact" @click="contactAdmin">
					联系管理员
				</button>
			</view>
			
			<!-- 权限说明 -->
			<view class="permission-info">
				<view class="info-title">权限说明</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-icon">•</text>
						<text class="info-text">学习小组功能需要特定权限才能访问</text>
					</view>
					<view class="info-item">
						<text class="info-icon">•</text>
						<text class="info-text">请确保使用授权账号登录</text>
					</view>
					<view class="info-item">
						<text class="info-icon">•</text>
						<text class="info-text">如需开通权限，请联系课程管理员</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'lp-auth-content',
	props: {
		// 是否有权限
		permission: {
			type: Boolean,
			default: false
		},
		// 无权限时跳转的登录页面
		auth_url: {
			type: String,
			default: '/pages/login/login'
		},
		// 权限检查类型
		authType: {
			type: String,
			default: 'group' // group: 小组权限, course: 课程权限
		}
	},
	computed: {
		// 是否已登录
		isLoggedIn() {
			return this.$store.state.user.token && this.$store.state.user.userInfo;
		}
	},
	methods: {
		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: this.auth_url
			});
		},
		
		// 联系管理员
		contactAdmin() {
			uni.showModal({
				title: '联系管理员',
				content: '请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>',
				showCancel: false,
				confirmText: '我知道了'
			});
		}
	}
}
</script>

<style scoped>
.auth-container {
	min-height: 100vh;
}

.no-permission {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 80vh;
	padding: 60rpx 40rpx;
	text-align: center;
}

.permission-icon {
	width: 200rpx;
	height: 200rpx;
	background: #f0f0f0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
}

.permission-icon .iconfont {
	font-size: 100rpx;
	color: #ccc;
}

.permission-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.permission-desc {
	font-size: 32rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 60rpx;
}

.permission-desc text {
	display: block;
	margin-bottom: 10rpx;
}

.permission-actions {
	display: flex;
	gap: 30rpx;
	margin-bottom: 80rpx;
}

.btn-login, .btn-contact {
	padding: 24rpx 48rpx;
	border-radius: 50rpx;
	font-size: 32rpx;
	border: none;
	min-width: 200rpx;
}

.btn-login {
	background: #2094CE;
	color: #fff;
}

.btn-contact {
	background: #f0f0f0;
	color: #666;
}

.permission-info {
	width: 100%;
	max-width: 600rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: left;
}

.info-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	text-align: center;
}

.info-list {
	space-y: 20rpx;
}

.info-item {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.info-icon {
	font-size: 32rpx;
	color: #2094CE;
	margin-right: 15rpx;
	line-height: 1.5;
}

.info-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	flex: 1;
}
</style>
