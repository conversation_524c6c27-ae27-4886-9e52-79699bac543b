<gui-page vue-id="5efa18e7-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" class="data-v-e1193002 vue-ref" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1 data-v-e1193002" style="background-color:#F8F8F8;" slot="gBody"><block wx:if="{{!currentPractice}}"><view class="practice-list data-v-e1193002"><view class="list-header data-v-e1193002"><view class="header-title data-v-e1193002">听力练习</view><view class="header-desc data-v-e1193002">选择一个练习开始训练</view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['startPractice',['$0'],[[['practiceList','',index]]]]]]]}}" class="practice-item data-v-e1193002" bindtap="__e"><view class="item-icon data-v-e1193002"><text class="iconfont icon-headphone data-v-e1193002"></text></view><view class="item-info data-v-e1193002"><view class="item-title data-v-e1193002">{{item.$orig.title}}</view><view class="item-desc data-v-e1193002">{{item.$orig.description}}</view><view class="item-meta data-v-e1193002"><text class="meta-duration data-v-e1193002">{{item.$orig.duration}}</text><text class="{{['meta-difficulty','data-v-e1193002',item.$orig.difficulty]}}">{{item.m0}}</text></view></view><view class="item-status data-v-e1193002"><block wx:if="{{item.$orig.bestScore!==null}}"><text class="status-score data-v-e1193002">{{item.$orig.bestScore+"分"}}</text></block><block wx:else><text class="status-new data-v-e1193002">NEW</text></block></view></view></block></view></block><block wx:else><view class="practice-content data-v-e1193002"><view class="progress-header data-v-e1193002"><view class="progress-info data-v-e1193002"><text class="data-v-e1193002">{{currentQuestionIndex+1+" / "+$root.g0}}</text></view><view class="progress-bar data-v-e1193002"><view class="progress-fill data-v-e1193002" style="{{'width:'+(progressPercent+'%')+';'}}"></view></view></view><view class="audio-player data-v-e1193002"><view class="audio-controls data-v-e1193002"><view data-event-opts="{{[['tap',[['togglePlay',['$event']]]]]}}" class="play-btn data-v-e1193002" bindtap="__e"><text class="{{['iconfont','data-v-e1193002',isPlaying?'icon-pause':'icon-play']}}"></text></view><view class="audio-info data-v-e1193002"><view class="audio-title data-v-e1193002">{{currentQuestion.title}}</view><view class="audio-time data-v-e1193002">{{$root.m1+" / "+$root.m2}}</view></view><view data-event-opts="{{[['tap',[['changeSpeed',['$event']]]]]}}" class="speed-btn data-v-e1193002" bindtap="__e"><text class="data-v-e1193002">{{playSpeed+"x"}}</text></view></view><view class="audio-progress data-v-e1193002"><view data-event-opts="{{[['tap',[['seekTo',['$event']]]]]}}" class="progress-track data-v-e1193002" bindtap="__e"><view class="progress-played data-v-e1193002" style="{{'width:'+(audioProgressPercent+'%')+';'}}"></view></view></view></view><view class="question-content data-v-e1193002"><view class="question-title data-v-e1193002">{{currentQuestion.question}}</view><view class="question-options data-v-e1193002"><block wx:for="{{$root.l1}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectAnswer',[index]]]]]}}" class="{{['option-item','data-v-e1193002',(selectedAnswer===index)?'selected':'',(showResult&&index===currentQuestion.correctAnswer)?'correct':'',(showResult&&selectedAnswer===index&&index!==currentQuestion.correctAnswer)?'wrong':'']}}" bindtap="__e"><view class="option-label data-v-e1193002">{{option.g1}}</view><view class="option-text data-v-e1193002">{{option.$orig}}</view></view></block></view></view><view class="action-buttons data-v-e1193002"><button class="btn-secondary data-v-e1193002" disabled="{{currentQuestionIndex===0}}" data-event-opts="{{[['tap',[['prevQuestion',['$event']]]]]}}" bindtap="__e">上一题</button><block wx:if="{{!isLastQuestion}}"><button data-event-opts="{{[['tap',[['nextQuestion',['$event']]]]]}}" class="btn-primary data-v-e1193002" bindtap="__e">下一题</button></block><block wx:else><button data-event-opts="{{[['tap',[['finishPractice',['$event']]]]]}}" class="btn-primary data-v-e1193002" bindtap="__e">完成练习</button></block></view></view></block><block wx:if="{{showResult&&practiceFinished}}"><view class="result-page data-v-e1193002"><view class="result-header data-v-e1193002"><view class="result-score data-v-e1193002">{{finalScore}}</view><view class="result-text data-v-e1193002">分</view></view><view class="result-details data-v-e1193002"><view class="detail-item data-v-e1193002"><text class="detail-label data-v-e1193002">正确率</text><text class="detail-value data-v-e1193002">{{correctRate+"%"}}</text></view><view class="detail-item data-v-e1193002"><text class="detail-label data-v-e1193002">用时</text><text class="detail-value data-v-e1193002">{{practiceTime}}</text></view></view><view class="result-actions data-v-e1193002"><button data-event-opts="{{[['tap',[['reviewAnswers',['$event']]]]]}}" class="btn-secondary data-v-e1193002" bindtap="__e">查看解析</button><button data-event-opts="{{[['tap',[['restartPractice',['$event']]]]]}}" class="btn-primary data-v-e1193002" bindtap="__e">重新练习</button></view></view></block></view></gui-page>