<gui-page class="root-box data-v-3368feed" vue-id="39b75fb9-1" fullPage="{{true}}" customHeader="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{order}}"><view class="root-box lp-flex-column data-v-3368feed" slot="gBody"><view class="head lp-flex-column lp-flex-center data-v-3368feed"><text class="gui-icons data-v-3368feed" style=";">{{order.status?'':''}}</text><text class="success-text data-v-3368feed">{{"报名"+(order.status?'成功':'失败')}}</text><view style="text-align:center;" class="data-v-3368feed"><image src="{{order.credential}}" mode="widthFix" data-event-opts="{{[['longpress',[['savePoster',['$event']]]]]}}" bindlongpress="__e" class="data-v-3368feed"></image></view><block wx:if="{{!order.status}}"><text style="font-size:28rpx;color:#aaa;" class="data-v-3368feed">{{"报名编号为"+order.sn_order}}</text></block></view></view></block></gui-page>