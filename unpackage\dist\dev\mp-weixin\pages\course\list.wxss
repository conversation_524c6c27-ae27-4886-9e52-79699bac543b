@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page,
.content {
  background: #f8f8f8;
}
.content {
  padding-top: 20rpx;
}
.navbar {
  position: fixed;
  left: 0;
  top: 0px;
  display: flex;
  width: 100%;
  height: 80rpx;
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  z-index: 10;
}
.navbar .nav-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 30rpx;
  color: #303133;
  position: relative;
}
.navbar .nav-item.current {
  color: #fa436a;
}
.navbar .nav-item.current:after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 120rpx;
  height: 0;
  border-bottom: 4rpx solid #fa436a;
}
.navbar .p-box {
  display: flex;
  flex-direction: column;
}
.navbar .p-box .yticon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  height: 14rpx;
  line-height: 1;
  margin-left: 4rpx;
  font-size: 26rpx;
  color: #888;
}
.navbar .p-box .yticon.active {
  color: #fa436a;
}
.navbar .p-box .xia {
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
}
.navbar .cate-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 80rpx;
  position: relative;
  font-size: 44rpx;
}
.navbar .cate-item:after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-left: 1px solid #ddd;
  width: 0;
  height: 36rpx;
}
/* 分类 */
.cate-mask {
  position: fixed;
  left: 0;
  top: 0px;
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0);
  z-index: 95;
  transition: .3s;
}
.cate-mask .cate-content {
  width: 630rpx;
  height: 100%;
  background: #fff;
  float: right;
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
  transition: .3s;
}
.cate-mask.none {
  display: none;
}
.cate-mask.show {
  background: rgba(0, 0, 0, 0.4);
}
.cate-mask.show .cate-content {
  -webkit-transform: translateX(0);
          transform: translateX(0);
}
.cate-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.cate-list .cate-item {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding-left: 30rpx;
  font-size: 28rpx;
  color: #555;
  position: relative;
}
.cate-list .two {
  height: 64rpx;
  color: #303133;
  font-size: 30rpx;
  background: #f8f8f8;
}
.cate-list .active {
  color: #fa436a;
}
/* 商品列表 */
.goods-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx;
  background: #fff;
}
.goods-list .goods-item {
  display: flex;
  flex-direction: column;
  width: 48%;
  padding-bottom: 40rpx;
}
.goods-list .goods-item:nth-child(2n+1) {
  margin-right: 4%;
}
.goods-list .image-wrapper {
  width: 100%;
  height: 180rpx;
  border-radius: 3px;
  overflow: hidden;
}
.goods-list .image-wrapper image {
  width: 100%;
  height: 100%;
  opacity: 1;
}
.goods-list .title {
  font-size: 32rpx;
  color: #303133;
  font-size: 28rpx;
  line-height: 45rpx;
}
.goods-list .price-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 10rpx;
  font-size: 22rpx;
  color: #909399;
}
.goods-list .price {
  font-size: 32rpx;
  color: #007aff;
  line-height: 1;
}
.goods-list .price:before {
  content: '￥';
  font-size: 26rpx;
}
.list-box {
  padding-bottom: 20rpx;
}
.list-box .item-box {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
}
.list-box .item-box .top-box {
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot {
  width: 50%;
  height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot .cover {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box-hot .cover :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.list-box .item-box .top-box .cover-box-hot .button {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-box {
  width: 50%;
  height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box .cover {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box .button {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .info-box {
  flex: 1;
  margin-left: 15rpx;
  height: 200rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.list-box .item-box .top-box .info-box .publish-date {
  font-size: 32rpx;
  font-weight: bold;
}
.list-box .item-box .top-box .info-box .lang-box {
  color: #999;
  font-size: 24rpx;
}
.list-box .item-box .top-box .info-box .title {
  font-weight: bold;
  font-size: 26rpx;
  color: #666666;
}
.list-box .item-box .top-box .info-box .end-date {
  font-size: 20rpx;
  color: #999999;
}
.list-box .item-box .top-box .info-box .total {
  font-size: 20rpx;
  color: #39b54a;
}
.list-box .item-box .top-box .info-box .des {
  font-size: 22rpx;
  color: #8f8f94;
}
.list-box .item-box .top-box .info-box .price {
  font-size: 24rpx;
  color: red;
  float: right;
}
.list-box .item-box .top-box .info-box .end {
  font-size: 24rpx;
  color: red;
  width: 100%;
}
.list-box .item-box .margin-tb-sm {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list-box .item-box .margin-tb-sm .text-sm {
  font-size: 24rpx;
}
.list-box .item-box .margin-tb-sm .title {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.list-box .item-box .margin-tb-sm .uni-row {
  flex-direction: row;
}
.list-box .item-box .margin-tb-sm .align-center {
  align-items: center;
}
.list-box .item-box .margin-tb-sm .margin-left-sm {
  margin-left: 20rpx;
}
/* 空白页 */
.empty {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  padding-bottom: 100rpx;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background: #fff;
}
.empty image {
  width: 240rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}
.empty .empty-tips {
  display: flex;
  font-size: 26rpx;
  color: #C0C4CC;
}
.empty .empty-tips .navigator {
  color: #007aff;
  margin-left: 16rpx;
}

