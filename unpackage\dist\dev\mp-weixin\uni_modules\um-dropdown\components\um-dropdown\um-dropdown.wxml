<view data-ref="dropdown" class="option vue-ref" style="{{'width:'+(width)+';'+('height:'+(height)+';')}}"><view data-event-opts="{{[['tap',[['fnShowOptionList']]]]}}" class="option-select-title" bindtap="__e"><input class="inp-select" placeholder="请选择" disabled="{{true}}" value="{{value}}"/><view class="{{['trans',showOptionList?'trans-from':'']}}"><um-icon vue-id="290bdfb4-1" name="down" bind:__l="__l"></um-icon></view></view><view class="option-list" style="{{$root.s0}}"><view class="option-list-padding"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['fnChangeOption',['$0',index],[[['optionList','',index]]]]]]]}}" class="option-item" style="{{item.s1}}" catchtap="__e">{{''+(rangeKey?item.$orig[rangeKey]:item.$orig)+''}}</view></block></block></view></view></view>