{"id": "qrcodeicu-render", "displayName": "老码十途-调用接口5秒合成图片，营销海报、商品详情图无痛搞定，nvue也能用", "version": "1.2.0", "description": "根治小程序营销海报各种兼容性疑难杂症；所见即所得、即改即生效．更多文档请移步 https://qrcode.icu/site/#/sdks", "keywords": ["海报", "poster", "图片合成", "图片处理"], "repository": "", "engines": {"HBuilderX": "^3.1.19"}, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "860608033"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y", "小红书": "y"}, "快应用": {"华为": "y", "联盟": "y"}}}}}