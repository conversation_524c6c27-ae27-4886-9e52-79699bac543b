/* 学习小组通用样式 */

/* 全局变量 */
:root {
  --primary-color: #2094CE;
  --success-color: #52c41a;
  --warning-color: #fa8c16;
  --error-color: #ff4d4f;
  --text-color: #333;
  --text-secondary: #666;
  --text-disabled: #999;
  --border-color: #f0f0f0;
  --bg-color: #F8F8F8;
  --card-bg: #fff;
  --border-radius: 20rpx;
  --shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

/* 通用布局 */
.container {
  padding: 30rpx;
}

.section {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.section-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.section-content {
  padding: 30rpx;
}

/* 卡片样式 */
.card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1rpx solid var(--border-color);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.3s;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #f8f9fa;
}

.list-item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.list-item-extra {
  margin-left: 20rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background: var(--primary-color);
  color: #fff;
}

.btn-secondary {
  background: #f0f0f0;
  color: var(--text-secondary);
}

.btn-success {
  background: var(--success-color);
  color: #fff;
}

.btn-warning {
  background: var(--warning-color);
  color: #fff;
}

.btn-danger {
  background: var(--error-color);
  color: #fff;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  line-height: 1;
}

.tag-primary {
  background: rgba(32, 148, 206, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background: rgba(250, 140, 22, 0.1);
  color: var(--warning-color);
}

.tag-danger {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.tag-gray {
  background: #f0f0f0;
  color: var(--text-disabled);
}

/* 进度条样式 */
.progress {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s;
}

.progress-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-left: 10rpx;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 20rpx;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.grid-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }

/* 文本工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--error-color); }
.text-secondary { color: var(--text-secondary); }
.text-disabled { color: var(--text-disabled); }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 48rpx; }

/* 显示/隐藏 */
.hidden { display: none; }
.visible { display: block; }

/* 圆角 */
.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-xl { border-radius: 20rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影 */
.shadow-sm { box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05); }
.shadow { box-shadow: var(--shadow); }
.shadow-lg { box-shadow: 0 8rpx 30rpx rgba(0,0,0,0.15); }

/* 边框 */
.border { border: 1rpx solid var(--border-color); }
.border-t { border-top: 1rpx solid var(--border-color); }
.border-b { border-bottom: 1rpx solid var(--border-color); }
.border-l { border-left: 1rpx solid var(--border-color); }
.border-r { border-right: 1rpx solid var(--border-color); }
