<gui-page class="vue-ref" vue-id="5c069991-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#fff;" slot="gBody"><view class="navbar"><block wx:for="{{listtabs1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabClick',[index]]]]]}}" class="{{['nav-item',(currenttabs1===index)?'current':'']}}" bindtap="__e">{{''+item.text+''}}</view></block></view><swiper class="swiper-box" style="{{'height:'+(mainHeight+'px')+';'}}" current="{{currenttabs1}}" duration="300" data-event-opts="{{[['change',[['changeTab',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="tabItem" wx:for-index="tabIndex" wx:key="tabIndex"><swiper-item class="tab-content"><scroll-view class="list-scroll-content" scroll-y="{{true}}"><block wx:if="{{tabItem.g0}}"><empty1 vue-id="{{('5c069991-2-'+tabIndex)+','+('5c069991-1')}}" bind:__l="__l"></empty1></block><view><view class="list-box"><block wx:for="{{tabItem.$orig.orderList}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToWeb',['$0'],[[['listtabs1','',tabIndex],['orderList','',index1]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item1.thumb}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item1.title}}</view><view class="des">{{item1.description}}</view><view class="end"><text style="text-align:right;float:right;">详情</text></view></view></view></view></block></view></view></scroll-view></swiper-item></block></swiper></view></gui-page>