<view class="{{['data-v-4b6f47fe','gui-flex','gui-columns','gui-sbody',fullPage?'gui-flex1':'',refresh||loadmore?'gui-flex1':'']}}"><block wx:if="{{customHeader}}"><view class="gui-header gui-transition-all data-v-4b6f47fe vue-ref" style="{{('height:'+(headerSets.height+statusBarHeight)+'px; z-index:'+headerSets.zIndex+';'+headerStyle)}}" id="guiPageHeader" data-ref="guiPageHeader"><view class="gui-page-status-bar data-v-4b6f47fe" style="{{('height:'+statusBarHeight+'px;'+statusBarStyle)}}"></view><view data-event-opts="{{[['tap',[['headerTap',['$event']]]]]}}" class="gui-flex gui-columns gui-justify-content-center data-v-4b6f47fe" style="{{'height:'+(headerSets.height+'px')+';'}}" catchtap="__e"><slot name="gHeader"></slot></view></view></block><block wx:if="{{customHeader&&isHeaderSized}}"><view style="{{('height:'+(headerSets.height+statusBarHeight)+'px; '+headerSizedStyle+';')}}" class="data-v-4b6f47fe"></view></block><block wx:if="{{!refresh&&!loadmore}}"><view class="{{['gui-flex','gui-columns','data-v-4b6f47fe','vue-ref',fullPage?'gui-flex1':'']}}" id="guiPageBody" data-ref="guiPageBody"><slot name="gBody"></slot></view></block><block wx:if="{{refresh||loadmore}}"><view class="gui-flex gui-columns gui-flex1 data-v-4b6f47fe vue-ref" style="{{'margin-top:'+(fixedTopMargin+'px')+';'+('height:'+(refreshBodyHeight+'px')+';')}}" id="guiPageBody" data-ref="guiPageBody"><scroll-view class="gui-relative data-v-4b6f47fe" style="{{'height:'+(refreshBodyHeight+'px')+';'+('opacity:'+(refreshBodyHeight<1?0:1)+';')}}" scroll-y="{{true}}" show-scrollbar="{{false}}" scroll-top="{{scrollTop}}" data-event-opts="{{[['touchstart',[['touchstart',['$event']]]],['touchmove',[['touchmove',['$event']]]],['touchend',[['touchend',['$event']]]],['scroll',[['scroll',['$event']]]],['scrolltolower',[['loadmorefun',['$event']]]]]}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindscroll="__e" bindscrolltolower="__e"><view class="data-v-4b6f47fe"><gui-refresh vue-id="199ab0f8-1" refreshText="{{refreshText}}" refreshBgColor="{{refreshBgColor}}" refreshColor="{{refreshColor}}" refreshFontSize="{{refreshFontSize}}" data-ref="guiPageRefresh" data-event-opts="{{[['^reload',[['reload']]]]}}" bind:reload="__e" class="data-v-4b6f47fe vue-ref" bind:__l="__l"></gui-refresh></view><slot name="gBody"></slot><block wx:if="{{loadmore}}"><view class="gui-page-loadmore data-v-4b6f47fe"><gui-loadmore vue-id="199ab0f8-2" loadMoreText="{{loadMoreText}}" loadMoreColor="{{loadMoreColor}}" loadMoreFontSize="{{loadMoreFontSize}}" data-ref="guipageloadmore" class="data-v-4b6f47fe vue-ref" bind:__l="__l"></gui-loadmore></view></block></scroll-view></view></block><block wx:if="{{customFooter}}"><view style="{{'height:'+(footerHeight)+';'}}" class="data-v-4b6f47fe"></view></block><block wx:if="{{customFooter}}"><view class="{{['gui-page-footer','gui-border-box','data-v-4b6f47fe','vue-ref',isSwitchPage?'gui-switch-page-footer':'']}}" style="{{'height:'+(footerHeight)+';'+('background-image:'+(footerSets.bg)+';')+('z-index:'+(footerSets.zIndex)+';')}}" id="guiPageFooter" data-ref="guiPageFooter"><view class="data-v-4b6f47fe"><slot name="gFooter"></slot></view><view style="{{('height:'+iphoneXButtomHeight+'; '+iphoneXButtomStyle)}}" class="data-v-4b6f47fe"></view></view></block><view class="gui-page-pendant data-v-4b6f47fe" style="{{'right:'+(pendantSets.right)+';'+('bottom:'+(pendantSets.bottom)+';')+('width:'+(pendantSets.width)+';')+('z-index:'+(pendantSets.zIndex)+';')}}"><slot name="gPendant"></slot></view><view class="gui-page-fixed-top data-v-4b6f47fe vue-ref" style="{{'top:'+(fixedTop+'px')+';'+('z-index:'+(fixedTopZIndex)+';')}}" id="guiPageFixedTop" data-ref="guiPageFixedTop"><slot name="gFixedTop"></slot></view><gui-page-loading vue-id="199ab0f8-3" data-ref="guipageloading" class="data-v-4b6f47fe vue-ref" bind:__l="__l"></gui-page-loading></view>