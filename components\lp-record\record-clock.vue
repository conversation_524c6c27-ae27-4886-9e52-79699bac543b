<template>
	<view class="root-box">
		<canvas class="canvas" canvas-id="canvas" :style="{display:show ? 'block' : 'none'}">
			<view class="content">
				<slot></slot>
			</view>
		</canvas>
	</view>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				// 目标百分比
				value: 0,
				// 当前值
				currentValue: 0,
				// draw
				drawTimer: null,
				// 角度
				angle: 0,
				// 外圆
				canvasCenter: {
					width: 100,
					height: 100,
				},
				// 内圆
				innerCenter: {
					x: 50,
					y: 50,
					radius: 44
				}
			};
		},

		created() {
			//this.initDraw();
			this.resetDraw();
		},

		methods: {
			setValue: function(v) {
				this.value = v;
				this.pauseDraw();
				this.startDraw();
			},
			//-----------------------------------------------------------------------------------------------
			//
			// Draw
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 初始
			 */
			initDraw: function() {},

			/**
			 * 从置
			 */
			resetDraw: function() {
				this.pauseDraw();
				this.currentValue = 0;

				//清除canvas内容 方式一：不知道为啥 不起作用
				//this.canvasObj.clearRect(0,0,this.canvasObj.width,this.canvasObj.height);
				//清除canvas内容 方式二：填充canvas为白色
				this.canvasObj = uni.createCanvasContext('canvas', this);
				this.canvasObj.setFillStyle('#fff')
				this.canvasObj.fillRect(0, 0, this.canvasObj.width, this.canvasObj.height)
				this.canvasObj.draw();

				this.drawBg();
			},

			startDraw: function() {
				const _this = this;
				this.drawTimer = setInterval(function() {
					const start = _this.currentValue;
					_this.currentValue += (_this.value - _this.currentValue) * 0.1;
					_this.draw(start, _this.currentValue);
					if (_this.value - _this.currentValue <= 0.001) {
						_this.currentValue = _this.value;
						_this.pauseDraw();
					}

				}, 50);
			},

			pauseDraw: function() {
				clearInterval(this.drawTimer);
			},

			drawBg: function() {
				const _this = this;
				let centerX = _this.innerCenter.x;
				let centerY = _this.innerCenter.y;
				let radius = _this.innerCenter.radius;

				// 录音过程圆圈动画的背景园
				_this.canvasObj.beginPath();
				_this.canvasObj.setStrokeStyle("#fe3b54");
				_this.canvasObj.setGlobalAlpha(0.3)
				_this.canvasObj.setLineWidth(3);
				_this.canvasObj.arc(centerX, centerY, radius, 0, 2 * Math.PI);
				_this.canvasObj.stroke();
				_this.canvasObj.draw();
			},

			draw: function(startValue, endValue) {
				const _this = this;
				const innerCenter = _this.innerCenter;
				const PI = Math.PI;

				let centerX = innerCenter.x;
				let centerY = innerCenter.y;
				let radius = innerCenter.radius;

				// 录音过程圆圈动画
				_this.canvasObj.beginPath();
				_this.canvasObj.setStrokeStyle("#fe3b54");
				_this.canvasObj.setGlobalAlpha(1)
				_this.canvasObj.setLineWidth(3);
				_this.canvasObj.arc(centerX, centerY, radius, 2 * PI * startValue - 0.5 * PI, 2 * PI * endValue -
					0.5 * PI, false);
				_this.canvasObj.stroke();
				_this.canvasObj.draw(true);
			},
		},
	}
</script>

<style lang="scss" scoped>
	canvas {
		margin: 10rpx 60rpx;
		position: relative;
		width: 200rpx;
		height: 200rpx;
		z-index: 10;
	}
</style>
