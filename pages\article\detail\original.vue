<template>
	<view class="root lp-flex-column" v-if="article" style="height: 100%;">
		<scroll-view enable-flex="true" v-if="type=='original' || article.is_trans" class="root-box" scroll-y="true"
			:scroll-top="custom_y" @scroll="onScrollHandler">
			<!-- 文章内容 -->
			<view class="panel main-box">
				<view class="head head-box">
					<view class="left-box">
						<view class="title-icon"></view>
						<text class="publish-date">{{article.publish_date}}</text>
						<text class="lang">{{article.trans_dir.substr(isOriginal ? 0 : 2,1)}}</text>
					</view>
					<view class="right-box">
						<text class="gui-icons"
							@tap="onLikeHandler">{{article.is_collect ? '&#xe605;' : '&#xe6ad;'}}</text>
					</view>
				</view>

				<view class="body">
					<!-- 取第个内容的标题 -->
					<view class="title-box">{{contents[0].title}}</view>
					<view class="cover-box">
						<image class="cover" mode="aspectFill" :src="article.picture"></image>
					</view>
			
						<view class="content-box">
							<view class="rich-box" v-if="article.type != 2">
								<rich-text v-for="(item ,index) in contents" :key="item.id"
									@longpress="onSetClipboardDataHandler(item.content)"
									:nodes="item.content | formatRichText"></rich-text>
							</view>
							<!-- <view v-else>
								<lp-audio-player class="source-player" ref="sourcePlayer" :audio="articleAudio">
								</lp-audio-player>
							</view> -->
						</view>
				

					<view class="content-foot-box">
						<!-- <text>主编：{{article.editor_text}}</text>
						<text>审校：{{article.reviser_text}}</text> -->
						<text><text style="width: 120rpx;">文章来源：</text>{{article.source ? article.source : '--'}}</text>
						<text class="declaration"><text style="width: 120rpx;">声明：</text>本材料为独家材料，版权归《{{article.source ? article.source : '--'}}》所有，仅供培训班内学员学习使用，禁止转载传播，一经发现将追究法律责任。</text>
					</view>
				</view>
			</view>

			<!-- 打卡 -->
			<!-- <view class="panel sign-in-box">
				<view class="head">
					<view class="title-icon"></view>
					<text>打卡</text>
				</view>

				<view class="body">-->
					<!-- 会员查看 -->
				<!--	<lp-auth-content :permission="article.is_read"
						:auth_url="'/pages/course/detail/detail?course_id='+article.course_id">
						<view class="btn-box">
							<view v-if="!article.is_clock" class="btn" @tap="doClock()">马上打卡</view>
							<template v-else>
								<view class="btn done" @tap="goShare()">已打卡</view>
								<text class="sign-in-time">{{article.clock_time.created_at}}</text>
							</template>
						</view>
					</lp-auth-content>
				</view>
			</view> -->

			<!-- 翻译  -->
			<view class="panel tran-box" v-if="isOriginal">
				<view class="head">
					<view class="title-icon"></view>
					<view class="title-box">
						<text>翻译</text>
						<!-- <text class="des">（您可以同时提交文本和音频）</text> -->
					</view>
				</view>

				<view class="body">

						<lp-tran :article="article" :type="type" @get-input="onGetInputHandler"
							@dirty="onTranDirtyHandler"></lp-tran>
				</view>
			</view>

			<!-- 名师解析  -->
			<view class="panel analyse-box" v-if="!isOriginal && article.comment.length">
				<view class="head">
					<view class="title-icon"></view>
					<view class="title-box">
						<text>译文解析</text>
					</view>
				</view>

				<view class="body">

						<lp-analyse v-for="(item ,index) in article.comment" :key="item.id" :comment="item">
						</lp-analyse>
				</view>
			</view>
			
			<!-- 名师点评  -->
			<view class="panel comment-box" v-if="!isOriginal && article.trans.length && article.trans[0].comments.length">
				<view class="head">
					<view class="title-icon"></view>
					<view class="title-box">
						<text>老师点评</text>
					</view>
				</view>
			
				<view class="body">
						<view class="content-box">
							<template v-for="(comment,index) in article.trans[0].comments">
								<view v-if="comment.author" style="font-size: 32rpx;font-weight: bold;">{{comment.author.name}}点评：</view>
								<rich-text :key="comment.id"
									@longpress="onSetClipboardDataHandler(comment.content)"
									:nodes="comment.content | formatRichText"></rich-text>
							</template>
						</view>
				</view>
			</view>
		</scroll-view>

		<view class="nopublish lp-flex-column lp-flex-center" v-else>
			<text class="gui-icons" style="font-size: 100rpx;">&#xe687;</text>
			<text>译文即将公布!</text>
		</view>

		<lp-input ref="input" v-if="isOriginal"></lp-input>
	</view>
	<view class="nopublish lp-flex-column lp-flex-center" v-else>
		<text class="gui-icons">数据正在玩命加载中...</text>
	</view>
</template>

<script>
	import lpTran from './tran.vue';
	import lpAnalyse from './analyse.vue';
	import lpAudioPlayer from '@/components/audio-player/audio-player.vue';
	import util from '@/common/js/util.js';

	let uploader;

	export default {
		components: {
			lpTran,
			lpAnalyse,
			lpAudioPlayer
		},
		props: {
			article_id: {
				type: String,
				default: 1
			},
			type: {
				type: String,
				default: 'original'
			}
		},
		data() {
			return {
				article: null,
				custom_y: 0,
			}
		},
		onReady() {
			this.ready();
			uni.$on('article_update', (article) => {
				this.article = article;
			});
		},
		computed: {
			articleAudio: function() {
				return {
					src: this.contents ? this.contents[0].file : ''
				}
			},
			isOriginal: function() {
				return this.type == 'original';
			},
			// 当前内容
			contents: function() {
				return this.article ? this.article[this.type] : null;
			}
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			ready: function() {
				let self = this;
				if(this.isOriginal){
					this.apiGetDetail(this.article_id).then(data => {
						this.article = data;
						self.sendArticleUpdate();
					});
				}
			},
			/**
			 * 打卡
			 */
			doClock: function() {
				this.apiDoClock(this.article_id).then((data) => {
					uni.showToast({
						title: '打卡成功'
					});
					this.article.is_clock = 1;
					this.article.clock_time = {
						created_at: data.created_at
					};
					this.sendArticleUpdate();
					setTimeout(() => {
						this.goShare();
					}, 1500)
				});
			},

			goShare: function() {
				uni.navigateTo({
					url: '/pages/article/sign-in-share/sign-in-share?article_id=' + this.article_id
				});
			},

			sendArticleUpdate: function() {
				uni.$emit('article_update', this.article);
			},
			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onLikeHandler: function() {
				const is_collect = this.article.is_collect;
				this.apiDoCollect(this.article.id, is_collect ? 2 : 1).then(data => {
					if (data.status) {
						uni.showToast({
							icon: 'none',
							title: is_collect ? '取消收藏' : '收藏成功'
						});
						this.article.is_collect = !is_collect;
						this.sendArticleUpdate();
					}
				})
			},
			onSetClipboardDataHandler: function(text) {
				console.log(text);
				uni.setClipboardData({
					data: util.html.getPureContent(text),
					success: function() {
						uni.showToast({
							icon: 'none',
							title: '已复制'
						})
					}
				});
			},
			onScrollHandler: function(event) {
				//this.custom_y = event.detail.scrollTop;
			},
			onTranDirtyHandler: function() {
				this.custom_y = 99999;
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 获取文章详情
			 * @param {Object} id
			 */
			apiGetDetail: function(id) {
				return this.$http.get('/v1/article_detail', {
					params: {
						id
					}
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
			/**
			 * 打卡
			 * @param {Object} id 文章ID
			 */
			apiDoClock: function(id) {
				return this.$http.post('/v1/doClock', {
					id
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},
			/**
			 * 收藏
			 * @param {string} id 文章ID
			 * @param {int} type 1 收藏 2取消收藏
			 */
			apiDoCollect: function(id, type) {
				return this.$http.post('/v1/doCollect', {
					id,
					type
				}).then((res) => {
					return Promise.resolve(res.data);
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.panel {
		margin-bottom: 50rpx;

		.head {
			display: flex;
			font-size: 32rpx;
			margin: 30rpx;
			font-weight: bold;
		}

		.body {
			padding: 30rpx;
		}

		.title-icon {
			width: 10rpx;
			background-color: $uni-color-primary;
			margin-right: 20rpx;
		}

		.des {
			font-size: 24rpx;
			font-weight: 100;
			color: $uni-text-color-grey;
		}

	}

	.root{
		display: flex;
	}
	.root-box {
		height: 1000rpx;
		flex:1;
		display: flex;
		flex-direction: column;

		/* 文章内容 */
		.main-box {
			.head-box {
				.left-box {
					display: flex;
					flex: 1;

					.lang {
						background: #aaa;
						padding: 5rpx 15rpx;
						font-size: 24rpx;
						border-radius: 10rpx;
						color: #fff;
						font-weight: unset;
						margin: 0 10rpx;

					}
				}

				.right-box {
					.gui-icons {
						color: $uni-color-error;
					}
				}
			}

			.title-box {
				font-size: 40rpx;
			}

			.cover-box {
				margin: 30rpx 0;
				height: 600rpx;

				.cover {
					width: 100%;
					height: 100%;
				}
			}

			.content-box {
				.rich-box {
					margin-bottom: 20rpx;
				}
			}

			.content-foot-box {
				border-top: solid 1px #eee;
				display: flex;
				flex-direction: column;
				padding: 20rpx 0;
				font-size: 26rpx;
				color: #aaa;
				line-height: 40rpx;
				margin-top: 20rpx;
			}
			
			.declaration{
				color: $uni-color-error;
			}
		}

		/* 打卡 */
		.sign-in-box {
			.sign-in-time {
				color: $uni-text-color-grey;
				text-align: center;
				font-size: 24rpx;
				line-height: 50rpx;
			}

			.btn-box {
				display: flex;
				flex-direction: column;
				justify-content: center;

				.btn {
					border: solid 1px $uni-color-error;
					background-color: $uni-color-error;
					color: #fff;
					font-size: 32rpx;
					padding: 20rpx;
					text-align: center;
					border-radius: 40rpx;
				}

				.done {
					background-color: unset;
					color: $uni-color-error;
				}
			}
		}

		/* 翻译 */
		.tran-box {
			.title-box {
				display: flex;
				align-items: flex-end;
			}
		}
		
		/* 点评 */
		.comment-box{
			.content-box{
				font-size: 28rpx;
				border: solid 1px #eee;
				padding: 30rpx;
				background: #f5f5f5;
				border-radius: 10rpx;
				color: #999;
			}
		}
	}

	.nopublish {
		height: 400rpx;
		text-align: center;
		color: #c5c5c5;
		font-size: 28rpx;
	}
</style>
