{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/groups/index.vue?3047", "webpack:///D:/日语云课/pages/groups/index.vue?e28e", "webpack:///D:/日语云课/pages/groups/index.vue?2da0", "webpack:///D:/日语云课/pages/groups/index.vue?f326", "uni-app:///pages/groups/index.vue", "webpack:///D:/日语云课/pages/groups/index.vue?1266", "webpack:///D:/日语云课/pages/groups/index.vue?4a48"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "hasGroupPermission", "selectedGroupId", "selectedGroup", "selectedGroupIndex", "groupsLoading", "conceptTutorial", "id", "title", "description", "totalLessons", "completedLessons", "categories", "name", "icon", "lessons", "current<PERSON>iew", "selectedDate", "currentPracticeType", "groupList", "practiceTypes", "count", "reviewCourses", "date", "teacher", "duration", "thumbnail", "groupId", "practices", "type", "questionCount", "status", "computed", "totalMembers", "filteredReviewCourses", "courses", "filteredPractices", "practice", "onLoad", "onShow", "methods", "checkPermission", "console", "userToken", "userInfo", "<PERSON><PERSON><PERSON><PERSON>", "checkGroupAccess", "isLoggedIn", "uni", "initializeData", "loadGroupsFromAPI", "apiService", "response", "level", "members", "progress", "completedCourses", "totalCourses", "startDate", "endDate", "enterConceptCategory", "mask", "category", "url", "startConceptLearning", "limit", "firstCourse", "continueConceptLearning", "lastRecord", "viewConceptProgress", "selectConceptTutorial", "selectGroupForDetail", "selectGroup", "itemList", "success", "fail", "switchView", "onDateChange", "selectPracticeType", "quickViewReview", "setTimeout", "quickViewPractice", "playCourseReview", "startPractice", "viewGroupMembers", "getGroupColor", "goToLogin", "contactAdmin", "content", "showCancel", "confirmText", "enterGroup", "joinGroup"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAmlB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkRvmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC,aACA;UACAL;UACAM;UACAC;UACAC;UACAN;QACA,GACA;UACAF;UACAM;UACAC;UACAC;UACAN;QACA,GACA;UACAF;UACAM;UACAC;UACAC;UACAN;QACA,GACA;UACAF;UACAM;UACAC;UACAC;UACAN;QACA;MAEA;MACAO;MAAA;MACAC;MACAC;MACAC;MACA;MACAC,gBACA;QAAAb;QAAAM;QAAAC;QAAAO;MAAA,GACA;QAAAd;QAAAM;QAAAC;QAAAO;MAAA,GACA;QAAAd;QAAAM;QAAAC;QAAAO;MAAA,GACA;QAAAd;QAAAM;QAAAC;QAAAO;MAAA,EACA;MACA;MACAC,gBACA;QACAf;QACAC;QACAe;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACApB;QACAC;QACAe;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACApB;QACAC;QACAe;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACApB;QACAC;QACAe;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;MACAC,YACA;QACArB;QACAC;QACAe;QACAM;QACAC;QACAL;QACAM;QACAJ;MACA,GACA;QACApB;QACAC;QACAe;QACAM;QACAC;QACAL;QACAM;QACAJ;MACA;IAEA;EACA;EACAK;IACA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QAAA,OACA;MAAA,EACA;MAEA;QACAC;UAAA;QAAA;MACA;MAEA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;QAAA,OACA;MAAA,EACA;MAEA;QACAR;UAAA;UAAA,OACAS;YAAA;UAAA;QAAA,EACA;MACA;MAEA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;IACA;IACAC;MACAC;;MAEA;MACA;MACA;MACA;MAEAA;QACAC;QACAC;QACAC;MACA;MAEA;QACAH;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACA;MAEAJ;MACAA;MACAA;MACAA;QAAAG;QAAAE;MAAA;;MAEA;MACA;QACA;QACAL;QACA;MACA;;MAEA;MACA;QACA;QACAA;MACA;QACA;QACAA;MACA;MAEAA;;MAEA;MACA;MAEA;QACAA;QACA;QACAM;UACAxC;UACAM;UACAW;QACA;MACA;IACA;IAEA;IACAwB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAR;gBAAA;gBAAA,OAEAS;kBACApB;gBACA;cAAA;gBAFAqB;gBAIAV;;gBAEA;gBACA;kBAAA;kBAAA;oBACAnC;oBACAM;oBACAJ;oBACA4C;oBACAtB;oBACAjB;oBACAwC;oBACAnB;oBACAoB;oBACAC;oBACAC;oBACAjC;oBACAkC;oBACAC;kBACA;gBAAA;gBAEAjB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAM;kBACAxC;kBACAM;kBACAW;gBACA;;gBAEA;gBACA,oBACA;kBACAlB;kBACAM;kBACAJ;kBACA4C;kBACAtB;kBACAjB;kBACAwC;kBACAnB;kBACAoB;kBACAC;kBACAC;gBACA,EACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAlB;gBAEAM;kBACAxC;kBACAqD;gBACA;;gBAEA;gBAAA;gBAAA,OACAV;kBACAW;kBACA/B;gBACA;cAAA;gBAHAqB;gBAKAV;gBAEAM;;gBAEA;gBACAA;kBACAe;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;gBACAM;;gBAEA;gBACAA;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAhB;kBACAxC;kBACAqD;gBACA;;gBAEA;gBAAA;gBAAA,OACAV;kBACAW;kBACA/B;kBACAkC;gBACA;cAAA;gBAJAb;gBAMAJ;gBAEA;kBACAkB;kBACAlB;oBACAe;kBACA;gBACA;kBACAf;oBACAxC;oBACAM;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA4B;gBACAM;;gBAEA;gBACAA;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAnB;kBACAxC;kBACAqD;gBACA;;gBAEA;gBAAA;gBAAA,OACAV;kBACApB;kBACAkC;gBACA;cAAA;gBAHAb;gBAKAJ;gBAEA;kBACAoB;kBACApB;oBACAe;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;gBACAM;;gBAEA;gBACAA;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEArB;kBACAxC;kBACAqD;gBACA;;gBAEA;gBAAA;gBAAA,OACAV;cAAA;gBAAAC;gBACAV;gBAEAM;;gBAEA;gBACAA;kBACAe;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;gBACAM;;gBAEA;gBACAA;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACAxB;QACAxC;QACAiE,WACA,aACA,aACA,aACA,YACA;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA/B;QACAxC;QACAM;QACAW;MACA;MAEAuD;QACAhC;UACAe;QACA;MACA;IACA;IAEA;IACAkB;MACAjC;QACAxC;QACAM;QACAW;MACA;MAEAuD;QACAhC;UACAe;QACA;MACA;IACA;IAEA;IACAmB;MACAlC;QACAe;MACA;IACA;IAEA;IACAoB;MACAnC;QACAe;MACA;IACA;IAEA;IACAqB;MACApC;QACAxC;QACAM;QACAW;MACA;MAEAuD;QACAhC;UACAe;QACA;MACA;IACA;IAEA;IACAsB;MACA,cACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MACA;IACA;IAIA;IACAC;MACAtC;QACAe;MACA;IACA;IAEA;IACAwB;MACAvC;QACAxC;QACAgF;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAhD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAK;kBACAxC;kBACAgF;kBACAE;kBACAhB;oBACA;sBACA1B;wBACAe;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACAf;kBACAe;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;gBACAM;kBACAxC;kBACAM;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAjD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAK;kBACAxC;kBACAgF;kBACAE;kBACAhB;oBACA;sBACA1B;wBACAe;sBACA;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKAZ;cAAA;gBAEAH;kBACAxC;kBACAM;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA4B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC77BA;AAAA;AAAA;AAAA;AAAw3B,CAAgB,43BAAG,EAAC,C;;;;;;;;;;;ACA54B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6e8c2b60&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e8c2b60\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/index.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6e8c2b60&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hasGroupPermission ? _vm.groupList.length : null\n  var g1 = _vm.hasGroupPermission ? _vm.groupList.length : null\n  var l0 = _vm.hasGroupPermission\n    ? _vm.__map(_vm.groupList, function (group, index) {\n        var $orig = _vm.__get_orig(group)\n        var m0 = _vm.getGroupColor(index)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 =\n    _vm.hasGroupPermission &&\n    !(_vm.selectedGroupId === \"concept\") &&\n    _vm.selectedGroup\n      ? _vm.getGroupColor(_vm.selectedGroupIndex)\n      : null\n  var m2 =\n    _vm.hasGroupPermission &&\n    !(_vm.selectedGroupId === \"concept\") &&\n    _vm.selectedGroup\n      ? _vm.getGroupColor(_vm.selectedGroupIndex)\n      : null\n  var g2 =\n    _vm.hasGroupPermission &&\n    !(_vm.selectedGroupId === \"concept\") &&\n    _vm.selectedGroup\n      ? Math.floor(\n          (_vm.selectedGroup.courseCount * _vm.selectedGroup.progress) / 100\n        )\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1 clean-groups-page\">\n\t\t\t<!-- 有权限时显示内容 -->\n\t\t\t<view v-if=\"hasGroupPermission\" class=\"page-content\">\n\t\t\t\t<!-- 简洁的页面头部 -->\n\t\t\t\t<view class=\"page-header\">\n\t\t\t\t\t<view class=\"header-content\">\n\t\t\t\t\t\t<view class=\"title-section\">\n\t\t\t\t\t\t\t<text class=\"page-title\">🎓 GST派遣日语培训班</text>\n\t\t\t\t\t\t\t<text class=\"page-subtitle\">与同伴一起进步，共同成长</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stats-section\">\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{groupList.length}}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">个小组</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">{{totalMembers}}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">名成员</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t<text class=\"stat-number\">48</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">门课程</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 左右联动布局 -->\n\t\t\t\t<view class=\"split-layout\">\n\t\t\t\t\t<!-- 左侧小组列表 -->\n\t\t\t\t\t<view class=\"left-panel\">\n\t\t\t\t\t\t<view class=\"panel-header\">\n\t\t\t\t\t\t\t<text class=\"panel-title\">学习内容</text>\n\t\t\t\t\t\t\t<text class=\"panel-subtitle\">{{groupList.length + 1}}项</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<scroll-view class=\"group-list\" scroll-y=\"true\">\n\t\t\t\t\t\t\t<!-- 公共新概念教程 -->\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"concept-tutorial-item\"\n\t\t\t\t\t\t\t\t:class=\"{ 'active': selectedGroupId === 'concept' }\"\n\t\t\t\t\t\t\t\t@click=\"selectConceptTutorial()\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"concept-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t<text class=\"concept-title\">新概念教程</text>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-badge\">公共</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\tclass=\"group-item\"\n\t\t\t\t\t\t\t\tv-for=\"(group, index) in groupList\"\n\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t:class=\"{ 'active': selectedGroupId === group.id }\"\n\t\t\t\t\t\t\t\t@click=\"selectGroupForDetail(group, index)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<view class=\"simple-group-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"group-level-badge\" :style=\"{ background: getGroupColor(index) }\">\n\t\t\t\t\t\t\t\t\t\t{{group.level}}\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"simple-group-name\">{{group.name}}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"simple-status-dot\" :class=\"group.status\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 右侧详情面板 -->\n\t\t\t\t\t<view class=\"right-panel\">\n\t\t\t\t\t\t<!-- 新概念教程详情 -->\n\t\t\t\t\t\t<view v-if=\"selectedGroupId === 'concept'\" class=\"concept-detail-content\">\n\t\t\t\t\t\t\t<!-- 教程头部 -->\n\t\t\t\t\t\t\t<view class=\"concept-detail-header\">\n\t\t\t\t\t\t\t\t<view class=\"concept-bg\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-overlay\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-detail-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-title\">{{conceptTutorial.title}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-detail-subtitle\">{{conceptTutorial.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"concept-progress-info\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"progress-info-text\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时\n\t\t\t\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 学习分类 -->\n\t\t\t\t\t\t\t<view class=\"concept-categories\">\n\t\t\t\t\t\t\t\t<view class=\"categories-title\">学习分类</view>\n\t\t\t\t\t\t\t\t<view class=\"categories-grid\">\n\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\tclass=\"category-card\"\n\t\t\t\t\t\t\t\t\t\tv-for=\"(category, index) in conceptTutorial.categories\"\n\t\t\t\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t\t\t\t@click=\"enterConceptCategory(category)\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-icon\">{{category.icon}}</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-name\">{{category.name}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-desc\">{{category.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"category-lessons\">{{category.lessons}}课时</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"category-arrow\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 快速操作 -->\n\t\t\t\t\t\t\t<view class=\"concept-actions\">\n\t\t\t\t\t\t\t\t<view class=\"concept-action-title\">快速开始</view>\n\t\t\t\t\t\t\t\t<view class=\"concept-action-buttons\">\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn primary\" @click=\"startConceptLearning()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">🚀</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">开始学习</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">从第一课开始</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn secondary\" @click=\"continueConceptLearning()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">📖</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">继续学习</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">从上次位置继续</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"concept-btn tertiary\" @click=\"viewConceptProgress()\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-icon\">📊</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"concept-btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-title\">学习进度</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"concept-btn-desc\">查看详细进度</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 小组详情 -->\n\t\t\t\t\t\t<view v-else-if=\"selectedGroup\" class=\"detail-content\">\n\t\t\t\t\t\t\t<!-- 详情头部 -->\n\t\t\t\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t\t\t\t<view class=\"detail-bg\" :style=\"{ background: getGroupColor(selectedGroupIndex) }\">\n\t\t\t\t\t\t\t\t\t<view class=\"detail-overlay\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-avatar\">\n\t\t\t\t\t\t\t\t\t\t\t<image :src=\"selectedGroup.icon\" mode=\"aspectFit\" />\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"detail-info\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-title\">{{selectedGroup.name}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"detail-subtitle\">{{selectedGroup.description}}</text>\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"detail-level\">{{selectedGroup.level}}等级</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 统计卡片 -->\n\t\t\t\t\t\t\t<view class=\"detail-stats\">\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">📚</view>\n\t\t\t\t\t\t\t\t\t<view class=\"stat-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.courseCount}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">课程数量</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">👥</view>\n\t\t\t\t\t\t\t\t\t<view class=\"stat-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.memberCount}}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">小组成员</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-card-detail\">\n\t\t\t\t\t\t\t\t\t<view class=\"stat-icon-detail\">📈</view>\n\t\t\t\t\t\t\t\t\t<view class=\"stat-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-number-detail\">{{selectedGroup.progress}}%</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"stat-label-detail\">学习进度</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 进度详情 -->\n\t\t\t\t\t\t\t<view class=\"progress-detail\">\n\t\t\t\t\t\t\t\t<view class=\"progress-header\">\n\t\t\t\t\t\t\t\t\t<text class=\"progress-title\">学习进度</text>\n\t\t\t\t\t\t\t\t\t<text class=\"progress-value\">{{selectedGroup.progress}}%</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"progress-bar-detail\">\n\t\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\t\tclass=\"progress-fill-detail\"\n\t\t\t\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\t\t\t\twidth: selectedGroup.progress + '%',\n\t\t\t\t\t\t\t\t\t\t\tbackground: getGroupColor(selectedGroupIndex)\n\t\t\t\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t\t\t></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"progress-desc\">已完成 {{Math.floor(selectedGroup.courseCount * selectedGroup.progress / 100)}} / {{selectedGroup.courseCount}} 门课程</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 功能按钮 -->\n\t\t\t\t\t\t\t<view class=\"detail-actions\">\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large primary\" @click=\"quickViewReview(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">🎥</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">课程回顾</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">观看录制的课程视频</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large secondary\" @click=\"quickViewPractice(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">✍️</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">在线练习</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">完成课后练习题目</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large tertiary\" @click=\"enterGroup(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">📊</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">小组详情</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">查看详细信息和设置</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"action-row\">\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn-large quaternary\" @click=\"viewGroupMembers(selectedGroup)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-icon-large\">👥</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-content\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-title\">小组成员</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"btn-desc\">查看所有小组成员</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn-arrow-large\">→</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 未选择状态 -->\n\t\t\t\t\t\t<view v-else class=\"empty-detail\">\n\t\t\t\t\t\t\t<view class=\"empty-icon\">👈</view>\n\t\t\t\t\t\t\t<text class=\"empty-title\">选择一个小组</text>\n\t\t\t\t\t\t\t<text class=\"empty-desc\">点击左侧小组查看详细信息</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 无权限提示 -->\n\t\t<view v-else class=\"no-permission-page\">\n\t\t\t<view class=\"permission-container\">\n\t\t\t\t<view class=\"permission-icon\">🔒</view>\n\t\t\t\t<text class=\"permission-title\">访问受限</text>\n\t\t\t\t<text class=\"permission-desc\">您暂时没有访问学习小组的权限</text>\n\t\t\t\t<text class=\"permission-hint\">请联系管理员开通权限或使用授权账号登录</text>\n\t\t\t\t<view class=\"permission-actions\">\n\t\t\t\t\t<button class=\"btn-login\" @click=\"goToLogin\">重新登录</button>\n\t\t\t\t\t<button class=\"btn-contact\" @click=\"contactAdmin\">联系管理员</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\t</gui-page>\n</template>\n\n<script>\nimport apiService from '@/common/js/apiService.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\thasGroupPermission: false,\n\t\t\tselectedGroupId: null,\n\t\t\tselectedGroup: null,\n\t\t\tselectedGroupIndex: 0,\n\t\t\tgroupsLoading: false,\n\t\t\t// 新概念教程数据\n\t\t\tconceptTutorial: {\n\t\t\t\tid: 'concept',\n\t\t\t\ttitle: '新概念日语教程',\n\t\t\t\tdescription: '适合所有学员的基础教程',\n\t\t\t\ttotalLessons: 48,\n\t\t\t\tcompletedLessons: 0,\n\t\t\t\tcategories: [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'basic',\n\t\t\t\t\t\tname: '基础入门',\n\t\t\t\t\t\ticon: '🌱',\n\t\t\t\t\t\tlessons: 12,\n\t\t\t\t\t\tdescription: '日语基础知识和发音'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'grammar',\n\t\t\t\t\t\tname: '语法精讲',\n\t\t\t\t\t\ticon: '📝',\n\t\t\t\t\t\tlessons: 16,\n\t\t\t\t\t\tdescription: '系统学习日语语法'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'vocabulary',\n\t\t\t\t\t\tname: '词汇扩展',\n\t\t\t\t\t\ticon: '📚',\n\t\t\t\t\t\tlessons: 12,\n\t\t\t\t\t\tdescription: '常用词汇和表达'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 'conversation',\n\t\t\t\t\t\tname: '会话练习',\n\t\t\t\t\t\ticon: '💬',\n\t\t\t\t\t\tlessons: 8,\n\t\t\t\t\t\tdescription: '实用对话和交流'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t},\n\t\t\tcurrentView: 'review', // 'review' 或 'practice'\n\t\t\tselectedDate: '',\n\t\t\tcurrentPracticeType: 'all',\n\t\t\tgroupList: [],\n\t\t\t// 练习类型\n\t\t\tpracticeTypes: [\n\t\t\t\t{ id: 'listening', name: '听力练习', icon: '🎧', count: 25 },\n\t\t\t\t{ id: 'grammar', name: '语法练习', icon: '📝', count: 30 },\n\t\t\t\t{ id: 'vocabulary', name: '词汇练习', icon: '📚', count: 40 },\n\t\t\t\t{ id: 'speaking', name: '口语练习', icon: '🗣️', count: 15 }\n\t\t\t],\n\t\t\t// 课程回顾数据\n\t\t\treviewCourses: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '第一课：基础发音练习',\n\t\t\t\t\tdate: '2024-01-15',\n\t\t\t\t\tteacher: '田中老师',\n\t\t\t\t\tduration: '45:30',\n\t\t\t\t\tthumbnail: '/static/imgs/course-thumb1.png',\n\t\t\t\t\tgroupId: 1\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '第二课：日常问候语',\n\t\t\t\t\tdate: '2024-01-16',\n\t\t\t\t\tteacher: '佐藤老师',\n\t\t\t\t\tduration: '38:20',\n\t\t\t\t\tthumbnail: '/static/imgs/course-thumb2.png',\n\t\t\t\t\tgroupId: 1\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '第三课：数字与时间',\n\t\t\t\t\tdate: '2024-01-17',\n\t\t\t\t\tteacher: '山田老师',\n\t\t\t\t\tduration: '42:15',\n\t\t\t\t\tthumbnail: '/static/imgs/course-thumb3.png',\n\t\t\t\t\tgroupId: 2\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\ttitle: '第四课：家族称呼',\n\t\t\t\t\tdate: '2024-01-18',\n\t\t\t\t\tteacher: '田中老师',\n\t\t\t\t\tduration: '39:45',\n\t\t\t\t\tthumbnail: '/static/imgs/course-thumb4.png',\n\t\t\t\t\tgroupId: 2\n\t\t\t\t}\n\t\t\t],\n\t\t\t// 练习数据\n\t\t\tpractices: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '五十音图听力练习',\n\t\t\t\t\tdate: '2024-01-15',\n\t\t\t\t\ttype: '听力练习',\n\t\t\t\t\tquestionCount: 20,\n\t\t\t\t\tduration: 15,\n\t\t\t\t\tstatus: 'completed',\n\t\t\t\t\tgroupId: 1\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '基础语法选择题',\n\t\t\t\t\tdate: '2024-01-16',\n\t\t\t\t\ttype: '语法练习',\n\t\t\t\t\tquestionCount: 25,\n\t\t\t\t\tduration: 20,\n\t\t\t\t\tstatus: 'in-progress',\n\t\t\t\t\tgroupId: 1\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 总成员数\n\t\ttotalMembers() {\n\t\t\treturn this.groupList.reduce((total, group) => total + group.memberCount, 0);\n\t\t},\n\n\t\t// 过滤后的课程回顾\n\t\tfilteredReviewCourses() {\n\t\t\tlet courses = this.reviewCourses.filter(course =>\n\t\t\t\t!this.selectedGroup || course.groupId === this.selectedGroup.id\n\t\t\t);\n\n\t\t\tif (this.selectedDate) {\n\t\t\t\tcourses = courses.filter(course => course.date === this.selectedDate);\n\t\t\t}\n\n\t\t\treturn courses.sort((a, b) => new Date(b.date) - new Date(a.date));\n\t\t},\n\n\t\t// 过滤后的练习\n\t\tfilteredPractices() {\n\t\t\tlet practices = this.practices.filter(practice =>\n\t\t\t\t!this.selectedGroup || practice.groupId === this.selectedGroup.id\n\t\t\t);\n\n\t\t\tif (this.currentPracticeType !== 'all') {\n\t\t\t\tpractices = practices.filter(practice =>\n\t\t\t\t\tpractice.type === this.practiceTypes.find(t => t.id === this.currentPracticeType)?.name\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn practices.sort((a, b) => new Date(b.date) - new Date(a.date));\n\t\t}\n\t},\n\tasync onLoad() {\n\t\tthis.checkPermission();\n\t\tawait this.initializeData();\n\t},\n\tasync onShow() {\n\t\t// 每次显示页面时都检查权限，确保权限状态是最新的\n\t\tthis.checkPermission();\n\t\t// 刷新数据\n\t\tif (this.hasGroupPermission) {\n\t\t\tawait this.loadGroupsFromAPI();\n\t\t}\n\t},\n\tmethods: {\n\t\t// 检查用户权限\n\t\tcheckPermission() {\n\t\t\tconsole.log('=== 开始检查小组权限 ===');\n\n\t\t\t// 检查用户是否登录\n\t\t\tconst userToken = this.$store.state.user.token;\n\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\n\t\t\tconsole.log('权限检查数据:', {\n\t\t\t\tuserToken: userToken ? '存在' : '不存在',\n\t\t\t\tuserInfo: userInfo,\n\t\t\t\thasLogin: hasLogin\n\t\t\t});\n\n\t\t\tif (!userToken && !hasLogin) {\n\t\t\t\tconsole.log('用户未登录，拒绝访问');\n\t\t\t\tthis.hasGroupPermission = false;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 检查用户是否有小组权限\n\t\t\tthis.checkGroupAccess();\n\t\t},\n\t\t\n\t\t// 检查小组访问权限\n\t\tcheckGroupAccess() {\n\t\t\tconst userInfo = this.$store.state.user.userInfo;\n\t\t\tconst userMember = this.$store.state.user.member;\n\t\t\tconst hasLogin = this.$store.getters.hasLogin;\n\t\t\tconst isLoggedIn = this.$store.getters.isLoggedIn;\n\n\t\t\tconsole.log('=== 小组权限详细检查 ===');\n\t\t\tconsole.log('用户信息:', userInfo);\n\t\t\tconsole.log('会员信息:', userMember);\n\t\t\tconsole.log('登录状态:', { hasLogin, isLoggedIn });\n\n\t\t\t// 为彭伟用户(ID: 576)特别开放权限\n\t\t\tif (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {\n\t\t\t\tthis.hasGroupPermission = true;\n\t\t\t\tconsole.log('✅ 为用户彭伟开放小组权限');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 一般权限检查逻辑 - 只要登录就可以访问\n\t\t\tif (hasLogin || isLoggedIn || userInfo) {\n\t\t\t\tthis.hasGroupPermission = true;\n\t\t\t\tconsole.log('✅ 登录用户可以访问小组功能');\n\t\t\t} else {\n\t\t\t\tthis.hasGroupPermission = false;\n\t\t\t\tconsole.log('❌ 用户未登录，无法访问小组功能');\n\t\t\t}\n\n\t\t\tconsole.log('=== 最终权限结果:', this.hasGroupPermission, '===');\n\n\t\t\t// 强制触发视图更新\n\t\t\tthis.$forceUpdate();\n\n\t\t\tif (!this.hasGroupPermission) {\n\t\t\t\tconsole.log('权限被拒绝，用户信息:', userInfo);\n\t\t\t\t// 显示提示信息\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 初始化数据\n\t\tasync initializeData() {\n\t\t\tif (this.hasGroupPermission) {\n\t\t\t\tawait this.loadGroupsFromAPI();\n\t\t\t\t// 默认选择新概念教程\n\t\t\t\tthis.selectConceptTutorial();\n\t\t\t}\n\t\t},\n\n\t\t// 从API加载小组数据\n\t\tasync loadGroupsFromAPI() {\n\t\t\ttry {\n\t\t\t\tthis.groupsLoading = true;\n\t\t\t\tconsole.log('🔄 开始加载小组数据...');\n\n\t\t\t\tconst response = await apiService.getGroups({\n\t\t\t\t\tstatus: 'active'\n\t\t\t\t});\n\n\t\t\t\tconsole.log('✅ 小组数据加载成功:', response.data);\n\n\t\t\t\t// 转换数据格式以适配现有UI\n\t\t\t\tthis.groupList = response.data.groups.map(group => ({\n\t\t\t\t\tid: group.id,\n\t\t\t\t\tname: group.name,\n\t\t\t\t\tdescription: group.description,\n\t\t\t\t\tlevel: group.level,\n\t\t\t\t\tstatus: group.status,\n\t\t\t\t\ticon: group.icon || '/static/imgs/group-default.png',\n\t\t\t\t\tmembers: group.currentMembers || 0,\n\t\t\t\t\tcourses: group.courseRecords?.length || 0,\n\t\t\t\t\tprogress: Math.round(group.progress || 0),\n\t\t\t\t\tcompletedCourses: Math.floor((group.progress || 0) / 100 * (group.courseRecords?.length || 0)),\n\t\t\t\t\ttotalCourses: group.courseRecords?.length || 0,\n\t\t\t\t\tteacher: group.teacher,\n\t\t\t\t\tstartDate: group.startDate,\n\t\t\t\t\tendDate: group.endDate\n\t\t\t\t}));\n\n\t\t\t\tconsole.log('📊 转换后的小组数据:', this.groupList);\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('❌ 加载小组数据失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载小组数据失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\n\t\t\t\t// 使用默认数据作为备用\n\t\t\t\tthis.groupList = [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tname: 'N5基础班',\n\t\t\t\t\t\tdescription: '日语入门基础学习',\n\t\t\t\t\t\tlevel: 'N5',\n\t\t\t\t\t\tstatus: 'active',\n\t\t\t\t\t\ticon: '/static/imgs/group1.png',\n\t\t\t\t\t\tmembers: 25,\n\t\t\t\t\t\tcourses: 12,\n\t\t\t\t\t\tprogress: 75,\n\t\t\t\t\t\tcompletedCourses: 9,\n\t\t\t\t\t\ttotalCourses: 12\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t} finally {\n\t\t\t\tthis.groupsLoading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 进入新概念分类学习\n\t\tasync enterConceptCategory(category) {\n\t\t\ttry {\n\t\t\t\tconsole.log('进入新概念分类:', category);\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: `正在加载${category.name}...`,\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\n\t\t\t\t// 获取该分类的课程列表\n\t\t\t\tconst response = await apiService.getCourses({\n\t\t\t\t\tcategory: category.id,\n\t\t\t\t\tstatus: 'published'\n\t\t\t\t});\n\n\t\t\t\tconsole.log('分类课程数据:', response.data);\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 跳转到课程列表页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/concept/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`\n\t\t\t\t});\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取分类课程失败:', error);\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 降级处理：直接跳转\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/courses/category?category=${category.id}&name=${category.name}`\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 开始新概念学习\n\t\tasync startConceptLearning() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在加载课程...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\n\t\t\t\t// 获取第一个语法课程开始学习\n\t\t\t\tconst response = await apiService.getCourses({\n\t\t\t\t\tcategory: 'grammar',\n\t\t\t\t\tstatus: 'published',\n\t\t\t\t\tlimit: 1\n\t\t\t\t});\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\tif (response.data.courses.length > 0) {\n\t\t\t\t\tconst firstCourse = response.data.courses[0];\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/courses/detail?courseId=${firstCourse.id}`\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无可学习的课程',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('开始学习失败:', error);\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 降级处理：跳转到新概念教程页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/concept/lesson?lessonId=1&isFirst=true'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 继续新概念学习\n\t\tasync continueConceptLearning() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在查找学习记录...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\n\t\t\t\t// 获取用户的学习记录，找到最后学习的课程\n\t\t\t\tconst response = await apiService.getLearningRecords({\n\t\t\t\t\tstatus: 'started',\n\t\t\t\t\tlimit: 1\n\t\t\t\t});\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\tif (response.data.records.length > 0) {\n\t\t\t\t\tconst lastRecord = response.data.records[0];\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/courses/detail?courseId=${lastRecord.Course.id}`\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 如果没有进行中的学习记录，开始新的学习\n\t\t\t\t\tthis.startConceptLearning();\n\t\t\t\t}\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('继续学习失败:', error);\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 降级处理：跳转到新概念教程页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/concept/lesson?lessonId=1&continue=true'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 查看新概念学习进度\n\t\tasync viewConceptProgress() {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在加载进度数据...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\n\t\t\t\t// 获取学习进度数据\n\t\t\t\tconst response = await apiService.getLearningProgress();\n\t\t\t\tconsole.log('学习进度数据:', response.data);\n\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 跳转到学习进度页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/learning/progress'\n\t\t\t\t});\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('查看进度失败:', error);\n\t\t\t\tuni.hideLoading();\n\n\t\t\t\t// 降级处理：直接跳转\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/concept/progress'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 选择新概念教程\n\t\tselectConceptTutorial() {\n\t\t\tthis.selectedGroupId = 'concept';\n\t\t\tthis.selectedGroup = null;\n\t\t\tthis.selectedGroupIndex = -1;\n\t\t},\n\n\t\t// 选择小组用于详情显示（左右联动）\n\t\tselectGroupForDetail(group, index) {\n\t\t\tthis.selectedGroupId = group.id;\n\t\t\tthis.selectedGroup = group;\n\t\t\tthis.selectedGroupIndex = index;\n\t\t},\n\n\t\t// 选择小组 - 显示操作选择弹窗（保留原有功能）\n\t\tselectGroup(group) {\n\t\t\tthis.selectedGroupId = group.id;\n\t\t\tthis.selectedGroup = group;\n\n\t\t\t// 显示操作选择弹窗\n\t\t\tuni.showActionSheet({\n\t\t\t\ttitle: `${group.name} - 请选择操作`,\n\t\t\t\titemList: [\n\t\t\t\t\t'🎥 查看课程回顾',\n\t\t\t\t\t'✍️ 进入练习题库',\n\t\t\t\t\t'📊 查看小组详情',\n\t\t\t\t\t'👥 查看小组成员'\n\t\t\t\t],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tswitch(res.tapIndex) {\n\t\t\t\t\t\tcase 0:\n\t\t\t\t\t\t\tthis.quickViewReview(group);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 1:\n\t\t\t\t\t\t\tthis.quickViewPractice(group);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 2:\n\t\t\t\t\t\t\tthis.enterGroup(group);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\tcase 3:\n\t\t\t\t\t\t\tthis.viewGroupMembers(group);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\t// 用户取消选择，重置选中状态\n\t\t\t\t\tthis.selectedGroupId = null;\n\t\t\t\t\tthis.selectedGroup = null;\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 切换视图\n\t\tswitchView(view) {\n\t\t\tthis.currentView = view;\n\t\t},\n\n\t\t// 日期选择\n\t\tonDateChange(e) {\n\t\t\tthis.selectedDate = e.detail.value;\n\t\t},\n\n\t\t// 选择练习类型\n\t\tselectPracticeType(type) {\n\t\t\tthis.currentPracticeType = type.id;\n\t\t},\n\n\t\t// 快速查看课程回顾 - 直接跳转\n\t\tquickViewReview(group) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '正在进入课程回顾',\n\t\t\t\ticon: 'loading',\n\t\t\t\tduration: 1000\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/course-review?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\n\t\t\t\t});\n\t\t\t}, 500);\n\t\t},\n\n\t\t// 快速查看练习 - 直接跳转\n\t\tquickViewPractice(group) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '正在进入练习题库',\n\t\t\t\ticon: 'loading',\n\t\t\t\tduration: 1000\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/practice?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\n\t\t\t\t});\n\t\t\t}, 500);\n\t\t},\n\n\t\t// 播放课程回顾视频\n\t\tplayCourseReview(course) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/course-review?courseId=${course.id}&groupId=${course.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 开始练习\n\t\tstartPractice(practice) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/groups/practice?practiceId=${practice.id}&groupId=${practice.groupId}`\n\t\t\t});\n\t\t},\n\n\t\t// 查看小组成员\n\t\tviewGroupMembers(group) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '正在加载成员列表',\n\t\t\t\ticon: 'loading',\n\t\t\t\tduration: 1000\n\t\t\t});\n\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/members?groupId=${group.id}&groupName=${encodeURIComponent(group.name)}`\n\t\t\t\t});\n\t\t\t}, 500);\n\t\t},\n\n\t\t// 获取小组颜色\n\t\tgetGroupColor(index) {\n\t\t\tconst colors = [\n\t\t\t\t'linear-gradient(135deg, #FF6B6B, #EE4437)', // 红色\n\t\t\t\t'linear-gradient(135deg, #4ECDC4, #44A08D)', // 青色\n\t\t\t\t'linear-gradient(135deg, #45B7D1, #96C93D)', // 蓝绿色\n\t\t\t\t'linear-gradient(135deg, #FFA726, #FB8C00)', // 橙色\n\t\t\t\t'linear-gradient(135deg, #AB47BC, #8E24AA)'  // 紫色\n\t\t\t];\n\t\t\treturn colors[index % colors.length];\n\t\t},\n\n\n\n\t\t// 跳转到登录页面\n\t\tgoToLogin() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login/login'\n\t\t\t});\n\t\t},\n\n\t\t// 联系管理员\n\t\tcontactAdmin() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '联系管理员',\n\t\t\t\tcontent: '请通过以下方式联系管理员开通权限：\\n\\n微信：admin123\\n电话：400-123-4567\\n邮箱：<EMAIL>',\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '我知道了'\n\t\t\t});\n\t\t},\n\n\t\t// 进入小组详情\n\t\tasync enterGroup(group) {\n\t\t\ttry {\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tconst userToken = uni.getStorageSync('token');\n\t\t\t\tif (!userToken) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '需要登录',\n\t\t\t\t\t\tcontent: '请先登录后再查看小组详情',\n\t\t\t\t\t\tconfirmText: '去登录',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 跳转到小组详情页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/groups/group-detail?groupId=${group.id}&groupName=${group.name}`\n\t\t\t\t});\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('进入小组详情失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '进入失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 加入小组\n\t\tasync joinGroup(group) {\n\t\t\ttry {\n\t\t\t\t// 检查用户是否已登录\n\t\t\t\tconst userToken = uni.getStorageSync('token');\n\t\t\t\tif (!userToken) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '需要登录',\n\t\t\t\t\t\tcontent: '请先登录后再加入小组',\n\t\t\t\t\t\tconfirmText: '去登录',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 调用加入小组API\n\t\t\t\tawait apiService.joinGroup(group.id);\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `成功加入${group.name}`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\n\t\t\t\t// 刷新小组数据\n\t\t\t\tawait this.loadGroupsFromAPI();\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加入小组失败:', error);\n\t\t\t\t// API服务已经显示了错误提示，这里不需要重复显示\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n/* ===== 基础页面样式 ===== */\n.clean-groups-page {\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\tmin-height: 100vh;\n}\n\n.page-content {\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* ===== 页面头部样式 ===== */\n.page-header {\n\tbackground: white;\n\tborder-radius: 0 0 30rpx 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.header-content {\n\tpadding: 40rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.title-section {\n\tflex: 1;\n}\n\n.page-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.page-subtitle {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.stats-section {\n\tdisplay: flex;\n\tgap: 25rpx;\n}\n\n.stat-item {\n\ttext-align: center;\n}\n\n.stat-number {\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n\tdisplay: block;\n\tline-height: 1;\n}\n\n.stat-label {\n\tfont-size: 18rpx;\n\tcolor: #999;\n\tmargin-top: 5rpx;\n}\n\n/* ===== 左右联动布局 ===== */\n.split-layout {\n\tdisplay: flex;\n\theight: calc(100vh - 200rpx);\n\tbackground: white;\n\tborder-radius: 20rpx 20rpx 0 0;\n\toverflow: hidden;\n\tmargin: 0 20rpx;\n\tbox-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);\n}\n\n/* ===== 左侧面板 ===== */\n.left-panel {\n\twidth: 200rpx;\n\tbackground: #f8f9fa;\n\tborder-right: 1rpx solid #e9ecef;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.panel-header {\n\tpadding: 25rpx 15rpx 20rpx;\n\tbackground: white;\n\tborder-bottom: 1rpx solid #e9ecef;\n\ttext-align: center;\n}\n\n.panel-title {\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.panel-subtitle {\n\tfont-size: 18rpx;\n\tcolor: #999;\n}\n\n.group-list {\n\tflex: 1;\n\tpadding: 10rpx 0;\n}\n\n/* ===== 小组列表项 ===== */\n.group-item {\n\tmargin: 0 10rpx 15rpx;\n\tbackground: white;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tborder: 2rpx solid transparent;\n\ttext-align: center;\n\tposition: relative;\n}\n\n.group-item.active {\n\tborder-color: #667eea;\n\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));\n\ttransform: scale(1.05);\n\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n}\n\n.group-item:active {\n\ttransform: scale(0.95);\n}\n\n.simple-group-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.group-level-badge {\n\tcolor: white;\n\tpadding: 8rpx 12rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 18rpx;\n\tfont-weight: 700;\n\tmin-width: 40rpx;\n\ttext-align: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);\n}\n\n.simple-group-name {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\ttext-align: center;\n\tline-height: 1.3;\n\tword-break: break-all;\n}\n\n.simple-status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n}\n\n.simple-status-dot.active {\n\tbackground: #4CAF50;\n\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);\n}\n\n.simple-status-dot.completed {\n\tbackground: #2196F3;\n\tbox-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);\n}\n\n.simple-status-dot.pending {\n\tbackground: #FF9800;\n\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);\n}\n.groups-container {\n\tpadding: 30rpx;\n}\n\n.page-title {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\ttext-align: center;\n\tmargin-bottom: 40rpx;\n}\n\n.groups-grid {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 30rpx;\n}\n\n.group-card {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n\tdisplay: flex;\n\talign-items: center;\n\tposition: relative;\n}\n\n.group-icon {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tmargin-right: 30rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbackground: #f5f5f5;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.group-icon image {\n\twidth: 80rpx;\n\theight: 80rpx;\n}\n\n.group-info {\n\tflex: 1;\n}\n\n.group-name {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.group-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tline-height: 1.4;\n}\n\n.group-stats {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.stat-item {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tbackground: #f8f8f8;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n}\n\n.group-status {\n\tposition: absolute;\n\ttop: 20rpx;\n\tright: 20rpx;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n.group-status.active {\n\tbackground: #e8f5e8;\n\tcolor: #52c41a;\n}\n\n.group-status.completed {\n\tbackground: #f0f0f0;\n\tcolor: #999;\n}\n\n.group-status.pending {\n\tbackground: #fff7e6;\n\tcolor: #fa8c16;\n}\n\n/* 简洁清爽的样式 */\n.clean-groups-page {\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\tmin-height: 100vh;\n}\n\n.page-content {\n\tposition: relative;\n\tz-index: 1;\n}\n\n/* 简洁的头部样式 */\n.page-header {\n\tbackground: white;\n\tborder-radius: 0 0 30rpx 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.header-content {\n\tpadding: 40rpx 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.title-section {\n\tflex: 1;\n}\n\n.page-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.page-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.stats-section {\n\tdisplay: flex;\n\tgap: 30rpx;\n}\n\n.stat-item {\n\ttext-align: center;\n}\n\n.stat-number {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n\tdisplay: block;\n\tline-height: 1;\n}\n\n.stat-label {\n\tfont-size: 20rpx;\n\tcolor: #999;\n\tmargin-top: 5rpx;\n}\n\n.header-content-wrapper {\n\tposition: relative;\n\tz-index: 2;\n\tpadding: 60rpx 30rpx 50rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.title-section-enhanced {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n}\n\n.title-icon-container {\n\tposition: relative;\n\tmargin-right: 25rpx;\n}\n\n.icon-bg-circle {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tborder-radius: 50%;\n\topacity: 0.2;\n}\n\n.title-icon-enhanced {\n\tposition: relative;\n\tz-index: 2;\n\tfont-size: 70rpx;\n\tdisplay: block;\n\ttext-align: center;\n\tline-height: 100rpx;\n}\n\n.icon-pulse {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder: 3rpx solid rgba(102,126,234,0.3);\n\tborder-radius: 50%;\n\tanimation: iconPulse 3s ease-in-out infinite;\n}\n\n@keyframes iconPulse {\n\t0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }\n\t50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.3; }\n}\n\n.title-text-enhanced {\n\tposition: relative;\n}\n\n.main-title-enhanced {\n\tfont-size: 52rpx;\n\tfont-weight: 800;\n\tcolor: #333;\n\tline-height: 1.2;\n\tmargin-bottom: 8rpx;\n\tbackground: linear-gradient(135deg, #333, #667eea);\n\tbackground-clip: text;\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n}\n\n.sub-title-enhanced {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n\tmargin-bottom: 15rpx;\n}\n\n.title-decoration {\n\twidth: 80rpx;\n\theight: 6rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tborder-radius: 3rpx;\n\tanimation: decorationGlow 2s ease-in-out infinite;\n}\n\n@keyframes decorationGlow {\n\t0%, 100% { box-shadow: 0 0 10rpx rgba(102,126,234,0.3); }\n\t50% { box-shadow: 0 0 20rpx rgba(102,126,234,0.6); }\n}\n\n.stats-section-enhanced {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tflex-wrap: wrap;\n}\n\n.stat-card-enhanced {\n\tposition: relative;\n\tbackground: rgba(255,255,255,0.9);\n\tborder-radius: 20rpx;\n\tpadding: 25rpx 20rpx;\n\tmin-width: 120rpx;\n\ttext-align: center;\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255,255,255,0.3);\n\tbox-shadow:\n\t\t0 8rpx 25rpx rgba(0,0,0,0.1),\n\t\tinset 0 1rpx 0 rgba(255,255,255,0.6);\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\toverflow: hidden;\n}\n\n.stat-card-enhanced:hover {\n\ttransform: translateY(-5rpx);\n\tbox-shadow:\n\t\t0 15rpx 35rpx rgba(0,0,0,0.15),\n\t\tinset 0 1rpx 0 rgba(255,255,255,0.8);\n}\n\n.stat-icon-bg {\n\tfont-size: 32rpx;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.stat-number-enhanced {\n\tfont-size: 36rpx;\n\tfont-weight: 800;\n\tcolor: #333;\n\tline-height: 1;\n\tmargin-bottom: 5rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tbackground-clip: text;\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n}\n\n.stat-label-enhanced {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n.stat-sparkle {\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n\twidth: 8rpx;\n\theight: 8rpx;\n\tbackground: #667eea;\n\tborder-radius: 50%;\n\tanimation: sparkle 2s ease-in-out infinite;\n}\n\n@keyframes sparkle {\n\t0%, 100% { opacity: 0.3; transform: scale(1); }\n\t50% { opacity: 1; transform: scale(1.5); }\n}\n\n/* 装饰元素 */\n.header-decorations {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tpointer-events: none;\n\tz-index: 1;\n}\n\n.deco-circle {\n\tposition: absolute;\n\tborder-radius: 50%;\n\tbackground: rgba(255,255,255,0.1);\n}\n\n.deco-1 {\n\twidth: 60rpx;\n\theight: 60rpx;\n\ttop: 20rpx;\n\tright: 80rpx;\n\tanimation: float1 6s ease-in-out infinite;\n}\n\n.deco-2 {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tbottom: 30rpx;\n\tleft: 60rpx;\n\tanimation: float2 8s ease-in-out infinite;\n}\n\n.deco-triangle {\n\tposition: absolute;\n\twidth: 0;\n\theight: 0;\n\tborder-left: 15rpx solid transparent;\n\tborder-right: 15rpx solid transparent;\n\tborder-bottom: 25rpx solid rgba(255,255,255,0.1);\n}\n\n.deco-3 {\n\ttop: 60rpx;\n\tleft: 40rpx;\n\tanimation: float3 7s ease-in-out infinite;\n}\n\n.deco-square {\n\tposition: absolute;\n\tbackground: rgba(255,255,255,0.1);\n\ttransform: rotate(45deg);\n}\n\n.deco-4 {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tbottom: 60rpx;\n\tright: 40rpx;\n\tanimation: float4 5s ease-in-out infinite;\n}\n\n@keyframes float1 {\n\t0%, 100% { transform: translateY(0) rotate(0deg); }\n\t50% { transform: translateY(-20rpx) rotate(180deg); }\n}\n\n@keyframes float2 {\n\t0%, 100% { transform: translateX(0) rotate(0deg); }\n\t50% { transform: translateX(15rpx) rotate(-180deg); }\n}\n\n@keyframes float3 {\n\t0%, 100% { transform: translateY(0) rotate(0deg); }\n\t50% { transform: translateY(10rpx) rotate(120deg); }\n}\n\n@keyframes float4 {\n\t0%, 100% { transform: translateY(0) rotate(45deg); }\n\t50% { transform: translateY(-15rpx) rotate(225deg); }\n}\n\n.title-icon {\n\tfont-size: 60rpx;\n\tmargin-right: 20rpx;\n}\n\n.title-text {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.main-title {\n\tfont-size: 48rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tline-height: 1.2;\n}\n\n.sub-title {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-top: 5rpx;\n}\n\n.stats-section {\n\tdisplay: flex;\n\tgap: 30rpx;\n}\n\n.stat-item {\n\ttext-align: center;\n}\n\n.stat-number {\n\tdisplay: block;\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n\tline-height: 1;\n}\n\n.stat-label {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tmargin-top: 5rpx;\n}\n\n/* 左右联动布局 */\n.split-layout {\n\tdisplay: flex;\n\theight: calc(100vh - 200rpx);\n\tbackground: white;\n\tborder-radius: 20rpx 20rpx 0 0;\n\toverflow: hidden;\n\tmargin: 0 20rpx;\n\tbox-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);\n}\n\n/* 左侧面板 */\n.left-panel {\n\twidth: 200rpx;\n\tbackground: #f8f9fa;\n\tborder-right: 1rpx solid #e9ecef;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.panel-header {\n\tpadding: 25rpx 15rpx 20rpx;\n\tbackground: white;\n\tborder-bottom: 1rpx solid #e9ecef;\n\ttext-align: center;\n}\n\n.panel-title {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.panel-subtitle {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.group-list {\n\tflex: 1;\n\tpadding: 10rpx 0;\n}\n\n/* 简化的小组列表项 */\n.group-item {\n\tmargin: 0 10rpx 15rpx;\n\tbackground: white;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tborder: 2rpx solid transparent;\n\ttext-align: center;\n\tposition: relative;\n}\n\n.group-item.active {\n\tborder-color: #667eea;\n\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));\n\ttransform: scale(1.05);\n\tbox-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n}\n\n.group-item:active {\n\ttransform: scale(0.95);\n}\n\n.simple-group-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.group-level-badge {\n\tcolor: white;\n\tpadding: 8rpx 12rpx;\n\tborder-radius: 12rpx;\n\tfont-size: 20rpx;\n\tfont-weight: 700;\n\tmin-width: 40rpx;\n\ttext-align: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);\n}\n\n.simple-group-name {\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\ttext-align: center;\n\tline-height: 1.3;\n\tword-break: break-all;\n}\n\n.simple-status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n}\n\n.simple-status-dot.active {\n\tbackground: #4CAF50;\n\tbox-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);\n}\n\n.simple-status-dot.completed {\n\tbackground: #2196F3;\n\tbox-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);\n}\n\n.simple-status-dot.pending {\n\tbackground: #FF9800;\n\tbox-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);\n}\n\n/* 新概念教程样式 */\n.concept-tutorial-item {\n\tmargin: 0 10rpx 20rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tborder-radius: 15rpx;\n\tpadding: 25rpx 15rpx;\n\ttransition: all 0.3s ease;\n\tborder: 2rpx solid transparent;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.concept-tutorial-item::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));\n\tpointer-events: none;\n}\n\n.concept-tutorial-item.active {\n\ttransform: scale(1.05);\n\tbox-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);\n\tborder-color: rgba(255,255,255,0.3);\n}\n\n.concept-tutorial-item:active {\n\ttransform: scale(0.95);\n}\n\n.concept-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 12rpx;\n\tposition: relative;\n\tz-index: 1;\n}\n\n.concept-icon {\n\tfont-size: 32rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.concept-title {\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tcolor: white;\n\ttext-align: center;\n\tline-height: 1.3;\n}\n\n.concept-badge {\n\tbackground: rgba(255,255,255,0.2);\n\tcolor: white;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 18rpx;\n\tfont-weight: 500;\n}\n\n/* 新概念教程详情页面 */\n.concept-detail-content {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.concept-detail-header {\n\tposition: relative;\n\theight: 200rpx;\n\toverflow: hidden;\n}\n\n.concept-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.concept-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0,0,0,0.2);\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n}\n\n.concept-detail-icon {\n\tfont-size: 60rpx;\n\tmargin-right: 20rpx;\n}\n\n.concept-detail-info {\n\tflex: 1;\n\tcolor: white;\n}\n\n.concept-detail-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.concept-detail-subtitle {\n\tfont-size: 24rpx;\n\topacity: 0.9;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.concept-progress-info {\n\tbackground: rgba(255,255,255,0.2);\n\tpadding: 8rpx 15rpx;\n\tborder-radius: 15rpx;\n\tdisplay: inline-block;\n}\n\n.progress-info-text {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n}\n\n/* 学习分类 */\n.concept-categories {\n\tpadding: 30rpx;\n\tflex: 1;\n}\n\n.categories-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.categories-grid {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n}\n\n.category-card {\n\tbackground: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\ttransition: all 0.3s ease;\n}\n\n.category-card:active {\n\tbackground: #e9ecef;\n\ttransform: scale(0.98);\n}\n\n.category-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 15rpx;\n}\n\n.category-info {\n\tflex: 1;\n}\n\n.category-name {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.category-desc {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.category-lessons {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.category-arrow {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n/* 快速操作 */\n.concept-actions {\n\tpadding: 0 30rpx 30rpx;\n}\n\n.concept-action-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.concept-action-buttons {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n}\n\n.concept-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tborder-radius: 15rpx;\n\ttransition: all 0.3s ease;\n}\n\n.concept-btn.primary {\n\tbackground: linear-gradient(135deg, #4CAF50, #45A049);\n\tcolor: white;\n}\n\n.concept-btn.secondary {\n\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\n\tcolor: white;\n}\n\n.concept-btn.tertiary {\n\tbackground: linear-gradient(135deg, #FF9800, #F57C00);\n\tcolor: white;\n}\n\n.concept-btn:active {\n\ttransform: scale(0.98);\n}\n\n.concept-btn-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 15rpx;\n}\n\n.concept-btn-content {\n\tflex: 1;\n}\n\n.concept-btn-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.concept-btn-desc {\n\tfont-size: 22rpx;\n\topacity: 0.9;\n}\n\n/* 无权限页面样式 */\n.no-permission-page {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 1000;\n}\n\n.permission-container {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 30rpx;\n\tpadding: 80rpx 60rpx;\n\ttext-align: center;\n\tmax-width: 600rpx;\n\tmargin: 0 40rpx;\n\tbackdrop-filter: blur(20rpx);\n\tbox-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);\n}\n\n.permission-icon {\n\tfont-size: 120rpx;\n\tmargin-bottom: 40rpx;\n\topacity: 0.8;\n}\n\n.permission-title {\n\tfont-size: 48rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.permission-desc {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n\tline-height: 1.5;\n}\n\n.permission-hint {\n\tfont-size: 28rpx;\n\tcolor: #999;\n\tmargin-bottom: 60rpx;\n\tdisplay: block;\n\tline-height: 1.5;\n}\n\n.permission-actions {\n\tdisplay: flex;\n\tgap: 30rpx;\n\tjustify-content: center;\n}\n\n.btn-login, .btn-contact {\n\tpadding: 24rpx 48rpx;\n\tborder-radius: 50rpx;\n\tfont-size: 32rpx;\n\tborder: none;\n\tmin-width: 200rpx;\n\ttransition: all 0.3s ease;\n}\n\n.btn-login {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.btn-contact {\n\tbackground: #f0f0f0;\n\tcolor: #666;\n}\n\n.btn-login:active {\n\ttransform: scale(0.95);\n}\n\n.btn-contact:active {\n\ttransform: scale(0.95);\n}\n\n/* 右侧面板 */\n.right-panel {\n\tflex: 1;\n\tbackground: white;\n\toverflow-y: auto;\n}\n\n.detail-content {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 详情头部 */\n.detail-header {\n\tposition: relative;\n\theight: 200rpx;\n\toverflow: hidden;\n}\n\n.detail-bg {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n}\n\n.detail-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0,0,0,0.3);\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n}\n\n.detail-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tmargin-right: 20rpx;\n}\n\n.detail-avatar image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 20rpx;\n}\n\n.detail-info {\n\tflex: 1;\n\tcolor: white;\n}\n\n.detail-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tdisplay: block;\n\tmargin-bottom: 8rpx;\n}\n\n.detail-subtitle {\n\tfont-size: 24rpx;\n\topacity: 0.9;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.detail-level {\n\tbackground: rgba(255,255,255,0.2);\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tdisplay: inline-block;\n}\n\n/* 统计卡片 */\n.detail-stats {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tgap: 20rpx;\n}\n\n.stat-card-detail {\n\tflex: 1;\n\tbackground: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 25rpx 20rpx;\n\ttext-align: center;\n\ttransition: all 0.3s ease;\n}\n\n.stat-card-detail:active {\n\tbackground: #e9ecef;\n\ttransform: scale(0.95);\n}\n\n.stat-icon-detail {\n\tfont-size: 40rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.stat-number-detail {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.stat-label-detail {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n/* 进度详情 */\n.progress-detail {\n\tpadding: 0 30rpx 30rpx;\n}\n\n.progress-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.progress-value {\n\tfont-size: 28rpx;\n\tfont-weight: 700;\n\tcolor: #667eea;\n}\n\n.progress-bar-detail {\n\theight: 12rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 6rpx;\n\toverflow: hidden;\n\tmargin-bottom: 15rpx;\n}\n\n.progress-fill-detail {\n\theight: 100%;\n\tborder-radius: 6rpx;\n\ttransition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.progress-desc {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\ttext-align: center;\n}\n\n/* 功能按钮 */\n.detail-actions {\n\tflex: 1;\n\tpadding: 0 30rpx 30rpx;\n}\n\n.action-row {\n\tmargin-bottom: 15rpx;\n}\n\n.action-btn-large {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 25rpx;\n\tborder-radius: 20rpx;\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.action-btn-large.primary {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.action-btn-large.secondary {\n\tbackground: linear-gradient(135deg, #4ECDC4, #44A08D);\n\tcolor: white;\n}\n\n.action-btn-large.tertiary {\n\tbackground: linear-gradient(135deg, #45B7D1, #96C93D);\n\tcolor: white;\n}\n\n.action-btn-large.quaternary {\n\tbackground: linear-gradient(135deg, #FFA726, #FB8C00);\n\tcolor: white;\n}\n\n.action-btn-large:active {\n\ttransform: scale(0.98);\n}\n\n.btn-icon-large {\n\tfont-size: 40rpx;\n\tmargin-right: 20rpx;\n}\n\n.btn-content {\n\tflex: 1;\n}\n\n.btn-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n}\n\n.btn-desc {\n\tfont-size: 22rpx;\n\topacity: 0.9;\n}\n\n.btn-arrow-large {\n\tfont-size: 24rpx;\n\topacity: 0.8;\n}\n\n/* 空状态 */\n.empty-detail {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 60rpx;\n\ttext-align: center;\n}\n\n.empty-icon {\n\tfont-size: 80rpx;\n\tmargin-bottom: 30rpx;\n\topacity: 0.5;\n}\n\n.empty-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 15rpx;\n}\n\n.empty-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tline-height: 1.5;\n}\n\n/* 美化的小组卡片 */\n.beautiful-group-card {\n\tposition: relative;\n\tbackground: white;\n\tborder-radius: 25rpx;\n\tpadding: 30rpx;\n\tbox-shadow: 0 8rpx 25rpx rgba(0,0,0,0.08);\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\toverflow: hidden;\n}\n\n.beautiful-group-card:active {\n\ttransform: scale(0.98);\n}\n\n.beautiful-group-card.selected {\n\ttransform: translateY(-8rpx);\n\tbox-shadow: 0 20rpx 40rpx rgba(102, 126, 234, 0.2);\n}\n\n.card-decoration {\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 0 25rpx 0 100rpx;\n\topacity: 0.1;\n}\n\n/* 美化的小组头部 */\n.beautiful-group-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-bottom: 25rpx;\n}\n\n.group-avatar-section {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n}\n\n.avatar-container {\n\tposition: relative;\n\tmargin-right: 20rpx;\n}\n\n.group-avatar-img {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 20rpx;\n}\n\n.level-badge {\n\tposition: absolute;\n\tbottom: -8rpx;\n\tright: -8rpx;\n\tcolor: white;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);\n}\n\n.group-basic-info {\n\tflex: 1;\n}\n\n.group-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tline-height: 1.3;\n\tmargin-bottom: 8rpx;\n}\n\n.group-subtitle {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.status-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tbackground: #f8f9fa;\n}\n\n.status-dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tmargin-right: 8rpx;\n}\n\n.status-indicator.active .status-dot {\n\tbackground: #4CAF50;\n}\n\n.status-indicator.completed .status-dot {\n\tbackground: #2196F3;\n}\n\n.status-indicator.pending .status-dot {\n\tbackground: #FF9800;\n}\n\n.status-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n/* 美化的统计数据 */\n.beautiful-stats {\n\tdisplay: flex;\n\tgap: 15rpx;\n\tmargin-bottom: 25rpx;\n}\n\n.stat-card {\n\tflex: 1;\n\tbackground: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx 15rpx;\n\tdisplay: flex;\n\talign-items: center;\n\ttransition: all 0.3s ease;\n}\n\n.stat-card:active {\n\tbackground: #e9ecef;\n\ttransform: scale(0.95);\n}\n\n.stat-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 12rpx;\n}\n\n.stat-info {\n\tflex: 1;\n}\n\n.stat-value {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tline-height: 1;\n}\n\n.stat-name {\n\tfont-size: 22rpx;\n\tcolor: #999;\n\tmargin-top: 4rpx;\n}\n\n/* 美化的进度条 */\n.beautiful-progress {\n\tmargin-bottom: 25rpx;\n}\n\n.progress-label {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n}\n\n.progress-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n.progress-percent {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tfont-weight: 600;\n}\n\n.progress-track {\n\theight: 8rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 4rpx;\n\toverflow: hidden;\n}\n\n.progress-bar-fill {\n\theight: 100%;\n\tborder-radius: 4rpx;\n\ttransition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* 美化的操作按钮 */\n.beautiful-actions {\n\tdisplay: flex;\n\tgap: 15rpx;\n}\n\n.action-button {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 18rpx 20rpx;\n\tborder-radius: 15rpx;\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.action-button.primary {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.action-button.secondary {\n\tbackground: #f8f9fa;\n\tcolor: #333;\n\tborder: 2rpx solid #e9ecef;\n}\n\n.action-button:active {\n\ttransform: scale(0.95);\n}\n\n.action-button.primary:active {\n\tbackground: linear-gradient(135deg, #5a6fd8, #6a42a0);\n}\n\n.action-button.secondary:active {\n\tbackground: #e9ecef;\n}\n\n.btn-icon {\n\tfont-size: 24rpx;\n}\n\n.btn-text {\n\tflex: 1;\n\ttext-align: center;\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n}\n\n.btn-arrow {\n\tfont-size: 20rpx;\n\topacity: 0.8;\n}\n\n/* 操作提示样式 */\n.operation-tips {\n\tpadding: 20rpx;\n\tmargin-top: 20rpx;\n}\n\n.tips-card {\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder-radius: 20rpx;\n\tpadding: 25rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 15rpx rgba(0,0,0,0.05);\n\tbackdrop-filter: blur(10rpx);\n}\n\n.tips-icon {\n\tfont-size: 40rpx;\n\tmargin-right: 20rpx;\n}\n\n.tips-content {\n\tflex: 1;\n}\n\n.tips-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.tips-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.group-avatar {\n\tposition: relative;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n}\n\n.group-level {\n\tposition: absolute;\n\tbottom: -10rpx;\n\tright: -10rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n\tpadding: 5rpx 10rpx;\n\tborder-radius: 10rpx;\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n}\n\n.group-status-badge {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 22rpx;\n\tfont-weight: 500;\n}\n\n.stat-number {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.stat-label {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.progress-bar {\n\theight: 8rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 4rpx;\n\toverflow: hidden;\n\tmargin: 15rpx 0;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tborder-radius: 4rpx;\n\ttransition: width 0.3s ease;\n}\n\n.group-actions {\n\tdisplay: flex;\n\tgap: 15rpx;\n\tmargin-top: 20rpx;\n}\n\n.action-btn {\n\tflex: 1;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n\tpadding: 15rpx;\n\ttext-align: center;\n\ttransition: all 0.3s ease;\n}\n\n.action-icon {\n\tdisplay: block;\n\tfont-size: 24rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.action-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n}\n\n.selected-group-content {\n\tbackground: white;\n\tmargin: 30rpx 20rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);\n}\n\n.content-header {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n\tpadding: 30rpx;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.content-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n.view-toggle {\n\tdisplay: flex;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n}\n\n.toggle-btn {\n\tpadding: 10rpx 20rpx;\n\tfont-size: 24rpx;\n\ttransition: all 0.3s ease;\n}\n\n.toggle-btn.active {\n\tbackground: white;\n\tcolor: #667eea;\n}\n\n.date-filter {\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.date-picker {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n\tpadding: 15rpx 20rpx;\n}\n\n.date-icon {\n\tfont-size: 24rpx;\n\tmargin-right: 10rpx;\n}\n\n.date-text {\n\tflex: 1;\n\tfont-size: 26rpx;\n\tcolor: #333;\n}\n\n.date-arrow {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.course-list {\n\tpadding: 20rpx 30rpx;\n}\n\n.course-item {\n\tdisplay: flex;\n\tbackground: #f8f9fa;\n\tborder-radius: 16rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n\ttransition: all 0.3s ease;\n}\n\n.course-thumbnail {\n\tposition: relative;\n\twidth: 160rpx;\n\theight: 120rpx;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tmargin-right: 20rpx;\n}\n\n.play-overlay {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\tbackground: rgba(0, 0, 0, 0.6);\n\tborder-radius: 50%;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.play-icon {\n\tcolor: white;\n\tfont-size: 24rpx;\n}\n\n.course-duration {\n\tposition: absolute;\n\tbottom: 8rpx;\n\tright: 8rpx;\n\tbackground: rgba(0, 0, 0, 0.7);\n\tcolor: white;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 6rpx;\n\tfont-size: 20rpx;\n}\n\n.course-details {\n\tflex: 1;\n}\n\n.course-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.course-date {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 8rpx;\n}\n\n.course-teacher {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-bottom: 10rpx;\n}\n\n.course-tags {\n\tdisplay: flex;\n\tgap: 8rpx;\n}\n\n.tag {\n\tbackground: #667eea;\n\tcolor: white;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 20rpx;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6e8c2b60&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662925944\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}