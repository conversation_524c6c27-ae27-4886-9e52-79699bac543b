<view class="content"><view class="navbar"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tabClick',[index]]]]]}}" class="{{['nav-item',(tabCurrentIndex===index)?'current':'']}}" bindtap="__e">{{''+item.text+''}}</view></block></view><swiper class="swiper-box" current="{{tabCurrentIndex}}" duration="300" data-event-opts="{{[['change',[['changeTab',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="tabItem" wx:for-index="tabIndex" wx:key="tabIndex"><swiper-item class="tab-content"><scroll-view class="list-scroll-content" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['loadData',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{tabItem.g0}}"><empty vue-id="{{'58187223-1-'+tabIndex}}" bind:__l="__l"></empty></block><block wx:for="{{tabItem.$orig.orderList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['navList','',tabIndex],['orderList','',index]]]]]]]}}" class="order-item" bindtap="__e"><view class="item-box"><view class="top-box"><text class="publish-date">{{item.publish_date}}</text><view class="type-box" style="margin-right:20rpx;"><text class="gui-icons">{{item.type==1?'':''}}</text><text class="lang">{{item.trans}}</text></view></view><view class="center-box"><view class="left-box"><view class="title">{{item.title}}</view></view><view class="cover-box" style="margin-right:20rpx;"><image class="cover" src="{{item.picture+'?x-oss-process=image/resize,m_lfit,h_150,w_150'}}"></image></view></view></view></view></block><uni-load-more vue-id="{{'58187223-2-'+tabIndex}}" status="{{tabItem.$orig.loadingType}}" bind:__l="__l"></uni-load-more></scroll-view></swiper-item></block></swiper></view>