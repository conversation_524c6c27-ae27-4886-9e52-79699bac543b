<template>
  <view>
    <swiper :indicator-dots="isShowDot && showDot" class="swiper" :style="{height:(height*col)+'rpx'}" :current="curDot" @change="swiperChange" :circular='circular'>
      <swiper-item v-for="(item, index) in listdivInfo" :key="index" class="swiper-item">
        <view v-for="(child, code) in item" class="smallItem" :key="code" :style="{ width: width + '%' }" @tap="navToPage(child)">
          <view class="image">
            <u-image :src="child.icon" :width="size+'rpx'" :height="size+'rpx'" >
            </u-image>
          </view>
          <view class="name">{{ child.name }}</view>
        </view>
      </swiper-item>
    </swiper>
	<!--弹出层1 提示 开始-->
	<u-popup v-model="tip" width="80%" mode="center" border-radius="10" :show="tip" :safeAreaInsetBottom="false">
		<view class="view-pup2">
			<view class="view-pup2-box"></view>
			<view class="view-pup2-warn">
				<view class="view-pup2-warn-title">温馨提示</view>
				<view class="view-pup2-warn-text" style="text-align: left;">{{moduleContent}}</view>
			</view>
			<view class="view-pup2-button">
				<view class="view-pup2-button-list view-pup2-button-list1"
					@click="confirm()">确定</view>
			</view>
		</view>
	</u-popup>
  </view>
  
</template>



<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    //一行排列数
    nums: {
      type: Number,
      default: 4,
    },

    //排列行数
    col: {
      type: Number,
      default: 1,
    },
    //是否展示指示灯
    isShowDot: {
      type: Boolean,
      default: false,
    },
	
	//图标大小
	size: {
	  type: Number,
	  default: 80,
	},
	
	//高度
	height: {
	  type: Number,
	  default: 150,
	},
	
  },

  watch: {
    list: {
      handler: function (newVal, oldVal) {
        this.listdiv();
      },
      deep: true,
    },
  },

  mounted() {
    this.listdiv();
  },
  data() {
    return {
      listdivInfo: [],
      width: 25,
      showDot: true,
	  curDot :0,
	  circular: true,
	  tip:false,
	  moduleContent:'感谢您的关注和支持，本课程正在精心准备中。',
    };
  },
  methods: {
    async listdiv() {
      this.width = 100 / this.nums;
      var arr = [];
      let that = this;
      console.log(that.nums * that.col);
      await this.list.forEach((v, index) => {
        var num = Math.floor(index / (that.nums * that.col));
        if (!arr[num]) {
          arr[num] = [];
          arr[num].push(v);
        } else {
          arr[num].push(v);
        }
      });
      this.listdivInfo = arr;
      if (this.listdivInfo.length > 1) {
        this.showDot = true;
      } else {
        this.showDot = false;
      }
    },
	
	
	swiperChange(e) {
		this.curDot = e.detail.current;
	},
	leftImg(){
	 
	    this.circular = false
	 
		let num = this.listdivInfo.length - 1
		if (this.curDot <= 0) {
	 
	        this.circular = true
	 
			this.curDot = num
		} else {
			this.curDot--
		}
	},
	rightImg(){
	 
	    this.circular = true
	 
		let num = this.listdivInfo.length - 1
		if (this.curDot >= num) {
			this.curDot = 0
		} else {
			this.curDot++
		}
	},
	//详情页
	navToPage(item) {
		let jump_type = item.jump_type;
		let title = item.name;
		let son_type = item.son_type
		if(jump_type==1){
			let id = item.id;
			uni.navigateTo({
				url: `/pages/category/list?id=${id}&type=${jump_type}&title=${title}&son_type=${son_type}`
			})
		}else if(jump_type==2){
			let id = item.jump_pid;
			uni.navigateTo({
				url: `/pages/category/list?id=${id}&type=${jump_type}&title=${title}&son_type=${son_type}`
			})
		}else if(jump_type==3){
			let id = item.jump_course_id;
			uni.navigateTo({
				url: `/pages/course/course?id=${id}`
			})
		}else if(jump_type==4){
			let id = item.jump_course_id;
			uni.navigateTo({
				url: `/pages/category/list-recommend?id=${id}&title=${title}`,
			})
		}
		else{
			this.tip = true;
		}
		
	},
	confirm() {
		this.tip = false;
	},
  },
};
</script>

<style lang="scss" scoped>
.top{
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.swiper {
  width: 92%;
  margin: 8rpx auto;
  background: white;
  border-radius: 32rpx;
}

.swiper-item {
  display: flex;
  flex-wrap: wrap;

  .smallItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 16rpx 0;
    overflow: hidden;

    image {
      width: 80rpx;
      height: 80rpx;
    }

    .name {
      margin-top: 8rpx;
      font-size: 26rpx;
    }
  }
}
.ceshi_prew text {
  color: #fff;
  font-size: 30rpx;
  float: left;
  margin-top: 25rpx;
}
 
.ceshi_next text {
  color: #fff;
  font-size: 30rpx;
  display: block;
  float: right;
  margin-top: 25rpx;
}
 
.ceshi_next {
  width: 40rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-top-left-radius: 80rpx;
  border-bottom-left-radius: 80rpx;
}
 
.ceshi_prew {
  width: 40rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 80rpx;
  border-bottom-right-radius: 80rpx;
}

</style>
