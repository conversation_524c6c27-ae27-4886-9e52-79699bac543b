<template>
	<gui-page :fullPage="true"  class="root-box">
		<!-- 页面主体 -->
		<view slot="gBody" class=" lp-flex-column">
			<!-- 列表内容 -->
			<scroll-view v-if="list.length>0" scroll-y="true" @scrolltolower="onScrolltolowerHandler">
				<view class="list-box">
					<navigator v-for="(item,index) in list" :key="item.id">
						<view class="item-box">
							<view class="top-box">
								<view class="cover-box">
									<image class="cover" :src="item.picture"></image>
									<image class="cover cover-mask" src="/static/imgs/cover_mask.png"></image>
								</view>
								<view class="info-box">
									<text class="title">{{item.title}}</text>
								</view>
							</view>
							<view class="center-box lp-flex lp-flex-space-between">
								<text>兑换时间：{{item.created_time}}</text>
								<text>兑换码：{{item.sn}}</text>
							</view>
						</view>
					</navigator>
				</view>
			</scroll-view>
			<!-- 暂无数据 -->
			<nodata v-else="total == 0" text="暂无数据"></nodata>
			<!-- 兑换 -->
			<view class="btn-box">
				<navigator url="./exchange">兑换</navigator>
			</view>
		</view>
	</gui-page>
</template>

<script>
	import nodata from '@/components/nodata/nodata.vue';

	export default {
		components: {
			nodata
		},
		data() {
			return {
				// 加载中
				loading: false,
				current_page: 1,
				// 总页数
				total_page: 1,
				// 总数量
				total: 0,
				// 列表数据
				list: [],
				// 刷新
				isHide: false,
			}
		},
		onShow: function() {
			if(this.isHide){
				this.isHide = false;
				this.getCourseList(1, true);
			}
		},
		onHide: function() {
			this.isHide = true;
		},
		created: function() {
			this.getCourseList(1, true);
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			getCourseList: function(page, force) {
				if (this.loading) {
					return
				}

				if (!force && page > this.total_page) {
					return
				}
				this.loading = true;
				this.apiGetCourseList(page,getApp().globalData.r_type).then(pagination => {
					this.current_page = pagination.current_page;
					this.total_page = pagination.last_page;
					this.total = pagination.total;
					this.list = pagination.data;
					this.loading = false;
				})
			},
			//-----------------------------------------------------------------------------------------------
			//
			// hander
			//
			//-----------------------------------------------------------------------------------------------
			onScrolltolowerHandler: function() {
				this.getCourseList(this.current_page + 1);
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			apiGetCourseList: function(page,r_type) {
				return this.$http.get('/v1/member/exchangesList', {
					params: {
						page,
						r_type
					}
				}).then(res => {
					return Promise.resolve(res.data.data);
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		flex: 1;
		background-color: #f2f2f2;

		scroll-view {
			max-height: 100%;
		}

		.list-box {
			padding-bottom: 130rpx;

			.item-box {
				margin: 30rpx 30rpx 0 30rpx;
				background-color: #fff;
				color: #666;
				border-radius: 0 0 10rpx 10rpx;

				.top-box {
					position: relative;
					height: 448rpx;

					.cover-box {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;

						.cover {
							position: absolute;
							width: 100%;
							height: 100%;
						}
					}

					.info-box {
						position: relative;
						z-index: 1;
						color: #fff;
						padding: 30rpx;

						.title {
							font-size: 48rpx;
						}
					}
				}

				.center-box {
					padding: 30rpx;
					font-size: 24rpx;
				}
			}
		}

		/* 兑换按钮 */
		.btn-box {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 99;

			navigator {
				padding: 30rpx;
				background: $uni-color-error;
				text-align: center;
				color: #fff;
			}
		}
	}
</style>
