<template>
	<gui-page :fullPage="true" :customHeader="false" class="root-box">
		<!-- 自定义头部导航 -->
		<!-- <view slot="gHeader">
			<lp-head>报名结果</lp-head>
		</view> -->
		<!-- 页面主体 -->
		<view slot="gBody" class="root-box lp-flex-column">
			<!-- <view class="content-box">
				<view>感谢您对《人民中国》系列课程的信任与支持！</view>
				<view>开课在即，请您联系助教老师进入班级学习群。</view>
				<view>微信号“日语世界”：lp18902494162。</view>
				<view>咨询电话：18902494162</view>
			</view> -->
			<!-- <image :src="order.credential" style="text-align: center;" mode="aspectFill"></image> -->
			<view style="text-align: center;">
				<image :src="order.credential" mode="widthFix" @longpress="savePoster"></image>
			</view>
		</view>
	</gui-page>
</template>

<script>
	export default {
		data() {
			return {
				sn_order: null,
				order: null,
				retry_num: 3,
			};
		},
		onLoad(options) {
			this.sn_order = options.sn_order;
			this.getOrderDetail();

		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------
			getOrderDetail: function() {
				let self = this;
				uni.showLoading();
				this.apiGetOrderDetail(this.sn_order).then(data => {
					console.log(data);
					uni.hideLoading();
					this.order = data;
					if (!data.status) {
						if (--this.retry_num <= 0) {
							uni.showModal({
								title: '查找报名状态失败，报名编号：' + this.sn_order + '请联系工作人员',
								success() {
									uni.redirectTo({
										url: '/pagesA/about-us/about-us'
									});
								}
							})
						} else {
							setTimeout(this.getOrderDetail, 1000);
						}
					}
				});
			},
			//-----------------------------------------------------------------------------------------------
			//
			// api
			//
			//-----------------------------------------------------------------------------------------------
			/**
			 * 获取订单详情
			 * @param {Object} order_num 订单号
			 */
			apiGetOrderDetail: function(order_num) {
				return this.$http.get('/v1/queryOrder', {
					params: {
						order_num
					}
				}).then((res) => {
					return Promise.resolve(res.data.data);
				});
			},

			// 保存到相册
			savePoster() {
				uni.getSetting({ //获取用户的当前设置
					success: (res) => {
						if (res.authSetting['scope.writePhotosAlbum']) { //验证用户是否授权可以访问相册
							this.saveImageToPhotosAlbum();
						} else {
							uni.authorize({ //如果没有授权，向用户发起请求
								scope: 'scope.writePhotosAlbum',
								success: () => {
									this.saveImageToPhotosAlbum();
								},
								fail: () => {
									uni.showToast({
										title: "请打开保存相册权限，再点击保存相册分享",
										icon: "none",
										duration: 3000
									});
									setTimeout(() => {
										uni.openSetting({ //调起客户端小程序设置界面,让用户开启访问相册
											success: (res2) => {
												// console.log(res2.authSetting)
											}
										});
									}, 3000);
								}
							})
						}
					}
				})
			},
			saveImageToPhotosAlbum() {
				var _this = this;
				// uni.getImageInfo({
				// 	src: _this.order.credential,
				// 	success: res => {
						//先下载到本地获取临时路径
						uni.downloadFile({
							url: _this.order.credential,
							success: (res) => {
								console.log('downloadFile success, res is', res.tempFilePath)
								//将临时路径保存到相册，即可在相册中查看图片
								uni.saveImageToPhotosAlbum({
									filePath: res.tempFilePath, //不支持网络地址
									success: function() {
										uni.showToast({
											title: '保存图片到相册成功',
											icon: 'none'
										});
									},
									fail:function(){
										uni.showToast({
											title: '保存图片到相册失败',
											icon: 'none'
										});
									}
								});
							},
							fail: (err) => {
								console.log('downloadFile fail, err is:', err)
							}
						})
					// },
					// fail: err => {
					// 	console.log(err, 'err')
					// }
				// })
			
			},
			
		}
	}
</script>

<style lang="scss" scoped>
	.root-box {
		color: #666;

		.head {
			padding: 100rpx 0;

			.gui-icons {
				font-size: 200rpx;
				color: #28b28b;
			}

			.success-text {
				margin: 20rpx 0;
				font-size: 36rpx;
				font-weight: bold;
			}
		}

		.content-box {
			font-size: 28rpx;
			padding: 60rpx;
			text-align: center;
		}
	}
</style>
