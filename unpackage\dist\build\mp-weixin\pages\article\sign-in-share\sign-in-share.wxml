<gui-page class="root-box data-v-74bcffbe" vue-id="e9b478ae-1" fullPage="{{true}}" customHeader="{{true}}" bind:__l="__l" vue-slots="{{['gHeader','gBody']}}"><view slot="gHeader" class="data-v-74bcffbe"><view class="gui-flex gui-nowrap gui-rows gui-align-items-center gui-padding head-box data-v-74bcffbe"><gui-header-leading vue-id="{{('e9b478ae-2')+','+('e9b478ae-1')}}" class="data-v-74bcffbe" bind:__l="__l"></gui-header-leading><view class="gui-header-content nav-box data-v-74bcffbe"></view></view></view><view class="body-box lp-flex-column data-v-74bcffbe" slot="gBody"><view class="content-box lp-flex-column data-v-74bcffbe"><block wx:if="{{!isReady}}"><view class="loading data-v-74bcffbe">海报生成中...</view></block><block wx:else><view class="pop-box lp-flex-column lp-flex-center data-v-74bcffbe"><view class="poster-box lp-flex-center data-v-74bcffbe"><image class="posterImage data-v-74bcffbe" src="{{posterImage||''}}" mode="heightFix"></image></view><view class="lp-flex-center btn-box data-v-74bcffbe"><view class="lp-flex-column lp-flex-center btn data-v-74bcffbe"><button type="primary" size="mini" data-event-opts="{{[['tap',[['saveImage']]]]}}" catchtap="__e" class="data-v-74bcffbe"><view class="lp-flex-center icon-box data-v-74bcffbe"><text class="gui-icons data-v-74bcffbe"></text></view></button><text class="data-v-74bcffbe">保存图片</text></view><view class="lp-flex-column lp-flex-center btn data-v-74bcffbe"><button type="primary" open-type="share" size="mini" class="data-v-74bcffbe"><view class="lp-flex-center icon-box data-v-74bcffbe"><text class="gui-icons data-v-74bcffbe"></text></view></button><text class="data-v-74bcffbe">微信分享</text></view></view></view></block><view class="hideCanvasView data-v-74bcffbe"><canvas class="hideCanvas data-v-74bcffbe" style="{{'width:'+((poster.width||10)+'px')+';'+('height:'+((poster.height||10)+'px')+';')}}" id="default_PosterCanvasId" canvas-id="default_PosterCanvasId"></canvas></view></view></view></gui-page>