<gui-page class="vue-ref" vue-id="439118e4-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#F8F8F8;" slot="gBody"><block wx:if="{{empty===true}}"><view class="empty"><image src="/static/emptyCart.jpg" mode="aspectFit"></image><view class="empty-tips">空空如也<navigator class="navigator" url="../index/index" open-type="switchTab">随便逛逛></navigator></view></view></block><block wx:else><view><block wx:if="{{$root.g0>0}}"><scroll-view scroll-y="true" data-event-opts="{{[['scrolltolower',[['onScrolltolowerHandler',['$event']]]]]}}" bindscrolltolower="__e"><view class="list-box"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['list','',index]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item.picture}}"></image></view><view class="info-box lp-flex-column"><text class="title">{{item.title}}</text><text class="total">{{"时间："+item.pay_time}}</text></view></view></view></block></view></scroll-view></block></view></block></view></gui-page>