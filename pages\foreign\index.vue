<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 头部轮播 -->
			<view class="carousel-section">
				<!-- 标题栏和状态栏占位符 -->
				<!-- <view class="titleNview-placing"></view> -->
				<!-- 背景色区域 -->
				<!-- <view class="titleNview-background" :style="{backgroundColor:titleNViewBackground}"></view> -->
				<swiper class="carousel" circular @change="swiperChange" autoplay="true">
					<swiper-item v-for="(item, index) in carouselList" :key="index" class="carousel-item" 
						@tap="navTo(item,'swiper')">
						<image :src="item.thumb" />
					</swiper-item>
				</swiper>
				<!-- 自定义swiper指示器 -->
				<view class="swiper-dots">
					<text class="num">{{swiperCurrent+1}}</text>
					<text class="sign">/</text>
					<text class="num">{{swiperLength}}</text>
				</view>
			</view>
			<!--分类-->
			<view class="flexbox hbclass" v-if="superInList.length > 0">
			    <view   class="flex1" v-for="(item,index) in superInList" :key="index" @tap="navTo(item,'link')">
			        <image class="icon120" mode="aspectFit" :src="item.thumb"></image>
			        <view class="txt26 nowrap">{{item.title}}</view>
			    </view>
			</view>
			<!--更多服务-->
			<view class="bg-box hbclass" v-if="otherList.length > 0">
			    <view class="hot-service">
			        <image ariaHidden="true" class="bg-img" lazyLoad="true" mode="aspectFill" :src="rmfwBgUrl"></image>
			        <view ariaRole="heading" class="hot-service-title">
			            <view class="hot-service-title-h3">更多服务</view>
			        </view>
			        <view class="hot-service-content">
			            <swiper bind:change="handleChange" class="swiper-container-row"  previousMargin="6rpx">
			                <swiper-item class="swiper-item" data-index="index" v-for="(item,index) in otherList" :key="index">
			                    <view  bind:tap="handleTap" class="srv-item"  v-for="(item,index) in otherList" :key="index" @tap="navTo(item,'link')">
			                        <image class="srv-item-icon" :src="item.picture"></image>
			                        <text class="srv-item-title nowrap">{{item.name}}</text>
			                    </view>
			                </swiper-item>
			            </swiper>
			           <!-- <view class="indicator" wx:if="{{list[1]}}">
			                <view style="margin: 0 auto">
			                    <view :class="indicator-child {{current==index?'active':''}}" :style="indicatorStyle" v-for="(item,index) in superInList" :key="index"></view>
			                </view>
			            </view> -->
			        </view>
			    </view>
			</view>
			<!--更多课程-->
			<view class="bg-box hbclass" v-if="courseList.length > 0">
			    <view class="hot-service">
			        <image ariaHidden="true" class="bg-img" lazyLoad="true" mode="aspectFill" :src="rmfwBgUrl"></image>
			        <view ariaRole="heading" class="hot-service-title">
			            <view class="hot-service-title-h3">更多课程</view>
			        </view>
			        <view class="hot-service-content">
			            <swiper bind:change="handleChange" :class="courseList.length>4?'swiper-container':'swiper-container-row'"  previousMargin="6rpx">
			                <swiper-item class="swiper-item" data-index="index" v-for="(item,index) in courseList" :key="index">
			                    <view  bind:tap="handleTap" class="srv-item" style="width: 25%;" v-for="(item1,index1) in courseList" :key="index1" @tap="navTo(item1,'link')">
			                        <image class="srv-item-icon1" :src="item1.icon" style="width: 60rpx;height: 60rpx;border-radius: 60rpx;"></image>
			                        <text class="srv-item-title nowrap">{{item1.name}}</text>
			                    </view>
			                </swiper-item>
			            </swiper>
			           <!-- <view class="indicator" wx:if="{{list[1]}}">
			                <view style="margin: 0 auto">
			                    <view :class="indicator-child {{current==index?'active':''}}" :style="indicatorStyle" v-for="(item,index) in superInList" :key="index"></view>
			                </view>
			            </view> -->
			        </view>
			    </view>
			</view>
			<!--推荐阅读-->
			<view class="everyone-doing bg hbclass" >
			    <view class="service-main">
			        <view ariaLabel="大家都在办" ariaRole="text" class="listbox service-list">
			            <view ariaLabel="换一换" ariaRole="button" bind:tap="handleTitleTap" class="titlebox">
			                <view class="h2title viewtitle">推荐阅读</view>
			                <view ariaLabel="换一换" ariaRole="button" bindtap="handleRefresh" class="service-hot-title">
			                    <!-- <image class="refresh-icon" src="https://fingertip-static.gdbs.gov.cn/static/yueshengshi/5c3e7e2e7c05cd222057ad3a0d89661d.png"></image> -->
			                    <!-- <view @tap="navTo(more,'link')">查看更多</view> -->
			                </view>
			            </view>
			            <view class="content service-hot-list">
			                <view class="list-box">
			                	<view class="item-box lp-flex-column" v-for="(item1, index1) in newsList" :key="index1"
			                	@tap="navTo(item1,'link')"
			                		>
			                		<view class="top-box lp-flex">
			                			        
			                			<view class="cover-box lp-flex-center">
			                				<image class="cover" :src="item1.thumb"></image>
			                				<!-- <view class="button"  >{{item1.status_text}}</view> -->
			                			</view>
			                			<view class="info-box lp-flex-column">
			                				<view class="title">{{item1.title}}</view>
			                				<!-- <view class="des">{{item1.des}}</view> -->
			                				<view class="end"><text style="text-align: right;float: right;">更多</text></view>
			                			</view>
			                		</view>
			                	</view>
			                </view>
			            </view>
			        </view>
			    </view>
			</view>
			
		</view>
	</gui-page>
</template>
<script>
	var graceJs = require('@/GraceUI5/js/grace.js');
	// 模拟 api 请求数据，格式见 article.js
	var artciles = require('@/GraceUI5/demoData/article.js');
	export default {
		data() {
			return {
				// 页面加载
				pageLoading: true,
				// 主体高度
				mainHeight: 200,
				// 滚动区域滚动距离
				scrollTop: 0,
				// 加载更多延迟
				loadMoreTimer: null,
				// 分类
				navItems: [{
						id: 1,
						title: '赛事公告',
						icon_url:'news',
						type:0,
						styles:1
					}, {
						id: 2,
						name: '参赛须知',
						content:'news',
						type:8,
						styles:2
					},
					{
						id: 3,
						name: '大赛平台',
						content:'news',
						type:9,
						styles:2
					}, {
						id: 4,
						name: '常见问题',
						content:'news',
						type:10,
						styles:3
					}
				],
				// 当前展示的分类索引
				currentIndex: 0,
				// 新闻列表数据， 分类切换重新获取第一页
				newsList: [],
				// 页码
				page: 1,
				swiperCurrent: 0,
				swiperLength: 0,
				carouselList: [],
				styles:1,
				content:'<p>1212121</p>',
				superInList: [{
						id: 1,
						title: '报名参赛',
						icon_url:'/static/chaoqicuiban.png',
					}, {
						id: 2,
						title: '写作赛题',
						icon_url:'/static/dingdanguanli.png',
					},
					{
						id: 3,
						title: '参赛须知',
						icon_url:'/static/kaohepingguguanli.png',
					}, {
						id: 4,
						title: '最新公告',
						icon_url:'/static/tongzhizhongxin.png',
					},
					 {
						id: 4,
						title: '常见问题',
						icon_url:'/static/yanguankaohe.png',
					}
				],
				rmfwBgUrl: "https://jpworld-match.oss-cn-shenzhen.aliyuncs.com/images/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20221105124531.png",
				otherList:[],
				courseList:[],
				more:{
					title:'联普日语社区',
					link:'https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzI2Mjc4ODk4NA==#wechat_redirect',
					type:'web'
				}
			}
		},
		onLoad: function() {
			// 01. 获取页面主体高度
			graceJs.getRefs('guiPage', this, 0, (ref) => {
				ref.getDomSize('guiPageBody', (e) => {
					// 主体高度 = 页面高度 - 自定义区域高度
					graceJs.select('#myheader', (e2) => {
						// 如果自定义区域有 margin 尺寸获取不到请加上这个值，可以利用 uni.upx2px() 转换
						// this.mainHeight = e.height - e2.height;
						this.pageLoading = false;
						this.loadData()
						
						//this.loadData2()
						// 第一次加载数据
						//this.getNews();
					});
				});
			});
			wx.showShareMenu({
					withShareTicket:true,
					menus:["shareAppMessage","shareTimeline"]
				})
			
		},
		onShareAppMessage() {
					return {
						title: "人民中国杯日语国际写作大赛",//标题
						imageUrl: "static/logo.png",//封面
						path: "/pages/index/index"//此处链接为要分享的页面链接	
					};
			},
			// 分享到朋友圈
			onShareTimeline() {
				return {
					title: "人民中国杯日语国际写作大赛",//标题
					imageUrl: "static/logo.png",//封面
					path: "/pages/index/index"//此处链接为要分享的页面链接	
				};
			},
		methods: {
			loadData(source) {
				let that = this;
				this.$http.get("v1/foreign/index").then(res => {
					that.carouselList = res.data.data.banner;
					that.swiperLength = res.data.data.banner.length;
					that.superInList = res.data.data.home_menu
					that.newsList = res.data.data.game_foot_post
					that.otherList = res.data.data.project
					that.courseList = res.data.data.projectType;
				})
			},
			loadData2(){
				uni.request({
					url: 'https://practice.jpworld.cn/api/v1/project_list', //仅为示例，并非真实接口地址。
					data: {
						id: 2,
					},
					header: {
						'custom-header': 'hello' //自定义请求头信息
					},
					success: (res) => {
						console.log(res.data);
						// let allList = res.data.data.list.data;
						// allList.forEach(item => {
						
						//        console.log(item)
						
						// })
						
						this.courseList = res.data.data.list.data;
					}
				});
			},
			//轮播图切换修改背景色
			swiperChange(e) {
				const index = e.detail.current;
				this.swiperCurrent = index;
			},
			navchange: function(index) {
				// 刷新当前分类对应的数据
				if (this.currentIndex != index) {
					this.styles = this.navItems[index].styles;
					this.page = 1;
					this.currentIndex = index;
					this.getNews();
					// 重置加载组件状态
					this.$refs.loadmorecom.stoploadmore();
				}
			},
			// 新闻加载函数
			// isReload 代表下拉刷新
			getNews: function(isReload) {
				if (this.page <= 1) {
					this.newsList = [];
					if (!isReload) {
						this.pageLoading = true;
					}
				}
				let item = this.navItems[this.currentIndex]
				let url = 'v1/news'
				// if(item.content=='news'){
				// 	url = 'v1/news'
				// }else{
				// 	url = 'v1/web-zbs'
				// }
				this.$http.get(url, {
					params: {
						type: 0,
						page: this.page,
						styles:1
					}
				}).then(res => {
					if(this.styles==2){
						this.content = res.data.data.content
						this.pageLoading = false;
					}else{
						var demoArr = graceJs.arrayConcat(res.data.data.data);
						console.log(demoArr)
						if (this.page >= 2) {
							this.newsList = this.newsList.concat(demoArr);
							// 加载完成后停止加载动画
							this.$refs.loadmorecom.stoploadmore();
							// 假定第3页加载了全部数据，通知组件不再加载更多
							// 实际开发由接口返回值来决定
							if (this.page >= 3) {
								this.$refs.loadmorecom.nomore();
							}
						}
						// 第一页 有可能是第一次加载或者刷新
						else {
							this.newsList = [];
							this.newsList = demoArr;
							// 刷新
							if (isReload) {
								this.$refs.refreshcom.endReload();
							}
							this.pageLoading = false;
						}
						this.page++;
					}
					
				})
			},
			scroll: function(e) {
				this.scrollTop = e.detail.scrollTop;
			},
			// 下拉刷新相关事件绑定
			touchstart: function(e) {
				if (this.scrollTop > 0) {
					return;
				}
				this.$refs.refreshcom.touchstart(e);
			},
			touchmove: function(e) {
				if (this.scrollTop > 0) {
					return;
				}
				this.$refs.refreshcom.touchmove(e);
			},
			touchend: function(e) {
				if (this.scrollTop > 0) {
					return;
				}
				this.$refs.refreshcom.touchend(e);
			},
			// 刷新事件
			reload: function() {
				this.page = 1;
				this.getNews(true);
				// 刷新时重置加载组件状态
				this.$refs.loadmorecom.stoploadmore();
			},
			// 加载更多事件
			loadmorefun: function() {
				// 获取加载组件状态看一下是否还能继续加载
				// 保证触底只执行一次加载
				if (this.loadMoreTimer != null) {
					clearTimeout(this.loadMoreTimer);
				}
				this.loadMoreTimer = setTimeout(() => {
					var status = this.$refs.loadmorecom.loadMoreStatus;
					if (status != 0) {
						return null;
					}
					this.$refs.loadmorecom.loading();
					// 此处开启加载动画执行加载数据的函数
					this.getNews();
				}, 80);
			},
			// 新闻点击
			newstap: function(e) {
				// 获取新闻 id
				var newsId = e;
				console.log(newsId);
				// 打开新闻详情页面
				//uni.navigateTo()
			},
			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			// 跳转
			navTo(item,type){
				console.log(item,type)
				if(type=='info'){
					// 作品详情跳转
					this.comJs.navToInfo(item.id)
					return
				}
				if(type=='link'){
					switch(item.type){
						case "web":
							uni.navigateTo({
								url:"/pages/webView/webView?data="+encodeURIComponent(JSON.stringify(item))
							})
							break;
						case "mini_app":
							console.log(item,type)
							uni.navigateTo({
								url: item.link
							})
							break;
						case "popu":
							this.module = "show";
							this.moduleTitle = item.title;
							this.moduleContent = item.description;
							break;
						case "other_mini":		// 当前页内部弹窗，不跳转
							this.getUrl(item.app_id,item.link) 
							break;
					}
					return;
				}
				if(type=='swiper'){
					switch(item.type){
						case "web":			// 项目外部跳转，需要使用web-view跳转外部H5页面
							uni.navigateTo({
								url:"/pages/webView/webView?data="+encodeURIComponent(JSON.stringify(item))
							})
							break;
						case "mini_app":	// 项目内部跳转
							uni.navigateTo({
								url:item.link
							})
							break;
						case "popu":		// 当前页内部弹窗，不跳转
							this.module = "show";
							this.moduleTitle = item.title;
							this.moduleContent = item.description;
							break;
						
					}
					return;
				}
				if(type=='other'){
					this.getUrl('wx9fbee055617c932a','/pages/index/index')
				}
			},
			// 详情
			toDetail: function(e) {
				// 获取 id
				var id = e.id;
				uni.navigateTo({
					url: `/pages/article/article?id=${id}`
				})  
			},
			getUrl(appId,path){
				 console.log(appId)
			      uni.navigateToMiniProgram({
			            appId: appId,
			            path: path,
			            success(res) {
			                              // 打开成功
			            }
			    })
			},
		}
	}
</script>
<style lang="scss">
	.header {
		padding: 15rpx 30rpx;
		height: 100rpx;
	}

	/* 头部 轮播图 */
	.carousel-section {
		position: relative;
		padding-top: 10px;

		.titleNview-placing {
			height: var(--status-bar-height);
			padding-top: 44px;
			box-sizing: content-box;
		}

		.titleNview-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 426upx;
			transition: .4s;
		}
	}

	.carousel {
		width: 100%;
		height: 350upx;

		.carousel-item {
			width: 100%;
			height: 100%;
			padding: 0 28upx;
			overflow: hidden;
		}

		image {
			width: 100%;
			height: 100%;
			border-radius: 10upx;
		}
	}

	.swiper-dots {
		display: flex;
		position: absolute;
		left: 60upx;
		bottom: 15upx;
		width: 72upx;
		height: 36upx;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;

		.num {
			width: 36upx;
			height: 36upx;
			border-radius: 50px;
			font-size: 24upx;
			color: #fff;
			text-align: center;
			line-height: 36upx;
		}

		.sign {
			position: absolute;
			top: 0;
			left: 50%;
			line-height: 36upx;
			font-size: 12upx;
			color: #fff;
			transform: translateX(-50%);
		}
	}

	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		flex-wrap: wrap;
		padding: 30upx 22upx;
		background: #fff;

		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: $font-sm + 2upx;
			color: $font-color-dark;
		}

		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			margin-bottom: 14upx;
			border-radius: 50%;
			opacity: .7;
			box-shadow: 4upx 4upx 20upx rgba(250, 67, 106, 0.3);
		}
	}

	.ad-1 {
		width: 100%;
		height: 210upx;
		padding: 10upx 0;
		background: #fff;

		image {
			width: 100%;
			height: 100%;
		}
	}
	.rich-text{
		padding: 30rpx;
		margin-top: 30rpx;
	}
	.content {
		padding: 15px;
	}
	.flexbox {
	    margin: 36rpx 16rpx 6rpx;
	    padding-bottom: 12rpx;
	}
	
	.flex1,.flexbox {
	    display: flex;
	}
	
	.flex1 {
	    align-items: center;
	    flex-direction: column;
	    justify-content: center;
	    width: 25%;
	}
	
	.icon120 {
	    height: 100rpx;
	    width: 100rpx;
	}
	
	.nowrap {
	    overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	}
	
	.txt26 {
	    color: #000;
	    display: block;
	    font-size: 28rpx;
	    height: 36rpx;
	    line-height: 36rpx;
	    margin-top: 6rpx;
	    text-align: center;
	    width: 130rpx;
	}
	.bg-box {
	    align-items: center;
	    display: flex;
	    justify-content: center;
	    width: 100%;
	}
	
	.hot-service {
	    background: #fff;
	    border-radius: 8rpx;
	    box-sizing: border-box;
	    margin: 18rpx;
	    overflow: hidden;
	    width: 670rpx;
	}
	
	.bg-img {
	    height: 88rpx;
	    position: absolute;
	    width: 670rpx;
	    z-index: 1;
	}
	
	.hot-service-content {
	    padding: 6rpx 6rpx 0;
	}
	
	.hot-service-title {
	    background-position: 50%;
	    background-size: cover;
	    border-radius: 8rpx 8rpx 0 0;
	    display: block;
	    height: 88rpx;
	    position: relative;
	    width: 670rpx;
	    z-index: 2;
	}
	
	.hot-service-title-h3 {
	    color: #2e3f56;
	    font-size: 32rpx;
	    font-weight: 700;
	    line-height: 88rpx;
	    margin-left: 30rpx;
	}
	
	.swiper-container {
	    height: 300rpx;
	}
	
	.swiper-container .swiper-item {
	    display: flex;
	    flex-wrap: wrap;
	}
	
	.swiper-container-row {
	    height: 166rpx;
	}
	
	.swiper-container-row .swiper-item {
	    display: flex;
	}
	
	.srv-col {
	    box-sizing: border-box;
	    flex: 1;
	    width: 160rpx;
	}
	
	.srv-item {
	    align-items: center;
	    display: flex;
	    flex-direction: column;
	    height: 144rpx;
	    justify-content: center;
	    text-align: center;
	    width: 33%;
	}
	
	.srv-item:nth-child(4n) {
	    margin-right: 0rpx;
	}
	
	.srv-item-icon {
	    height: 80rpx;
	    margin-bottom: 12rpx;
	    margin-top: 6rpx;
	    width: 80rpx;
		border-radius: 80rpx;
	}
	
	.srv-item-icon1 {
	    height: 50rpx;
	    margin-bottom: 12rpx;
	    margin-top: 6rpx;
	    width: 50rpx;
		border-radius: 50rpx;
	}
	
	.srv-item-title {
	    box-sizing: border-box;
	    color: #000;
	    display: block;
	    font-size: 28rpx;
	    height: 36rpx;
	    line-height: 36rpx;
	    overflow: hidden;
	    text-align: center;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	    width: 100%;
	}
	
	.indicator {
	    display: flex;
	    height: 8rpx;
	    margin-bottom: 30rpx;
	    margin-top: 12rpx;
	    width: 670rpx;
	}
	
	.indicator-child {
	    background: rgba(56,136,255,.5);
	    border-radius: 4rpx;
	    float: left;
	    height: 8rpx;
	    margin-right: 10rpx;
	    transition: all .3s ease;
	    width: 8rpx;
	}
	
	.active {
	    background-color: #3888ff;
	    width: 50rpx;
	}
	.bg {
	    width: 100%;
	}
	
	.service-main {
	    margin: 0 auto;
	}
	
	.h2title {
	    color: #000;
	    display: block;
	    font-size: 40rpx;
	    height: 60rpx;
	    line-height: 60rpx;
	}
	
	.listbox {
	    background: #fff;
	    border: 1rpx solid #ebebeb;
	    border-radius: 8rpx;
	    margin: 40rpx 40rpx 0;
	    padding-bottom: 20rpx;
	}
	
	.titlebox {
	    align-items: center;
	    color: #3888ff;
	    display: flex;
	    height: 60rpx;
	    justify-content: space-between;
	    padding-bottom: 18rpx;
	    padding-top: 36rpx;
	}
	
	.service-list {
	    background-color: initial!important;
	    border: 1rpx solid transparent!important;
	    box-shadow: none!important;
	    margin-top: 0!important;
	}
	
	.service-list-title {
	    padding-left: 0rpx!important;
	}
	
	.viewtitle {
	    font-weight: 700;
	}
	
	.service-main .service-hot-title {
	    align-items: center;
	    color: #3888ff;
	    display: inline-flex;
	    font-size: 30rpx;
	    height: 40rpx;
	    justify-content: space-between;
	    line-height: 40rpx;
	    width: 133rpx;
	}
	
	.content.service-hot-list {
	    background-color: #fff;
	    border-radius: 8rpx;
		margin-top: 20rpx;
	}
	
	.service-main .service-hot-title .refresh-icon {
	    height: 27rpx;
	    width: 30rpx;
	}
	
	.service-main .service-hot-list .service-hot-item {
	    align-items: center;
	    box-shadow: inset 0 -1rpx 0 0 #ebebeb;
	    display: flex;
	    margin: 0 40rpx;
	    padding: 36rpx 0;
	    position: relative;
	}
	
	.service-main .service-hot-list .service-hot-item .title {
	    color: #000;
	    font-family: PingFangSC-Regular;
	    font-size: 30rpx;
	    line-height: 40rpx;
	    max-width: 540rpx;
	}
	
	.service-main .service-hot-list .service-hot-item .tag {
	    background: rgba(66,147,244,.1);
	    border-radius: 4rpx;
	    color: #4293f4;
	    display: inline-block;
	    font-family: PingFangSC-Regular;
	    font-size: 26rpx;
	    font-weight: 700;
	    height: 36rpx;
	    line-height: 36rpx;
	    margin-left: 12rpx;
	    padding: 0 12rpx;
	}
	
	.service-main .service-hot-list .service-hot-item .arrow {
	    height: 24rpx;
	    position: absolute;
	    right: 0;
	    width: 14rpx;
	}
	
	.service-main .service-hot-list .service-hot-item:last-child {
	    box-shadow: none;
	}
	
	.twoNoWrap {
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	    display: -webkit-box;
	}
	
	.nowrap,.twoNoWrap {
	    overflow: hidden;
	    text-overflow: ellipsis;
	}
	
	.nowrap {
	    white-space: nowrap;
	}
	.item {
	    border-bottom: 1rpx solid #ebebeb;
	    box-sizing: border-box;
	    display: flex;
	    height: 160rpx;
	    margin: 0 auto;
	    width: 610rpx;
	}
	
	.pop-box .item {
	    padding-left: 0rpx;
	    width: 670rpx;
	}
	
	.item-icon {
	    height: 150rpx;
	    margin-right: 30rpx;
	    // margin-top: 39rpx;
	    vertical-align: middle;
	    width: 150rpx;
	}
	
	.item-text {
	    display: flex;
	    flex-direction: column;
	}
	
	.item-title {
	    color: #000;
	    font-size: 34rpx;
	    height: 48rpx;
	    line-height: 48rpx;
	    margin-bottom: 6rpx;
	    margin-top: 36rpx;
	}
	
	.item-title .nowrap {
	    display: inline-block;
	    font-weight: 700;
	    margin-right: 10rpx;
	    max-width: 500rpx;
	    vertical-align: middle;
	}
	
	.item-desc {
	    color: rgba(0,0,0,.3);
	    font-size: 24rpx;
	    height: 34rpx;
	    line-height: 34rpx;
	    margin-bottom: 20rpx;
	    width: 456rpx;
	}
	
	.item-title .topic-tip {
	    background: rgba(69,154,255,.1);
	    border-radius: 4rpx;
	    color: #3888ff;
	    display: inline-block;
	    font-size: 26rpx;
	    font-weight: 700;
	    height: 36rpx;
	    line-height: 36rpx;
	    text-align: center;
	    width: 50rpx;
	}
	
	.nowrap {
	    overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	}
	
	.service-banner__list {
	    padding-left: 0;
	}
	
	.content__desc {
	    color: rgba(0,0,0,.3)!important;
	    font-size: 24rpx!important;
	}
	
	.pop-item {
	    margin: 0;
	}
	
	.pop-item:last-child {
	    border-bottom: none;
	}
	.list-box {
		padding-bottom: 20rpx;
	
		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding:10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			 border-bottom: 1rpx solid #ebebeb;
	        
		
			.top-box {
				position: relative;
	
				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
					.cover :after{
					    background-color: red;
					    border-radius: 10rpx;
					    color: #fff;
					    content: "hot";
					    font-size: 25rpx;
					    line-height: 1;
					    padding: 2rpx 6rpx;
					    position: absolute;
					    left: 5rpx;
					    top: 5rpx;
					}
					.button{
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
				
				.cover-box {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;
					
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
					.button{
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
				  
				
	
				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
	
					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}
	
					.lang-box {
						color: $uni-text-color-grey;
						font-size: 24rpx;
					}
	
					.title {
						font-weight: bold;
						font-size: 26rpx;
						color: #666666;
					}
	
					.end-date {
						font-size: 20rpx;
						color: #999999;
					}
	
					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
					
					.des{
						font-size:22rpx;
						color: #8f8f94;
					
						
					}
					.price{
						font-size:24rpx;
						color: red;
						float: right;
					}
					.end{
						font-size:24rpx;
						color: red;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}
			
			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;
	
				.text-sm {
					font-size: 24upx;
				}
	
				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}
	
				.uni-row {
					flex-direction: row;
				}
	
				.align-center {
					align-items: center;
				}
	
				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
		:last-child{
		   border-bottom: 1rpx solid #fff;
		}
	}
	.lp-flex {
		display: flex;
		flex-direction: row;
	}
	
	.lp-flex-column {
		display: flex;
		flex-direction: column;
	}
	
</style>
