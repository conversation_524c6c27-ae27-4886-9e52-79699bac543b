<gui-page class="root-box lp-flex-column data-v-2fbdbe34" vue-id="1c037ce0-1" fullPage="{{true}}" bind:__l="__l" vue-slots="{{['gBody']}}"><view slot="gBody" class="data-v-2fbdbe34"><view class="panel base-box lp-flex-column data-v-2fbdbe34"><view class="cover-box data-v-2fbdbe34"><image class="cover data-v-2fbdbe34" show-menu-by-longpress="1" src="{{project.picture}}" mode="scaleToFill"></image></view><view class="info-box lp-flex-column data-v-2fbdbe34"><view class="title-box lp-flex lp-flex-space-between data-v-2fbdbe34"><text class="title data-v-2fbdbe34">{{project.name}}</text></view><view class="lp-flex lp-flex-space-between data-v-2fbdbe34"><view class="data-v-2fbdbe34"><text class="label data-v-2fbdbe34">课程时间：</text><text class="course_date data-v-2fbdbe34">{{project.class_time+" 至 "+project.end_time+''}}</text></view></view></view></view><view class="panel content-box data-v-2fbdbe34"><view class="head data-v-2fbdbe34">课程详情</view><view class="body data-v-2fbdbe34" style="text-align:center;"><block wx:if="{{project.class_picture!=null}}"><image show-menu-by-longpress="1" src="{{project.class_picture}}" mode="widthFix" class="data-v-2fbdbe34"></image></block><u-parse vue-id="{{('1c037ce0-2')+','+('1c037ce0-1')}}" html="{{project.content}}" class="data-v-2fbdbe34" bind:__l="__l"></u-parse></view></view><view class="page-bottom data-v-2fbdbe34"><view class="p-b-btn data-v-2fbdbe34"><image style="width:50rpx;height:50rpx;" src="/static/kf.png" class="data-v-2fbdbe34"></image><text class="data-v-2fbdbe34">客服</text><button class="contact-btn data-v-2fbdbe34" open-type="contact">a</button></view><view class="p-b-btn data-v-2fbdbe34" style="margin-left:10rpx;"><text style="font-size:35rpx;color:#FD6D24;" class="data-v-2fbdbe34">{{"￥"+project.price}}</text></view><block wx:if="{{project.is_over===false&&project.sign===0&&is_show===true}}"><view data-event-opts="{{[['tap',[['toggleSpec',['$event']]]]]}}" class="buy data-v-2fbdbe34" bindtap="__e">我要报名</view></block><block wx:else><block wx:if="{{project.is_over===false&&project.sign===1}}"><view class="buy data-v-2fbdbe34" style="background-color:#e5e5e5;">已报名</view></block><block wx:else><block wx:if="{{project.is_over===true}}"><view class="buy data-v-2fbdbe34" style="background-color:#e5e5e5;">已结束</view></block></block></block></view><view data-event-opts="{{[['touchmove',[['stopPrevent',['$event']]]],['tap',[['toggleSpec',['$event']]]]]}}" class="{{['popup','spec','data-v-2fbdbe34',specClass]}}" catchtouchmove="__e" bindtap="__e"><view class="mask data-v-2fbdbe34"></view><view data-event-opts="{{[['tap',[['stopPrevent',['$event']]]]]}}" class="layer attr-content data-v-2fbdbe34" catchtap="__e"><view class="a-t data-v-2fbdbe34"><image src="{{project.picture}}" class="data-v-2fbdbe34"></image><view class="right data-v-2fbdbe34"><text class="title data-v-2fbdbe34">{{project.name}}</text><text class="price data-v-2fbdbe34">{{"¥"+project.price}}</text></view></view><scroll-view style="height:800rpx;margin:30rpx 0;" scroll-y="true" class="data-v-2fbdbe34"><form data-event-opts="{{[['submit',[['onFormSubmitHandler',['$event']]]],['reset',[['onFormResetHandler',['$event']]]]]}}" bindsubmit="__e" bindreset="__e" class="data-v-2fbdbe34"><view class="form lp-flex-column data-v-2fbdbe34"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{item.$orig.value_type=='text'}}"><view class="form-item lp-flex data-v-2fbdbe34"><block wx:if="{{item.$orig.is_show=='1'}}"><view style="color:red;" class="data-v-2fbdbe34">*</view></block><block wx:else><view style="color:white;" class="data-v-2fbdbe34">*</view></block><view class="control-label data-v-2fbdbe34">{{item.$orig.title}}</view><view class="input-box data-v-2fbdbe34"><input name="{{item.$orig.field_name}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['form_data',[['form','id',item.$orig.id,'field_name']]]]]]]}}" value="{{form_data[item.$orig.field_name]}}" bindinput="__e" class="data-v-2fbdbe34"/></view></view></block><block wx:if="{{item.$orig.value_type=='radio'}}"><view class="form-item lp-flex data-v-2fbdbe34"><block wx:if="{{item.$orig.is_show=='1'}}"><view style="color:red;" class="data-v-2fbdbe34">*</view></block><block wx:else><view style="color:white;" class="data-v-2fbdbe34">*</view></block><view class="control-label data-v-2fbdbe34">{{item.$orig.title}}</view><view class="input-box data-v-2fbdbe34"><radio-group class="lp-flex-column data-v-2fbdbe34" name="{{item.$orig.field_name}}"><block wx:for="{{item.l0}}" wx:for-item="option_value" wx:for-index="index" wx:key="$orig"><label class="data-v-2fbdbe34"><block wx:if="{{form_data[item.$orig.field_name]!=undefined}}"><radio value="{{index}}" checked="{{option_value.g0}}" class="data-v-2fbdbe34">{{''+option_value.$orig+''}}</radio></block><block wx:else><radio value="{{index}}" checked="{{false}}" class="data-v-2fbdbe34">{{option_value.$orig}}</radio></block></label></block></radio-group></view></view></block><block wx:if="{{item.$orig.value_type=='checkbox'}}"><view class="form-item lp-flex data-v-2fbdbe34"><block wx:if="{{item.$orig.is_show=='1'}}"><view style="color:red;" class="data-v-2fbdbe34">*</view></block><view class="control-label data-v-2fbdbe34">{{item.$orig.title}}</view><view class="input-box data-v-2fbdbe34"><checkbox-group class="lp-flex-column data-v-2fbdbe34" name="{{item.$orig.field_name}}"><block wx:for="{{item.l1}}" wx:for-item="option_value" wx:for-index="__i0__" wx:key="$orig"><label class="data-v-2fbdbe34"><block wx:if="{{form_data[item.$orig.field_name]!=undefined}}"><checkbox value="{{option_value.$orig}}" checked="{{option_value.g1}}" class="data-v-2fbdbe34">{{''+option_value.$orig+''}}</checkbox></block><block wx:else><checkbox value="{{option_value.$orig}}" checked="{{false}}" class="data-v-2fbdbe34">{{option_value.$orig}}</checkbox></block></label></block></checkbox-group></view></view></block></block><button class="btn1 data-v-2fbdbe34" form-type="submit">提交</button></view></form></scroll-view></view></view></view></gui-page>