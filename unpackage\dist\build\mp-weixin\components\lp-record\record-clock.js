(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/lp-record/record-clock"],{"005a":function(t,a,e){"use strict";var n=e("90a3"),r=e.n(n);r.a},"1d4e":function(t,a,e){"use strict";e.r(a);var n=e("aac5"),r=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(s);a["default"]=r.a},"8d6e":function(t,a,e){"use strict";e.d(a,"b",(function(){return n})),e.d(a,"c",(function(){return r})),e.d(a,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},r=[]},"90a3":function(t,a,e){},aac5:function(t,a,e){"use strict";(function(t){Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var e={props:{show:{type:Boolean,default:!0}},data:function(){return{value:0,currentValue:0,drawTimer:null,angle:0,canvasCenter:{width:100,height:100},innerCenter:{x:50,y:50,radius:44}}},created:function(){this.resetDraw()},methods:{setValue:function(t){this.value=t,this.pauseDraw(),this.startDraw()},initDraw:function(){},resetDraw:function(){this.pauseDraw(),this.currentValue=0,this.canvasObj=t.createCanvasContext("canvas",this),this.canvasObj.setFillStyle("#fff"),this.canvasObj.fillRect(0,0,this.canvasObj.width,this.canvasObj.height),this.canvasObj.draw(),this.drawBg()},startDraw:function(){var t=this;this.drawTimer=setInterval((function(){var a=t.currentValue;t.currentValue+=.1*(t.value-t.currentValue),t.draw(a,t.currentValue),t.value-t.currentValue<=.001&&(t.currentValue=t.value,t.pauseDraw())}),50)},pauseDraw:function(){clearInterval(this.drawTimer)},drawBg:function(){var t=this.innerCenter.x,a=this.innerCenter.y,e=this.innerCenter.radius;this.canvasObj.beginPath(),this.canvasObj.setStrokeStyle("#fe3b54"),this.canvasObj.setGlobalAlpha(.3),this.canvasObj.setLineWidth(3),this.canvasObj.arc(t,a,e,0,2*Math.PI),this.canvasObj.stroke(),this.canvasObj.draw()},draw:function(t,a){var e=this.innerCenter,n=Math.PI,r=e.x,s=e.y,i=e.radius;this.canvasObj.beginPath(),this.canvasObj.setStrokeStyle("#fe3b54"),this.canvasObj.setGlobalAlpha(1),this.canvasObj.setLineWidth(3),this.canvasObj.arc(r,s,i,2*n*t-.5*n,2*n*a-.5*n,!1),this.canvasObj.stroke(),this.canvasObj.draw(!0)}}};a.default=e}).call(this,e("df3c")["default"])},f252:function(t,a,e){"use strict";e.r(a);var n=e("8d6e"),r=e("1d4e");for(var s in r)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return r[t]}))}(s);e("005a");var i=e("828b"),c=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"77a722b2",null,!1,n["a"],void 0);a["default"]=c.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/lp-record/record-clock-create-component',
    {
        'components/lp-record/record-clock-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f252"))
        })
    },
    [['components/lp-record/record-clock-create-component']]
]);
