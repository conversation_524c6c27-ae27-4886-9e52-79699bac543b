<block wx:if="{{!hidden}}"><view data-event-opts="{{[['tap',[['tapme',['$event']]]]]}}" class="gui-load-more gui-flex gui-rows gui-align-items-center gui-justify-content-center" catchtap="__e"><block wx:if="{{loadMoreStatus==1}}"><view data-ref="loadingiconforloadmore" class="gui-load-more-icon vue-ref"><text class="gui-icons gui-rotate360 gui-block-text" style="{{'font-size:'+(loadMoreFontSize)+';'+('color:'+(loadMoreColor[loadMoreStatus])+';')}}"></text></view></block><text class="gui-block-text" style="{{'font-size:'+(loadMoreFontSize)+';'+('color:'+(loadMoreColor[loadMoreStatus])+';')}}">{{loadMoreText[loadMoreStatus]}}</text></view></block>