/* #ifdef MP-BAIDU */
@import "iconsforbaidu.css";
/* #endif */

/* 规划 H5 端字体 */
/* #ifdef H5 */
body{font-family:-apple-system,Helvetica,sans-serif,Arial;}
/* #endif */

/* 图标加载 */
/* #ifndef APP-NVUE */
	/* #ifndef MP-BAIDU */
	@font-face{
		font-family : "gui-iconfont";
		font-weight : normal;
		font-style  : normal; 
		src         : url('@/static/grace.ttf') format('truetype');
	}
	/* #endif */
.gui-icons{font-family:"gui-iconfont"; font-style:normal;}
/* #endif */

/* 页面主体结构 */
.gui-body{width:750rpx;}

/* 去除图片空白 */
.gui-img-in{font-size:0;}

/* #ifndef APP-NVUE */
.gui-flex{display:flex;}
.gui-border-box{box-sizing:border-box;}
.gui-body{width:750rpx; box-sizing:border-box;}
/* #endif */


/* flex 布局 */
.gui-rows{flex-direction:row;}
.gui-row{flex-direction:row;}
.gui-columns{flex-direction:column;}
.gui-column{flex-direction:column;}
.gui-wrap{flex-direction:row; flex-wrap:wrap;}
.gui-nowrap{flex-direction:row; flex-wrap:nowrap;}
.gui-space-around{justify-content:space-around;}
.gui-space-between{justify-content:space-between;}
.gui-justify-content-start{justify-content:flex-start;}
.gui-justify-content-center{justify-content:center;}
.gui-justify-content-end{justify-content:flex-end;}
.gui-align-items-start{align-items:flex-start;}
.gui-align-items-center{align-items:center;}
.gui-align-items-end{align-items:flex-end;}
.gui-flex1{flex:1;}

/* 文本对齐 */
.gui-text-left{text-align:left;}
.gui-text-center{text-align:center;}
.gui-text-right{text-align:right;}
.gui-ellipsis{overflow:hidden;}
/* #ifndef APP-NVUE */
.gui-ellipsis{white-space:nowrap; text-overflow:ellipsis;}
.gui-block-text{display:block;}
/* #endif */

/* 文本修饰 */
.gui-bold{font-weight:bold;}
.gui-line-through{text-decoration:line-through;}
.gui-underline{text-decoration:underline;}
.gui-italic{font-style:italic;}

/* 定位 */
.gui-relative{position:relative;}
.gui-absolute-lt{position:absolute; z-index:2; left:0; top:0;}
.gui-absolute-rt{position:absolute; z-index:2; right:0; top:0;}
.gui-absolute-lb{position:absolute; z-index:2; left:0; bottom:0;}
.gui-absolute-rb{position:absolute; z-index:2; right:0; bottom:0;}
.gui-fixed-lt{position:fixed; z-index:2; left:0; top:0;}
.gui-fixed-rt{position:fixed; z-index:2; right:0; top:0;}
.gui-fixed-lb{position:fixed; z-index:2; left:0; bottom:0;}
.gui-fixed-rb{position:fixed; z-index:2; right:0; bottom:0;}

/* 背景色 */
.gui-bg-red{background-color:#EE0A25 !important;}
.gui-bg-green{background-color:#07C160 !important;}
.gui-bg-blue{background-color:#008AFF !important;}
.gui-bg-orange{background-color:#ED6A0C !important;}
.gui-bg-yellow{background-color:#FBDE4E !important;}
.gui-bg-purple{background-color:#8A3FD4 !important;}
.gui-bg-white{background-color:#FFFFFF !important;}
.gui-bg-black{background-color:#2B2E3D !important;}
.gui-bg-black{background-color:#2B2E3D !important;}
.gui-bg-black2{background-color:#656565 !important;}
.gui-bg-black3{background-color:#969799 !important;}
.gui-bg-black4{background-color:#C8C9CC !important;}
.gui-bg-black-opacity7{background-color:rgba(0,0,0,0.7);}
.gui-bg-black-opacity5{background-color:rgba(0,0,0,0.5);}
.gui-bg-black-opacity3{background-color:rgba(0,0,0,0.3);}
.gui-gtbg-red{background-image:linear-gradient(45deg, #FF0066 , #D50000) !important;}
.gui-gtbg-blue{background-image:linear-gradient(45deg, #5887DF , #008AFF) !important;}
.gui-gtbg-green{background-image:linear-gradient(45deg, #39B55A , #8DC63E) !important;}
.gui-bg-gray{background-color:#F7F8FA !important;}
.gui-bg-white{background-color:#FFFFFF !important;}


/* 内置颜色 */
.gui-color-black{color:#2B2E3D !important;}
.gui-color-white{color:#FFFFFF !important;}
.gui-color-gray{color:rgba(69, 90, 100, 0.6) !important;}
.gui-color-gray-light{color:rgba(69, 90, 100, 0.3) !important;}
.gui-color-blue{color:#008AFF !important;}
.gui-color-red{color:#EE0A25 !important;}
.gui-color-orange{color:#ED6A0C !important;}
.gui-color-purple{color:#8A3FD4 !important;}
.gui-color-green{color:#39B55A !important;}
.gui-color-yellow{color:#FBDE4E !important;}



/* 边框 */
/* #ifdef APP-NVUE */
.gui-border{border-style:solid; border-width:1rpx; border-color:#F1F2F3;}
.gui-border-l{border-left-style:solid; border-left-width:1rpx; border-left-color:#F1F2F3;}
.gui-border-r{border-right-style:solid; border-right-width:1rpx; border-right-color:#F1F2F3;}
.gui-border-t{border-top-style:solid; border-top-width:1rpx; border-top-color:#F1F2F3;}
.gui-border-b{border-bottom-style:solid; border-bottom-width:1rpx; border-bottom-color:#F1F2F3;}
.gui-noborder{border-right-width:0; border-top-width:0; border-left-width:0; border-bottom-width:0;}
/* #endif */
/* #ifndef APP-NVUE */
.gui-border{border:1rpx solid #F1F2F3;}
.gui-border-l{border-left:1rpx solid #F1F2F3;}
.gui-border-r{border-right:1rpx solid #F1F2F3;}
.gui-border-t{border-top:1rpx solid #F1F2F3;}
.gui-border-b{border-bottom:1rpx solid #F1F2F3;}
.gui-noborder{border:none !important;}
/* #endif */

/* 自定义头部导航相关 */
.gui-header-content{width:100rpx; flex:1; text-align:center; margin-left:10rpx; margin-right:168rpx;}
.gui-headr-back{width:148rpx; line-height:40px; font-size:32rpx;}


/* 宫格布局 */
.gui-grids{padding:0;}
.gui-grids-items{width:138rpx;}
/* #ifndef APP-NVUE */
.gui-grids-items{box-sizing:border-box;}
/* #endif */
.gui-grids-icon{height:80rpx; font-size:68rpx; line-height:80rpx; text-align:center;}
/* #ifndef APP-NVUE */
.gui-grids-icon{display:block; width:100%;}
/* #endif */
.gui-grids-icon-img{width:80rpx; height:80rpx; border-radius:6rpx;}
.gui-grids-text{line-height:50rpx; text-align:center; font-size:24rpx; margin-top:2px;}
/* #ifndef APP-NVUE */
.gui-grids-text{display:block; width:100%;}
/* #endif */


/* 列表样式 */
/* #ifndef APP-NVUE */
.gui-list-items, .gui-list-title{display:flex;}
.gui-list-icon, .gui-list-title-text, .gui-list-title-desc, .gui-list-body-desc, .gui-list-arrow-right{display:block;}
/* #endif */
.gui-list-items{flex-direction:row; flex-wrap:nowrap; align-items:center; justify-content:center;}
.gui-list-icon{width:80rpx; height:80rpx; line-height:80rpx; text-align:center; font-size:44rpx;}
.gui-list-image{width:80rpx; height:80rpx; border-radius:80rpx; font-size:0;}
.gui-list-body{padding:25rpx 0; margin-left:25rpx; width:100rpx; flex:1;}
.gui-list-title{flex-direction:row; flex-wrap:nowrap; justify-content:space-between; align-items:center;}
.gui-list-one-line{line-height:60rpx !important;}
.gui-list-title-text{font-size:26rpx; line-height:44rpx;}
.gui-list-title-desc{font-size:22rpx; line-height:30rpx;}
.gui-list-body-desc{font-size:22rpx; line-height:32rpx;}
.gui-list-arrow-right{width:50rpx; height:50rpx; line-height:50rpx; font-size:30rpx; text-align:right;}


/* 徽章 */
.gui-badge{border-radius:38rpx; height:38rpx; line-height:38rpx; padding:0 13rpx; font-size:22rpx;}
.gui-badge-absolute{position:absolute; right:0rpx; top:4rpx; z-index:1;}
.gui-badge-point{width:20rpx; height:20rpx; border-radius:12rpx; position:absolute; right:4rpx; top:4rpx; z-index:1; background-color:#FF0000;}
.gui-badge-gender{width:38rpx; height:38rpx; border-radius:30rpx; text-align:center; font-size:22rpx !important; line-height:38rpx; position:absolute; right:6rpx; top:4rpx; z-index:1;}


/* 滚动区域 */
/* #ifndef APP-NVUE */
.gui-scroll-x{display:flex; white-space:nowrap;}
.gui-scroll-x-item, .gui-scroll-x-items{display:inline-flex; vertical-align:top;}
/* #endif */
/* #ifdef MP-ALIPAY */
.gui-scroll-x{display:block; white-space:nowrap;}
/* #endif */
.gui-scroll-x{width:750rpx; flex-direction:row; overflow:hidden;}


/* 卡片列表 */
.gui-card-list{}
.gui-card-item{width:330rpx; margin-bottom:30rpx;}
.gui-card-img{width:330rpx; height:191rpx; overflow:hidden; position:relative;}
.gui-card-title{margin-top:3px;}
.gui-card-desc{margin-top:3px;}
.gui-card-tip{width:68rpx; height:40rpx; line-height:40rpx; text-align:center;}
.gui-card-mask-title{line-height:60rpx; height:60rpx; padding:0 10rpx; width:330rpx;}


/* 底部导航相关 */
.gui-footer-icon-buttons{width:80rpx; height:80rpx; margin:10rpx;}
.gui-footer-icon-buttons-icon{text-align:center; font-size:38rpx; line-height:50rpx;}
.gui-footer-icon-buttons-text{text-align:center; font-size:20rpx; line-height:30rpx;}
.gui-footer-large-buttons{margin-left:25rpx; margin-right:25rpx;}
.gui-footer-large-button{width:218rpx; height:80rpx;}
.gui-footer-large-button-text{line-height:80rpx !important;}

/* 通用标题布局 */
.gui-title-line{width:50rpx; height:1px; background-color:#E1E2E3; flex:1;}
.gui-title-text{line-height:60rpx;}
.gui-title-icon{width:50rpx; font-size:32rpx;}

/* 表单 */
.gui-form{overflow:hidden;}
.gui-form-item{flex-direction:row; flex-wrap:nowrap; align-items:center;}
.gui-form-label{width:130rpx; height:100rpx; font-size:28rpx; line-height:100rpx; overflow:hidden;}
.gui-form-icon{width:60rpx; height:60rpx; line-height:60rpx; font-size:28rpx;}
.gui-form-body{width:200rpx; margin-left:20rpx; overflow:hidden; flex:1;}
.gui-form-input{height:40rpx; line-height:40rpx; margin:20rpx 0; background-color:rgba(255,255,255,0); border-width:0px; font-size:28rpx;}
.gui-check-item{margin:10rpx 10rpx 0 0; padding:0 10rpx; font-size:28rpx; flex-direction:row; flex-wrap:nowrap; align-items:center;}
.gui-check-item-y{margin:10rpx 0; font-size:28rpx;}
.gui-textarea{height:120rpx; padding:15rpx; line-height:38rpx; background-color:rgba(255,255,255,0); border-width:0px; font-size:28rpx;}
@font-face{font-family:"gui-formicons"; src:url('data:application/ttf;charset=utf-8;base64,OLh6+EVGahJS0OU2yaKO26Kiu6Zv+fbC+9P6l/wm8ZwtrOU5zo2XwdDjj7ilb9szx6Pz8hzzU1DUMrbXMHC2NbU15WTlxOxUdK2llbX0DSSdFF0GClXLlRPIOJppGChZi5s6MnpKNvaqMLkwKoD8NsI9B7wqBANQgCNQhGDQgBAwhFKwhHGhBeDCDCOAI2YElRARjiAS2EBlMIQx4iGInC9nJQTHaVXQA60121033F008BO4gBKhATdCAWWEFskIYcQBnigDbEBX2IBwYQHyQhIThBohj9KScAF0gKAuQMEpALsJAHSEG+/kuiIgA4aBqIoCiQhzQx6qgEABMoBTShNDCC5oAC1AQWUAuIQRYwhzaCM7QFGOgo6EHXQAn6DDbQJNjjczuo4gsok+FuApClC9pt9nPwK3ehR05loNUk');}

/* 底部导航 */
/* #ifndef APP-NVUE */
.gui-form-item{display:flex;}
.gui-form-label, .gui-form-icon{display:block;}
.gui-form-picker{display:flex;}
.gui-check-item{display:flexbox;}
.gui-check-item-y{display:block;}
.gui-textarea{box-sizing:border-box;}
/* #endif */

/* 评论列表 */
.gui-comments{}
.gui-comments-items{margin-top:35rpx;}
.gui-comments-face{width:80rpx; height:80rpx; border-radius:80rpx; margin-right:25rpx;}
.gui-comments-body{width:580rpx; overflow:hidden;}
.gui-comments-header-text{line-height:40rpx;}
.gui-comments-info{margin-top:2px;}
.gui-comments-info-text{font-size:22rpx; line-height:40rpx; margin-top:10rpx;}
.gui-comments-content{line-height:36rpx; font-size:26rpx; padding:8rpx 0;}
.gui-comments-replay{font-size:24rpx; color:#666666; border-radius:3px; margin:3px 0; padding:15rpx; line-height:36rpx;}
.gui-comments-replay-btn{font-size:20rpx; line-height:44rpx; padding:0rpx 20rpx; border-radius:44rpx;}
.gui-comments-imgs{margin:8rpx 0;}
.gui-comments-image{width:180rpx; height:128rpx; margin-right:10rpx; margin-bottom:10rpx; font-size:0; overflow:hidden;}

/* 底部导航相关 */
.gui-footer-input-body{padding:0 20rpx; height:70rpx; border-radius:66rpx; margin:0 30rpx;}
.gui-footer-input-icon{width:66rpx; text-align:center; line-height:66rpx; font-size:30rpx; margin-right:10rpx;}
.gui-footer-input{width:100rpx; flex:1; font-size:26rpx; height:32rpx; line-height:32rpx; padding:0; overflow:hidden;}

.gui-common-line{height:20rpx; background-color:#F7F8FA;}


/* css3 动画 不支持 nvue */
/* #ifndef APP-NVUE */
.gui-transition-all{transition:all 0.2s ease-in 0s;}
/* 使用记录 : gui-switch-navigation2 gui-popup */
@keyframes gui-fade-in{0%{opacity:0;} 100%{opacity:1;}}
.gui-fade-in{animation:gui-fade-in 350ms ease-in forwards;}
/* 使用记录 : gui-image gui-popup */
@keyframes gui-fade-out{0%{opacity:1;} 100%{opacity:0;}}
.gui-fade-out{animation:gui-fade-out 350ms ease-out forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-top-in{0%{transform:translateY(-1000px);} 100%{transform:translateY(0px);}}
.gui-top-in{animation:gui-top-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-top-out{0%{transform:translateY(0px);} 100%{transform:translateY(-1000px);}}
.gui-top-out{animation:gui-top-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-bottom-in{0%{transform:translateY(600px);} 100%{transform:translateY(0px);}}
.gui-bottom-in{animation:gui-bottom-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-bottom-out{0%{transform:translateY(0px);} 100%{transform:translateY(600px);}}
.gui-bottom-out{animation:gui-bottom-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-left-in{0%{transform:translateX(-600px);} 100%{transform:translateX(0px);}}
.gui-left-in{animation:gui-left-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-left-out{0%{transform:translateX(0px);} 100%{transform:translateX(-600px);}}
.gui-left-out{animation:gui-left-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-right-in{0%{transform:translateX(600px);} 100%{transform:translateX(0px);}}
.gui-right-in{animation:gui-right-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-right-out{0%{transform:translateX(0px);} 100%{transform:translateX(600px);}}
.gui-right-out{animation:gui-right-out 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-scale-in{0%{transform:scale(0.3,0.3);} 100%{transform:scale(1,1);}}
.gui-scale-in{animation:gui-scale-in 350ms linear forwards;}
/* 使用记录 : gui-popup */
@keyframes gui-scale-out{0%{transform:scale(1,1);} 100%{transform:scale(0.3,0.3);}}
.gui-scale-out{animation:gui-scale-out 350ms linear forwards;}
/* 使用记录 : gui-page */
@keyframes gui-rotate360{0%{transform:rotate(0deg);} 50%{transform:rotate(180deg);} 100%{transform:rotate(360deg);}}
.gui-rotate360{animation:gui-rotate360 1200ms infinite linear;}
/* #endif */


/* 不支持 nvue 的常用样式 */
/* #ifndef APP-NVUE */
.gui-box-shadow{box-shadow:0px 0px 16rpx #323232;}
.gui-transition-all{transition:all 0.2s ease-in 0s;}
::-webkit-scrollbar {display: none;}
/* #endif */
