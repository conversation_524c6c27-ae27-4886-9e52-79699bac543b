<template>
	 <!-- headerBG="linear-gradient(to right, #0585F3, #04C7ED)" statusBarBG="linear-gradient(to right, #0585F3, #04C7ED)" -->
	<gracePage>
		<view slot="gBody">
			<web-view :src="obj.link" @message="message"></web-view>
		</view>
	</gracePage>
</template>

<script>
	export default{
		onLoad(option) {
			console.log(option)
			let obj = JSON.parse(decodeURIComponent(option.data));
			obj.link = obj.link + "?data=" + encodeURIComponent(obj.datas);
			console.log(obj)
			// let obj = {
			// 	token:{"access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvdGVzdC5taW5pLmpwd29ybGQuY25cL2FwaVwvYXV0aFwvd3hsb2dpbiIsImlhdCI6MTU5ODQ5MDQwMiwiZXhwIjoxOTU4NDkwNDAyLCJuYmYiOjE1OTg0OTA0MDIsImp0aSI6Iko4YTFQSkNiNE1DU2lhZU0iLCJzdWIiOjY4NTEsInBydiI6Ijg3ZTBhZjFlZjlmZDE1ODEyZmRlYzk3MTUzYTE0ZTBiMDQ3NTQ2YWEifQ.i_x2cuR7kH9Hma2-0VlB6YS8sFi3W8AKpvd1pwrxtbc","token_type":"bearer","expires_in":6000000},
			// 	typeId: 3,
			// 	typeTitle: "唱歌"
			// }
			// obj.link = "/hybrid/html/upOpus.html" + "?data=" + encodeURIComponent(JSON.stringify(obj));
			this.obj = JSON.parse(JSON.stringify(obj));
		},
		data(){
			return {
				obj:{}
			}
		},
		methods:{
			message(event){
			        console.log('接收到消息',event.detail.data)
			    }
		}
	}
</script>
