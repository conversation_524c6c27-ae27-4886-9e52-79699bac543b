<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="groups-page">
			<!-- 有权限时显示内容 -->
			<view v-if="hasGroupPermission" class="page-content">
				<!-- 页面头部 -->
				<view class="page-header">
					<view class="header-content">
						<view class="title-section">
							<text class="page-title">🎓 GST派遣日语培训班</text>
							<text class="page-subtitle">与同伴一起进步，共同成长</text>
						</view>
						<view class="stats-section">
							<view class="stat-item">
								<text class="stat-number">{{groupList.length}}</text>
								<text class="stat-label">个小组</text>
							</view>
							<view class="stat-item">
								<text class="stat-number">{{totalMembers}}</text>
								<text class="stat-label">名成员</text>
							</view>
							<view class="stat-item">
								<text class="stat-number">48</text>
								<text class="stat-label">门课程</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 左右联动布局 -->
				<view class="split-layout">
					<!-- 左侧小组列表 -->
					<view class="left-panel">
						<view class="panel-header">
							<text class="panel-title">学习内容</text>
							<text class="panel-subtitle">{{groupList.length + 1}}项</text>
						</view>
						<scroll-view class="group-list" scroll-y="true">
							<!-- 公共新概念教程 -->
							<view
								class="concept-tutorial-item"
								:class="{ 'active': selectedGroupId === 'concept' }"
								@click="selectConceptTutorial()"
							>
								<view class="concept-content">
									<view class="concept-icon">📖</view>
									<text class="concept-title">新概念教程</text>
									<view class="concept-badge">公共</view>
								</view>
							</view>
							<view
								class="group-item"
								v-for="(group, index) in groupList"
								:key="index"
								:class="{ 'active': selectedGroupId === group.id }"
								@click="selectGroupForDetail(group, index)"
							>
								<view class="simple-group-content">
									<view class="group-level-badge" :style="{ background: getGroupColor(index) }">
										{{group.level}}
									</view>
									<text class="simple-group-name">{{group.name}}</text>
									<view class="simple-status-dot" :class="group.status"></view>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 右侧详情面板 -->
					<view class="right-panel">
						<!-- 新概念教程详情 -->
						<view v-if="selectedGroupId === 'concept'" class="concept-detail-content">
							<!-- 教程头部 -->
							<view class="concept-detail-header">
								<view class="concept-bg">
									<view class="concept-overlay">
										<view class="concept-detail-icon">📖</view>
										<view class="concept-detail-info">
											<text class="concept-detail-title">{{conceptTutorial.title}}</text>
											<text class="concept-detail-subtitle">{{conceptTutorial.description}}</text>
											<view class="concept-progress-info">
												<text class="progress-info-text">
													{{conceptTutorial.completedLessons}} / {{conceptTutorial.totalLessons}} 课时
												</text>
											</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 学习分类 -->
							<view class="concept-categories">
								<view class="categories-title">学习分类</view>
								<view class="categories-grid">
									<view
										class="category-card"
										v-for="(category, index) in conceptTutorial.categories"
										:key="index"
										@click="enterConceptCategory(category)"
									>
										<view class="category-icon">{{category.icon}}</view>
										<view class="category-info">
											<text class="category-name">{{category.name}}</text>
											<text class="category-desc">{{category.description}}</text>
											<text class="category-lessons">{{category.lessons}}课时</text>
										</view>
										<view class="category-arrow">→</view>
									</view>
								</view>
							</view>

							<!-- 快速操作 -->
							<view class="concept-actions">
								<view class="concept-action-title">快速开始</view>
								<view class="concept-action-buttons">
									<view class="concept-btn primary" @click="startConceptLearning()">
										<view class="concept-btn-icon">🚀</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">开始学习</text>
											<text class="concept-btn-desc">从第一课开始</text>
										</view>
									</view>
									<view class="concept-btn secondary" @click="continueConceptLearning()">
										<view class="concept-btn-icon">📖</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">继续学习</text>
											<text class="concept-btn-desc">从上次位置继续</text>
										</view>
									</view>
									<view class="concept-btn tertiary" @click="viewConceptProgress()">
										<view class="concept-btn-icon">📊</view>
										<view class="concept-btn-content">
											<text class="concept-btn-title">学习进度</text>
											<text class="concept-btn-desc">查看详细进度</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 小组详情 -->
						<view v-else-if="selectedGroup" class="detail-content">
							<!-- 详情头部 -->
							<view class="detail-header">
								<view class="detail-bg" :style="{ background: getGroupColor(selectedGroupIndex) }">
									<view class="detail-overlay">
										<view class="detail-avatar">
											<image :src="selectedGroup.icon" mode="aspectFit" />
										</view>
										<view class="detail-info">
											<text class="detail-title">{{selectedGroup.name}}</text>
											<text class="detail-subtitle">{{selectedGroup.description}}</text>
											<view class="detail-level">{{selectedGroup.level}}等级</view>
										</view>
									</view>
								</view>
							</view>

							<!-- 统计卡片 -->
							<view class="detail-stats">
								<view class="stat-card-detail">
									<view class="stat-icon-detail">👥</view>
									<text class="stat-number-detail">{{selectedGroup.members}}</text>
									<text class="stat-label-detail">成员</text>
								</view>
								<view class="stat-card-detail">
									<view class="stat-icon-detail">📚</view>
									<text class="stat-number-detail">{{selectedGroup.courses}}</text>
									<text class="stat-label-detail">课程</text>
								</view>
								<view class="stat-card-detail">
									<view class="stat-icon-detail">⭐</view>
									<text class="stat-number-detail">{{selectedGroup.progress}}%</text>
									<text class="stat-label-detail">进度</text>
								</view>
							</view>

							<!-- 进度详情 -->
							<view class="progress-detail">
								<view class="progress-header">
									<text class="progress-title">学习进度</text>
									<text class="progress-value">{{selectedGroup.progress}}%</text>
								</view>
								<view class="progress-bar-detail">
									<view class="progress-fill-detail" :style="{ width: selectedGroup.progress + '%', background: getGroupColor(selectedGroupIndex) }"></view>
								</view>
								<text class="progress-desc">已完成 {{selectedGroup.completedCourses}} / {{selectedGroup.totalCourses}} 门课程</text>
							</view>

							<!-- 功能按钮 -->
							<view class="detail-actions">
								<view class="action-row">
									<view class="action-btn-large primary" @click="enterGroup(selectedGroup)">
										<view class="btn-icon-large">🚀</view>
										<view class="btn-content">
											<text class="btn-title">进入小组</text>
											<text class="btn-desc">开始学习</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
								<view class="action-row">
									<view class="action-btn-large secondary" @click="viewGroupProgress(selectedGroup)">
										<view class="btn-icon-large">📊</view>
										<view class="btn-content">
											<text class="btn-title">学习进度</text>
											<text class="btn-desc">查看详细进度</text>
										</view>
										<view class="btn-arrow-large">→</view>
									</view>
								</view>
							</view>
						</view>

						<!-- 未选择状态 -->
						<view v-else class="empty-detail">
							<view class="empty-icon">👈</view>
							<text class="empty-title">选择一个小组</text>
							<text class="empty-desc">点击左侧小组查看详细信息</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 无权限提示 -->
			<view v-else class="no-permission-page">
				<view class="permission-container">
					<view class="permission-icon">🔒</view>
					<text class="permission-title">访问受限</text>
					<text class="permission-desc">您暂时没有访问学习小组的权限</text>
					<text class="permission-hint">请联系管理员开通权限或使用授权账号登录</text>
					<view class="permission-actions">
						<button class="btn-login" @click="goToLogin">重新登录</button>
						<button class="btn-contact" @click="contactAdmin">联系管理员</button>
					</view>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>

export default {
	data() {
		return {
			pageLoading: false,
			hasGroupPermission: false,
			selectedGroupId: null,
			selectedGroup: null,
			selectedGroupIndex: 0,
			// 新概念教程数据
			conceptTutorial: {
				id: 'concept',
				title: '新概念英语教程',
				description: '从基础到高级的系统化英语学习',
				totalLessons: 144,
				completedLessons: 32,
				categories: [
					{
						id: 'basic',
						name: '基础语法',
						description: '掌握基本语法结构',
						icon: '📝',
						lessons: 36
					},
					{
						id: 'vocabulary',
						name: '词汇积累',
						description: '扩展词汇量',
						icon: '📚',
						lessons: 48
					},
					{
						id: 'listening',
						name: '听力训练',
						description: '提升听力理解能力',
						icon: '🎧',
						lessons: 36
					},
					{
						id: 'speaking',
						name: '口语练习',
						description: '提高口语表达能力',
						icon: '🗣️',
						lessons: 24
					}
				]
			},
			// 小组数据
			groupList: [
				{
					id: 1,
					name: 'N5基础班',
					description: '日语入门基础学习',
					level: 'N5',
					status: 'active',
					icon: '/static/imgs/group1.png',
					members: 25,
					courses: 12,
					progress: 75,
					completedCourses: 9,
					totalCourses: 12
				},
				{
					id: 2,
					name: 'N4进阶班',
					description: '日语进阶学习',
					level: 'N4',
					status: 'completed',
					icon: '/static/imgs/group2.png',
					members: 18,
					courses: 15,
					progress: 100,
					completedCourses: 15,
					totalCourses: 15
				},
				{
					id: 3,
					name: 'N3中级班',
					description: '日语中级学习',
					level: 'N3',
					status: 'pending',
					icon: '/static/imgs/group3.png',
					members: 12,
					courses: 18,
					progress: 45,
					completedCourses: 8,
					totalCourses: 18
				}
			]
		};
	},
	computed: {
		totalMembers() {
			return this.groupList.reduce((total, group) => total + group.members, 0);
		}
	},
	onLoad() {
		this.checkPermission();
		this.initializeData();
	},
	onShow() {
		// 每次显示页面时都检查权限，确保权限状态是最新的
		this.checkPermission();
	},
	methods: {
		// 检查用户权限
		checkPermission() {
			console.log('=== 开始检查小组权限 ===');
			
			// 检查用户是否登录
			const userToken = this.$store.state.user.token;
			const userInfo = this.$store.state.user.userInfo;
			const hasLogin = this.$store.getters.hasLogin;
			
			console.log('权限检查数据:', {
				userToken: userToken ? '存在' : '不存在',
				userInfo: userInfo,
				hasLogin: hasLogin
			});
			
			if (!userToken && !hasLogin) {
				console.log('用户未登录，拒绝访问');
				this.hasGroupPermission = false;
				return;
			}
			
			// 检查用户是否有小组权限
			this.checkGroupAccess();
		},
		
		// 检查小组访问权限
		checkGroupAccess() {
			const userInfo = this.$store.state.user.userInfo;
			const userMember = this.$store.state.user.member;
			const hasLogin = this.$store.getters.hasLogin;
			const isLoggedIn = this.$store.getters.isLoggedIn;

			console.log('=== 小组权限详细检查 ===');
			console.log('用户信息:', userInfo);
			console.log('会员信息:', userMember);
			console.log('登录状态:', { hasLogin, isLoggedIn });

			// 为彭伟用户(ID: 576)特别开放权限
			if (userInfo && (userInfo.id === 576 || userInfo.name === '彭伟')) {
				this.hasGroupPermission = true;
				console.log('✅ 为用户彭伟开放小组权限');
				return;
			}

			// 一般权限检查逻辑 - 只要登录就可以访问
			if (hasLogin || isLoggedIn || userInfo) {
				this.hasGroupPermission = true;
				console.log('✅ 登录用户可以访问小组功能');
			} else {
				this.hasGroupPermission = false;
				console.log('❌ 用户未登录，无法访问小组功能');
			}

			console.log('=== 最终权限结果:', this.hasGroupPermission, '===');
			
			// 强制触发视图更新
			this.$forceUpdate();

			if (!this.hasGroupPermission) {
				console.log('权限被拒绝，用户信息:', userInfo);
				// 显示提示信息
				uni.showToast({
					title: '请先登录',
					icon: 'none',
					duration: 2000
				});
			}
		},
		
		// 初始化数据
		initializeData() {
			// 默认选择第一个小组
			if (this.groupList.length > 0) {
				this.selectGroupForDetail(this.groupList[0], 0);
			}
		},
		
		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		
		// 联系管理员
		contactAdmin() {
			uni.showModal({
				title: '联系管理员',
				content: '请通过以下方式联系管理员开通权限：\n\n微信：admin123\n电话：400-123-4567\n邮箱：<EMAIL>',
				showCancel: false,
				confirmText: '我知道了'
			});
		},
		
		// 选择小组查看详情
		selectGroupForDetail(group, index) {
			this.selectedGroupId = group.id;
			this.selectedGroup = group;
			this.selectedGroupIndex = index;
		},
		
		// 选择新概念教程
		selectConceptTutorial() {
			this.selectedGroupId = 'concept';
			this.selectedGroup = null;
		},
		
		// 获取小组颜色
		getGroupColor(index) {
			const colors = [
				'linear-gradient(135deg, #667eea, #764ba2)',
				'linear-gradient(135deg, #f093fb, #f5576c)',
				'linear-gradient(135deg, #4facfe, #00f2fe)',
				'linear-gradient(135deg, #43e97b, #38f9d7)',
				'linear-gradient(135deg, #fa709a, #fee140)'
			];
			return colors[index % colors.length];
		},
		
		// 进入小组
		enterGroup(group) {
			uni.showToast({
				title: `进入${group.name}`,
				icon: 'success'
			});
		},
		
		// 查看小组进度
		viewGroupProgress(group) {
			uni.showToast({
				title: `查看${group.name}进度`,
				icon: 'success'
			});
		},
		
		// 新概念教程相关方法
		enterConceptCategory(category) {
			uni.showToast({
				title: `进入${category.name}`,
				icon: 'success'
			});
		},
		
		startConceptLearning() {
			uni.showToast({
				title: '开始新概念学习',
				icon: 'success'
			});
		},
		
		continueConceptLearning() {
			uni.showToast({
				title: '继续新概念学习',
				icon: 'success'
			});
		},
		
		viewConceptProgress() {
			uni.showToast({
				title: '查看新概念进度',
				icon: 'success'
			});
		}
	}
}
</script>

<style scoped>
/* ===== 基础页面样式 ===== */
.groups-page {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}

.page-content {
	position: relative;
	z-index: 1;
}

/* ===== 页面头部样式 ===== */
.page-header {
	background: white;
	border-radius: 0 0 30rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.header-content {
	padding: 40rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.title-section {
	flex: 1;
}

.page-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.page-subtitle {
	font-size: 22rpx;
	color: #666;
}

.stats-section {
	display: flex;
	gap: 25rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 28rpx;
	font-weight: 700;
	color: #667eea;
	display: block;
	line-height: 1;
}

.stat-label {
	font-size: 18rpx;
	color: #999;
	margin-top: 5rpx;
}

/* ===== 左右联动布局 ===== */
.split-layout {
	display: flex;
	height: calc(100vh - 200rpx);
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;
	margin: 0 20rpx;
	box-shadow: 0 -5rpx 20rpx rgba(0,0,0,0.1);
}

/* ===== 左侧面板 ===== */
.left-panel {
	width: 200rpx;
	background: #f8f9fa;
	border-right: 1rpx solid #e9ecef;
	display: flex;
	flex-direction: column;
}

.panel-header {
	padding: 25rpx 15rpx 20rpx;
	background: white;
	border-bottom: 1rpx solid #e9ecef;
	text-align: center;
}

.panel-title {
	font-size: 22rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.panel-subtitle {
	font-size: 18rpx;
	color: #999;
}

.group-list {
	flex: 1;
	padding: 10rpx 0;
}

/* ===== 小组列表项 ===== */
.group-item {
	margin: 0 10rpx 15rpx;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	text-align: center;
	position: relative;
}

.group-item.active {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
	transform: scale(1.05);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.group-item:active {
	transform: scale(0.95);
}

.simple-group-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.group-level-badge {
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	font-size: 18rpx;
	font-weight: 700;
	min-width: 40rpx;
	text-align: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
}

.simple-group-name {
	font-size: 20rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	line-height: 1.3;
	word-break: break-all;
}

.simple-status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	position: absolute;
	top: 10rpx;
	right: 10rpx;
}

.simple-status-dot.active {
	background: #4CAF50;
	box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.5);
}

.simple-status-dot.completed {
	background: #2196F3;
	box-shadow: 0 0 8rpx rgba(33, 150, 243, 0.5);
}

.simple-status-dot.pending {
	background: #FF9800;
	box-shadow: 0 0 8rpx rgba(255, 152, 0, 0.5);
}

/* ===== 新概念教程样式 ===== */
.concept-tutorial-item {
	margin: 0 10rpx 20rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	border-radius: 15rpx;
	padding: 25rpx 15rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
	position: relative;
	overflow: hidden;
}

.concept-tutorial-item.active {
	transform: scale(1.05);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
	border-color: rgba(255,255,255,0.3);
}

.concept-tutorial-item:active {
	transform: scale(0.95);
}

.concept-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	position: relative;
	z-index: 1;
}

.concept-icon {
	font-size: 32rpx;
	margin-bottom: 5rpx;
}

.concept-title {
	font-size: 20rpx;
	font-weight: 600;
	color: white;
	text-align: center;
	line-height: 1.3;
}

.concept-badge {
	background: rgba(255,255,255,0.2);
	color: white;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 16rpx;
	font-weight: 500;
}

/* ===== 右侧面板 ===== */
.right-panel {
	flex: 1;
	display: flex;
	flex-direction: column;
}

/* ===== 新概念教程详情 ===== */
.concept-detail-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.concept-detail-header {
	position: relative;
	height: 180rpx;
	overflow: hidden;
}

.concept-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
}

.concept-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.2);
	display: flex;
	align-items: center;
	padding: 30rpx;
}

.concept-detail-icon {
	font-size: 50rpx;
	margin-right: 20rpx;
}

.concept-detail-info {
	flex: 1;
	color: white;
}

.concept-detail-title {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.concept-detail-subtitle {
	font-size: 22rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}

.concept-progress-info {
	background: rgba(255,255,255,0.2);
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	display: inline-block;
}

.progress-info-text {
	font-size: 18rpx;
	font-weight: 600;
}

/* ===== 学习分类 ===== */
.concept-categories {
	padding: 30rpx;
	flex: 1;
}

.categories-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.categories-grid {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.category-card {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}

.category-card:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.category-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
}

.category-info {
	flex: 1;
}

.category-name {
	font-size: 24rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.category-desc {
	font-size: 20rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.category-lessons {
	font-size: 18rpx;
	color: #999;
}

.category-arrow {
	font-size: 18rpx;
	color: #999;
}

/* ===== 快速操作 ===== */
.concept-actions {
	padding: 0 30rpx 30rpx;
}

.concept-action-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.concept-action-buttons {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.concept-btn {
	display: flex;
	align-items: center;
	padding: 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}

.concept-btn.primary {
	background: linear-gradient(135deg, #4CAF50, #45A049);
	color: white;
}

.concept-btn.secondary {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.concept-btn.tertiary {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}

.concept-btn:active {
	transform: scale(0.98);
}

.concept-btn-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
}

.concept-btn-content {
	flex: 1;
}

.concept-btn-title {
	font-size: 24rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}

.concept-btn-desc {
	font-size: 20rpx;
	opacity: 0.9;
}

/* ===== 小组详情 ===== */
.detail-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.detail-header {
	position: relative;
	height: 180rpx;
	overflow: hidden;
}

.detail-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.detail-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.3);
	display: flex;
	align-items: center;
	padding: 30rpx;
}

.detail-avatar {
	width: 70rpx;
	height: 70rpx;
	margin-right: 20rpx;
}

.detail-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
}

.detail-info {
	flex: 1;
	color: white;
}

.detail-title {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
	margin-bottom: 8rpx;
}

.detail-subtitle {
	font-size: 22rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 10rpx;
}

.detail-level {
	background: rgba(255,255,255,0.2);
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	font-size: 18rpx;
	font-weight: 600;
	display: inline-block;
}

/* ===== 统计卡片 ===== */
.detail-stats {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.stat-card-detail {
	flex: 1;
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.stat-card-detail:active {
	background: #e9ecef;
	transform: scale(0.95);
}

.stat-icon-detail {
	font-size: 32rpx;
	margin-bottom: 10rpx;
}

.stat-number-detail {
	font-size: 28rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.stat-label-detail {
	font-size: 20rpx;
	color: #666;
}

/* ===== 进度详情 ===== */
.progress-detail {
	padding: 0 30rpx 30rpx;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.progress-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
}

.progress-value {
	font-size: 26rpx;
	font-weight: 700;
	color: #667eea;
}

.progress-bar-detail {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 15rpx;
}

.progress-fill-detail {
	height: 100%;
	border-radius: 6rpx;
	transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-desc {
	font-size: 20rpx;
	color: #666;
	text-align: center;
}

/* ===== 功能按钮 ===== */
.detail-actions {
	flex: 1;
	padding: 0 30rpx 30rpx;
}

.action-row {
	margin-bottom: 15rpx;
}

.action-btn-large {
	display: flex;
	align-items: center;
	padding: 25rpx;
	border-radius: 20rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.action-btn-large.primary {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.action-btn-large.secondary {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
}

.action-btn-large:active {
	transform: scale(0.98);
}

.btn-icon-large {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.btn-content {
	flex: 1;
}

.btn-title {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 5rpx;
}

.btn-desc {
	font-size: 20rpx;
	opacity: 0.9;
}

.btn-arrow-large {
	font-size: 22rpx;
	opacity: 0.8;
}

/* ===== 空状态 ===== */
.empty-detail {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
	text-align: center;
}

.empty-icon {
	font-size: 60rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 15rpx;
}

.empty-desc {
	font-size: 22rpx;
	color: #999;
	line-height: 1.5;
}

/* ===== 无权限页面样式 ===== */
.no-permission-page {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.permission-container {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 80rpx 60rpx;
	text-align: center;
	max-width: 600rpx;
	margin: 0 40rpx;
	backdrop-filter: blur(20rpx);
	box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.2);
}

.permission-icon {
	font-size: 80rpx;
	margin-bottom: 40rpx;
	opacity: 0.8;
}

.permission-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.permission-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
	display: block;
	line-height: 1.5;
}

.permission-hint {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 60rpx;
	display: block;
	line-height: 1.5;
}

.permission-actions {
	display: flex;
	gap: 30rpx;
	justify-content: center;
}

.btn-login, .btn-contact {
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	font-size: 26rpx;
	border: none;
	min-width: 160rpx;
	transition: all 0.3s ease;
}

.btn-login {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.btn-contact {
	background: #f0f0f0;
	color: #666;
}

.btn-login:active {
	transform: scale(0.95);
}

.btn-contact:active {
	transform: scale(0.95);
}
</style>
