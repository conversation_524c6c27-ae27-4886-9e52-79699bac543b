/**
 * GST日语培训班API服务
 * 用于UniApp小程序调用后端API接口
 */

// API配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:8005/api',
    timeout: 10000
  },
  // 生产环境
  production: {
    baseUrl: 'https://your-domain.com/api',
    timeout: 10000
  }
}

// 获取当前环境配置
const ENV = process.env.NODE_ENV || 'development'
const config = API_CONFIG[ENV]

console.log('🔗 API服务配置:', {
  环境: ENV,
  接口地址: config.baseUrl,
  超时时间: config.timeout + 'ms'
})

class ApiService {
  constructor() {
    this.baseUrl = config.baseUrl
    this.timeout = config.timeout
  }

  /**
   * 通用请求方法
   * @param {string} url 请求路径
   * @param {object} options 请求选项
   */
  async request(url, options = {}) {
    const token = uni.getStorageSync('token')
    
    return new Promise((resolve, reject) => {
      // 显示加载提示
      if (options.showLoading !== false) {
        uni.showLoading({
          title: options.loadingText || '加载中...',
          mask: true
        })
      }

      uni.request({
        url: this.baseUrl + url,
        method: options.method || 'GET',
        data: options.data,
        timeout: this.timeout,
        header: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
          ...options.header
        },
        success: (res) => {
          uni.hideLoading()
          
          if (res.statusCode === 200 && res.data.success) {
            resolve(res.data)
          } else {
            // 处理业务错误
            const errorMsg = res.data?.message || '请求失败'
            if (options.showError !== false) {
              uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 2000
              })
            }
            reject(new Error(errorMsg))
          }
        },
        fail: (error) => {
          uni.hideLoading()
          
          let errorMsg = '网络请求失败'
          if (error.errMsg) {
            if (error.errMsg.includes('timeout')) {
              errorMsg = '请求超时，请检查网络'
            } else if (error.errMsg.includes('fail')) {
              errorMsg = '网络连接失败'
            }
          }
          
          if (options.showError !== false) {
            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
          }
          
          reject(new Error(errorMsg))
        }
      })
    })
  }

  // ==================== 用户认证相关 ====================

  /**
   * 用户登录
   * @param {string} identifier 用户名/邮箱/手机号
   * @param {string} password 密码
   */
  async login(identifier, password) {
    return this.request('/auth/login', {
      method: 'POST',
      data: { identifier, password },
      loadingText: '登录中...'
    })
  }

  /**
   * 用户注册
   * @param {object} userData 用户数据
   */
  async register(userData) {
    return this.request('/auth/register', {
      method: 'POST',
      data: userData,
      loadingText: '注册中...'
    })
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser() {
    return this.request('/auth/me', {
      showLoading: false
    })
  }

  /**
   * 更新用户资料
   * @param {object} profileData 资料数据
   */
  async updateProfile(profileData) {
    return this.request('/auth/profile', {
      method: 'PUT',
      data: profileData,
      loadingText: '更新中...'
    })
  }

  /**
   * 修改密码
   * @param {string} currentPassword 当前密码
   * @param {string} newPassword 新密码
   */
  async changePassword(currentPassword, newPassword) {
    return this.request('/auth/password', {
      method: 'PUT',
      data: { currentPassword, newPassword },
      loadingText: '修改中...'
    })
  }

  // ==================== 小组管理相关 ====================

  /**
   * 获取小组列表
   * @param {object} params 查询参数
   */
  async getGroups(params = {}) {
    const query = new URLSearchParams(params).toString()
    return this.request(`/groups${query ? '?' + query : ''}`)
  }

  /**
   * 获取小组详情
   * @param {number} groupId 小组ID
   */
  async getGroupDetail(groupId) {
    return this.request(`/groups/${groupId}`)
  }

  /**
   * 加入小组
   * @param {number} groupId 小组ID
   */
  async joinGroup(groupId) {
    return this.request(`/groups/${groupId}/join`, {
      method: 'POST',
      loadingText: '加入中...'
    })
  }

  /**
   * 退出小组
   * @param {number} groupId 小组ID
   */
  async leaveGroup(groupId) {
    return this.request(`/groups/${groupId}/leave`, {
      method: 'POST',
      loadingText: '退出中...'
    })
  }

  /**
   * 获取小组成员列表
   * @param {number} groupId 小组ID
   */
  async getGroupMembers(groupId) {
    return this.request(`/groups/${groupId}/members`)
  }

  // ==================== 课程管理相关 ====================

  /**
   * 获取课程列表
   * @param {object} params 查询参数
   */
  async getCourses(params = {}) {
    const query = new URLSearchParams(params).toString()
    return this.request(`/courses${query ? '?' + query : ''}`)
  }

  /**
   * 获取课程详情
   * @param {number} courseId 课程ID
   */
  async getCourseDetail(courseId) {
    return this.request(`/courses/${courseId}`)
  }

  /**
   * 获取小组课程列表
   * @param {number} groupId 小组ID
   */
  async getGroupCourses(groupId) {
    return this.request(`/groups/${groupId}/courses`)
  }

  // ==================== 学习记录相关 ====================

  /**
   * 提交学习记录
   * @param {object} recordData 学习记录数据
   */
  async submitLearningRecord(recordData) {
    return this.request('/learning/records', {
      method: 'POST',
      data: recordData,
      showLoading: false
    })
  }

  /**
   * 获取学习记录
   * @param {object} params 查询参数
   */
  async getLearningRecords(params = {}) {
    const query = new URLSearchParams(params).toString()
    return this.request(`/learning/records${query ? '?' + query : ''}`)
  }

  /**
   * 获取学习进度统计
   * @param {number} userId 用户ID (可选)
   */
  async getLearningProgress(userId) {
    const url = userId ? `/learning/progress/${userId}` : '/learning/progress'
    return this.request(url)
  }

  // ==================== 作业管理相关 ====================

  /**
   * 获取作业列表
   * @param {object} params 查询参数
   */
  async getAssignments(params = {}) {
    const query = new URLSearchParams(params).toString()
    return this.request(`/assignments${query ? '?' + query : ''}`)
  }

  /**
   * 获取作业详情
   * @param {number} assignmentId 作业ID
   */
  async getAssignmentDetail(assignmentId) {
    return this.request(`/assignments/${assignmentId}`)
  }

  /**
   * 提交作业
   * @param {number} assignmentId 作业ID
   * @param {object} submissionData 提交数据
   */
  async submitAssignment(assignmentId, submissionData) {
    return this.request(`/assignments/${assignmentId}/submit`, {
      method: 'POST',
      data: submissionData,
      loadingText: '提交中...'
    })
  }

  /**
   * 获取作业提交记录
   * @param {number} assignmentId 作业ID
   */
  async getAssignmentSubmission(assignmentId) {
    return this.request(`/assignments/${assignmentId}/submission`)
  }

  // ==================== 系统相关 ====================

  /**
   * 获取系统配置
   */
  async getSystemConfig() {
    return this.request('/system/config', {
      showLoading: false
    })
  }

  /**
   * 上传文件
   * @param {string} filePath 文件路径
   * @param {string} type 文件类型
   */
  async uploadFile(filePath, type = 'image') {
    const token = uni.getStorageSync('token')
    
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title: '上传中...',
        mask: true
      })

      uni.uploadFile({
        url: this.baseUrl + '/system/upload',
        filePath: filePath,
        name: 'file',
        formData: { type },
        header: {
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        success: (res) => {
          uni.hideLoading()
          
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve(data)
            } else {
              uni.showToast({
                title: data.message || '上传失败',
                icon: 'none'
              })
              reject(new Error(data.message))
            }
          } catch (error) {
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
            reject(error)
          }
        },
        fail: (error) => {
          uni.hideLoading()
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  }
}

// 创建API服务实例
const apiService = new ApiService()

// 导出API服务
export default apiService

// 也可以按模块导出
export const authApi = {
  login: apiService.login.bind(apiService),
  register: apiService.register.bind(apiService),
  getCurrentUser: apiService.getCurrentUser.bind(apiService),
  updateProfile: apiService.updateProfile.bind(apiService),
  changePassword: apiService.changePassword.bind(apiService)
}

export const groupApi = {
  getGroups: apiService.getGroups.bind(apiService),
  getGroupDetail: apiService.getGroupDetail.bind(apiService),
  joinGroup: apiService.joinGroup.bind(apiService),
  leaveGroup: apiService.leaveGroup.bind(apiService),
  getGroupMembers: apiService.getGroupMembers.bind(apiService)
}

export const courseApi = {
  getCourses: apiService.getCourses.bind(apiService),
  getCourseDetail: apiService.getCourseDetail.bind(apiService),
  getGroupCourses: apiService.getGroupCourses.bind(apiService)
}

export const learningApi = {
  submitLearningRecord: apiService.submitLearningRecord.bind(apiService),
  getLearningRecords: apiService.getLearningRecords.bind(apiService),
  getLearningProgress: apiService.getLearningProgress.bind(apiService)
}

export const assignmentApi = {
  getAssignments: apiService.getAssignments.bind(apiService),
  getAssignmentDetail: apiService.getAssignmentDetail.bind(apiService),
  submitAssignment: apiService.submitAssignment.bind(apiService),
  getAssignmentSubmission: apiService.getAssignmentSubmission.bind(apiService)
}
