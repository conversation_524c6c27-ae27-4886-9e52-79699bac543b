@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-abdab3e4 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-abdab3e4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-abdab3e4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-abdab3e4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-abdab3e4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-abdab3e4 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-abdab3e4::after {
  border: none;
}
.u-hover-class.data-v-abdab3e4 {
  opacity: 0.7;
}
.u-primary-light.data-v-abdab3e4 {
  color: #ecf5ff;
}
.u-warning-light.data-v-abdab3e4 {
  color: #fdf6ec;
}
.u-success-light.data-v-abdab3e4 {
  color: #f5fff0;
}
.u-error-light.data-v-abdab3e4 {
  color: #fef0f0;
}
.u-info-light.data-v-abdab3e4 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-abdab3e4 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-abdab3e4 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-abdab3e4 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-abdab3e4 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-abdab3e4 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-abdab3e4 {
  color: #398ade;
}
.u-warning-dark.data-v-abdab3e4 {
  color: #f1a532;
}
.u-success-dark.data-v-abdab3e4 {
  color: #53c21d;
}
.u-error-dark.data-v-abdab3e4 {
  color: #e45656;
}
.u-info-dark.data-v-abdab3e4 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-abdab3e4 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-abdab3e4 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-abdab3e4 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-abdab3e4 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-abdab3e4 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-abdab3e4 {
  color: #9acafc;
}
.u-warning-disabled.data-v-abdab3e4 {
  color: #f9d39b;
}
.u-success-disabled.data-v-abdab3e4 {
  color: #a9e08f;
}
.u-error-disabled.data-v-abdab3e4 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-abdab3e4 {
  color: #c4c6c9;
}
.u-primary.data-v-abdab3e4 {
  color: #3c9cff;
}
.u-warning.data-v-abdab3e4 {
  color: #f9ae3d;
}
.u-success.data-v-abdab3e4 {
  color: #5ac725;
}
.u-error.data-v-abdab3e4 {
  color: #f56c6c;
}
.u-info.data-v-abdab3e4 {
  color: #909399;
}
.u-primary-bg.data-v-abdab3e4 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-abdab3e4 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-abdab3e4 {
  background-color: #5ac725;
}
.u-error-bg.data-v-abdab3e4 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-abdab3e4 {
  background-color: #909399;
}
.u-main-color.data-v-abdab3e4 {
  color: #303133;
}
.u-content-color.data-v-abdab3e4 {
  color: #606266;
}
.u-tips-color.data-v-abdab3e4 {
  color: #909193;
}
.u-light-color.data-v-abdab3e4 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-abdab3e4 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-abdab3e4 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-abdab3e4 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-abdab3e4 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-abdab3e4::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@-webkit-keyframes rowup-data-v-abdab3e4 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
    -webkit-transform-origin: center center;
            transform-origin: center center;
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    -webkit-transform-origin: center center;
            transform-origin: center center;
}
}
@keyframes rowup-data-v-abdab3e4 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
    -webkit-transform-origin: center center;
            transform-origin: center center;
}
100% {
    -webkit-transform: translate(-50%, -50%) rotate(360deg);
    -webkit-transform-origin: center center;
            transform-origin: center center;
}
}
.imt-audio.theme1.data-v-abdab3e4 {
  padding: 0 30rpx 30rpx;
  background: #fff;
}
.imt-audio.theme1 .top > view:nth-child(2) .title.data-v-abdab3e4 {
  font-weight: bold;
  font-size: 34rpx;
  margin-top: 50rpx;
  text-align: center;
}
.imt-audio.theme1 .top > view:nth-child(2) .singer.data-v-abdab3e4 {
  color: #999;
  font-size: 26rpx;
  margin-top: 10rpx;
  text-align: center;
  margin-bottom: 18rpx;
}
.imt-audio.theme1 .audio-wrapper.data-v-abdab3e4 {
  display: flex;
  align-items: center;
  width: 90%;
  margin: 0 auto;
}
.imt-audio.theme1 .audio-button-box.data-v-abdab3e4 {
  display: flex;
  align-items: center;
  margin: 40rpx auto 0;
  justify-content: space-around;
  height: 100rpx;
}
.imt-audio.theme1 .audio-number.data-v-abdab3e4 {
  font-size: 24rpx;
  line-height: 1;
  color: #333;
}
.imt-audio.theme1 .audio-slider.data-v-abdab3e4 {
  flex: 1;
  margin: 0 30rpx 0 35rpx;
}
.imt-audio.theme1 .audio-control-wrapper.data-v-abdab3e4 {
  margin: 20rpx auto;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.imt-audio.theme1 .cover.data-v-abdab3e4 {
  width: 350rpx;
  height: 350rpx;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}
.imt-audio.theme1 .playbox.data-v-abdab3e4 {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.imt-audio.theme1 .play.data-v-abdab3e4,
.imt-audio.theme1 .pause.data-v-abdab3e4 {
  width: 100rpx;
  height: 100rpx;
}
.imt-audio.theme1 .play.loading.data-v-abdab3e4,
.imt-audio.theme1 .pause.loading.data-v-abdab3e4 {
  width: 80rpx;
  height: 80rpx;
  -webkit-animation: rotating-data-v-abdab3e4 2s linear infinite;
          animation: rotating-data-v-abdab3e4 2s linear infinite;
}
.imt-audio.theme1 .prevbtn.data-v-abdab3e4,
.imt-audio.theme1 .nextbtn.data-v-abdab3e4 {
  width: 40rpx;
  height: 40rpx;
}
.imt-audio.theme1 .prevplay.data-v-abdab3e4 {
  width: 40rpx;
  height: 40rpx;
  -webkit-transform: rotateZ(180deg);
          transform: rotateZ(180deg);
}
.imt-audio.theme1 .nextplay.data-v-abdab3e4 {
  width: 40rpx;
  height: 40rpx;
}
.imt-audio.theme2.data-v-abdab3e4 {
  background: #fff;
  border: 1px solid #cecece;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
}
.imt-audio.theme2 .top.data-v-abdab3e4 {
  background: #fff;
  display: flex;
  align-items: center;
  height: 150rpx;
}
.imt-audio.theme2 .top > view.data-v-abdab3e4:nth-child(2) {
  flex: 1;
  margin: 0 30rpx;
}
.imt-audio.theme2 .top > view:nth-child(2) .title.data-v-abdab3e4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
}
.imt-audio.theme2 .top > view:nth-child(2) .title text.data-v-abdab3e4 {
  font-size: 30rpx;
  text-align: left;
  max-width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  flex: 1;
}
.imt-audio.theme2 .top > view:nth-child(2) .title .audio-number.data-v-abdab3e4 {
  font-size: 24rpx;
  line-height: 1;
  color: #333;
}
.imt-audio.theme2 .top > view:nth-child(2) .singer.data-v-abdab3e4 {
  color: #999;
  font-size: 26rpx;
  margin-top: 10rpx;
  text-align: left;
  margin-bottom: 18rpx;
  max-width: 100%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.imt-audio.theme2 .cover.data-v-abdab3e4 {
  width: 120rpx;
  height: 120rpx;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2px solid #fff;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}
.imt-audio.theme2 .cover.on.data-v-abdab3e4 {
  -webkit-animation: 10s rowup-data-v-abdab3e4 linear infinite normal;
  animation: 10s rowup-data-v-abdab3e4 linear infinite normal;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}
.imt-audio.theme2 .audio-control-wrapper.data-v-abdab3e4 {
  width: 150rpx;
  height: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #efefef;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.imt-audio.theme2 .play.data-v-abdab3e4 {
  width: 80rpx;
  height: 80rpx;
  z-index: 99;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
}
.imt-audio.theme2 .play.loading.data-v-abdab3e4 {
  width: 60rpx;
  height: 60rpx;
  -webkit-animation: rotating-data-v-abdab3e4 2s linear infinite;
          animation: rotating-data-v-abdab3e4 2s linear infinite;
}
.imt-audio.theme2 .prevbtn.data-v-abdab3e4 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 40rpx;
}
.imt-audio.theme2 .nextbtn.data-v-abdab3e4 {
  width: 48rpx;
  height: 48rpx;
  margin-left: 40rpx;
}
.imt-audio.theme3.data-v-abdab3e4 {
  background: #ccc;
  width: 100%;
  overflow: hidden;
  display: flex;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
  max-height: 200rpx;
  position: relative;
}
.imt-audio.theme3 .top.data-v-abdab3e4 {
  width: 140rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.imt-audio.theme3 .audio-wrapper.data-v-abdab3e4 {
  display: flex;
  flex-direction: column;
  flex: 1;
  color: #fff;
  margin-left: 20rpx;
}
.imt-audio.theme3 .audio-wrapper .titlebox.data-v-abdab3e4 {
  display: flex;
  line-height: 46rpx;
  margin-bottom: 30rpx;
}
.imt-audio.theme3 .audio-wrapper .titlebox .title.data-v-abdab3e4 {
  font-size: 30rpx;
  max-width: 50%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.imt-audio.theme3 .audio-wrapper .titlebox .singer.data-v-abdab3e4 {
  margin-left: 20rpx;
  font-size: 28rpx;
  max-width: 50%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.imt-audio.theme3 .slidebox.data-v-abdab3e4 {
  display: flex;
  justify-content: space-between;
  width: 96%;
}
.imt-audio.theme3 .slidebox view.data-v-abdab3e4:first-child {
  font-size: 28rpx;
}
.imt-audio.theme3 .slidebox view.data-v-abdab3e4:last-child {
  font-size: 28rpx;
}
.imt-audio.theme3 .slidebox view:last-child text.data-v-abdab3e4:last-child {
  margin-left: 40rpx;
}
.imt-audio.theme3.data-v-abdab3e4 .uni-slider-tap-area {
  padding: 0;
}
.imt-audio.theme3.data-v-abdab3e4 .uni-slider-wrapper {
  min-height: 0;
}
.imt-audio.theme3.data-v-abdab3e4 .uni-slider-handle-wrapper {
  height: 4px;
}
.imt-audio.theme3 .audio-slider.data-v-abdab3e4 {
  position: absolute;
  top: 0;
  margin: 0;
  width: 100%;
  left: 0;
  padding: 0;
}
.imt-audio.theme3 .cover.data-v-abdab3e4 {
  width: 120rpx;
  height: 120rpx;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
}
.imt-audio.theme3 .play.data-v-abdab3e4 {
  width: 80rpx;
  height: 80rpx;
  z-index: 99;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.imt-audio.theme3 .play.loading.data-v-abdab3e4 {
  width: 60rpx;
  height: 60rpx;
  -webkit-animation: rotating_theme3-data-v-abdab3e4 2s linear infinite;
          animation: rotating_theme3-data-v-abdab3e4 2s linear infinite;
}
@-webkit-keyframes rotating-data-v-abdab3e4 {
0% {
    -webkit-transform: rotateZ(0deg);
            transform: rotateZ(0deg);
}
100% {
    -webkit-transform: rotateZ(360deg);
            transform: rotateZ(360deg);
}
}
@keyframes rotating-data-v-abdab3e4 {
0% {
    -webkit-transform: rotateZ(0deg);
            transform: rotateZ(0deg);
}
100% {
    -webkit-transform: rotateZ(360deg);
            transform: rotateZ(360deg);
}
}
@-webkit-keyframes rotating_theme3-data-v-abdab3e4 {
0% {
    -webkit-transform: translate(-50%, -50%) rotateZ(0deg);
            transform: translate(-50%, -50%) rotateZ(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotateZ(360deg);
            transform: translate(-50%, -50%) rotateZ(360deg);
}
}
@keyframes rotating_theme3-data-v-abdab3e4 {
0% {
    -webkit-transform: translate(-50%, -50%) rotateZ(0deg);
            transform: translate(-50%, -50%) rotateZ(0deg);
}
100% {
    -webkit-transform: translate(-50%, -50%) rotateZ(360deg);
            transform: translate(-50%, -50%) rotateZ(360deg);
}
}
.theme3 .audio-slider.data-v-abdab3e4 {
  margin-top: -8px !important;
}

