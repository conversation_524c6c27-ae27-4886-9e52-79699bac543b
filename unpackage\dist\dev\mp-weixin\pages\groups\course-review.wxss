
.filter-bar.data-v-43bcbcc8 {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
}
.filter-item.data-v-43bcbcc8 {
	padding: 16rpx 32rpx;
	background: #f8f8f8;
	border-radius: 30rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
}
.filter-item.active.data-v-43bcbcc8 {
	background: #2094CE;
	color: #fff;
}
.course-list.data-v-43bcbcc8 {
	padding: 0 30rpx;
}
.course-item.data-v-43bcbcc8 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.course-cover.data-v-43bcbcc8 {
	width: 160rpx;
	height: 120rpx;
	border-radius: 16rpx;
	overflow: hidden;
	position: relative;
	margin-right: 20rpx;
}
.course-cover image.data-v-43bcbcc8 {
	width: 100%;
	height: 100%;
}
.course-duration.data-v-43bcbcc8 {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0,0,0,0.7);
	color: #fff;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	font-size: 20rpx;
}
.course-info.data-v-43bcbcc8 {
	flex: 1;
}
.course-title.data-v-43bcbcc8 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.course-desc.data-v-43bcbcc8 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
	line-height: 1.4;
}
.course-meta.data-v-43bcbcc8 {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 12rpx;
}
.meta-tag.data-v-43bcbcc8 {
	font-size: 22rpx;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}
.meta-tag.completed.data-v-43bcbcc8 {
	background: #e8f5e8;
	color: #52c41a;
}
.meta-tag.learning.data-v-43bcbcc8 {
	background: #e6f7ff;
	color: #1890ff;
}
.meta-tag.pending.data-v-43bcbcc8 {
	background: #f0f0f0;
	color: #999;
}
.meta-time.data-v-43bcbcc8 {
	font-size: 22rpx;
	color: #999;
}
.course-progress.data-v-43bcbcc8 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.progress-bar.data-v-43bcbcc8 {
	flex: 1;
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}
.progress-fill.data-v-43bcbcc8 {
	height: 100%;
	background: #2094CE;
	transition: width 0.3s;
}
.progress-text.data-v-43bcbcc8 {
	font-size: 22rpx;
	color: #666;
	min-width: 60rpx;
}
.course-action.data-v-43bcbcc8 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 20rpx;
}
.course-action .iconfont.data-v-43bcbcc8 {
	font-size: 36rpx;
}
.icon-play.data-v-43bcbcc8 {
	color: #2094CE;
}
.icon-check.data-v-43bcbcc8 {
	color: #52c41a;
}
.icon-lock.data-v-43bcbcc8 {
	color: #ccc;
}
.empty-state.data-v-43bcbcc8 {
	text-align: center;
	padding: 100rpx 30rpx;
	color: #999;
}
.empty-state image.data-v-43bcbcc8 {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

