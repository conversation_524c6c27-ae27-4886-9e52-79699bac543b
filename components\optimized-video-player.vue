<template>
	<view class="video-container" :class="{ fullscreen: isFullscreen }">
		<!-- 视频播放器 -->
		<video
			ref="videoPlayer"
			:id="playerId"
			:src="currentVideoSrc"
			:poster="poster"
			:controls="showNativeControls"
			:autoplay="autoplay"
			:loop="loop"
			:muted="muted"
			:show-center-play-btn="true"
			:enable-play-gesture="true"
			:object-fit="objectFit"
			@play="handlePlay"
			@pause="handlePause"
			@ended="handleEnded"
			@timeupdate="handleTimeUpdate"
			@error="handleError"
			@loadstart="handleLoadStart"
			@canplay="handleCanPlay"
			@waiting="handleWaiting"
			@fullscreenchange="handleFullscreenChange"
			class="video-player"
			:style="videoStyle"
		/>

		<!-- 自定义控制栏 -->
		<view class="custom-controls" v-if="showCustomControls && !isLoading && !hasError">
			<!-- 播放/暂停按钮 -->
			<view class="control-left">
				<view class="play-btn" @click="togglePlay">
					<text class="iconfont" :class="isPlaying ? 'icon-pause' : 'icon-play'"></text>
				</view>
				<text class="time-text">{{currentTimeText}} / {{durationText}}</text>
			</view>

			<!-- 进度条 -->
			<view class="progress-container" @click="seekTo">
				<view class="progress-bg">
					<view class="progress-buffer" :style="{ width: bufferPercent + '%' }"></view>
					<view class="progress-current" :style="{ width: progressPercent + '%' }"></view>
					<view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
				</view>
			</view>

			<!-- 右侧控制 -->
			<view class="control-right">
				<view class="quality-btn" @click="showQualityMenu = !showQualityMenu">
					<text class="quality-text">{{currentQuality}}</text>
				</view>
				<view class="speed-btn" @click="showSpeedMenu = !showSpeedMenu">
					<text class="speed-text">{{playbackRate}}x</text>
				</view>
				<view class="fullscreen-btn" @click="toggleFullscreen">
					<text class="iconfont icon-fullscreen"></text>
				</view>
			</view>
		</view>

		<!-- 质量选择菜单 -->
		<view class="quality-menu" v-if="showQualityMenu">
			<view
				class="quality-item"
				v-for="quality in qualityOptions"
				:key="quality"
				:class="{ active: quality === currentQuality }"
				@click="changeQuality(quality)"
			>
				<text>{{quality}}</text>
			</view>
		</view>

		<!-- 速度选择菜单 -->
		<view class="speed-menu" v-if="showSpeedMenu">
			<view
				class="speed-item"
				v-for="speed in speedOptions"
				:key="speed"
				:class="{ active: speed === playbackRate }"
				@click="changeSpeed(speed)"
			>
				<text>{{speed}}x</text>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="isLoading">
			<view class="loading-spinner"></view>
			<text class="loading-text">{{loadingText}}</text>
		</view>

		<!-- 错误状态 -->
		<view class="error-overlay" v-if="hasError" @click="retry">
			<text class="error-icon">⚠️</text>
			<text class="error-text">{{errorText}}</text>
			<text class="retry-text">点击重试</text>
		</view>

		<!-- 播放提示 -->
		<view class="play-hint" v-if="!isPlaying && !isLoading && !hasError && currentVideoSrc">
			<view class="play-hint-btn" @click="togglePlay">
				<text class="iconfont icon-play"></text>
			</view>
		</view>
	</view>
</template>

<script>
import { VideoOptimizer } from '@/utils/video-optimizer.js';
import cleanup from '@/mixins/cleanup.js';

export default {
	name: 'OptimizedVideoPlayer',
	mixins: [cleanup],
	props: {
		src: String,
		poster: String,
		autoplay: Boolean,
		loop: Boolean,
		muted: Boolean,
		courseId: [String, Number],
		videoId: [String, Number],
		showCustomControls: {
			type: Boolean,
			default: true
		},
		showNativeControls: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			playerId: `video_${Date.now()}`,
			videoOptimizer: null,
			videoContext: null,

			// 播放状态
			isPlaying: false,
			isLoading: false,
			hasError: false,
			isFullscreen: false,

			// 时间相关
			currentTime: 0,
			duration: 0,
			currentTimeText: '00:00',
			durationText: '00:00',
			progressPercent: 0,
			bufferPercent: 0,

			// 质量和速度
			currentQuality: '480p',
			playbackRate: 1,
			qualityOptions: ['240p', '360p', '480p', '720p', '1080p'],
			speedOptions: [0.5, 0.75, 1, 1.25, 1.5, 2],

			// UI状态
			showQualityMenu: false,
			showSpeedMenu: false,

			// 文本
			loadingText: '加载中...',
			errorText: '播放失败',

			// 其他
			lastProgressSaveTime: 0,
			healthCheckTimer: null,
			lastHealthCheckTime: 0
		};
	},
	computed: {
		currentVideoSrc() {
			if (!this.src) return '';
			if (this.videoOptimizer) {
				return this.videoOptimizer.getQualityUrl(this.src, this.currentQuality);
			}
			return this.src;
		},
		
		videoStyle() {
			return {
				width: '100%',
				height: this.isFullscreen ? '100vh' : '400rpx',
				backgroundColor: '#000'
			};
		},
		
		showCenterPlayBtn() {
			return !this.showCustomControls;
		},
		
		enablePlayGesture() {
			return true;
		},
		
		objectFit() {
			return 'contain';
		}
	},
	watch: {
		src: {
			handler(newSrc) {
				if (newSrc) {
					this.loadVideo();
				}
			},
			immediate: true
		}
	},
	mounted() {
		this.initVideoOptimizer();
		this.initVideoContext();
		this.loadSavedProgress();
	},
	beforeDestroy() {
		try {
			this.cleanup();
		} catch (e) {
			console.warn('视频播放器清理时出错:', e);
		}
	},

	destroyed() {
		// 确保所有资源都被清理
		try {
			if (this.videoContext) {
				this.videoContext = null;
			}
			if (this.videoOptimizer) {
				this.videoOptimizer.cleanup();
				this.videoOptimizer = null;
			}
		} catch (e) {
			console.warn('视频播放器销毁时出错:', e);
		}
	},
	methods: {
		// 初始化视频优化器
		initVideoOptimizer() {
			try {
				this.videoOptimizer = new VideoOptimizer();
				this.currentQuality = this.videoOptimizer.getOptimalQuality();
			} catch (e) {
				console.error('初始化视频优化器失败:', e);
			}
		},
		
		// 初始化视频上下文
		initVideoContext() {
			this.$nextTick(() => {
				try {
					if (this.playerId) {
						this.videoContext = uni.createVideoContext(this.playerId, this);
					}
				} catch (e) {
					console.warn('初始化视频上下文失败:', e);
				}
			});
		},
		
		// 加载视频
		loadVideo() {
			console.log('开始加载视频:', this.currentVideoSrc);
			this.isLoading = true;
			this.hasError = false;
			this.errorText = '播放失败';

			// 预加载下一个视频
			this.preloadNextVideo();
		},
		
		// 加载保存的进度
		loadSavedProgress() {
			if (!this.videoId || !this.videoOptimizer) return;
			
			const savedProgress = this.videoOptimizer.getProgress(this.videoId);
			if (savedProgress && savedProgress.progress > 10) {
				// 如果有保存的进度且大于10秒，询问是否继续播放
				uni.showModal({
					title: '继续播放',
					content: `上次播放到 ${this.formatTime(savedProgress.progress)}，是否继续？`,
					success: (res) => {
						if (res.confirm && this.videoContext) {
							this.videoContext.seek(savedProgress.progress);
						}
					}
				});
			}
		},
		
		// 预加载下一个视频
		preloadNextVideo() {
			this.$emit('request-next-video', (nextVideoUrl) => {
				if (nextVideoUrl && this.videoOptimizer) {
					this.videoOptimizer.preloadVideo(nextVideoUrl, 'low');
				}
			});
		},
		
		// 播放事件处理
		handlePlay() {
			console.log('视频开始播放');
			this.isPlaying = true;
			this.isLoading = false;
			this.$emit('play');

			this.startHealthCheck();
		},
		
		handlePause() {
			this.isPlaying = false;
			this.$emit('pause');
			
			this.stopHealthCheck();
		},
		
		handleEnded() {
			this.isPlaying = false;
			this.$emit('ended');
			
			this.stopHealthCheck();
			
			// 保存完成状态
			if (this.videoId && this.videoOptimizer) {
				this.videoOptimizer.saveProgress(this.videoId, this.duration, this.duration);
			}
		},
		
		handleTimeUpdate(e) {
			// 安全地访问事件数据
			if (!e || !e.detail) {
				console.warn('handleTimeUpdate: 事件数据无效', e);
				return;
			}

			const currentTime = e.detail.currentTime;
			const duration = e.detail.duration;

			// 验证时间数据有效性
			if (typeof currentTime !== 'number' || typeof duration !== 'number') {
				console.warn('handleTimeUpdate: 时间数据类型无效', { currentTime, duration });
				return;
			}

			// 验证数据合理性
			if (currentTime < 0 || duration <= 0 || currentTime > duration + 1) { // 允许1秒的误差
				console.warn('handleTimeUpdate: 时间数据不合理', { currentTime, duration });
				return;
			}

			this.currentTime = currentTime;
			this.duration = duration;
			this.currentTimeText = this.formatTime(currentTime);
			this.durationText = this.formatTime(duration);
			this.progressPercent = duration > 0 ? (currentTime / duration) * 100 : 0;

			// 批量保存进度
			if (this.videoId && currentTime > 0 && this.videoOptimizer) {
				this.videoOptimizer.batchSaveProgress(this.videoId, currentTime, duration);
			}

			this.$emit('timeupdate', { currentTime, duration });
		},
		
		handleError(e) {
			this.hasError = true;
			this.isLoading = false;
			this.isPlaying = false;
			
			console.error('视频播放错误:', e);
			
			// 尝试降级质量
			if (this.videoOptimizer) {
				const lowerQuality = this.videoOptimizer.autoDowngradeQuality(this.currentQuality);
				if (lowerQuality !== this.currentQuality) {
					this.errorText = '正在尝试降低清晰度...';
					this.currentQuality = lowerQuality;
					this.$setTimeout(() => {
						this.retry();
					}, 1000);
				} else {
					this.errorText = '播放失败，点击重试';
				}
			}
			
			this.$emit('error', e);
		},
		
		handleLoadStart() {
			console.log('视频开始加载');
			this.isLoading = true;
		},

		handleCanPlay() {
			console.log('视频可以播放');
			this.isLoading = false;
		},
		
		handleWaiting() {
			this.isLoading = true;
		},
		
		handleFullscreenChange(e) {
			this.isFullscreen = e.detail.fullScreen;
			this.$emit('fullscreenchange', e);
		},
		
		// 重试加载
		retry() {
			this.hasError = false;
			this.loadVideo();
		},
		
		// 健康检查
		startHealthCheck() {
			this.lastHealthCheckTime = this.currentTime;
			this.healthCheckTimer = this.$setInterval(() => {
				if (this.videoOptimizer && this.isPlaying) {
					const isHealthy = this.videoOptimizer.checkPlaybackHealth(
						this.currentTime,
						this.lastHealthCheckTime,
						2000
					);

					if (!isHealthy) {
						console.warn('播放不流畅，考虑降低质量');
						// 可以在这里实现自动降级质量的逻辑
					}
				}

				this.lastHealthCheckTime = this.currentTime;
			}, 2000);
		},
		
		stopHealthCheck() {
			if (this.healthCheckTimer) {
				clearInterval(this.healthCheckTimer);
			}
		},
		
		// 格式化时间
		formatTime(seconds) {
			if (this.videoOptimizer) {
				return this.videoOptimizer.formatTime(seconds);
			}
			
			if (isNaN(seconds) || seconds < 0) return '00:00';
			
			const hours = Math.floor(seconds / 3600);
			const minutes = Math.floor((seconds % 3600) / 60);
			const secs = Math.floor(seconds % 60);
			
			if (hours > 0) {
				return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			} else {
				return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			}
		},
		
		// 播放控制方法
		togglePlay() {
			if (!this.videoContext) return;

			try {
				if (this.isPlaying) {
					this.videoContext.pause();
				} else {
					this.videoContext.play();
				}
			} catch (e) {
				console.warn('播放控制失败:', e);
			}
		},

		seekTo(e) {
			if (!this.videoContext || !this.duration) return;

			try {
				const rect = e.currentTarget.getBoundingClientRect();
				const percent = (e.detail.x - rect.left) / rect.width;
				const seekTime = percent * this.duration;
				this.videoContext.seek(seekTime);
			} catch (e) {
				console.warn('跳转失败:', e);
			}
		},

		changeQuality(quality) {
			this.currentQuality = quality;
			this.showQualityMenu = false;

			if (this.videoOptimizer) {
				// 这里可以实现质量切换逻辑
				console.log('切换质量到:', quality);
			}
		},

		changeSpeed(speed) {
			this.playbackRate = speed;
			this.showSpeedMenu = false;

			try {
				if (this.videoContext) {
					this.videoContext.playbackRate(speed);
				}
			} catch (e) {
				console.warn('设置播放速度失败:', e);
			}
		},

		toggleFullscreen() {
			try {
				if (this.videoContext) {
					if (this.isFullscreen) {
						this.videoContext.exitFullScreen();
					} else {
						this.videoContext.requestFullScreen();
					}
				}
			} catch (e) {
				console.warn('全屏切换失败:', e);
			}
		},

		cleanup() {
			try {
				// 停止健康检查
				this.stopHealthCheck();

				// 清理视频优化器
				if (this.videoOptimizer) {
					this.videoOptimizer.cleanup();
				}

				// 清理视频上下文
				if (this.videoContext) {
					try {
						this.videoContext.pause();
					} catch (e) {
						// 忽略暂停错误
					}
					this.videoContext = null;
				}

				// 重置状态
				this.isPlaying = false;
				this.isLoading = false;
				this.hasError = false;

			} catch (e) {
				console.warn('视频播放器清理失败:', e);
			}
		}
	}
};
</script>

<style scoped>
.video-container {
	position: relative;
	width: 100%;
	min-height: 400rpx;
	background: #000;
	overflow: hidden;
	border-radius: 12rpx;
}

.video-container.fullscreen {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	border-radius: 0;
}

.video-player {
	width: 100%;
	height: 400rpx;
	display: block;
	background: #000;
	border-radius: 12rpx;
}

/* 自定义控制栏 */
.custom-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
	padding: 20rpx;
	display: flex;
	align-items: center;
	color: #fff;
	z-index: 10;
}

.control-left {
	display: flex;
	align-items: center;
	margin-right: 20rpx;
}

.play-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.time-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9);
	min-width: 120rpx;
}

.progress-container {
	flex: 1;
	margin: 0 20rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
}

.progress-bg {
	position: relative;
	width: 100%;
	height: 6rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 3rpx;
}

.progress-buffer {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 3rpx;
	transition: width 0.3s ease;
}

.progress-current {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	background: #007AFF;
	border-radius: 3rpx;
	transition: width 0.1s ease;
}

.progress-thumb {
	position: absolute;
	top: -6rpx;
	width: 18rpx;
	height: 18rpx;
	background: #007AFF;
	border-radius: 50%;
	transform: translateX(-50%);
	transition: left 0.1s ease;
}

.control-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.quality-btn, .speed-btn, .fullscreen-btn {
	padding: 10rpx 16rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	font-size: 24rpx;
}

.quality-text, .speed-text {
	color: #fff;
}

/* 菜单样式 */
.quality-menu, .speed-menu {
	position: absolute;
	bottom: 100rpx;
	right: 20rpx;
	background: rgba(0, 0, 0, 0.9);
	border-radius: 12rpx;
	padding: 20rpx;
	min-width: 120rpx;
	z-index: 20;
}

.quality-item, .speed-item {
	padding: 16rpx 20rpx;
	color: #fff;
	text-align: center;
	border-radius: 8rpx;
	margin-bottom: 8rpx;
}

.quality-item:last-child, .speed-item:last-child {
	margin-bottom: 0;
}

.quality-item.active, .speed-item.active {
	background: #007AFF;
}

/* 加载和错误状态 */
.loading-overlay, .error-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.8);
	color: #fff;
	border-radius: 12rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	border-top: 4rpx solid #007AFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

.loading-text, .error-text {
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.error-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.retry-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

/* 播放提示 */
.play-hint {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 12rpx;
}

.play-hint-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.play-hint-btn .iconfont {
	font-size: 48rpx;
	color: #333;
}

/* 图标字体 */
.iconfont {
	font-family: 'iconfont';
}

.icon-play::before {
	content: '▶';
}

.icon-pause::before {
	content: '⏸';
}

.icon-fullscreen::before {
	content: '⛶';
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.custom-controls {
		padding: 15rpx;
	}

	.time-text {
		font-size: 22rpx;
		min-width: 100rpx;
	}

	.quality-btn, .speed-btn, .fullscreen-btn {
		padding: 8rpx 12rpx;
		font-size: 22rpx;
	}
}
</style>
