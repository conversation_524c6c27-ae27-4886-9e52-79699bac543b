(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["GraceUI5/components/gui-refresh"],{"08b7":function(e,t,r){"use strict";r.r(t);var n=r("4336"),s=r.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);t["default"]=s.a},"34b5":function(e,t,r){"use strict";var n=r("93e4"),s=r.n(n);s.a},4336:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"gui-refresh",props:{refreshText:{type:Array,default:function(){return["继续下拉刷新","松开手指开始刷新","数据刷新中","数据已刷新"]}},refreshBgColor:{type:Array,default:function(){return["#FFFFFF","#FFFFFF","#FFFFFF","#63D2BC"]}},refreshColor:{type:Array,default:function(){return["rgba(69, 90, 100, 0.6)","rgba(69, 90, 100, 0.6)","#63D2BC","#FFFFFF"]}},refreshFontSize:{type:String,default:"26rpx"}},data:function(){return{reScrollTop:0,refreshHeight:0,refreshY:0,refreshStatus:0,refreshTimer:0}},methods:{touchstart:function(e){this.reScrollTop>10||(this.refreshY=e.changedTouches[0].pageY)},touchmove:function(e){if(this.refreshStatus>=1)return null;if(!(this.reScrollTop>10)){var t=e.changedTouches[0].pageY-this.refreshY;t/=2,t>=50&&(t=50,this.refreshStatus=1),t>15&&(this.refreshHeight=t)}},touchend:function(e){if(!(this.reScrollTop>10))return this.refreshStatus<1?this.resetFresh():void(1==this.refreshStatus&&(this.refreshStatus=2,this.$emit("reload")))},scroll:function(e){this.reScrollTop=e.detail.scrollTop},endReload:function(){var e=this;this.refreshStatus=3,setTimeout((function(){e.resetFresh()}),1e3)},resetFresh:function(){return this.refreshHeight=0,this.refreshStatus=0,null},rotate360:function(){var e=this.$refs.loadingIcon;animation.transition(e,{styles:{transform:"rotate(7200deg)"},duration:2e4,timingFunction:"linear",needLayout:!1,delay:0})}}};t.default=n},"4e2d":function(e,t,r){"use strict";r.r(t);var n=r("f2b4"),s=r("08b7");for(var i in s)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return s[e]}))}(i);r("34b5");var o=r("828b"),u=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"19da84aa",null,!1,n["a"],void 0);t["default"]=u.exports},"93e4":function(e,t,r){},f2b4:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return s})),r.d(t,"a",(function(){}));var n=function(){var e=this.$createElement;this._self._c},s=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'GraceUI5/components/gui-refresh-create-component',
    {
        'GraceUI5/components/gui-refresh-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4e2d"))
        })
    },
    [['GraceUI5/components/gui-refresh-create-component']]
]);
