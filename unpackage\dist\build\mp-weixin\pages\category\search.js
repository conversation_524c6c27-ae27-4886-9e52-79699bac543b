(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/category/search"],{"1e4e":function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("7eb4")),s=i(n("ee10")),c=i(n("7ca3")),o=n("8f59");function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){(0,c.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var l={components:{mEmptyData:function(){n.e("components/m-empty-data/m-empty-data").then(function(){return resolve(n("cd92"))}.bind(null,n)).catch(n.oe)}},computed:u(u({},(0,o.mapState)(["userInfo"])),(0,o.mapGetters)(["hasLogin"])),data:function(){return{searchValue:"",placeholder:"请输入关键词搜索相关内容",popularShow:!1,searchRecent:!1,searchRecentList:[],page:1,limit:15,currentIndex:0,type:1,list:[],role:0,optionVal1:"",def:0,optionList1:["课程","资料"]}},onLoad:function(){this.searchRecentList=this.$db.get("historySearch"),""==this.searchValue&&null!=this.searchRecentList&&""!=this.searchRecentList&&(this.searchRecent=!0),this.hasLogin&&1==this.userInfo.memberRole?this.role=1:this.role=0},onReachBottom:function(){this.page++,this.getList()},methods:{getList:function(){0==this.role?this.getPositionList():this.getResumeList()},getPositionList:function(){var t=this;return(0,s.default)(a.default.mark((function e(){var n,i,s,c;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n={page:t.page,limit:t.limit,postName:t.searchValue},n.latitude=getApp().globalData.location.latitude,n.longitude=getApp().globalData.location.longitude,n.pcitycode=getApp().globalData.location.pcitycode,t.status="请求中",e.next=7,t.$apis.getPositionList(n);case 7:if(i=e.sent,i){for(c in s=i.data,s)s[c].skill&&(s[c].skill=s[c].skill.split(","));t.list=t.list.concat(s||[]),t.changeStatus(i)}case 9:case"end":return e.stop()}}),e)})))()},getResumeList:function(){var t=this;return(0,s.default)(a.default.mark((function e(){var n,i,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n={page:t.page,limit:t.limit,keyword:t.searchValue},n.pcitycode=getApp().globalData.location.pcitycode,t.status="请求中",e.next=5,t.$apis.getResumeList(n);case 5:i=e.sent,i&&(s=i.data,t.list=t.list.concat(s||[]),t.changeStatus(i));case 7:case"end":return e.stop()}}),e)})))()},changeStatus:function(t){0===this.list.length?this.status="暂无数据":this.page>=Math.ceil(t.count/this.limit)?this.status="没有更多":this.status="请求更多"},positionDetail:function(t){this.$mRouter.push({route:this.$mRoutesConfig.positionDetail,query:{id:t.id}})},detail:function(t){this.$mRouter.push({route:this.$mRoutesConfig.resumeDetail,query:{id:t.id}})},recentClick:function(t){this.searchValue=t,this.search()},search:function(){var e=this;this.searchValue?(t.showLoading(),this.$http.get("v1/course/search",{params:{keyword:this.searchValue}}).then((function(n){t.hideLoading(),console.log(n),0==n.data.code&&(e.list=n.data.data)}))):t.showToast({icon:"none",title:"请输入要搜索的关键字"})},clearRecent:function(){var e=this;t.showModal({title:"提示",content:"确定清空搜索记录吗",success:function(n){n.confirm&&(t.removeStorageSync("historySearch"),e.searchRecent=!1,e.currentIndex=0)}})},historySearch:function(){var t=this.$db.get("historySearch");console.log(t),null!=t&&""!=t||(t=[]);for(var e=0;e<t.length;e++)t[e]==this.searchValue&&(console.log("删除该元素"+t[e]),t.splice(e,1));10==t.length&&t.splice(9,1),t.unshift(this.searchValue),this.$db.set("historySearch",t)},popular:function(t){console.log(t),this.searchValue=t.text},cancelSearch:function(){this.popularShow=!1,this.searchValue="",this.searchRecent=!0},searchInput:function(t){this.searchValue=t.detail.value,this.searchRecentList=this.$db.get("historySearch"),this.popularShow=!1,""==this.searchValue&&null!=this.searchRecentList&&""!=this.searchRecentList&&(this.searchRecent=!0,this.currentIndex=0)},navToDetailPage:function(e){var n=e.id;t.navigateTo({url:"/pages/course/course?id=".concat(n)})},confirm:function(){this.search()}}};e.default=l}).call(this,n("df3c")["default"])},5541:function(t,e,n){},6563:function(t,e,n){"use strict";n.r(e);var i=n("6970"),a=n("d008");for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);n("b6cb");var c=n("828b"),o=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"73841ea4",null,!1,i["a"],void 0);e["default"]=o.exports},6970:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return i}));var i={umDropdown:function(){return n.e("uni_modules/um-dropdown/components/um-dropdown/um-dropdown").then(n.bind(null,"203e"))},mEmptyData:function(){return n.e("components/m-empty-data/m-empty-data").then(n.bind(null,"cd92"))}},a=function(){var t=this.$createElement,e=(this._self._c,0==this.list.length&&0==this.searchRecent),n=this.list.length>0&&0==this.searchRecent;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},s=[]},"91ab":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("5788");i(n("3240"));var a=i(n("6563"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},b6cb:function(t,e,n){"use strict";var i=n("5541"),a=n.n(i);a.a},d008:function(t,e,n){"use strict";n.r(e);var i=n("1e4e"),a=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);e["default"]=a.a}},[["91ab","common/runtime","common/vendor"]]]);