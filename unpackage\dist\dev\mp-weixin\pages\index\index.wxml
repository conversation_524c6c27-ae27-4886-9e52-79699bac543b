<gui-page class="vue-ref" vue-id="8dd740cc-1" fullPage="{{true}}" isLoading="{{pageLoading}}" data-ref="guiPage" bind:__l="__l" vue-slots="{{['gBody']}}"><view class="gui-flex1" style="background-color:#F8F8F8;" slot="gBody"><view style="padding:10rpx 30rpx;"><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="search-box" bindtap="__e"><text class="grace-icons icon-search" style="margin-right:20rpx;"></text>搜索关键词</view></view><swiper class="swiper-container-small" bind:change="handleChange" previousMargin="6rpx"><swiper-item class="swiper-item" data-index="index"><block wx:for="{{list1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="srv-item-small" bind:tap="handleTap" data-event-opts="{{[['tap',[['navTo',['$0','link'],[[['list1','',index]]]]]]]}}" bindtap="__e"><text class="srv-item-title nowrap">{{item.title}}</text></view></block></swiper-item></swiper><view class="carousel-section"><swiper class="carousel" circular="{{true}}" autoplay="true" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{carouselList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['navToWeb',['$0','swiper'],[[['carouselList','',index]]]]]]]}}" class="carousel-item" bindtap="__e"><image src="{{item.thumb}}"></image></swiper-item></block></swiper><view class="swiper-dots"><text class="num">{{swiperCurrent+1}}</text><text class="sign">/</text><text class="num">{{swiperLength}}</text></view></view><scroll-x vue-id="{{('8dd740cc-2')+','+('8dd740cc-1')}}" list="{{iconList1}}" nums="{{5}}" col="{{2}}" bind:__l="__l"></scroll-x><view class="hot-service" style="margin:auto;"><image class="bg-img" ariaHidden="true" lazyLoad="true" mode="aspectFill" src="{{moreBgUrl}}"></image><view class="hot-service-title" ariaRole="heading"><view class="hot-service-title-h3">更多课程</view></view></view><scroll-x vue-id="{{('8dd740cc-3')+','+('8dd740cc-1')}}" list="{{iconList2}}" nums="{{5}}" col="{{3}}" size="{{60}}" height="{{130}}" bind:__l="__l"></scroll-x><view style="padding:20rpx 0;"><view class="everyone-doing bg hbclass"><view class="service-main"><view class="listbox service-list"><view class="titlebox"><view class="h2title viewtitle">推荐阅读</view></view><view class="content service-hot-list"><view class="list-box"><block wx:for="{{courseList}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToWeb',['$0'],[[['courseList','',index1]]]]]]]}}" class="item-box lp-flex-column" bindtap="__e"><view class="top-box lp-flex"><view class="cover-box lp-flex-center"><image class="cover" src="{{item1.thumb}}"></image></view><view class="info-box lp-flex-column"><view class="title">{{item1.title}}</view><view class="end"><text style="text-align:right;float:right;">更多</text></view></view></view></view></block></view></view></view></view></view></view><u-popup bind:input="__e" vue-id="{{('8dd740cc-4')+','+('8dd740cc-1')}}" width="80%" mode="center" border-radius="10" show="{{tip}}" safeAreaInsetBottom="{{false}}" value="{{tip}}" data-event-opts="{{[['^input',[['__set_model',['','tip','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="view-pup2"><view class="view-pup2-box"></view><view class="view-pup2-warn"><view class="view-pup2-warn-title">温馨提示</view><view class="view-pup2-warn-text" style="text-align:left;">{{moduleContent}}</view></view><view class="view-pup2-button"><view data-event-opts="{{[['tap',[['confirm']]]]}}" class="view-pup2-button-list view-pup2-button-list1" bindtap="__e">确定</view></view></view></u-popup></view></gui-page>