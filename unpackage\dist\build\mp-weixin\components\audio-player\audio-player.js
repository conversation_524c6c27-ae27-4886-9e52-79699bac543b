(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/audio-player/audio-player"],{"0b96":function(t,e,a){},8152:function(t,e,a){"use strict";var r=a("0b96"),n=a.n(r);n.a},d16d:function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={props:{color:{type:String,default:"#333"},mini:{type:Boolean,default:!1},nobroud:{type:Boolean,default:!1},autoPlay:{type:Boolean,default:!1},audio:{type:Object,default:{title:"我们都一样",singer:"张杰",epname:"杰哥精选",coverImgUrl:"https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/images/jie02.jpg?sign=00e5e68d81145037000a162e2220736a&t=1556345760",src:"https://7465-test01-632ffe-1258717418.tcb.qcloud.la/personal/player/song/%E6%88%91%E4%BB%AC%E9%83%BD%E4%B8%80%E6%A0%B7%20-%20%E5%BC%A0%E6%9D%B0.mp3?sign=008d62b6bea06a8a6814b5f284fac0ac&t=1556345730"}}},data:function(){return{playStatus:2,player:null,playTime:"00:00",timer:null,audioLength:1}},beforeDestroy:function(){this.destroy()},created:function(){var e=this;this.player=t.createInnerAudioContext(),this.player.onTimeUpdate((function(){try{if(1!=e.playStatus)return;e.audioLength=e.player.duration;var t=e.player.currentTime/e.audioLength;t=Math.round(100*t);var a=e.$refs.graceSingleSlider;a&&a.setProgress(t),e.playTime=e.timeFormat(e.player.currentTime)}catch(r){}})),this.player.onPlay((function(){e.playStatus=1,e.audioLength=e.player.duration})),this.player.onPause((function(){e.playStatus=2})),this.player.onEnded((function(){e.playStatus=2})),this.load(this.audio,this.autoPlay),this.player.currentTime},methods:{load:function(t,e){console.log("audio.src"+t.src),this.player.singer=t.singer,this.player.coverImgUrl=t.coverImgUrl,this.player.src=t.src,e&&this.player.play()},progressChange:function(t){var e=this;null!=this.timer&&clearTimeout(this.timer),this.player.pause();var a=this.audioLength*t/100;a=Math.round(a),this.playTime=this.timeFormat(a),this.timer=setTimeout((function(){e.player.seek(a),e.player.play()}),800)},timeFormat:function(t){if(t=Math.round(t),t<60)return t<10?"00:0"+t:"00:"+t;var e=t%60;t-=e;var a=t/60;return a<10&&(a="0"+a),e<10&&(e="0"+e),a+":"+e},pause:function(){this.player&&this.player.pause()},play:function(){this.player&&this.player.play()},destroy:function(){this.player&&this.player.destroy()}}};e.default=a}).call(this,a("df3c")["default"])},e8d8:function(t,e,a){"use strict";a.r(e);var r=a("efa2"),n=a("eb39");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("8152");var o=a("828b"),u=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"d89cb552",null,!1,r["a"],void 0);e["default"]=u.exports},eb39:function(t,e,a){"use strict";a.r(e);var r=a("d16d"),n=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=n.a},efa2:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return r}));var r={guiSingleSlider:function(){return a.e("GraceUI5/components/gui-single-slider").then(a.bind(null,"a88a"))}},n=function(){var t=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/audio-player/audio-player-create-component',
    {
        'components/audio-player/audio-player-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e8d8"))
        })
    },
    [['components/audio-player/audio-player-create-component']]
]);
