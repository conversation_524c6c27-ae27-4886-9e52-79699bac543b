<view class="lp-record"><view data-event-opts="{{[['tap',[['showPicker',['$event']]]]]}}" bindtap="__e"><slot></slot></view><view class="conbox record"><block wx:if="{{source}}"><view class="panel source-player"><view class="head">原音播放</view><zaudio vue-id="c1eaefe8-1" theme="{{themelist[0].val}}" autoplay="{{true}}" continue="{{true}}" bind:__l="__l"></zaudio></view></block><view class="panel"><view class="time">{{''+showRecordTime+''}}</view><view class="c999">{{'最短'+minTime+"秒，最长"+maxTime+'秒'}}</view><view data-event-opts="{{[['touchmove',[['onMoveHandler',['$event']]]]]}}" class="record-box" catchtouchmove="__e"><block wx:if="{{isRecording}}"><view class="btn empty" style="background:unset;"></view></block><block wx:else><block wx:if="{{playPath&&playing==1}}"><view data-event-opts="{{[['touchstart',[['stopVoice',['$event']]]]]}}" class="btn stop" catchtouchstart="__e"><text class="gui-icons"></text></view></block><block wx:if="{{playPath&&playing==0}}"><view data-event-opts="{{[['touchstart',[['playVoice',['$event']]]]]}}" class="btn play" catchtouchstart="__e"><text class="gui-icons" style="margin-left:8rpx;"></text></view></block></block><view data-event-opts="{{[['touchstart',[['onTouchStartHandler',['$event']]]],['longpress',[['onLongpressHandler',['$event']]]],['touchend',[['onTouchEndHandler',['$event']]]]]}}" class="btn recording" bindtouchstart="__e" bindlongpress="__e" bindtouchend="__e"><text class="{{['ws-icon',(isRecording&&!isRecordPaused)?'flash':'']}}" style="font-size:70rpx;">{{''+(isRecording&&!isRecordPaused?'':'')+''}}</text></view><block wx:if="{{isRecording}}"><view data-event-opts="{{[['touchstart',[['onRecordEndHandler',['$event']]]]]}}" class="btn confirm" catchtouchstart="__e"><text class="ws-icon"></text></view></block><block wx:if="{{!isRecording&&playPath}}"><view data-event-opts="{{[['touchstart',[['onConfirmHandler',['$event']]]]]}}" class="btn confirm" catchtouchstart="__e"><text class="gui-icons"></text></view></block></view><view class="c666 fz32 domess">{{isRecording?isRecordPaused?'已暂停':'录音中':playPath?'录音完成':'点击开始录音'}}</view></view></view></view>