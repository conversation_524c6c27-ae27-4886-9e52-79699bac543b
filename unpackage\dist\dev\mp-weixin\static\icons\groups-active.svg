<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 小组图标 - 选中状态 -->
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 - 渐变色 -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient)"/>
  
  <!-- 三个人的图标 - 白色 -->
  <!-- 左侧人物 -->
  <circle cx="22" cy="24" r="5" fill="white"/>
  <path d="M15 42c0-3.9 3.1-7 7-7s7 3.1 7 7H15z" fill="white"/>
  
  <!-- 右侧人物 -->
  <circle cx="42" cy="24" r="5" fill="white"/>
  <path d="M35 42c0-3.9 3.1-7 7-7s7 3.1 7 7H35z" fill="white"/>
  
  <!-- 中间人物（稍大一些，表示领导者） -->
  <circle cx="32" cy="20" r="6" fill="white"/>
  <path d="M24 42c0-4.4 3.6-8 8-8s8 3.6 8 8H24z" fill="white"/>
  
  <!-- 小组标识 - 高亮 -->
  <circle cx="48" cy="16" r="8" fill="white" opacity="0.9"/>
  <text x="48" y="20" text-anchor="middle" font-family="Arial" font-size="10" fill="#667eea" font-weight="bold">组</text>
  
  <!-- 添加一些装饰性的点 -->
  <circle cx="16" cy="16" r="2" fill="white" opacity="0.6"/>
  <circle cx="48" cy="48" r="2" fill="white" opacity="0.6"/>
  <circle cx="16" cy="48" r="2" fill="white" opacity="0.6"/>
</svg>
