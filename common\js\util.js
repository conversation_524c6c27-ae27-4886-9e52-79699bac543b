export default {
	html: {
		/**
		 * 获取富文本纯文本
		 * @param {string} value = [value] 
		 **/
		getPureContent: function(inputText) {
			// value = value.replace(/(\n)/g, "");
			// value = value.replace(/(\t)/g, "");
			// value = value.replace(/(\r)/g, "");
			// value = value.replace(/<\/?[^>]*>/g, "");
			// value = value.replace(/\s*/g, "");
			// value = value.replace(/&([^&;]+);/g, function(matchStr, b) {
			//         var entity = {
			//             quot: '"',
			//             lt: '<',
			//             gt: '>',
			//             apos: "'",
			//             amp: '&',
			//             ldquo: '“',
			//             rdquo: '”',
			// 			nbsp:''
			//         };
			//         var r = entity[b];
			//         return typeof r === 'string' ? r : matchStr;
			// });
			// return value;
			var returnText = "" + inputText;
			    returnText = returnText.replace(/<\/div>/ig, '\r\n');
			    returnText = returnText.replace(/<\/li>/ig, '\r\n');
			    returnText = returnText.replace(/<li>/ig, '  *  ');
			    returnText = returnText.replace(/<\/ul>/ig, '\r\n');
			    //-- remove BR tags and replace them with line break
			    returnText = returnText.replace(/<br\s*[\/]?>/gi, "\r\n");
			 
			    //-- remove P and A tags but preserve what's inside of them
			    returnText=returnText.replace(/<p.*?>/gi, "\r\n");
			    returnText=returnText.replace(/<a.*href="(.*?)".*>(.*?)<\/a>/gi, " $2 ($1)");
			 
			    //-- remove all inside SCRIPT and STYLE tags
			    returnText=returnText.replace(/<script.*>[\w\W]{1,}(.*?)[\w\W]{1,}<\/script>/gi, "");
			    returnText=returnText.replace(/<style.*>[\w\W]{1,}(.*?)[\w\W]{1,}<\/style>/gi, "");
			    //-- remove all else
			    returnText=returnText.replace(/<(?:.|\s)*?>/g, "");
			 
			    //-- get rid of more than 2 multiple line breaks:
			    returnText=returnText.replace(/(?:(?:\r\n|\r|\n)\s*){2,}/gim, "\r\n\r\n");
			 
			    //-- get rid of more than 2 spaces:
			    returnText = returnText.replace(/ +(?= )/g,'');
			 
			    //-- get rid of html-encoded characters:
			    returnText=returnText.replace(/&nbsp;/gi," ");
			    returnText=returnText.replace(/&amp;/gi,"&");
			    returnText=returnText.replace(/&quot;/gi,'"');
			    returnText=returnText.replace(/&lt;/gi,'<');
			    returnText=returnText.replace(/&gt;/gi,'>');
				returnText=returnText.replace(/&ldquo;/gi,'"');
				returnText=returnText.replace(/&rsquo;/gi,"'");
				returnText=returnText.replace(/&rdquo;/gi,'"');
			   return returnText;

		}
	}
}
