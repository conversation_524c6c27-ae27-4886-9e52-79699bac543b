<block wx:if="{{isLoading}}"><view data-event-opts="{{[['tap',[['stopfun',['$event']]]],['touchmove',[['stopfun',['$event']]]]]}}" class="gui-page-loading gui-flex gui-nowrap gui-align-items-center gui-justify-content-center gui-page-loading-bg data-v-6a6af1bb" catchtap="__e" catchtouchmove="__e"><view class="gui-page-loading-point gui-flex gui-rows gui-justify-content-center data-v-6a6af1bb"><view class="gui-page-loading-points animate1 gui-page-loading-color data-v-6a6af1bb"></view><view class="gui-page-loading-points animate2 gui-page-loading-color data-v-6a6af1bb"></view><view class="gui-page-loading-points animate3 gui-page-loading-color data-v-6a6af1bb"></view></view></view></block>