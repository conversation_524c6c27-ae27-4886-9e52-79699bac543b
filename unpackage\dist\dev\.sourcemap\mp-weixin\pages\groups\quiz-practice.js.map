{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?1272", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?28bb", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?c487", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?c647", "uni-app:///pages/groups/quiz-practice.vue", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?59a2", "webpack:///D:/日语云课/pages/groups/quiz-practice.vue?b446"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageLoading", "groupId", "practiceId", "currentPractice", "currentQuestionIndex", "<PERSON><PERSON><PERSON><PERSON>", "inputAnswer", "showResult", "practiceFinished", "answers", "startTime", "timeLimit", "timeLeft", "timer", "practiceList", "id", "title", "description", "type", "questionCount", "difficulty", "bestScore", "computed", "currentQuestion", "progressPercent", "isLastQuestion", "finalScore", "correctCount", "totalQuestions", "correctRate", "practiceTime", "averageTime", "onLoad", "onUnload", "clearInterval", "methods", "startPractice", "practice", "questions", "generateQuestions", "createSampleQuestion", "points", "baseQuestion", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "selectAnswer", "checkAnswer", "userAnswer", "uni", "icon", "nextQuestion", "isCorrect", "questionId", "prevQuestion", "finishPractice", "restartPractice", "backToList", "reviewAnswers", "startTimer", "formatTime", "getIconClass", "getTypeText", "getDifficultyText", "getGrade", "getScoreClass", "loadSpecificPractice"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACqC;;;AAGjG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA2lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4K/mB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAEAC,eACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA,uDACAC;QACAC;MAAA,EACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;MAEA;QACAD;MACA;MAEA;IACA;IAEA;IACAE;MACA;QACAzB;QACA0B;MACA;MAEA;QACA;UACA,uCACAC;YACAxB;YACAyB;YACAC;YACAC;YACAC;UAAA;QAEA;UACA,uCACAJ;YACAxB;YACAyB;YACAC;YACAC;YACAC;UAAA;QAEA;UACA,uCACAJ;YACAxB;YACAyB;YACAC;YACAC;YACAC;UAAA;QAEA;UACA,uCACAJ;YACAxB;YACAyB;YACAE;YACAC;UAAA;MACA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;MAEA;QACAC;UACAlC;UACAmC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAH;MACA;MAEA;MACA;QACAI;MACA;QACAA;MACA;MAEA;QACAC;QACAL;QACAJ;QACAQ;QACAZ;MACA;MAEA;QACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAc;MACA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;UACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;MAEA;MACA;QACAP;MACA;MAEA;MACA;QACAI;MACA;QACAA;MACA;MAEA;QACAC;QACAL;QACAJ;QACAQ;QACAZ;MACA;MAEA;MACA;QACAP;MACA;IACA;IAEA;IACAuB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACAT;QACAlC;QACAmC;MACA;IACA;IAEA;IACAS;MAAA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxkBA;AAAA;AAAA;AAAA;AAAg4B,CAAgB,o4BAAG,EAAC,C;;;;;;;;;;;ACAp5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/groups/quiz-practice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/groups/quiz-practice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./quiz-practice.vue?vue&type=template&id=5012569e&scoped=true&\"\nvar renderjs\nimport script from \"./quiz-practice.vue?vue&type=script&lang=js&\"\nexport * from \"./quiz-practice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5012569e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/groups/quiz-practice.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=template&id=5012569e&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.currentPractice\n    ? _vm.__map(_vm.practiceList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.getIconClass(item.type)\n        var m1 = _vm.getTypeText(item.type)\n        var m2 = _vm.getDifficultyText(item.difficulty)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n        }\n      })\n    : null\n  var g0 =\n    !!_vm.currentPractice && !_vm.practiceFinished\n      ? _vm.currentPractice.questions.length\n      : null\n  var m3 =\n    !!_vm.currentPractice && !_vm.practiceFinished && _vm.timeLimit > 0\n      ? _vm.formatTime(_vm.timeLeft)\n      : null\n  var m4 =\n    !!_vm.currentPractice && !_vm.practiceFinished\n      ? _vm.getTypeText(_vm.currentQuestion.type)\n      : null\n  var l1 =\n    !!_vm.currentPractice &&\n    !_vm.practiceFinished &&\n    _vm.currentQuestion.type === \"choice\"\n      ? _vm.__map(_vm.currentQuestion.options, function (option, index) {\n          var $orig = _vm.__get_orig(option)\n          var g1 = String.fromCharCode(65 + index)\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var m5 = _vm.practiceFinished ? _vm.getScoreClass(_vm.finalScore) : null\n  var m6 = _vm.practiceFinished ? _vm.getGrade(_vm.finalScore) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        m3: m3,\n        m4: m4,\n        l1: l1,\n        m5: m5,\n        m6: m6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\n\t\t\t<!-- 练习列表 -->\n\t\t\t<view class=\"practice-list\" v-if=\"!currentPractice\">\n\t\t\t\t<view class=\"list-header\">\n\t\t\t\t\t<view class=\"header-title\">答题练习</view>\n\t\t\t\t\t<view class=\"header-desc\">选择一个练习开始训练</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view \n\t\t\t\t\tclass=\"practice-item\" \n\t\t\t\t\tv-for=\"(item, index) in practiceList\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"startPractice(item)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"item-icon\" :class=\"[item.type]\">\n\t\t\t\t\t\t<text class=\"iconfont\" :class=\"[getIconClass(item.type)]\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t<view class=\"item-title\">{{item.title}}</view>\n\t\t\t\t\t\t<view class=\"item-desc\">{{item.description}}</view>\n\t\t\t\t\t\t<view class=\"item-meta\">\n\t\t\t\t\t\t\t<text class=\"meta-count\">{{item.questionCount}}题</text>\n\t\t\t\t\t\t\t<text class=\"meta-type\">{{getTypeText(item.type)}}</text>\n\t\t\t\t\t\t\t<text class=\"meta-difficulty\" :class=\"[item.difficulty]\">{{getDifficultyText(item.difficulty)}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-status\">\n\t\t\t\t\t\t<text class=\"status-score\" v-if=\"item.bestScore !== null\">{{item.bestScore}}分</text>\n\t\t\t\t\t\t<text class=\"status-new\" v-else>NEW</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 练习界面 -->\n\t\t\t<view class=\"practice-content\" v-else-if=\"!practiceFinished\">\n\t\t\t\t<!-- 进度条 -->\n\t\t\t\t<view class=\"progress-header\">\n\t\t\t\t\t<view class=\"progress-info\">\n\t\t\t\t\t\t<text>{{currentQuestionIndex + 1}} / {{currentPractice.questions.length}}</text>\n\t\t\t\t\t\t<text class=\"time-left\" v-if=\"timeLimit > 0\">剩余时间: {{formatTime(timeLeft)}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: progressPercent + '%' }\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 题目内容 -->\n\t\t\t\t<view class=\"question-content\">\n\t\t\t\t\t<view class=\"question-header\">\n\t\t\t\t\t\t<view class=\"question-type\">{{getTypeText(currentQuestion.type)}}</view>\n\t\t\t\t\t\t<view class=\"question-points\">{{currentQuestion.points}}分</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"question-title\">{{currentQuestion.question}}</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 图片题目 -->\n\t\t\t\t\t<view class=\"question-image\" v-if=\"currentQuestion.image\">\n\t\t\t\t\t\t<image :src=\"currentQuestion.image\" mode=\"aspectFit\"></image>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 选择题选项 -->\n\t\t\t\t\t<view class=\"question-options\" v-if=\"currentQuestion.type === 'choice'\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\tv-for=\"(option, index) in currentQuestion.options\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\t:class=\"{ \n\t\t\t\t\t\t\t\tselected: selectedAnswer === index,\n\t\t\t\t\t\t\t\tcorrect: showResult && index === currentQuestion.correctAnswer,\n\t\t\t\t\t\t\t\twrong: showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\t@click=\"selectAnswer(index)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<view class=\"option-label\">{{String.fromCharCode(65 + index)}}</view>\n\t\t\t\t\t\t\t<view class=\"option-text\">{{option}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 填空题 -->\n\t\t\t\t\t<view class=\"question-input\" v-else-if=\"currentQuestion.type === 'fill'\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tv-model=\"inputAnswer\" \n\t\t\t\t\t\t\t:placeholder=\"currentQuestion.placeholder || '请输入答案'\"\n\t\t\t\t\t\t\tclass=\"input-field\"\n\t\t\t\t\t\t\t:disabled=\"showResult\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 判断题 -->\n\t\t\t\t\t<view class=\"question-judge\" v-else-if=\"currentQuestion.type === 'judge'\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"judge-option\" \n\t\t\t\t\t\t\t:class=\"{ selected: selectedAnswer === true }\"\n\t\t\t\t\t\t\t@click=\"selectAnswer(true)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"judge-icon\">○</text>\n\t\t\t\t\t\t\t<text class=\"judge-text\">正确</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"judge-option\" \n\t\t\t\t\t\t\t:class=\"{ selected: selectedAnswer === false }\"\n\t\t\t\t\t\t\t@click=\"selectAnswer(false)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"judge-icon\">×</text>\n\t\t\t\t\t\t\t<text class=\"judge-text\">错误</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 解析 -->\n\t\t\t\t\t<view class=\"question-explanation\" v-if=\"showResult && currentQuestion.explanation\">\n\t\t\t\t\t\t<view class=\"explanation-title\">解析</view>\n\t\t\t\t\t\t<view class=\"explanation-content\">{{currentQuestion.explanation}}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t<button class=\"btn-secondary\" @click=\"prevQuestion\" :disabled=\"currentQuestionIndex === 0\">\n\t\t\t\t\t\t上一题\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn-check\" @click=\"checkAnswer\" v-if=\"!showResult\">\n\t\t\t\t\t\t检查答案\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"nextQuestion\" v-if=\"showResult && !isLastQuestion\">\n\t\t\t\t\t\t下一题\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"finishPractice\" v-if=\"showResult && isLastQuestion\">\n\t\t\t\t\t\t完成练习\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 结果页面 -->\n\t\t\t<view class=\"result-page\" v-if=\"practiceFinished\">\n\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t<view class=\"result-score\" :class=\"[getScoreClass(finalScore)]\">{{finalScore}}</view>\n\t\t\t\t\t<view class=\"result-text\">分</view>\n\t\t\t\t\t<view class=\"result-grade\">{{getGrade(finalScore)}}</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"result-details\">\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">正确题数</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{correctCount}} / {{totalQuestions}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">正确率</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{correctRate}}%</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">用时</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{practiceTime}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-item\">\n\t\t\t\t\t\t<text class=\"detail-label\">平均每题</text>\n\t\t\t\t\t\t<text class=\"detail-value\">{{averageTime}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"result-actions\">\n\t\t\t\t\t<button class=\"btn-secondary\" @click=\"reviewAnswers\">查看解析</button>\n\t\t\t\t\t<button class=\"btn-primary\" @click=\"restartPractice\">重新练习</button>\n\t\t\t\t\t<button class=\"btn-secondary\" @click=\"backToList\">返回列表</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageLoading: false,\n\t\t\tgroupId: '',\n\t\t\tpracticeId: '',\n\t\t\tcurrentPractice: null,\n\t\t\tcurrentQuestionIndex: 0,\n\t\t\tselectedAnswer: null,\n\t\t\tinputAnswer: '',\n\t\t\tshowResult: false,\n\t\t\tpracticeFinished: false,\n\t\t\tanswers: [],\n\t\t\tstartTime: null,\n\t\t\ttimeLimit: 0, // 时间限制（秒）\n\t\t\ttimeLeft: 0,\n\t\t\ttimer: null,\n\t\t\t\n\t\t\tpracticeList: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\ttitle: '语法基础练习',\n\t\t\t\t\tdescription: '助词、动词变位等基础语法',\n\t\t\t\t\ttype: 'grammar',\n\t\t\t\t\tquestionCount: 10,\n\t\t\t\t\tdifficulty: 'easy',\n\t\t\t\t\tbestScore: 92\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\ttitle: '词汇记忆练习',\n\t\t\t\t\tdescription: '常用词汇的读音和意思',\n\t\t\t\t\ttype: 'vocabulary',\n\t\t\t\t\tquestionCount: 15,\n\t\t\t\t\tdifficulty: 'medium',\n\t\t\t\t\tbestScore: null\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\ttitle: '阅读理解练习',\n\t\t\t\t\tdescription: '短文阅读理解能力训练',\n\t\t\t\t\ttype: 'reading',\n\t\t\t\t\tquestionCount: 8,\n\t\t\t\t\tdifficulty: 'hard',\n\t\t\t\t\tbestScore: 78\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\ttitle: '综合能力测试',\n\t\t\t\t\tdescription: '语法、词汇、阅读综合测试',\n\t\t\t\t\ttype: 'comprehensive',\n\t\t\t\t\tquestionCount: 20,\n\t\t\t\t\tdifficulty: 'hard',\n\t\t\t\t\tbestScore: 85\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\tcurrentQuestion() {\n\t\t\tif (!this.currentPractice || !this.currentPractice.questions) return null;\n\t\t\treturn this.currentPractice.questions[this.currentQuestionIndex];\n\t\t},\n\t\tprogressPercent() {\n\t\t\tif (!this.currentPractice) return 0;\n\t\t\treturn ((this.currentQuestionIndex + 1) / this.currentPractice.questions.length) * 100;\n\t\t},\n\t\tisLastQuestion() {\n\t\t\tif (!this.currentPractice) return false;\n\t\t\treturn this.currentQuestionIndex === this.currentPractice.questions.length - 1;\n\t\t},\n\t\tfinalScore() {\n\t\t\tif (this.answers.length === 0) return 0;\n\t\t\tconst totalPoints = this.answers.reduce((sum, answer) => sum + answer.points, 0);\n\t\t\tconst earnedPoints = this.answers.reduce((sum, answer) => sum + (answer.isCorrect ? answer.points : 0), 0);\n\t\t\treturn Math.round((earnedPoints / totalPoints) * 100);\n\t\t},\n\t\tcorrectCount() {\n\t\t\treturn this.answers.filter(answer => answer.isCorrect).length;\n\t\t},\n\t\ttotalQuestions() {\n\t\t\treturn this.answers.length;\n\t\t},\n\t\tcorrectRate() {\n\t\t\tif (this.totalQuestions === 0) return 0;\n\t\t\treturn Math.round((this.correctCount / this.totalQuestions) * 100);\n\t\t},\n\t\tpracticeTime() {\n\t\t\tif (!this.startTime) return '0分0秒';\n\t\t\tconst elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n\t\t\treturn this.formatTime(elapsed);\n\t\t},\n\t\taverageTime() {\n\t\t\tif (!this.startTime || this.totalQuestions === 0) return '0秒';\n\t\t\tconst elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n\t\t\tconst average = Math.round(elapsed / this.totalQuestions);\n\t\t\treturn `${average}秒`;\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.groupId) {\n\t\t\tthis.groupId = options.groupId;\n\t\t}\n\t\tif (options.practiceId) {\n\t\t\tthis.practiceId = options.practiceId;\n\t\t\tthis.loadSpecificPractice(options.practiceId);\n\t\t}\n\t},\n\tonUnload() {\n\t\tif (this.timer) {\n\t\t\tclearInterval(this.timer);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 开始练习\n\t\tstartPractice(practice) {\n\t\t\tthis.currentPractice = {\n\t\t\t\t...practice,\n\t\t\t\tquestions: this.generateQuestions(practice.id, practice.type)\n\t\t\t};\n\t\t\tthis.currentQuestionIndex = 0;\n\t\t\tthis.selectedAnswer = null;\n\t\t\tthis.inputAnswer = '';\n\t\t\tthis.showResult = false;\n\t\t\tthis.practiceFinished = false;\n\t\t\tthis.answers = [];\n\t\t\tthis.startTime = Date.now();\n\t\t\t\n\t\t\t// 设置时间限制\n\t\t\tif (practice.timeLimit) {\n\t\t\t\tthis.timeLimit = practice.timeLimit;\n\t\t\t\tthis.timeLeft = practice.timeLimit;\n\t\t\t\tthis.startTimer();\n\t\t\t}\n\t\t},\n\n\t\t// 生成题目\n\t\tgenerateQuestions(practiceId, type) {\n\t\t\t// 这里应该从API获取真实题目\n\t\t\tconst questions = [];\n\t\t\tconst questionCount = this.practiceList.find(p => p.id === practiceId)?.questionCount || 5;\n\t\t\t\n\t\t\tfor (let i = 0; i < questionCount; i++) {\n\t\t\t\tquestions.push(this.createSampleQuestion(type, i + 1));\n\t\t\t}\n\t\t\t\n\t\t\treturn questions;\n\t\t},\n\n\t\t// 创建示例题目\n\t\tcreateSampleQuestion(type, index) {\n\t\t\tconst baseQuestion = {\n\t\t\t\tid: index,\n\t\t\t\tpoints: 5\n\t\t\t};\n\n\t\t\tswitch (type) {\n\t\t\t\tcase 'grammar':\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...baseQuestion,\n\t\t\t\t\t\ttype: 'choice',\n\t\t\t\t\t\tquestion: `语法题${index}：下列哪个助词使用正确？`,\n\t\t\t\t\t\toptions: ['私は学校に行きます', '私は学校で行きます', '私は学校を行きます', '私は学校が行きます'],\n\t\t\t\t\t\tcorrectAnswer: 0,\n\t\t\t\t\t\texplanation: '「に」表示移动的目的地，所以用「学校に行きます」。'\n\t\t\t\t\t};\n\t\t\t\tcase 'vocabulary':\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...baseQuestion,\n\t\t\t\t\t\ttype: 'choice',\n\t\t\t\t\t\tquestion: `词汇题${index}：「ありがとう」的意思是？`,\n\t\t\t\t\t\toptions: ['对不起', '谢谢', '再见', '你好'],\n\t\t\t\t\t\tcorrectAnswer: 1,\n\t\t\t\t\t\texplanation: '「ありがとう」是表示感谢的常用语。'\n\t\t\t\t\t};\n\t\t\t\tcase 'reading':\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...baseQuestion,\n\t\t\t\t\t\ttype: 'choice',\n\t\t\t\t\t\tquestion: `阅读题${index}：根据短文内容，作者的观点是什么？`,\n\t\t\t\t\t\toptions: ['支持', '反对', '中立', '不明确'],\n\t\t\t\t\t\tcorrectAnswer: 0,\n\t\t\t\t\t\texplanation: '从文中的关键词可以看出作者的支持态度。'\n\t\t\t\t\t};\n\t\t\t\tdefault:\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...baseQuestion,\n\t\t\t\t\t\ttype: 'judge',\n\t\t\t\t\t\tquestion: `判断题${index}：日语中「です」是敬语形式。`,\n\t\t\t\t\t\tcorrectAnswer: true,\n\t\t\t\t\t\texplanation: '「です」确实是日语中的敬语形式，用于正式场合。'\n\t\t\t\t\t};\n\t\t\t}\n\t\t},\n\n\t\t// 选择答案\n\t\tselectAnswer(answer) {\n\t\t\tif (this.showResult) return;\n\t\t\tthis.selectedAnswer = answer;\n\t\t},\n\n\t\t// 检查答案\n\t\tcheckAnswer() {\n\t\t\tlet userAnswer = this.selectedAnswer;\n\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\tuserAnswer = this.inputAnswer.trim();\n\t\t\t}\n\n\t\t\tif (userAnswer === null || userAnswer === '') {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择或输入答案',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.showResult = true;\n\t\t},\n\n\t\t// 下一题\n\t\tnextQuestion() {\n\t\t\t// 记录答案\n\t\t\tlet userAnswer = this.selectedAnswer;\n\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\tuserAnswer = this.inputAnswer.trim();\n\t\t\t}\n\n\t\t\tlet isCorrect = false;\n\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\tisCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();\n\t\t\t} else {\n\t\t\t\tisCorrect = userAnswer === this.currentQuestion.correctAnswer;\n\t\t\t}\n\n\t\t\tthis.answers.push({\n\t\t\t\tquestionId: this.currentQuestion.id,\n\t\t\t\tuserAnswer: userAnswer,\n\t\t\t\tcorrectAnswer: this.currentQuestion.correctAnswer,\n\t\t\t\tisCorrect: isCorrect,\n\t\t\t\tpoints: this.currentQuestion.points\n\t\t\t});\n\n\t\t\tif (this.isLastQuestion) {\n\t\t\t\tthis.finishPractice();\n\t\t\t} else {\n\t\t\t\tthis.currentQuestionIndex++;\n\t\t\t\tthis.selectedAnswer = null;\n\t\t\t\tthis.inputAnswer = '';\n\t\t\t\tthis.showResult = false;\n\t\t\t}\n\t\t},\n\n\t\t// 上一题\n\t\tprevQuestion() {\n\t\t\tif (this.currentQuestionIndex > 0) {\n\t\t\t\tthis.currentQuestionIndex--;\n\t\t\t\t// 恢复之前的答案\n\t\t\t\tconst prevAnswer = this.answers[this.currentQuestionIndex];\n\t\t\t\tif (prevAnswer) {\n\t\t\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\t\t\tthis.inputAnswer = prevAnswer.userAnswer;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedAnswer = prevAnswer.userAnswer;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedAnswer = null;\n\t\t\t\t\tthis.inputAnswer = '';\n\t\t\t\t}\n\t\t\t\tthis.showResult = false;\n\t\t\t}\n\t\t},\n\n\t\t// 完成练习\n\t\tfinishPractice() {\n\t\t\t// 记录最后一题答案\n\t\t\tif (!this.showResult) {\n\t\t\t\tthis.checkAnswer();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet userAnswer = this.selectedAnswer;\n\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\tuserAnswer = this.inputAnswer.trim();\n\t\t\t}\n\n\t\t\tlet isCorrect = false;\n\t\t\tif (this.currentQuestion.type === 'fill') {\n\t\t\t\tisCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();\n\t\t\t} else {\n\t\t\t\tisCorrect = userAnswer === this.currentQuestion.correctAnswer;\n\t\t\t}\n\n\t\t\tthis.answers.push({\n\t\t\t\tquestionId: this.currentQuestion.id,\n\t\t\t\tuserAnswer: userAnswer,\n\t\t\t\tcorrectAnswer: this.currentQuestion.correctAnswer,\n\t\t\t\tisCorrect: isCorrect,\n\t\t\t\tpoints: this.currentQuestion.points\n\t\t\t});\n\n\t\t\tthis.practiceFinished = true;\n\t\t\tif (this.timer) {\n\t\t\t\tclearInterval(this.timer);\n\t\t\t}\n\t\t},\n\n\t\t// 重新练习\n\t\trestartPractice() {\n\t\t\tthis.currentQuestionIndex = 0;\n\t\t\tthis.selectedAnswer = null;\n\t\t\tthis.inputAnswer = '';\n\t\t\tthis.showResult = false;\n\t\t\tthis.practiceFinished = false;\n\t\t\tthis.answers = [];\n\t\t\tthis.startTime = Date.now();\n\t\t\t\n\t\t\tif (this.timeLimit > 0) {\n\t\t\t\tthis.timeLeft = this.timeLimit;\n\t\t\t\tthis.startTimer();\n\t\t\t}\n\t\t},\n\n\t\t// 返回列表\n\t\tbackToList() {\n\t\t\tthis.currentPractice = null;\n\t\t\tthis.practiceFinished = false;\n\t\t},\n\n\t\t// 查看解析\n\t\treviewAnswers() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 开始计时器\n\t\tstartTimer() {\n\t\t\tthis.timer = setInterval(() => {\n\t\t\t\tthis.timeLeft--;\n\t\t\t\tif (this.timeLeft <= 0) {\n\t\t\t\t\tthis.finishPractice();\n\t\t\t\t}\n\t\t\t}, 1000);\n\t\t},\n\n\t\t// 格式化时间\n\t\tformatTime(seconds) {\n\t\t\tconst mins = Math.floor(seconds / 60);\n\t\t\tconst secs = seconds % 60;\n\t\t\treturn `${mins}分${secs}秒`;\n\t\t},\n\n\t\t// 获取图标类名\n\t\tgetIconClass(type) {\n\t\t\tconst iconMap = {\n\t\t\t\t'grammar': 'icon-book',\n\t\t\t\t'vocabulary': 'icon-word',\n\t\t\t\t'reading': 'icon-read',\n\t\t\t\t'comprehensive': 'icon-star'\n\t\t\t};\n\t\t\treturn iconMap[type] || 'icon-edit';\n\t\t},\n\n\t\t// 获取类型文本\n\t\tgetTypeText(type) {\n\t\t\tconst typeMap = {\n\t\t\t\t'grammar': '语法',\n\t\t\t\t'vocabulary': '词汇',\n\t\t\t\t'reading': '阅读',\n\t\t\t\t'comprehensive': '综合',\n\t\t\t\t'choice': '选择题',\n\t\t\t\t'fill': '填空题',\n\t\t\t\t'judge': '判断题'\n\t\t\t};\n\t\t\treturn typeMap[type] || '未知';\n\t\t},\n\n\t\t// 获取难度文本\n\t\tgetDifficultyText(difficulty) {\n\t\t\tconst map = {\n\t\t\t\t'easy': '简单',\n\t\t\t\t'medium': '中等',\n\t\t\t\t'hard': '困难'\n\t\t\t};\n\t\t\treturn map[difficulty] || '未知';\n\t\t},\n\n\t\t// 获取分数等级\n\t\tgetGrade(score) {\n\t\t\tif (score >= 90) return '优秀';\n\t\t\tif (score >= 80) return '良好';\n\t\t\tif (score >= 70) return '中等';\n\t\t\tif (score >= 60) return '及格';\n\t\t\treturn '不及格';\n\t\t},\n\n\t\t// 获取分数样式类\n\t\tgetScoreClass(score) {\n\t\t\tif (score >= 90) return 'excellent';\n\t\t\tif (score >= 80) return 'good';\n\t\t\tif (score >= 60) return 'pass';\n\t\t\treturn 'fail';\n\t\t},\n\n\t\tloadSpecificPractice(practiceId) {\n\t\t\tconst practice = this.practiceList.find(p => p.id == practiceId);\n\t\t\tif (practice) {\n\t\t\t\tthis.startPractice(practice);\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n/* 基础样式与听力练习页面类似，这里只列出关键差异 */\n.practice-list, .practice-content, .result-page {\n\tpadding: 30rpx;\n}\n\n.practice-item {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.item-icon.grammar {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.item-icon.vocabulary {\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.item-icon.reading {\n\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.item-icon.comprehensive {\n\tbackground: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.question-input {\n\tmargin: 30rpx 0;\n}\n\n.input-field {\n\twidth: 100%;\n\theight: 80rpx;\n\tborder: 2rpx solid #f0f0f0;\n\tborder-radius: 16rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 32rpx;\n\tbackground: #fff;\n}\n\n.question-judge {\n\tdisplay: flex;\n\tgap: 30rpx;\n\tmargin: 30rpx 0;\n}\n\n.judge-option {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 30rpx;\n\tborder: 2rpx solid #f0f0f0;\n\tborder-radius: 16rpx;\n\ttransition: all 0.3s;\n}\n\n.judge-option.selected {\n\tborder-color: #2094CE;\n\tbackground: #f0f9ff;\n}\n\n.judge-icon {\n\tfont-size: 48rpx;\n\tmargin-right: 15rpx;\n}\n\n.judge-text {\n\tfont-size: 32rpx;\n}\n\n.question-explanation {\n\tmargin-top: 30rpx;\n\tpadding: 20rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 16rpx;\n}\n\n.explanation-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #666;\n\tmargin-bottom: 10rpx;\n}\n\n.explanation-content {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n}\n\n.btn-check {\n\tflex: 1;\n\theight: 88rpx;\n\tborder-radius: 44rpx;\n\tfont-size: 32rpx;\n\tborder: none;\n\tbackground: #fa8c16;\n\tcolor: #fff;\n}\n\n.result-score.excellent {\n\tcolor: #52c41a;\n}\n\n.result-score.good {\n\tcolor: #2094CE;\n}\n\n.result-score.pass {\n\tcolor: #fa8c16;\n}\n\n.result-score.fail {\n\tcolor: #ff4d4f;\n}\n\n.time-left {\n\tcolor: #ff4d4f;\n\tfont-weight: bold;\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quiz-practice.vue?vue&type=style&index=0&id=5012569e&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753663858343\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}