(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/about-us/about-us"],{"0840":function(t,e,n){"use strict";n.r(e);var u=n("ed1e"),r=n("8482");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("f915");var c=n("828b"),f=Object(c["a"])(r["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);e["default"]=f.exports},"274f":function(t,e,n){},"4ef5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={data:function(){return{ver:this.$store.state.ver}}}},8482:function(t,e,n){"use strict";n.r(e);var u=n("4ef5"),r=n.n(u);for(var a in u)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(a);e["default"]=r.a},ab76:function(t,e,n){"use strict";(function(t,e){var u=n("47a9");n("5788");u(n("3240"));var r=u(n("0840"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},ed1e:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return u}));var u={guiPage:function(){return n.e("GraceUI5/components/gui-page").then(n.bind(null,"5b34"))}},r=function(){var t=this.$createElement;this._self._c},a=[]},f915:function(t,e,n){"use strict";var u=n("274f"),r=n.n(u);r.a}},[["ab76","common/runtime","common/vendor"]]]);