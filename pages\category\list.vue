<template>
	<view class="container">
		<view v-if="type==1">
			<view class="scroll">
				<scroll-view class="scroll-left" scroll-y="true" >
					<view @click="postSonflData(item.id, index)" :class="{active: activeIndex === index}" v-for="(item, index) in classifyList" :key="index"><text v-if="activeIndex === index" style="color: blue;margin-right: 10rpx;font-weight: bold;">|</text>{{item.name}}</view>
				</scroll-view>
				<scroll-view class="scroll-right" scroll-y="true" >
							<uni-section v-if="son_fls.type==1">
							<uni-collapse ref="collapse" v-model="value" >
								<uni-collapse-item  :title="item.name" v-for="(item,index) in son_fls.class" :key="index" >
									<view class="list-box">
										<empty1 v-if=" item.class.length === 0"></empty1>
										<view class="item-box lp-flex-column" v-for="(item1, index1) in item.class"
											:key="index1" @tap="navToDetailPage(item1)">
											<view class="top-box lp-flex">
									
												<view class="cover-box lp-flex-center">
													<image class="cover" :src="item1.picture"></image>
												</view>
												<view class="info-box lp-flex-column">
													<view class="title">{{item1.title}}</view>
							
												</view>
											</view>
										</view>
									</view>
								</uni-collapse-item>
							</uni-collapse>
							</uni-section>
							<view class="list-box" v-else>
								<empty1 v-if=" son_fls.class.length === 0"></empty1>
								<view class="item-box lp-flex-column" v-for="(item1, index1) in son_fls.class"
									:key="index1" @tap="navToDetailPage(item1)">
									<view class="top-box lp-flex">
							
										<view class="cover-box lp-flex-center">
											<image class="cover" :src="item1.picture"></image>
										</view>
										<view class="info-box lp-flex-column">
											<view class="title">{{item1.title}}</view>
			
										</view>
									</view>
								</view>
							</view>
				</scroll-view>
			</view>
		</view>
			
		<view v-else>
			<view class="list-box" >
				<empty1 v-if=" classifyList.length === 0"></empty1>
				<view class="item-box lp-flex-column" v-for="(item1, index1) in classifyList"
					:key="index1" @tap="navToDetailPage(item1)">
					<view class="top-box lp-flex">
			
						<view class="cover-box lp-flex-center">
							<image class="cover" :src="item1.picture"></image>
						</view>
						<view class="info-box lp-flex-column">
							<view class="title">{{item1.title}}</view>
						
						</view>
					</view>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import { postClassifylist, postSonflData } from '@/request/index'
	import {postSearchCourse} from '@/request/search'
	import empty1 from "@/components/null";
	export default {
		components: {
			empty1
		},
		data() {
			return {
				keyword: '',
				activeIndex: 0,
				classifyList: [],
				flid: '',
				son_fls: [],
				goodstype: '',
				cateId:0,
				type:1,
				son_type:1
			};
		},
		onShareAppMessage(res) {
			let path = getCurrentPages()
			let path_share = path[0].$page.fullPath
			let path_title = path[0].data.title
			let userinfo = uni.getStorageSync('userinfo')
			let base_set = uni.getStorageSync('base_set')
			if(userinfo.uid=='' || !userinfo.uid){
				uni.navigateTo({
					url:'../login/login'
				})
				return {
					title: '请先登录后再分享给好友',
					path: ''
				}
			}else{
				if (res.from === 'button') {
					
				}
				return {
					title: base_set.title,
					path: `${path_share}?pid=${userinfo.uid}`
				}
			}
		},
		onLoad(options) {
			this.cateId = options.id;
			this.type = options.type;
			this.title = options.title;
			this.son_type = options.son_type;
			console.log(this.title);
			// 动态设置标题
			uni.setNavigationBarTitle({
				title: this.title
			});
			this.loadHotData()
		},
		methods: {
			
			async loadHotData(type = 'add', loading) {
				let that = this;
				uni.showLoading();
				this.$http.get("v1/course/getSubClass", {
					params: {
						id: this.cateId,
						type:this.son_type
					}
				}).then(res => {
					console.log(res)
					//if(this.type==1){
						that.classifyList = res.data.data.class
					//}else{
						that.classifyList = res.data.data.class;
					//}
					
					that.type = res.data.data.type
					if(that.type==1){
						that.postSonflData(that.classifyList[this.activeIndex].id, 0)
					}
					
				});
			},
			// 获取分类列表
			postClassifylist(goodstype) {
				postClassifylist({goodstype: goodstype}).then(res => {
					this.classifyList = res.data.data
					this.postSonflData(this.classifyList[0].id, 0)
				})
			},
			// 获取子分类列表
			postSonflData(id, key) {
				this.activeIndex = key
				this.flid = this.classifyList[key].id
				// postSonflData({goodstype: this.goodstype, flid: id}).then(res => {
				// 	this.son_fls = res.data.data
				// })
				this.$http.get("v1/course/getSubClass", {
					params: {
						id: id,
						type:1
					}
				}).then(res => {
					console.log(res)
					this.son_fls = res.data.data
				});
			},
			navigate(id) {
				// console.log(id)
				if(this.goodstype == 'course') {
					uni.navigateTo({
						url: `/pages/course-list/course-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`
					})
				} else {
					uni.navigateTo({
						url: `/pages/commodity-list/commodity-list?flid=${this.flid}&sonflid=${id}&goodstype=${this.goodstype}`
					})
				}
			},
			postSearchCourse(e) {
				console.log(123)
				var keyword
				if(e == 'hot') {
					keyword = e
				} else {
					keyword = this.keyword
					if(keyword == '') {
						uni.showToast({
							title: '搜索内容不能为空',
							icon: 'none'
						});
						return false
					}
				}
				uni.navigateTo({
					url: `/pages/course-list/course-list?keyword=${keyword}`
				});
			},
			navToDetailPage(item){
				let id = item.id
				uni.navigateTo({
					url: `/pages/course/course?id=${id}`
				});
			}
		}
	}
</script>

<style lang="less" scoped>


	// search框 
	.search {
		height: 90upx;
		background-color: #4b89ff;
		padding: 0 20upx;
		padding-top: 30upx;

		&-input {
			position: relative;
			display: flex;
			text {
				font-size: 31upx;
				color: #fff;
				margin-top: 10upx;
			}
			image {
				width: 25upx;
				height: 14upx;
				margin: 25upx 20upx 0 10upx;
			}
			input {
				width: 710upx;
				height: 63upx;
				border: 0;
				background-color: #fff;
				border-radius: 63upx;
				font-size: 24upx;
				padding-left: 20upx;
				box-sizing: border-box;
				
			}
			.goods-search {
				width: 28upx;
				height: 28upx;
				position: absolute;
				right: 10upx;
				top: -5upx;
				z-index: 99;
			}
		}
	}
	
	
	.scroll {
		height: calc(100vh - 5upx);
		// background-color: pink;
		display: flex;
		&-left {
			flex: 2;
			// background-color: red;
			view {
				height: 120upx;
				background-color: #eee;
				display: flex;
				//flex-direction: column;
				justify-content: center;
				align-items: center;
				// border-bottom: 2upx solid #ddd;
				font-size: 30rpx;
				color: #333;
				letter-spacing: 2upx;
			}
			.active {
				background-color: #fff;
			}
		}
		&-right {
			flex: 5;
			background-color: #fff;
			padding: 0 10upx;
			box-sizing: border-box;
			.item {
				display: inline-block;
				width: 250upx;
				height: 60upx;
				background-color: #eee;
				text-align: center;
				line-height: 60upx;
				border-radius: 60upx;
				margin: 20upx 12upx 0;
				text {
					font-size: 30upx;
					font-weight: 700;
					letter-spacing: 4upx;
					color: #333;
				}
			}
		}
	}
	.list-box {
		// padding-bottom: 20rpx;
	
		.item-box {
			// margin: 30rpx 30rpx 0 30rpx;
			padding: 10rpx 10rpx;
			background-color: #fff;
			border-radius: 10rpx;
			color: #666;
			font-size: 28rpx;
			border-bottom: 1rpx solid #ebebeb;
	
	
			.top-box {
				position: relative;
				padding: 20rpx;
	
				.cover-box-hot {
					width: 35%;
					height: auto;
					min-height: 120rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
	
					.cover :after {
						background-color: red;
						border-radius: 10rpx;
						color: #fff;
						content: "hot";
						font-size: 25rpx;
						line-height: 1;
						padding: 2rpx 6rpx;
						position: absolute;
						left: 5rpx;
						top: 5rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-box {
					width: 150rpx;
					height: auto;
					min-height: 150rpx;
					position: relative;
	
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
	
					.button {
						position: absolute;
						top: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: blue;
						color: white;
						padding: 5rpx 10rpx;
						font-size: 20rpx;
					}
				}
	
				.cover-large-box {
					width: 100%;
					height: auto;
					height: 200rpx;
					position: relative;
				
					.cover {
						width: 100%;
						height: 100%;
						border-radius: 10rpx;
					}
				
					.button {
						position: absolute;
						bottom: 0;
						right: 0;
						transform: translateX(0);
						border-radius: 20rpx;
						background-color: rgba(0, 0, 0, .5) !important;
						color: white;
						padding: 15rpx 20rpx;
						font-size: 20rpx;
					}
				}
	
				.info-box {
					flex: 1;
					margin-left: 15rpx;
					height: auto;
					overflow: hidden;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-between;
	
					.publish-date {
						font-size: 32rpx;
						font-weight: bold;
					}
	
					.lang-box {
						//color: $uni-text-color-grey;
						font-size: 24rpx;
					}
	
					.title {
						font-weight: bold;
						font-size: 24rpx;
						color: #666666;
					}
	
					.end-date {
						font-size: 20rpx;
						color: #999999;
					}
	
					.total {
						font-size: 20rpx;
						color: #39b54a;
					}
	
					.des {
						font-size: 22rpx;
						color: #8f8f94;
	
	
					}
	
					.price {
						font-size: 24rpx;
						color: red;
						float: right;
					}
	
					.end {
						font-size: 24rpx;
						color: blue;
						// display: flex;
						// justify-content: space-between;
						// align-items: center;
						width: 100%;
					}
				}
			}
	
			.margin-tb-sm {
				margin-top: 20upx;
				margin-bottom: 20upx;
				position: relative;
	
				.text-sm {
					font-size: 24upx;
				}
	
				.title {
					overflow: hidden;
					word-break: break-all;
					/* break-all(允许在单词内换行。) */
					text-overflow: ellipsis;
					/* 超出部分省略号 */
					display: -webkit-box;
					/** 对象作为伸缩盒子模型显示 **/
					-webkit-box-orient: vertical;
					/** 设置或检索伸缩盒对象的子元素的排列方式 **/
					-webkit-line-clamp: 2;
					/** 显示的行数 **/
				}
	
				.uni-row {
					flex-direction: row;
				}
	
				.align-center {
					align-items: center;
				}
	
				.margin-left-sm {
					margin-left: 20upx;
				}
			}
		}
	
		:last-child {
			// border-bottom: 1rpx solid #fff;
		}
	}
</style>
