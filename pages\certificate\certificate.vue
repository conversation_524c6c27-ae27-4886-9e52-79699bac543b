<template>
	<gui-page>

		<!-- 页面主体 -->
		<view slot="gBody" style="padding:30rpx 30rpx 30rpx 30rpx;">
			<!-- <view class="ws-btn-box" >
				<form @submit="loginNow" class="grace-form" style="margin-top:100rpx;">
					<view class="grace-form-item grace-border-b">

						<view class="grace-form-body">
							<input type="text"  class="ws-input" name="name" 
								placeholder="请输入姓名" />
						</view>
					</view>
					<view class="grace-margin-top">
						<button form-type="submit" type="primary" class="phone-login-btn">
							查询
						</button>

					</view>
				</form>
			</view> -->
			<view v-if="img != null" style="margin: 20rpx;text-align: center;">
				<image show-menu-by-longpress='1' :src="img" mode="aspectFill" style="width: 100%;height: 1000rpx;"></image>
				<!-- <button type="primary" style = "width: 600rpx;background-color: #FF3D00;margin-top: 100rpx;" class="phone-login-btn" @tap="savePoster">
					保存证书
				</button> -->
				<view style="font-size: 30rpx;color: #f00;margin-top: 40rpx;">长按上面证书，可保存证书到相册</view>
			</view>
			<view v-else>
				<ws-null  text="查无内容"></ws-null>
			</view>
		</view>
	</gui-page>
</template>

<script>
	var graceChecker = require("@/GraceUI5/js/checker.js");
	export default {
		data() {
			return {
				ver: this.$store.state.ver,
				img: null
			};
		},
		onLoad(option) {
			this.img = option.img
		},
		methods: {
			loginNow: function(e) {
				// 表单验证
				var formData = e.detail.value;
				// 验证通过
				this.$http.post("v1/member/doCertificate", {
					name: e.mp.detail.value.name,
				}).then(res => {
					console.log(res.data)
					if(res.data.data){
						this.img = res.data.data.img
					}else{
						
					}
					
				})
			},
			savePoster() {
				let that = this;
				uni.getSetting({ //获取用户的当前设置
					success: (res) => {
						if (res.authSetting['scope.writePhotosAlbum']) { //验证用户是否授权可以访问相册
							that.saveImageToPhotos();
						} else {
							uni.authorize({ //如果没有授权，向用户发起请求
								scope: 'scope.writePhotosAlbum',
								success: () => {
									that.saveImageToPhotos();
								},
								fail: () => {
									uni.showToast({
										title: "请打开保存相册权限，再点击保存相册分享",
										icon: "none",
										duration: 3000
									});
									setTimeout(() => {
										uni.openSetting({ //调起客户端小程序设置界面,让用户开启访问相册
											success: (res2) => {
												// console.log(res2.authSetting)
											}
										});
									}, 3000);
								}
							})
						}
					}
				})
			},
			saveImageToPhotos() {
				let that = this;
				console.log(that.img)
				uni.downloadFile({
					src: that.img,
					success: res => {
						console.log(res)
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success(res) {
								uni.showToast({
									title: '保存图片成功！',
								})
							},
							fail(err) {
								uni.showToast({
									title: '保存图片失败！',
								})
							}
						})
					},
					fail: err => {
						console.log(err, 'err')
					}
				})

			},
		}
	}
</script>

<style lang="scss">
	.root-box {
		.ver {
			color: #aaa;
			margin-top: 20rpx;
		}

		.logo-box {
			height: 400rpx;

			.gui-icons {
				font-size: 200rpx;
				color: $uni-text-color-grey;
			}
		}

		.info-box {
			padding: 60rpx;
			font-size: 28rpx;
			color: #666;
		}
		
		
	}
	
	.ws-input{
		width: 100%;
		background-color: #FFFFFF;
		font-size: 26rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		border-radius: 20rpx;
		height: 100rpx;
		border: 2rpx solid #F5F5F5;
		letter-spacing: 1px;
		margin-bottom: 20rpx;
	}
</style>
