{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/order/list.vue?7ea7", "webpack:///D:/日语云课/pages/order/list.vue?e823", "webpack:///D:/日语云课/pages/order/list.vue?d428", "webpack:///D:/日语云课/pages/order/list.vue?3c67", "uni-app:///pages/order/list.vue", "webpack:///D:/日语云课/pages/order/list.vue?c789", "webpack:///D:/日语云课/pages/order/list.vue?f6c6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "u_line_progress", "u_button", "data", "title", "empty", "loading", "current_page", "total_page", "total", "list", "onLoad", "created", "uni", "content", "success", "url", "onShow", "methods", "getCourseList", "onScrolltolowerHandler", "apiGetCourseList", "params", "page", "navToDetailPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAklB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6CtmB;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACA;MACAC;QACAT;QACAU;QACAC;UACA;YACAF;cACAG;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;EAEA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MAEA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAX;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAinC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAroC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=6546c710&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/list.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=6546c710&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.empty === true) ? _vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<gui-page :fullPage=\"true\" ref=\"guiPage\" :isLoading=\"pageLoading\">\r\n\t\t<view slot=\"gBody\" class=\"gui-flex1\" style=\"background-color:#F8F8F8;\">\r\n\t\t\t<!-- 空白页 -->\r\n\t\t\t<view v-if=\" empty===true\" class=\"empty\">\r\n\t\t\t\t<image src=\"/static/emptyCart.jpg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"empty-tips\">\r\n\t\t\t\t\t空空如也\r\n\t\t\t\t\t<navigator class=\"navigator\" url=\"../index/index\" open-type=\"switchTab\">随便逛逛></navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>\r\n\t\t\t\t<!-- 列表 -->\r\n\t\t\t\t<scroll-view v-if=\"list.length>0\" scroll-y=\"true\" @scrolltolower=\"onScrolltolowerHandler\">\r\n\t\t\t\t\t<view class=\"list-box\">\r\n\t\t\t\t\t\t<view class=\"item-box lp-flex-column\" v-for=\"(item,index) in list\" :key=\"index\"\r\n\t\t\t\t\t\t\t@tap=\"navToDetailPage(item)\">\r\n\t\t\t\t\t\t\t<view class=\"top-box lp-flex\">\r\n\t\t\t\t\t\t\t\t<view class=\"cover-box lp-flex-center\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"cover\" :src=\"item.picture\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"info-box lp-flex-column\">\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"name-box lp-flex lp-flex-space-between\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"publish-date\">{{item.publish_date}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"lang-box lp-flex lp-flex-center\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"gui-icons\" style=\"margin-right: 10rpx;\">{{item.type == 2 ? '&#xe62f;' : '&#xe656;'}} </text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"trans\">{{item.trans}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">{{item.title}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"total\">时间：{{item.pay_time}}</text>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</gui-page>\r\n</template>\r\n\r\n<script>\r\n\timport u_line_progress from '@/components/uview-ui/components/u-line-progress/u-line-progress.vue';\r\n\timport u_button from '@/components/uview-ui/components/u-button/u-button.vue';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tu_line_progress,\r\n\t\t\tu_button\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: 'Hello',\r\n\t\t\t\tempty: true, //空白页现实  true|false\r\n\t\t\t\t// 加载中\r\n\t\t\t\tloading: false,\r\n\t\t\t\tcurrent_page: 1,\r\n\t\t\t\t// 总页数\r\n\t\t\t\ttotal_page: 1,\r\n\t\t\t\t// 总数量\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\t// 列表数据\r\n\t\t\t\tlist: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: \"/pages/login/login\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getCourseList(1, true);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetCourseList: function(page, force) {\r\n\t\t\t\tif (this.loading) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!force && page > this.total_page) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tthis.apiGetCourseList(page).then(pagination => {\r\n\t\t\t\t\tthis.current_page = pagination.current_page;\r\n\t\t\t\t\tthis.total_page = pagination.last_page;\r\n\t\t\t\t\tthis.total = pagination.total;\r\n\t\t\t\t\tthis.list = pagination.data;\r\n\t\t\t\t\tif (pagination.data.length > 0) {\r\n\t\t\t\t\t\tthis.empty = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// hander\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tonScrolltolowerHandler: function() {\r\n\t\t\t\tthis.getCourseList(this.current_page + 1);\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// api\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tapiGetCourseList: function(page) {\r\n\t\t\t\treturn this.$http.get('/v1/course/userOrder', {\r\n\t\t\t\t\tparams: {\r\n\t\t\t\t\t\tpage,\r\n\t\t\t\t\t}\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//详情页\r\n\t\t\tnavToDetailPage(item) {\r\n\t\t\t\t//测试数据没有写id，用title代替\r\n\t\t\t\tlet id = item.good_id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/course/course?id=${id}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang='scss'>\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #F8F8F8;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #8f8f94;\r\n\t}\r\n\r\n\t/* 空白页 */\r\n\t.empty {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tpadding-bottom: 100upx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground: #fff;\r\n\r\n\t\timage {\r\n\t\t\twidth: 240upx;\r\n\t\t\theight: 160upx;\r\n\t\t\tmargin-bottom: 30upx;\r\n\t\t}\r\n\r\n\t\t.empty-tips {\r\n\t\t\tdisplay: flex;\r\n\t\t\tfont-size: $font-sm+2upx;\r\n\t\t\tcolor: $font-color-disabled;\r\n\r\n\t\t\t.navigator {\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t\tmargin-left: 16upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tscroll-view {\r\n\t\tmax-height: 100%;\r\n\t}\r\n\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.flex-sub {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.list-box {\r\n\t\tpadding-bottom: 40rpx;\r\n\r\n\t\t.item-box {\r\n\t\t\tmargin: 30rpx 30rpx 0 30rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t.top-box {\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.cover-box {\r\n\t\t\t\t\twidth: 200rpx;\r\n\t\t\t\t\theight: 120rpx;\r\n\r\n\t\t\t\t\t.cover {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-box {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-left: 30rpx;\r\n\r\n\t\t\t\t\t.publish-date {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.lang-box {\r\n\t\t\t\t\t\tcolor: $uni-text-color-grey;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #8f8f94;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.end-date {\r\n\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.total {\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #39b54a;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.margin-tb-sm {\r\n\t\t\t\tmargin-top: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t.text-sm {\r\n\t\t\t\t\tfont-size: 24upx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tword-break: break-all;\r\n\t\t\t\t\t/* break-all(允许在单词内换行。) */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t/* 超出部分省略号 */\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t/** 对象作为伸缩盒子模型显示 **/\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t/** 设置或检索伸缩盒对象的子元素的排列方式 **/\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/** 显示的行数 **/\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.uni-row {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.align-center {\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.margin-left-sm {\r\n\t\t\t\t\tmargin-left: 20upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662930014\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}