<h3 align="center" style="margin: 30px 0 30px;font-weight: bold;font-size:40px;">um-dropdown</h3>
<h3 align="center">下拉菜单</h3>

## 说明
um-dropdown 是基于[uni-app](https://uniapp.dcloud.io/)生态的一款下拉菜单组件，可自定义文本框样式，列表类容支持数组，数组内可使用object类型

## 使用方法
 符合[uni-app](https://uniapp.dcloud.io/)的插件模块化规范配置，直接引用即可。

```html
<template>
	<view style="width: 100px;height: 100px;">
		
		<um-dropdown @change="fnChange" rangeKey="label" :optionList="listData" selectedItemStyle="color:2973F8;"></um-dropdown>
	
	</view>
</template>
<script>
	export default {
		data() {
			return {
				optionVal: '', // 选中列表的值
				listData: [ // 列表数据
					{
						label: '北京',
						value: '1',
					},
					{
						label: '上海',
						value: '2',
					},
					{
						label: '重庆',
						value: '3',
					},
					{
						label: '成都',
						value: '4',
					}
				]
			}
		},

		methods: {
			// 列表选中时触发事件，带出选中的值
			fnChange(e) {
				this.optionVal = e
			}
		}
	}
</script>
```
## API

Props

|  属性名  |  说明  |  类型  |
|  ----  |  ----  |  ----  |
| optionList  | 菜单列表数据 | Array |
| rangeKey  | 如果数据中包含对象时，需要显示的key值 | String |
| listHeight  | 列表高度，设置后列表可以滑动（默认展示所有数据） | String |
| defaultIndex  | 默认选中的下标 | String，Number |
| width  | 菜单宽度（默认继承父元素width） | String |
| height  | 菜单高度（默认继承父元素height） | String |
| listStyle  | 自定义列表样式 | String，Object |
| itemStyle  | 自定义菜单样式 | String，Object |
| selectedItemStyle  | 菜单选中时的样式 | String，Object |


Events

|  属性名  |  说明  |  类型  |
|  ----  |  ----  |  ----  |
| change  | 列表点击事件，值发生改变时触发 | Handler |
| click  | 菜单点击事件 | Handler |
| openNull  | 菜单点击事件，列表为空时触发 | Handler |

平台差异

|  平台  |  说明  |
|  ----  |  ----  |
| H5  | 点击组件外部区域列表会自动收起 | 
| 微信小程序  | 点击组件外部区域，列表不会自动收起 | 
| 其他  | 问题待测，持续更新中 | 

## 交流反馈

留言信息无法实时查看，如需及时交流和反馈可加入QQ群：368365360