<grace-page vue-id="35a7246c-1" class="data-v-b237504c" bind:__l="__l" vue-slots="{{['gHeader','gBody']}}"><view slot="gHeader" class="data-v-b237504c"><ws-head vue-id="{{('35a7246c-2')+','+('35a7246c-1')}}" color="black" class="data-v-b237504c" bind:__l="__l"></ws-head></view><view style="padding:30rpx 30rpx 120rpx 30rpx;" slot="gBody" class="data-v-b237504c"><view class="ws-logo-box data-v-b237504c"><view class="logo-title data-v-b237504c">日语云课</view></view><block wx:if="{{loginType=='wx'}}"><view class="ws-btn-box wx-box data-v-b237504c"><block wx:if="{{canIUseProfile==false}}"><button class="wx-login-btn ws-mt data-v-b237504c" type="primary" open-type="getUserInfo" withCredentials="true" data-event-opts="{{[['getuserinfo',[['getUserInfo',['$event']]]]]}}" bindgetuserinfo="__e">登录</button></block><block wx:else><button class="wx-login-btn ws-mt data-v-b237504c" type="primary" data-event-opts="{{[['tap',[['getUserInfo',['$event']]]]]}}" bindtap="__e">登录</button></block></view></block><block wx:else><view class="ws-btn-box data-v-b237504c"><form data-event-opts="{{[['submit',[['loginNow',['$event']]]]]}}" class="grace-form data-v-b237504c" style="margin-top:80rpx;" bindsubmit="__e"><view class="grace-margin-top data-v-b237504c"><button class="phone-login-btn data-v-b237504c" type="default" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号</button></view></form></view></block></view></grace-page>