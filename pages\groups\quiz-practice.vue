<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 练习列表 -->
			<view class="practice-list" v-if="!currentPractice">
				<view class="list-header">
					<view class="header-title">答题练习</view>
					<view class="header-desc">选择一个练习开始训练</view>
				</view>
				
				<view 
					class="practice-item" 
					v-for="(item, index) in practiceList" 
					:key="index"
					@click="startPractice(item)"
				>
					<view class="item-icon" :class="[item.type]">
						<text class="iconfont" :class="[getIconClass(item.type)]"></text>
					</view>
					<view class="item-info">
						<view class="item-title">{{item.title}}</view>
						<view class="item-desc">{{item.description}}</view>
						<view class="item-meta">
							<text class="meta-count">{{item.questionCount}}题</text>
							<text class="meta-type">{{getTypeText(item.type)}}</text>
							<text class="meta-difficulty" :class="[item.difficulty]">{{getDifficultyText(item.difficulty)}}</text>
						</view>
					</view>
					<view class="item-status">
						<text class="status-score" v-if="item.bestScore !== null">{{item.bestScore}}分</text>
						<text class="status-new" v-else>NEW</text>
					</view>
				</view>
			</view>

			<!-- 练习界面 -->
			<view class="practice-content" v-else-if="!practiceFinished">
				<!-- 进度条 -->
				<view class="progress-header">
					<view class="progress-info">
						<text>{{currentQuestionIndex + 1}} / {{currentPractice.questions.length}}</text>
						<text class="time-left" v-if="timeLimit > 0">剩余时间: {{formatTime(timeLeft)}}</text>
					</view>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					</view>
				</view>

				<!-- 题目内容 -->
				<view class="question-content">
					<view class="question-header">
						<view class="question-type">{{getTypeText(currentQuestion.type)}}</view>
						<view class="question-points">{{currentQuestion.points}}分</view>
					</view>
					
					<view class="question-title">{{currentQuestion.question}}</view>
					
					<!-- 图片题目 -->
					<view class="question-image" v-if="currentQuestion.image">
						<image :src="currentQuestion.image" mode="aspectFit"></image>
					</view>

					<!-- 选择题选项 -->
					<view class="question-options" v-if="currentQuestion.type === 'choice'">
						<view 
							class="option-item" 
							v-for="(option, index) in currentQuestion.options" 
							:key="index"
							:class="{ 
								selected: selectedAnswer === index,
								correct: showResult && index === currentQuestion.correctAnswer,
								wrong: showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer
							}"
							@click="selectAnswer(index)"
						>
							<view class="option-label">{{String.fromCharCode(65 + index)}}</view>
							<view class="option-text">{{option}}</view>
						</view>
					</view>

					<!-- 填空题 -->
					<view class="question-input" v-else-if="currentQuestion.type === 'fill'">
						<input 
							v-model="inputAnswer" 
							:placeholder="currentQuestion.placeholder || '请输入答案'"
							class="input-field"
							:disabled="showResult"
						/>
					</view>

					<!-- 判断题 -->
					<view class="question-judge" v-else-if="currentQuestion.type === 'judge'">
						<view 
							class="judge-option" 
							:class="{ selected: selectedAnswer === true }"
							@click="selectAnswer(true)"
						>
							<text class="judge-icon">○</text>
							<text class="judge-text">正确</text>
						</view>
						<view 
							class="judge-option" 
							:class="{ selected: selectedAnswer === false }"
							@click="selectAnswer(false)"
						>
							<text class="judge-icon">×</text>
							<text class="judge-text">错误</text>
						</view>
					</view>

					<!-- 解析 -->
					<view class="question-explanation" v-if="showResult && currentQuestion.explanation">
						<view class="explanation-title">解析</view>
						<view class="explanation-content">{{currentQuestion.explanation}}</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<button class="btn-secondary" @click="prevQuestion" :disabled="currentQuestionIndex === 0">
						上一题
					</button>
					<button class="btn-check" @click="checkAnswer" v-if="!showResult">
						检查答案
					</button>
					<button class="btn-primary" @click="nextQuestion" v-if="showResult && !isLastQuestion">
						下一题
					</button>
					<button class="btn-primary" @click="finishPractice" v-if="showResult && isLastQuestion">
						完成练习
					</button>
				</view>
			</view>

			<!-- 结果页面 -->
			<view class="result-page" v-if="practiceFinished">
				<view class="result-header">
					<view class="result-score" :class="[getScoreClass(finalScore)]">{{finalScore}}</view>
					<view class="result-text">分</view>
					<view class="result-grade">{{getGrade(finalScore)}}</view>
				</view>
				
				<view class="result-details">
					<view class="detail-item">
						<text class="detail-label">正确题数</text>
						<text class="detail-value">{{correctCount}} / {{totalQuestions}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">正确率</text>
						<text class="detail-value">{{correctRate}}%</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">用时</text>
						<text class="detail-value">{{practiceTime}}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">平均每题</text>
						<text class="detail-value">{{averageTime}}</text>
					</view>
				</view>

				<view class="result-actions">
					<button class="btn-secondary" @click="reviewAnswers">查看解析</button>
					<button class="btn-primary" @click="restartPractice">重新练习</button>
					<button class="btn-secondary" @click="backToList">返回列表</button>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			groupId: '',
			practiceId: '',
			currentPractice: null,
			currentQuestionIndex: 0,
			selectedAnswer: null,
			inputAnswer: '',
			showResult: false,
			practiceFinished: false,
			answers: [],
			startTime: null,
			timeLimit: 0, // 时间限制（秒）
			timeLeft: 0,
			timer: null,
			
			practiceList: [
				{
					id: 1,
					title: '语法基础练习',
					description: '助词、动词变位等基础语法',
					type: 'grammar',
					questionCount: 10,
					difficulty: 'easy',
					bestScore: 92
				},
				{
					id: 2,
					title: '词汇记忆练习',
					description: '常用词汇的读音和意思',
					type: 'vocabulary',
					questionCount: 15,
					difficulty: 'medium',
					bestScore: null
				},
				{
					id: 3,
					title: '阅读理解练习',
					description: '短文阅读理解能力训练',
					type: 'reading',
					questionCount: 8,
					difficulty: 'hard',
					bestScore: 78
				},
				{
					id: 4,
					title: '综合能力测试',
					description: '语法、词汇、阅读综合测试',
					type: 'comprehensive',
					questionCount: 20,
					difficulty: 'hard',
					bestScore: 85
				}
			]
		}
	},
	computed: {
		currentQuestion() {
			if (!this.currentPractice || !this.currentPractice.questions) return null;
			return this.currentPractice.questions[this.currentQuestionIndex];
		},
		progressPercent() {
			if (!this.currentPractice) return 0;
			return ((this.currentQuestionIndex + 1) / this.currentPractice.questions.length) * 100;
		},
		isLastQuestion() {
			if (!this.currentPractice) return false;
			return this.currentQuestionIndex === this.currentPractice.questions.length - 1;
		},
		finalScore() {
			if (this.answers.length === 0) return 0;
			const totalPoints = this.answers.reduce((sum, answer) => sum + answer.points, 0);
			const earnedPoints = this.answers.reduce((sum, answer) => sum + (answer.isCorrect ? answer.points : 0), 0);
			return Math.round((earnedPoints / totalPoints) * 100);
		},
		correctCount() {
			return this.answers.filter(answer => answer.isCorrect).length;
		},
		totalQuestions() {
			return this.answers.length;
		},
		correctRate() {
			if (this.totalQuestions === 0) return 0;
			return Math.round((this.correctCount / this.totalQuestions) * 100);
		},
		practiceTime() {
			if (!this.startTime) return '0分0秒';
			const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
			return this.formatTime(elapsed);
		},
		averageTime() {
			if (!this.startTime || this.totalQuestions === 0) return '0秒';
			const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
			const average = Math.round(elapsed / this.totalQuestions);
			return `${average}秒`;
		}
	},
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId;
		}
		if (options.practiceId) {
			this.practiceId = options.practiceId;
			this.loadSpecificPractice(options.practiceId);
		}
	},
	onUnload() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		// 开始练习
		startPractice(practice) {
			this.currentPractice = {
				...practice,
				questions: this.generateQuestions(practice.id, practice.type)
			};
			this.currentQuestionIndex = 0;
			this.selectedAnswer = null;
			this.inputAnswer = '';
			this.showResult = false;
			this.practiceFinished = false;
			this.answers = [];
			this.startTime = Date.now();
			
			// 设置时间限制
			if (practice.timeLimit) {
				this.timeLimit = practice.timeLimit;
				this.timeLeft = practice.timeLimit;
				this.startTimer();
			}
		},

		// 生成题目
		generateQuestions(practiceId, type) {
			// 这里应该从API获取真实题目
			const questions = [];
			const questionCount = this.practiceList.find(p => p.id === practiceId)?.questionCount || 5;
			
			for (let i = 0; i < questionCount; i++) {
				questions.push(this.createSampleQuestion(type, i + 1));
			}
			
			return questions;
		},

		// 创建示例题目
		createSampleQuestion(type, index) {
			const baseQuestion = {
				id: index,
				points: 5
			};

			switch (type) {
				case 'grammar':
					return {
						...baseQuestion,
						type: 'choice',
						question: `语法题${index}：下列哪个助词使用正确？`,
						options: ['私は学校に行きます', '私は学校で行きます', '私は学校を行きます', '私は学校が行きます'],
						correctAnswer: 0,
						explanation: '「に」表示移动的目的地，所以用「学校に行きます」。'
					};
				case 'vocabulary':
					return {
						...baseQuestion,
						type: 'choice',
						question: `词汇题${index}：「ありがとう」的意思是？`,
						options: ['对不起', '谢谢', '再见', '你好'],
						correctAnswer: 1,
						explanation: '「ありがとう」是表示感谢的常用语。'
					};
				case 'reading':
					return {
						...baseQuestion,
						type: 'choice',
						question: `阅读题${index}：根据短文内容，作者的观点是什么？`,
						options: ['支持', '反对', '中立', '不明确'],
						correctAnswer: 0,
						explanation: '从文中的关键词可以看出作者的支持态度。'
					};
				default:
					return {
						...baseQuestion,
						type: 'judge',
						question: `判断题${index}：日语中「です」是敬语形式。`,
						correctAnswer: true,
						explanation: '「です」确实是日语中的敬语形式，用于正式场合。'
					};
			}
		},

		// 选择答案
		selectAnswer(answer) {
			if (this.showResult) return;
			this.selectedAnswer = answer;
		},

		// 检查答案
		checkAnswer() {
			let userAnswer = this.selectedAnswer;
			if (this.currentQuestion.type === 'fill') {
				userAnswer = this.inputAnswer.trim();
			}

			if (userAnswer === null || userAnswer === '') {
				uni.showToast({
					title: '请选择或输入答案',
					icon: 'none'
				});
				return;
			}

			this.showResult = true;
		},

		// 下一题
		nextQuestion() {
			// 记录答案
			let userAnswer = this.selectedAnswer;
			if (this.currentQuestion.type === 'fill') {
				userAnswer = this.inputAnswer.trim();
			}

			let isCorrect = false;
			if (this.currentQuestion.type === 'fill') {
				isCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();
			} else {
				isCorrect = userAnswer === this.currentQuestion.correctAnswer;
			}

			this.answers.push({
				questionId: this.currentQuestion.id,
				userAnswer: userAnswer,
				correctAnswer: this.currentQuestion.correctAnswer,
				isCorrect: isCorrect,
				points: this.currentQuestion.points
			});

			if (this.isLastQuestion) {
				this.finishPractice();
			} else {
				this.currentQuestionIndex++;
				this.selectedAnswer = null;
				this.inputAnswer = '';
				this.showResult = false;
			}
		},

		// 上一题
		prevQuestion() {
			if (this.currentQuestionIndex > 0) {
				this.currentQuestionIndex--;
				// 恢复之前的答案
				const prevAnswer = this.answers[this.currentQuestionIndex];
				if (prevAnswer) {
					if (this.currentQuestion.type === 'fill') {
						this.inputAnswer = prevAnswer.userAnswer;
					} else {
						this.selectedAnswer = prevAnswer.userAnswer;
					}
				} else {
					this.selectedAnswer = null;
					this.inputAnswer = '';
				}
				this.showResult = false;
			}
		},

		// 完成练习
		finishPractice() {
			// 记录最后一题答案
			if (!this.showResult) {
				this.checkAnswer();
				return;
			}

			let userAnswer = this.selectedAnswer;
			if (this.currentQuestion.type === 'fill') {
				userAnswer = this.inputAnswer.trim();
			}

			let isCorrect = false;
			if (this.currentQuestion.type === 'fill') {
				isCorrect = userAnswer.toLowerCase() === this.currentQuestion.correctAnswer.toLowerCase();
			} else {
				isCorrect = userAnswer === this.currentQuestion.correctAnswer;
			}

			this.answers.push({
				questionId: this.currentQuestion.id,
				userAnswer: userAnswer,
				correctAnswer: this.currentQuestion.correctAnswer,
				isCorrect: isCorrect,
				points: this.currentQuestion.points
			});

			this.practiceFinished = true;
			if (this.timer) {
				clearInterval(this.timer);
			}
		},

		// 重新练习
		restartPractice() {
			this.currentQuestionIndex = 0;
			this.selectedAnswer = null;
			this.inputAnswer = '';
			this.showResult = false;
			this.practiceFinished = false;
			this.answers = [];
			this.startTime = Date.now();
			
			if (this.timeLimit > 0) {
				this.timeLeft = this.timeLimit;
				this.startTimer();
			}
		},

		// 返回列表
		backToList() {
			this.currentPractice = null;
			this.practiceFinished = false;
		},

		// 查看解析
		reviewAnswers() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},

		// 开始计时器
		startTimer() {
			this.timer = setInterval(() => {
				this.timeLeft--;
				if (this.timeLeft <= 0) {
					this.finishPractice();
				}
			}, 1000);
		},

		// 格式化时间
		formatTime(seconds) {
			const mins = Math.floor(seconds / 60);
			const secs = seconds % 60;
			return `${mins}分${secs}秒`;
		},

		// 获取图标类名
		getIconClass(type) {
			const iconMap = {
				'grammar': 'icon-book',
				'vocabulary': 'icon-word',
				'reading': 'icon-read',
				'comprehensive': 'icon-star'
			};
			return iconMap[type] || 'icon-edit';
		},

		// 获取类型文本
		getTypeText(type) {
			const typeMap = {
				'grammar': '语法',
				'vocabulary': '词汇',
				'reading': '阅读',
				'comprehensive': '综合',
				'choice': '选择题',
				'fill': '填空题',
				'judge': '判断题'
			};
			return typeMap[type] || '未知';
		},

		// 获取难度文本
		getDifficultyText(difficulty) {
			const map = {
				'easy': '简单',
				'medium': '中等',
				'hard': '困难'
			};
			return map[difficulty] || '未知';
		},

		// 获取分数等级
		getGrade(score) {
			if (score >= 90) return '优秀';
			if (score >= 80) return '良好';
			if (score >= 70) return '中等';
			if (score >= 60) return '及格';
			return '不及格';
		},

		// 获取分数样式类
		getScoreClass(score) {
			if (score >= 90) return 'excellent';
			if (score >= 80) return 'good';
			if (score >= 60) return 'pass';
			return 'fail';
		},

		loadSpecificPractice(practiceId) {
			const practice = this.practiceList.find(p => p.id == practiceId);
			if (practice) {
				this.startPractice(practice);
			}
		}
	}
}
</script>

<style scoped>
/* 基础样式与听力练习页面类似，这里只列出关键差异 */
.practice-list, .practice-content, .result-page {
	padding: 30rpx;
}

.practice-item {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.item-icon.grammar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.item-icon.vocabulary {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.item-icon.reading {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.item-icon.comprehensive {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.question-input {
	margin: 30rpx 0;
}

.input-field {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	padding: 0 20rpx;
	font-size: 32rpx;
	background: #fff;
}

.question-judge {
	display: flex;
	gap: 30rpx;
	margin: 30rpx 0;
}

.judge-option {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.judge-option.selected {
	border-color: #2094CE;
	background: #f0f9ff;
}

.judge-icon {
	font-size: 48rpx;
	margin-right: 15rpx;
}

.judge-text {
	font-size: 32rpx;
}

.question-explanation {
	margin-top: 30rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
}

.explanation-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	margin-bottom: 10rpx;
}

.explanation-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.btn-check {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
	background: #fa8c16;
	color: #fff;
}

.result-score.excellent {
	color: #52c41a;
}

.result-score.good {
	color: #2094CE;
}

.result-score.pass {
	color: #fa8c16;
}

.result-score.fail {
	color: #ff4d4f;
}

.time-left {
	color: #ff4d4f;
	font-weight: bold;
}
</style>
