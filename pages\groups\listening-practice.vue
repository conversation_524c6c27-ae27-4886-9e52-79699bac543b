<template>
	<gui-page :fullPage="true" ref="guiPage" :isLoading="pageLoading">
		<view slot="gBody" class="gui-flex1" style="background-color:#F8F8F8;">
			<!-- 练习列表 -->
			<view class="practice-list" v-if="!currentPractice">
				<view class="list-header">
					<view class="header-title">听力练习</view>
					<view class="header-desc">选择一个练习开始训练</view>
				</view>
				
				<view 
					class="practice-item" 
					v-for="(item, index) in practiceList" 
					:key="index"
					@click="startPractice(item)"
				>
					<view class="item-icon">
						<text class="iconfont icon-headphone"></text>
					</view>
					<view class="item-info">
						<view class="item-title">{{item.title}}</view>
						<view class="item-desc">{{item.description}}</view>
						<view class="item-meta">
							<text class="meta-duration">{{item.duration}}</text>
							<text class="meta-difficulty" :class="item.difficulty">{{getDifficultyText(item.difficulty)}}</text>
						</view>
					</view>
					<view class="item-status">
						<text class="status-score" v-if="item.bestScore !== null">{{item.bestScore}}分</text>
						<text class="status-new" v-else>NEW</text>
					</view>
				</view>
			</view>

			<!-- 练习界面 -->
			<view class="practice-content" v-else>
				<!-- 进度条 -->
				<view class="progress-header">
					<view class="progress-info">
						<text>{{currentQuestionIndex + 1}} / {{currentPractice.questions.length}}</text>
					</view>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
					</view>
				</view>

				<!-- 音频播放器 -->
				<view class="audio-player">
					<view class="audio-controls">
						<view class="play-btn" @click="togglePlay">
							<text class="iconfont" :class="isPlaying ? 'icon-pause' : 'icon-play'"></text>
						</view>
						<view class="audio-info">
							<view class="audio-title">{{currentQuestion.title}}</view>
							<view class="audio-time">{{formatTime(currentTime)}} / {{formatTime(duration)}}</view>
						</view>
						<view class="speed-btn" @click="changeSpeed">
							<text>{{playSpeed}}x</text>
						</view>
					</view>
					<view class="audio-progress">
						<view class="progress-track" @click="seekTo">
							<view class="progress-played" :style="{ width: audioProgressPercent + '%' }"></view>
						</view>
					</view>
				</view>

				<!-- 题目 -->
				<view class="question-content">
					<view class="question-title">{{currentQuestion.question}}</view>
					<view class="question-options">
						<view 
							class="option-item" 
							v-for="(option, index) in currentQuestion.options" 
							:key="index"
							:class="{ 
								selected: selectedAnswer === index,
								correct: showResult && index === currentQuestion.correctAnswer,
								wrong: showResult && selectedAnswer === index && index !== currentQuestion.correctAnswer
							}"
							@click="selectAnswer(index)"
						>
							<view class="option-label">{{String.fromCharCode(65 + index)}}</view>
							<view class="option-text">{{option}}</view>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-buttons">
					<button class="btn-secondary" @click="prevQuestion" :disabled="currentQuestionIndex === 0">
						上一题
					</button>
					<button class="btn-primary" @click="nextQuestion" v-if="!isLastQuestion">
						下一题
					</button>
					<button class="btn-primary" @click="finishPractice" v-else>
						完成练习
					</button>
				</view>
			</view>

			<!-- 结果页面 -->
			<view class="result-page" v-if="showResult && practiceFinished">
				<view class="result-header">
					<view class="result-score">{{finalScore}}</view>
					<view class="result-text">分</view>
				</view>
				<view class="result-details">
					<view class="detail-item">
						<text class="detail-label">正确率</text>
						<text class="detail-value">{{correctRate}}%</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">用时</text>
						<text class="detail-value">{{practiceTime}}</text>
					</view>
				</view>
				<view class="result-actions">
					<button class="btn-secondary" @click="reviewAnswers">查看解析</button>
					<button class="btn-primary" @click="restartPractice">重新练习</button>
				</view>
			</view>
		</view>
	</gui-page>
</template>

<script>
export default {
	data() {
		return {
			pageLoading: false,
			groupId: '',
			practiceId: '',
			currentPractice: null,
			currentQuestionIndex: 0,
			selectedAnswer: null,
			showResult: false,
			practiceFinished: false,
			answers: [],
			startTime: null,
			
			// 音频相关
			audioContext: null,
			isPlaying: false,
			currentTime: 0,
			duration: 0,
			playSpeed: 1.0,
			
			practiceList: [
				{
					id: 1,
					title: '日常对话练习',
					description: '基础日常对话听力训练',
					duration: '10分钟',
					difficulty: 'easy',
					bestScore: 85
				},
				{
					id: 2,
					title: '购物场景对话',
					description: '商店购物相关对话练习',
					duration: '15分钟',
					difficulty: 'medium',
					bestScore: null
				},
				{
					id: 3,
					title: '新闻听力练习',
					description: '简单新闻内容听力理解',
					duration: '20分钟',
					difficulty: 'hard',
					bestScore: 78
				}
			]
		}
	},
	computed: {
		currentQuestion() {
			if (!this.currentPractice || !this.currentPractice.questions) return null;
			return this.currentPractice.questions[this.currentQuestionIndex];
		},
		progressPercent() {
			if (!this.currentPractice) return 0;
			return ((this.currentQuestionIndex + 1) / this.currentPractice.questions.length) * 100;
		},
		audioProgressPercent() {
			if (this.duration === 0) return 0;
			return (this.currentTime / this.duration) * 100;
		},
		isLastQuestion() {
			if (!this.currentPractice) return false;
			return this.currentQuestionIndex === this.currentPractice.questions.length - 1;
		},
		finalScore() {
			if (this.answers.length === 0) return 0;
			const correct = this.answers.filter(answer => answer.isCorrect).length;
			return Math.round((correct / this.answers.length) * 100);
		},
		correctRate() {
			return this.finalScore;
		},
		practiceTime() {
			if (!this.startTime) return '0分0秒';
			const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
			const minutes = Math.floor(elapsed / 60);
			const seconds = elapsed % 60;
			return `${minutes}分${seconds}秒`;
		}
	},
	onLoad(options) {
		if (options.groupId) {
			this.groupId = options.groupId;
		}
		if (options.practiceId) {
			this.practiceId = options.practiceId;
			this.loadSpecificPractice(options.practiceId);
		}
	},
	methods: {
		// 开始练习
		startPractice(practice) {
			this.currentPractice = {
				...practice,
				questions: this.generateQuestions(practice.id)
			};
			this.currentQuestionIndex = 0;
			this.selectedAnswer = null;
			this.showResult = false;
			this.practiceFinished = false;
			this.answers = [];
			this.startTime = Date.now();
		},

		// 生成题目（模拟数据）
		generateQuestions(practiceId) {
			// 这里应该从API获取真实的题目数据
			return [
				{
					id: 1,
					title: '对话1',
					audioUrl: '/static/audio/dialog1.mp3',
					question: '对话中提到的时间是？',
					options: ['上午9点', '下午2点', '晚上7点', '中午12点'],
					correctAnswer: 1
				},
				{
					id: 2,
					title: '对话2',
					audioUrl: '/static/audio/dialog2.mp3',
					question: '说话者要去哪里？',
					options: ['学校', '图书馆', '超市', '医院'],
					correctAnswer: 2
				}
			];
		},

		// 选择答案
		selectAnswer(index) {
			if (this.showResult) return;
			this.selectedAnswer = index;
		},

		// 下一题
		nextQuestion() {
			if (this.selectedAnswer === null) {
				uni.showToast({
					title: '请选择答案',
					icon: 'none'
				});
				return;
			}

			// 记录答案
			this.answers.push({
				questionId: this.currentQuestion.id,
				selectedAnswer: this.selectedAnswer,
				correctAnswer: this.currentQuestion.correctAnswer,
				isCorrect: this.selectedAnswer === this.currentQuestion.correctAnswer
			});

			if (this.isLastQuestion) {
				this.finishPractice();
			} else {
				this.currentQuestionIndex++;
				this.selectedAnswer = null;
				this.showResult = false;
			}
		},

		// 上一题
		prevQuestion() {
			if (this.currentQuestionIndex > 0) {
				this.currentQuestionIndex--;
				// 恢复之前的答案
				const prevAnswer = this.answers[this.currentQuestionIndex];
				this.selectedAnswer = prevAnswer ? prevAnswer.selectedAnswer : null;
			}
		},

		// 完成练习
		finishPractice() {
			if (this.selectedAnswer !== null) {
				this.answers.push({
					questionId: this.currentQuestion.id,
					selectedAnswer: this.selectedAnswer,
					correctAnswer: this.currentQuestion.correctAnswer,
					isCorrect: this.selectedAnswer === this.currentQuestion.correctAnswer
				});
			}
			
			this.practiceFinished = true;
			this.showResult = true;
		},

		// 重新练习
		restartPractice() {
			this.currentQuestionIndex = 0;
			this.selectedAnswer = null;
			this.showResult = false;
			this.practiceFinished = false;
			this.answers = [];
			this.startTime = Date.now();
		},

		// 查看解析
		reviewAnswers() {
			// 跳转到答案解析页面
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},

		// 音频控制
		togglePlay() {
			this.isPlaying = !this.isPlaying;
			// 这里应该控制实际的音频播放
		},

		changeSpeed() {
			const speeds = [1.0, 1.25, 1.5, 0.75];
			const currentIndex = speeds.indexOf(this.playSpeed);
			this.playSpeed = speeds[(currentIndex + 1) % speeds.length];
		},

		seekTo(e) {
			// 音频进度控制
		},

		formatTime(seconds) {
			const mins = Math.floor(seconds / 60);
			const secs = Math.floor(seconds % 60);
			return `${mins}:${secs.toString().padStart(2, '0')}`;
		},

		getDifficultyText(difficulty) {
			const map = {
				'easy': '简单',
				'medium': '中等',
				'hard': '困难'
			};
			return map[difficulty] || '未知';
		},

		loadSpecificPractice(practiceId) {
			const practice = this.practiceList.find(p => p.id == practiceId);
			if (practice) {
				this.startPractice(practice);
			}
		}
	}
}
</script>

<style scoped>
.practice-list {
	padding: 30rpx;
}

.list-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.header-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.header-desc {
	font-size: 28rpx;
	color: #666;
}

.practice-item {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.item-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.item-icon .iconfont {
	color: #fff;
	font-size: 36rpx;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
}

.item-meta {
	display: flex;
	gap: 15rpx;
}

.meta-duration {
	font-size: 24rpx;
	color: #999;
}

.meta-difficulty {
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.meta-difficulty.easy {
	background: #e8f5e8;
	color: #52c41a;
}

.meta-difficulty.medium {
	background: #fff7e6;
	color: #fa8c16;
}

.meta-difficulty.hard {
	background: #fff2f0;
	color: #ff4d4f;
}

.item-status {
	margin-left: 20rpx;
}

.status-score {
	font-size: 28rpx;
	font-weight: bold;
	color: #2094CE;
}

.status-new {
	font-size: 24rpx;
	color: #fff;
	background: #ff4d4f;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.practice-content {
	padding: 30rpx;
}

.progress-header {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.progress-info {
	text-align: center;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.progress-bar {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: #2094CE;
	transition: width 0.3s;
}

.audio-player {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.audio-controls {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.play-btn {
	width: 80rpx;
	height: 80rpx;
	background: #2094CE;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.play-btn .iconfont {
	color: #fff;
	font-size: 36rpx;
}

.audio-info {
	flex: 1;
}

.audio-title {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 8rpx;
}

.audio-time {
	font-size: 24rpx;
	color: #999;
}

.speed-btn {
	padding: 10rpx 20rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
}

.audio-progress {
	height: 6rpx;
	background: #f0f0f0;
	border-radius: 3rpx;
	overflow: hidden;
}

.progress-track {
	height: 100%;
	position: relative;
}

.progress-played {
	height: 100%;
	background: #2094CE;
}

.question-content {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.question-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.option-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 15rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.option-item.selected {
	border-color: #2094CE;
	background: #f0f9ff;
}

.option-item.correct {
	border-color: #52c41a;
	background: #f6ffed;
}

.option-item.wrong {
	border-color: #ff4d4f;
	background: #fff2f0;
}

.option-label {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 20rpx;
}

.option-item.selected .option-label {
	background: #2094CE;
	color: #fff;
}

.option-text {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}

.btn-secondary, .btn-primary {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}

.btn-secondary {
	background: #f0f0f0;
	color: #666;
}

.btn-primary {
	background: #2094CE;
	color: #fff;
}

.result-page {
	text-align: center;
	padding: 60rpx 30rpx;
}

.result-header {
	margin-bottom: 60rpx;
}

.result-score {
	font-size: 120rpx;
	font-weight: bold;
	color: #2094CE;
}

.result-text {
	font-size: 48rpx;
	color: #666;
}

.result-details {
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 32rpx;
	color: #666;
}

.detail-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.result-actions {
	display: flex;
	gap: 20rpx;
}
</style>
