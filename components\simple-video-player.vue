<template>
	<view class="simple-video-container">
		<!-- 视频播放器 -->
		<video 
			:id="playerId"
			:src="src"
			:poster="poster"
			:controls="true"
			:autoplay="autoplay"
			:show-center-play-btn="true"
			:enable-play-gesture="true"
			:object-fit="'contain'"
			@play="handlePlay"
			@pause="handlePause"
			@ended="handleEnded"
			@timeupdate="handleTimeUpdate"
			@error="handleError"
			@fullscreenchange="handleFullscreenChange"
			class="simple-video-player"
		/>
		
		<!-- 加载状态 -->
		<view class="simple-loading" v-if="isLoading">
			<view class="simple-spinner"></view>
			<text>加载中...</text>
		</view>
		
		<!-- 错误状态 -->
		<view class="simple-error" v-if="hasError" @click="retry">
			<text class="error-icon">⚠️</text>
			<text>播放失败，点击重试</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SimpleVideoPlayer',
	props: {
		src: String,
		poster: String,
		autoplay: {
			type: Boolean,
			default: false
		},
		videoId: [String, Number],
		courseId: [String, Number]
	},
	data() {
		return {
			playerId: `simple_video_${Date.now()}`,
			videoContext: null,
			isLoading: false,
			hasError: false,
			isPlaying: false,
			currentTime: 0,
			duration: 0
		};
	},
	mounted() {
		this.initVideoContext();
	},
	methods: {
		initVideoContext() {
			this.$nextTick(() => {
				try {
					this.videoContext = uni.createVideoContext(this.playerId, this);
				} catch (e) {
					console.warn('初始化视频上下文失败:', e);
				}
			});
		},
		
		handlePlay() {
			this.isPlaying = true;
			this.isLoading = false;
			this.$emit('play');
		},
		
		handlePause() {
			this.isPlaying = false;
			this.$emit('pause');
		},
		
		handleEnded() {
			this.isPlaying = false;
			this.$emit('ended');
		},
		
		handleTimeUpdate(e) {
			if (!e || !e.detail) return;
			
			this.currentTime = e.detail.currentTime;
			this.duration = e.detail.duration;
			
			this.$emit('timeupdate', {
				currentTime: this.currentTime,
				duration: this.duration
			});
		},
		
		handleError(e) {
			this.hasError = true;
			this.isLoading = false;
			this.isPlaying = false;
			console.error('视频播放错误:', e);
			this.$emit('error', e);
		},
		
		handleFullscreenChange(e) {
			this.$emit('fullscreenchange', e);
		},
		
		retry() {
			this.hasError = false;
			this.isLoading = true;
			
			this.$nextTick(() => {
				if (this.videoContext) {
					try {
						this.videoContext.play();
					} catch (e) {
						console.warn('重试播放失败:', e);
					}
				}
			});
		},
		
		// 公开的控制方法
		play() {
			if (this.videoContext) {
				this.videoContext.play();
			}
		},
		
		pause() {
			if (this.videoContext) {
				this.videoContext.pause();
			}
		},
		
		seek(time) {
			if (this.videoContext) {
				this.videoContext.seek(time);
			}
		}
	},
	
	beforeDestroy() {
		if (this.videoContext) {
			try {
				this.videoContext.pause();
			} catch (e) {
				// 忽略错误
			}
			this.videoContext = null;
		}
	}
};
</script>

<style scoped>
.simple-video-container {
	position: relative;
	width: 100%;
	background: #000;
	border-radius: 8rpx;
	overflow: hidden;
}

.simple-video-player {
	width: 100%;
	height: 400rpx;
	display: block;
}

.simple-loading, .simple-error {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.8);
	color: #fff;
	font-size: 28rpx;
}

.simple-spinner {
	width: 50rpx;
	height: 50rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
	border-top: 3rpx solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

.error-icon {
	font-size: 50rpx;
	margin-bottom: 15rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
