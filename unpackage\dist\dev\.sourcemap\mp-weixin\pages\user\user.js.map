{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/user/user.vue?a296", "webpack:///D:/日语云课/pages/user/user.vue?46de", "webpack:///D:/日语云课/pages/user/user.vue?eda3", "webpack:///D:/日语云课/pages/user/user.vue?bb69", "uni-app:///pages/user/user.vue", "webpack:///D:/日语云课/pages/user/user.vue?bbc4", "webpack:///D:/日语云课/pages/user/user.vue?8891"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "moveY", "pageAtTop", "components", "listCell", "data", "ver", "coverTransform", "coverTransition", "moving", "user", "is_login", "onLoad", "onShow", "uni", "title", "content", "success", "url", "methods", "navTo", "coverTouchstart", "startY", "coverTouchmove", "moveDistance", "coverTouchend", "login", "self", "out", "_this", "reload", "console", "curPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAklB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkEtmB;AAEA;EAAA;IAAA;EAAA;AAAA;AACA;EAAAC;EAAAC;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,2BACA;EAqBAC;IACA;IAEA;MACAC;QACAC;QACAC;QACAC;UACA;YACAH;cACAI;YACA;UACA;YACA;AACA;AACA;UAFA;QAIA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC;IAEA;AACA;AACA;AACA;IACAC;MACAN;QACAI;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAG;MACA;QACA;MACA;MACA;MACAC;IACA;IACAC;MACAtB;MACA;MACA;QACA;QACA;MACA;MACA;MACA;QACAuB;MACA;MAEA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAZ;UACAC;UACAC;UACAC;YACA;cACAU;cACAb;gBACAI;cACA;YACA;cACAJ;YACA;UACA;QACA;MACA;QACAA;UACAI;QACA;MACA;IACA;IACAU;MACA;MACAd;QACAC;QACAC;QACAC;UACA;YACAY;YACAA;YACAA;UAGA,wBAEA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACAC;MACA;MACAC;MACAA;MACAA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAAinC,CAAgB,6lCAAG,EAAC,C;;;;;;;;;;;ACAroC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/user.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/user.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./user.vue?vue&type=template&id=80842834&\"\nvar renderjs\nimport script from \"./user.vue?vue&type=script&lang=js&\"\nexport * from \"./user.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/user.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=template&id=80842834&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=script&lang=js&\"", "<template>  \r\n    <view>  \r\n\t\t\r\n\t\t<view class=\"user-section\">\r\n\t\t\t<image class=\"bg\" src=\"/static/user-bg.jpg\"></image>\r\n\t\t\t<view class=\"user-info-box\" @tap=\"login()\">\r\n\t\t\t\t<view class=\"portrait-box\">\r\n\t\t\t\t\t<image class=\"portrait\" :src=\"user.userInfo.avatar || '/static/missing-face.png'\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-box\" style=\"margin-left: 40rpx;text-align: left;\">\r\n\t\t\t\t\t<view class=\"username\">{{user.userInfo.name || '暂未登录'}}</view>\r\n\t\t\t\t\t<view class=\"sign\" v-if=\"!is_login\">点击立即登录</view>\r\n\t\t\t\t\t<view class=\"vip-box\">\r\n\t\t\t\t\t\t<text class=\"vip\" v-if=\"user.member && user.member.status==1\">VIP <text style=\"font-size: 20rpx;\"> 到期日期 {{user.member.end_date}}</text> </text>\r\n\t\t\t\t\t\t<!-- <text class=\"vip\" v-else @tap=\"goAuth\">报名成为会员</text> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view \r\n\t\t\tclass=\"cover-container\"\r\n\t\t\t:style=\"[{\r\n\t\t\t\ttransform: coverTransform,\r\n\t\t\t\ttransition: coverTransition\r\n\t\t\t}]\"\r\n\t\t\t@touchstart=\"coverTouchstart\"\r\n\t\t\t@touchmove=\"coverTouchmove\"\r\n\t\t\t@touchend=\"coverTouchend\"\r\n\t\t>\r\n\t\t\t<image class=\"arc\" src=\"/static/arc.png\"></image>\r\n\t\t\t<!-- <view class=\"tj-sction\">\r\n\t\t\t\t<view class=\"tj-item\">\r\n\t\t\t\t\t<text class=\"num\">{{user.member ? user.member.course_num : 0}}</text>\r\n\t\t\t\t\t<text>已学课程数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tj-item\">\r\n\t\t\t\t\t<text class=\"num\">{{user.member ? user.member.trans_num : 0}}</text>\r\n\t\t\t\t\t<text>已翻译数</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<!-- <view class=\"tj-item\">\r\n\t\t\t\t\t<text class=\"num\">20</text>\r\n\t\t\t\t\t<text>代金券</text>\r\n\t\t\t\t</view> -->\r\n\t\t\t<!-- </view> -->\r\n\t\t\t<!-- 浏览历史 -->\r\n\t\t\t<view class=\"history-section icon\">\r\n\t\t\t\t<list-cell icon=\"icon-gouwuche_\" iconColor=\"#e07472\" title=\"我的订单\" @eventClick=\"navTo('/pages/order/list')\"></list-cell>\r\n\t\t\t\t<!-- <list-cell icon=\"icon-iconfontweixin\" iconColor=\"#e07472\" title=\"我的课程\" @eventClick=\"navTo('/pages/projects/list')\"></list-cell> -->\r\n\t\t\t\t<!-- <list-cell icon=\"icon-iLinkapp-\" iconColor=\"#5fcda2\" title=\"我的作业\" @eventClick=\"navTo('/pages/article/list')\"></list-cell> -->\r\n\t\t\t\t<!-- <list-cell icon=\"icon-saomiao\" iconColor=\"#e07472\" title=\"证书下载\" @eventClick=\"navTo('/pages/certificate/certificate')\"></list-cell> -->\r\n\t\t\t\t<list-cell icon=\"icon-share\" iconColor=\"#9789f7\" title=\"我的兑换\" @eventClick=\"navTo('/pages/my-exchange/my-exchange')\"></list-cell>\r\n\t\t\t\t<!-- <list-cell icon=\"icon-saomiao\" iconColor=\"#9789f7\" title=\"我的推广\" @eventClick=\"navTo('/pages/popularize/popularize')\"></list-cell>\r\n\t\t\t\t -->\r\n\t\t\t\t <list-cell icon=\"icon-shoucang\" iconColor=\"#e07472\" title=\"我的收藏\" @eventClick=\"navTo('/pages/order/collect')\"></list-cell> \r\n\t\t\t\t <!-- <list-cell icon=\".icon-dianhua-copy\" iconColor=\"#9789f7\" title=\"客服中心\" @eventClick=\"navTo('/pages/service/service')\"></list-cell> -->\r\n\t\t\t\t<list-cell icon=\"icon-pinglun-copy\" iconColor=\"#e07472\" title=\"联系我们\" @eventClick=\"navTo('/pages/about-us/about-us')\"></list-cell>\r\n\t\t\t\t<list-cell icon=\"icon-huifu\" iconColor=\"#e07472\" title=\"退出登录\" @tap=\"out()\"></list-cell>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\t\r\n\t\t\r\n    </view>  \r\n</template>  \r\n<script>  \r\n\timport listCell from '@/components/mix-list-cell';\r\n    import {  \r\n        mapState \r\n    } from 'vuex';  \r\n\tlet startY = 0, moveY = 0, pageAtTop = true;\r\n    export default {\r\n\t\tcomponents: {\r\n\t\t\tlistCell\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tver: this.$store.state.ver,\r\n\t\t\t\tcoverTransform: 'translateY(0px)',\r\n\t\t\t\tcoverTransition: '0s',\r\n\t\t\t\tmoving: false,\r\n\t\t\t\tuser: null,\r\n\t\t\t\tis_login:false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(){\r\n\t\t},\r\n\t\t// #ifndef MP\r\n\t\tonNavigationBarButtonTap(e) {\r\n\t\t\tconst index = e.index;\r\n\t\t\tif (index === 0) {\r\n\t\t\t\tthis.navTo('/pages/set/set');\r\n\t\t\t}else if(index === 1){\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tconst pages = getCurrentPages();\r\n\t\t\t\tconst page = pages[pages.length - 1];\r\n\t\t\t\tconst currentWebview = page.$getAppWebview();\r\n\t\t\t\tcurrentWebview.hideTitleNViewButtonRedDot({\r\n\t\t\t\t\tindex\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/notice/notice'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #endif\r\n\t\t\tonShow() {\r\n\t\t\t\tthis.user = this.$store.state.user;\r\n\t\t\t\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t/* uni.switchtab({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t\t\t\t\t}) */\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.is_login = true;\r\n\t\t\t\t\tthis.$store.dispatch('refreshUserMember');\r\n\t\t\t\t}\r\n\t\t},\r\n        methods: {\r\n\r\n\t\t\t/**\r\n\t\t\t * 统一跳转接口,拦截未登录路由\r\n\t\t\t * navigator标签现在默认没有转场动画，所以用view\r\n\t\t\t */\r\n\t\t\tnavTo(url){\r\n\t\t\t\tuni.navigateTo({  \r\n\t\t\t\t\turl\r\n\t\t\t\t})  \r\n\t\t\t}, \r\n\t\r\n\t\t\t/**\r\n\t\t\t *  会员卡下拉和回弹\r\n\t\t\t *  1.关闭bounce避免ios端下拉冲突\r\n\t\t\t *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉\r\n\t\t\t *    transition设置0.1秒延迟，让css来过渡这段空窗期\r\n\t\t\t *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/\r\n\t\t\t */\r\n\t\t\tcoverTouchstart(e){\r\n\t\t\t\tif(pageAtTop === false){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.coverTransition = 'transform .1s linear';\r\n\t\t\t\tstartY = e.touches[0].clientY;\r\n\t\t\t},\r\n\t\t\tcoverTouchmove(e){\r\n\t\t\t\tmoveY = e.touches[0].clientY;\r\n\t\t\t\tlet moveDistance = moveY - startY;\r\n\t\t\t\tif(moveDistance < 0){\r\n\t\t\t\t\tthis.moving = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.moving = true;\r\n\t\t\t\tif(moveDistance >= 80 && moveDistance < 100){\r\n\t\t\t\t\tmoveDistance = 80;\r\n\t\t\t\t}\r\n\t\t\r\n\t\t\t\tif(moveDistance > 0 && moveDistance <= 80){\r\n\t\t\t\t\tthis.coverTransform = `translateY(${moveDistance}px)`;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcoverTouchend(){\r\n\t\t\t\tif(this.moving === false){\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.moving = false;\r\n\t\t\t\tthis.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)';\r\n\t\t\t\tthis.coverTransform = 'translateY(0px)';\r\n\t\t\t},\r\n\t\t\tlogin(){\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tself.reload = true;\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: \"/pages/userinfo/userinfo\",\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tout(){\r\n\t\t\t\tvar _this = this;\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '退出登录',\r\n\t\t\t\t\tcontent: '请确认是否退出登录当前账号',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t_this.$store.state.user.token= null;\r\n\t\t\t\t\t\t\t_this.$store.state.user.userInfo= null;\r\n\t\t\t\t\t\t\t_this.reload();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\treload() {\r\n\t\t\t        // 页面重载\r\n\t\t\t        const pages = getCurrentPages()\r\n\t\t\t        // 声明一个pages使用getCurrentPages方法\r\n\t\t\t        const curPage = pages[pages.length - 1]\r\n\t\t\t\t\tconsole.log(curPage);\r\n\t\t\t        // 声明一个当前页面\r\n\t\t\t        curPage.onLoad(curPage.options) // 传入参数\r\n\t\t\t        curPage.onShow()\r\n\t\t\t        curPage.onReady()\r\n\t\t\t        // 执行刷新\r\n\t\t\t    },\r\n\t\t\t\r\n        }  \r\n    }  \r\n</script>  \r\n<style lang='scss'>\r\n\tpage{\r\n\t\tbackground-color: #f8f8f8;\r\n\t}\r\n\t%flex-center {\r\n\t display:flex;\r\n\t flex-direction: column;\r\n\t justify-content: center;\r\n\t align-items: center;\r\n\t}\r\n\t%section {\r\n\t  display:flex;\r\n\t  justify-content: space-around;\r\n\t  align-content: center;\r\n\t  background: #fff;\r\n\t  border-radius: 10upx;\r\n\t}\r\n\r\n\t.user-section{\r\n\t\theight: 520upx;\r\n\t\tpadding: 100upx 30upx 0;\r\n\t\tposition:relative;\r\n\t\t.bg{\r\n\t\t\tposition:absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tfilter: blur(1px);\r\n\t\t\topacity: .7;\r\n\t\t}\r\n\t}\r\n\t.user-info-box{\r\n\t\theight: 180upx;\r\n\t\tdisplay:flex;\r\n\t\talign-items:center;\r\n\t\tposition:relative;\r\n\t\tz-index: 1;\r\n\t\t.portrait{\r\n\t\t\twidth: 130upx;\r\n\t\t\theight: 130upx;\r\n\t\t\tborder:5upx solid #fff;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t.username{\r\n\t\t\tfont-size: $font-lg + 6upx;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tmargin-left: 20upx;\r\n\t\t}\r\n\t}\r\n\r\n\t.vip-card-box{\r\n\t\tdisplay:flex;\r\n\t\tflex-direction: column;\r\n\t\tcolor: #f7d680;\r\n\t\theight: 240upx;\r\n\t\tbackground: linear-gradient(left, rgba(0,0,0,.7), rgba(0,0,0,.8));\r\n\t\tborder-radius: 16upx 16upx 0 0;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t\tpadding: 20upx 24upx;\r\n\t\t.card-bg{\r\n\t\t\tposition:absolute;\r\n\t\t\ttop: 20upx;\r\n\t\t\tright: 0;\r\n\t\t\twidth: 380upx;\r\n\t\t\theight: 260upx;\r\n\t\t}\r\n\t\t.b-btn{\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 20upx;\r\n\t\t\ttop: 16upx;\r\n\t\t\twidth: 132upx;\r\n\t\t\theight: 40upx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 40upx;\r\n\t\t\tfont-size: 22upx;\r\n\t\t\tcolor: #36343c;\r\n\t\t\tborder-radius: 20px;\r\n\t\t\tbackground: linear-gradient(left, #f9e6af, #ffd465);\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\t\t.tit{\r\n\t\t\tfont-size: $font-base+2upx;\r\n\t\t\tcolor: #f7d680;\r\n\t\t\tmargin-bottom: 28upx;\r\n\t\t\t.yticon{\r\n\t\t\t\tcolor: #f6e5a3;\r\n\t\t\t\tmargin-right: 16upx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.e-b{\r\n\t\t\tfont-size: $font-sm;\r\n\t\t\tcolor: #d8cba9;\r\n\t\t\tmargin-top: 10upx;\r\n\t\t}\r\n\t}\r\n\t.cover-container{\r\n\t\tbackground: $page-color-base;\r\n\t\tmargin-top: -150upx;\r\n\t\tpadding: 0 30upx;\r\n\t\tposition:relative;\r\n\t\theight: 100%;\r\n\t\t/* background: #f8f8f8; */\r\n\t\tpadding-bottom: 20upx;\r\n\t\t.arc{\r\n\t\t\tposition:absolute;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: -34upx;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 36upx;\r\n\t\t}\r\n\t}\r\n\t.tj-sction{\r\n\t\t@extend %section;\r\n\t\t.tj-item{\r\n\t\t\t@extend %flex-center;\r\n\t\t\tflex-direction: column;\r\n\t\t\theight: 140upx;\r\n\t\t\tfont-size: $font-sm;\r\n\t\t\tcolor: #75787d;\r\n\t\t}\r\n\t\t.num{\r\n\t\t\tfont-size: $font-lg;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tmargin-bottom: 8upx;\r\n\t\t}\r\n\t}\r\n\t.order-section{\r\n\t\t@extend %section;\r\n\t\tpadding: 28upx 0;\r\n\t\tmargin-top: 20upx;\r\n\t\t.order-item{\r\n\t\t\t@extend %flex-center;\r\n\t\t\twidth: 120upx;\r\n\t\t\theight: 120upx;\r\n\t\t\tborder-radius: 10upx;\r\n\t\t\tfont-size: $font-sm;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t}\r\n\t\t.yticon{\r\n\t\t\tfont-size: 48upx;\r\n\t\t\tmargin-bottom: 18upx;\r\n\t\t\tcolor: #fa436a;\r\n\t\t}\r\n\t\t.icon-shouhoutuikuan{\r\n\t\t\tfont-size:44upx;\r\n\t\t}\r\n\t}\r\n\t.history-section{\r\n\t\tpadding: 30upx 0 0;\r\n\t\tmargin-top: 20upx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius:10upx;\r\n\t\t.sec-header{\r\n\t\t\tdisplay:flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: $font-base;\r\n\t\t\tcolor: $font-color-dark;\r\n\t\t\tline-height: 40upx;\r\n\t\t\tmargin-left: 30upx;\r\n\t\t\t.yticon{\r\n\t\t\t\tfont-size: 44upx;\r\n\t\t\t\tcolor: #5eba8f;\r\n\t\t\t\tmargin-right: 16upx;\r\n\t\t\t\tline-height: 40upx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.h-list{\r\n\t\t\twhite-space: nowrap;\r\n\t\t\tpadding: 30upx 30upx 0;\r\n\t\t\timage{\r\n\t\t\t\tdisplay:inline-block;\r\n\t\t\t\twidth: 160upx;\r\n\t\t\t\theight: 160upx;\r\n\t\t\t\tmargin-right: 20upx;\r\n\t\t\t\tborder-radius: 10upx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.vip-box {}\r\n\t\r\n\t.vip {\r\n\t\tposition: absolute;\r\n\t\tfont-size: 28rpx;\r\n\t\tbackground: #F59A23;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 5rpx 10rpx;\r\n\t\tborder-radius: 10rpx\r\n\t}\r\n</style>", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662929634\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}