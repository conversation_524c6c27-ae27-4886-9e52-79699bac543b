<template>
	<view class="root-box" :class="{noauth:!permission}">
		<view class="content-box">
			<slot></slot>
		</view>
		<navigator class="noauth-content-box lp-flex-center" :url="auth_url" v-if="!permission">
			<view class="mask-box">
				<image class="mask" src="@/static/imgs/auth_content_mask.png"></image>
			</view>
			<view class="btn-box">
				<view class="btn">
					<text class="gui-icons">&#xe687;</text>
					<text style="margin: 0 10rpx;">加入会员可查看</text>
					<text class="gui-icons">&#xe601;</text>
				</view>
			</view>
		</navigator>
	</view>
</template>

<script>
	export default {
		props: {
			permission: {
				type: Boolean,
				default: false
			},
			auth_url: {
				type: String,
				default: '/pages/index/index'
			}
		},
		data() {
			return {

			};
		},
		methods: {
			//-----------------------------------------------------------------------------------------------
			//
			// action
			//
			//-----------------------------------------------------------------------------------------------

			//-----------------------------------------------------------------------------------------------
			//
			// handler
			//
			//-----------------------------------------------------------------------------------------------
			onTapHandler: function() {
				
			},
		}
	}
</script>

<style lang="scss" scoped>
	.noauth {
		height: 300rpx;
		overflow: hidden;
	}

	.root-box {
		position: relative;

		.content-box {
			position: relative;
			z-index: 0;
		}

		.noauth-content-box {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			z-index: 2;

			.mask-box {
				position: absolute;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;

				.mask {
					width: 100%;
					height: 100%;
				}
			}

			.btn-box {
				position: relative;
				z-index: 3;

				.btn {
					background-color: #F59A23;
					color: #fff;
					padding: 20rpx 30rpx;
					border-radius: 40rpx;
					font-size: 24rpx;
				}
			}
		}


	}
</style>
