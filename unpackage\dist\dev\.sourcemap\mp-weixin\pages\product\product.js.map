{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/日语云课/pages/product/product.vue?a73c", "webpack:///D:/日语云课/pages/product/product.vue?1da0", "webpack:///D:/日语云课/pages/product/product.vue?f67b", "webpack:///D:/日语云课/pages/product/product.vue?9264", "uni-app:///pages/product/product.vue", "webpack:///D:/日语云课/pages/product/product.vue?64ea", "webpack:///D:/日语云课/pages/product/product.vue?f2df"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uParse", "data", "project_id", "project", "specClass", "specSelected", "favorite", "shareList", "imgList", "src", "desc", "specList", "id", "name", "specChildList", "pid", "form", "form_data", "is_show", "onLoad", "withShareTicket", "menus", "onShareAppMessage", "title", "imageUrl", "path", "onShareTimeline", "filters", "methods", "ready", "console", "onApplyHandler", "uni", "url", "apiGetDetail", "params", "toggleSpec", "setTimeout", "onFormSubmitHandler", "content", "success", "self", "icon", "provider", "fail", "onFormResetHandler", "apiPay", "money", "json_data", "goService", "goShare"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wLAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAAqlB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0LzmB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC,UACA;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EACA;MACAC,o2BAQA;MACAC,WACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC,gBACA;QACAF;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,GACA;QACAD;QACAG;QACAF;MACA,EACA;MACA;MACAG;MACA;MACAC,YAEA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;IACA;IACA;IACAzB;MACA0B;MACAC;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAH;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAE;EACAC;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;QACA;QACA;QACA;MACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAC;UACAvB;QACA;MACA;QACA;MACA;IACA;IACA;IACAwB;MAAA;MACA;QACA;QACAC;UACA;QACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;QACAN;UACAT;UACAgB;UACAC;YACA;cACAC;cACAT;gBACAC;cACA;YACA;cACAD;YACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;YAAA;UAAA;UACAF;UACA;YACA;cACAE;gBACAU;gBACAnB;cACA;cACA;YACA;UACA;QAEA;QACAO;QACA;QACA;QACA;QACAE;QACA;UACAF;UACA;UACAE;YACAW;UAAA,GACA1C;YACAuC;cACAR;cACAA;gBACAC;cACA;YACA;YACAW;cACAZ;cACAA;gBACAU;gBACAnB;cACA;cACAO;YACA;UAAA,GACA;QACA;MACA;IAEA;IACAe,mDAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACA5C;QACA6C;QACAC;MACA;QACA;MACA;IACA;IACAC;MACAjB;QACAC;MACA;IACA;IACAiB;MACAlB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACldA;AAAA;AAAA;AAAA;AAA4oC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAhqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/product/product.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/product/product.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./product.vue?vue&type=template&id=2fbdbe34&scoped=true&\"\nvar renderjs\nimport script from \"./product.vue?vue&type=script&lang=js&\"\nexport * from \"./product.vue?vue&type=script&lang=js&\"\nimport style0 from \"./product.vue?vue&type=style&index=0&id=2fbdbe34&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2fbdbe34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/product/product.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=template&id=2fbdbe34&scoped=true&\"", "var components\ntry {\n  components = {\n    guiPage: function () {\n      return import(\n        /* webpackChunkName: \"GraceUI5/components/gui-page\" */ \"@/GraceUI5/components/gui-page.vue\"\n      )\n    },\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-parse/u-parse\" */ \"@/uni_modules/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l2 = _vm.__map(_vm.form, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 =\n      item.value_type == \"radio\"\n        ? _vm.__map(\n            item.option_values.split(\"|\"),\n            function (option_value, index) {\n              var $orig = _vm.__get_orig(option_value)\n              var g0 =\n                _vm.form_data[item.field_name] != undefined\n                  ? _vm.form_data[item.field_name].includes(index)\n                  : null\n              return {\n                $orig: $orig,\n                g0: g0,\n              }\n            }\n          )\n        : null\n    var l1 =\n      item.value_type == \"checkbox\"\n        ? _vm.__map(\n            item.option_values.split(\"|\"),\n            function (option_value, __i0__) {\n              var $orig = _vm.__get_orig(option_value)\n              var g1 =\n                _vm.form_data[item.field_name] != undefined\n                  ? _vm.form_data[item.field_name].includes(option_value)\n                  : null\n              return {\n                $orig: $orig,\n                g1: g1,\n              }\n            }\n          )\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n      l1: l1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l2: l2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=script&lang=js&\"", "<template>\n\t<gui-page :fullPage=\"true\"  class=\"root-box lp-flex-column\">\n\t\t<view slot=\"gBody\" >\n\t\t\t<!-- 基本信息 -->\n\t\t\t<view class=\"panel base-box lp-flex-column\">\n\t\t\t\t<view class=\"cover-box\">\n\t\t\t\t\t<image  show-menu-by-longpress='1' class=\"cover\" :src=\"project.picture\" mode=\"scaleToFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"info-box lp-flex-column\">\n\t\t\t\t\t<view class=\"title-box lp-flex lp-flex-space-between\">\n\t\t\t\t\t\t<text class=\"title\">{{project.name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"lp-flex lp-flex-space-between\">\n\t\t\t\t\t\t<view>\n\t\t\t\t\t\t\t<text class=\"label\">课程时间：</text>\n\t\t\t\t\t\t\t<text class=\"course_date\">{{project.class_time}} 至 {{project.end_time}} </text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- <view>\n\t\t\t\t\t\t\t<text class=\"label\">状态：</text>\n\t\t\t\t\t\t\t<text>{{project.status_text}}</text>\n\t\t\t\t\t\t</view> -->\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\n\t\t\t\t</view>\n\t\t\t</view>\r\n\t\t\t\n\t\t\t<!-- 主讲老师 -->\n\t\t\t<!-- <view class=\"panel lp-flex-column teacher-box\">\n\t\t\t\t<view class=\"head\">主讲老师</view>\n\t\t\t\t<view class=\"body lp-flex-column\">\n\t\t\t\t\t<view v-for=\"(item,index) in project.author\" :key=\"item.id\" class=\"item lp-flex\">\n\t\t\t\t\t\t<view class=\"avatar-box\">\n\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.avatar\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"lp-flex-column info-box\">\n\t\t\t\t\t\t\t<text class=\"name\">{{item.name}} </text>\n\t\t\t\t\t\t\t<text class=\"job\">{{item.job}} </text>\n\t\t\t\t\t\t\t<text class=\"intro\">{{item.intro}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view> -->\n\n\t\t\t<!-- 课程内容 -->\n\t\t\t<view class=\"panel content-box\" >\n\t\t\t\t<view class=\"head\">课程详情</view>\n\t\t\t\t<view class=\"body\" style=\"text-align: center;\">\r\n\t\t\t\t\t<image  v-if=\"project.class_picture != null\" show-menu-by-longpress='1' :src=\"project.class_picture\"  mode=\"widthFix\"></image>\n\t\t\t\t\t<!-- <rich-text class=\"rich-text\" :nodes=\"project.content | formatRichText\"></rich-text>\r\n\t\t\t\t\t -->\r\n\t\t\t\t\t <u-parse :html=\"project.content\" ></u-parse>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 报名 -->\n\t\t\t<!-- <view class=\"btn-box lp-flex lp-flex-space-between\">\n\t\t\t\t<view class=\"info\">￥{{project.price}}</view>\r\n\t\t\t\t<view class=\"btn lp-flex-center\" v-if=\"project.is_over === false && project.sign===0\" @click=\"toggleSpec\">我要报名</view>\r\t\t\t\t<view class=\"btn lp-flex-center\" style=\"background-color: #e5e5e5;\" v-else-if=\"project.is_over === false && project.sign===1\">已报名</view>\r\n\t\t\t\t<view class=\"btn lp-flex-center\" style=\"background-color: #e5e5e5;\" v-else-if=\"project.is_over === true\">已结束</view>\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t<!-- 底部操作菜单 -->\r\n\t\t\t<view class=\"page-bottom\">\r\n\t\t\t\t<view class=\"p-b-btn\">\r\n\t\t\t\t\t<!-- <text class=\"yticon icon-dianhua-copy\"></text> -->\r\n\t\t\t\t\t<image src=\"/static/kf.png\" style=\"width: 50rpx;height: 50rpx;\"></image>\r\n\t\t\t\t\t<text>客服</text>\r\n\t\t\t\t\t<button class='contact-btn' open-type='contact'>a</button> \r\n\t\t\t\t</view>\r\n\t\t\t\t<view   class=\"p-b-btn\" style=\"margin-left: 10rpx;\">\r\n\t\t\t\t\t<!-- <text class=\"yticon icon-share\"></text> -->\r\n\t\t\t\t\t<text style=\"font-size: 35rpx;color:#FD6D24\">￥{{project.price}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"buy\" v-if=\"project.is_over === false && project.sign===0 && is_show=== true\" @click=\"toggleSpec\">\r\n\t\t\t\t     我要报名\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buy\" style=\"background-color: #e5e5e5;\" v-else-if=\"project.is_over === false && project.sign===1\">\r\n\t\t\t\t     已报名\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"buy\" style=\"background-color: #e5e5e5;\" v-else-if=\"project.is_over === true\">\r\n\t\t\t\t     已结束\r\n\t\t\t\t</view>\r\n\t\t\t</view>\n\t\t\t<!-- 规格-模态层弹窗 -->\r\n\t\t\t<view \r\n\t\t\t\tclass=\"popup spec\" \r\n\t\t\t\t:class=\"specClass\"\r\n\t\t\t\************************=\"stopPrevent\"\r\n\t\t\t\t@click=\"toggleSpec\"\r\n\t\t\t>\r\n\t\t\t\t<!-- 遮罩层 -->\r\n\t\t\t\t<view class=\"mask\"></view>\r\n\t\t\t\t<view class=\"layer attr-content\" @click.stop=\"stopPrevent\">\r\n\t\t\t\t\t<view class=\"a-t\">\r\n\t\t\t\t\t\t<image :src=\"project.picture\"></image>\r\n\t\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t\t<text class=\"title\">{{project.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"price\">¥{{project.price}}</text>\r\n\t\t\t\t\t\t\t<!-- <text class=\"stock\">库存：188件</text>\r\n\t\t\t\t\t\t\t<view class=\"selected\">\r\n\t\t\t\t\t\t\t\t已选：\r\n\t\t\t\t\t\t\t\t<text class=\"selected-text\" v-for=\"(sItem, sIndex) in specSelected\" :key=\"sIndex\">\r\n\t\t\t\t\t\t\t\t\t{{sItem.name}}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view v-for=\"(item,index) in specList\" :key=\"index\" class=\"attr-list\">\r\n\t\t\t\t\t\t<text>{{item.name}}</text>\r\n\t\t\t\t\t\t<view class=\"item-list\">\r\n\t\t\t\t\t\t\t<text \r\n\t\t\t\t\t\t\t\tv-for=\"(childItem, childIndex) in specChildList\" \r\n\t\t\t\t\t\t\t\tv-if=\"childItem.pid === item.id\"\r\n\t\t\t\t\t\t\t\t:key=\"childIndex\" class=\"tit\"\r\n\t\t\t\t\t\t\t\t:class=\"{selected: childItem.selected}\"\r\n\t\t\t\t\t\t\t\t@click=\"selectSpec(childIndex, childItem.pid)\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{{childItem.name}}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<scroll-view style=\"height: 800rpx;margin: 30rpx 0;\" scroll-y=\"true\">\r\n\t\t\t\t\t<form @submit=\"onFormSubmitHandler\" @reset=\"onFormResetHandler\">\r\n\t\t\t\t\t\t<view class=\"form lp-flex-column\">\r\n\t\t\t\t\t\t\t<template v-for=\"(item,index) in form\">\r\n\t\t\t\t\t\t\t\t<!-- 输入性文本 -->\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.value_type=='text'\" class=\"form-item lp-flex\" :key=\"item.id\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.is_show=='1'\" style=\"color: red;\">*</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else style=\"color: white;\">*</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"control-label\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"input-box\">\r\n\t\t\t\t\t\t\t\t\t\t<input :name=\"item.field_name\" v-model=\"form_data[item.field_name]\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- 单选 -->\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.value_type=='radio'\" class=\"form-item lp-flex\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.is_show=='1'\" style=\"color: red;\">*</view>\r\n\t\t\t\t\t\t\t\t\t<view v-else style=\"color: white;\">*</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"control-label\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"input-box\">\r\n\t\t\t\t\t\t\t\t\t\t<radio-group class=\"lp-flex-column\" :name=\"item.field_name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"(option_value,index) in item.option_values.split('|')\" :key=\"option_value\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio v-if=\"form_data[item.field_name]!=undefined\" :value=\"index\" :checked=\"form_data[item.field_name].includes(index)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{option_value}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</radio>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<radio v-else :value=\"index\" :checked=\"false\">{{option_value}}</radio>\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t</radio-group>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<!-- 多选 -->\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.value_type=='checkbox'\" class=\"form-item lp-flex\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.is_show=='1'\" style=\"color: red;\">*</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"control-label\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"input-box\">\r\n\t\t\t\t\t\t\t\t\t\t<checkbox-group class=\"lp-flex-column\" :name=\"item.field_name\">\r\n\t\t\t\t\t\t\t\t\t\t\t<label v-for=\"option_value in item.option_values.split('|')\" :key=\"option_value\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<checkbox v-if=\"form_data[item.field_name]!=undefined\" :value=\"option_value\" :checked=\"form_data[item.field_name].includes(option_value)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{option_value}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</checkbox>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<checkbox v-else :value=\"option_value\" :checked=\"false\">{{option_value}}</checkbox>\r\n\t\t\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t<!-- <view class=\"btn-box\"> -->\r\n\t\t\t\t\t\t\t\t<button form-type=\"submit\" class=\"btn1\">提交</button>\r\n\t\t\t\t\t\t\t<!-- </view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<!-- <button class=\"btn\" @click=\"toggleSpec\">完成</button> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <button class=\"kf_button\" open-type='contact'>\r\n\t\t\t <image class=\"kf_image\" src=\"/static/kefu.png\"></image>\r\n\t\t\t</button> -->\n\t\t</view>\n\t</gui-page>\n</template>\n\n<script>\r\n\timport uParse from \"@/components/uview-ui/components/u-parse/u-parse.vue\"; \n\texport default {\r\n\t\tcomponents: {\r\n\t\t    uParse\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tproject_id: 1,\n\t\t\t\tproject: null,\r\n\t\t\t\tspecClass: 'none',\r\n\t\t\t\tspecSelected:[],\r\n\t\t\t\t\r\n\t\t\t\tfavorite: true,\r\n\t\t\t\tshareList: [],\r\n\t\t\t\timgList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: 'https://gd3.alicdn.com/imgextra/i3/0/O1CN01IiyFQI1UGShoFKt1O_!!0-item_pic.jpg_400x400.jpg'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: 'https://gd3.alicdn.com/imgextra/i3/TB1RPFPPFXXXXcNXpXXXXXXXXXX_!!0-item_pic.jpg_400x400.jpg'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: 'https://gd2.alicdn.com/imgextra/i2/38832490/O1CN01IYq7gu1UGShvbEFnd_!!38832490.jpg_400x400.jpg'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tdesc: `\r\n\t\t\t\t\t<div style=\"width:100%\">\r\n\t\t\t\t\t\t<img style=\"width:100%;display:block;\" src=\"https://gd3.alicdn.com/imgextra/i4/479184430/O1CN01nCpuLc1iaz4bcSN17_!!479184430.jpg_400x400.jpg\" />\r\n\t\t\t\t\t\t<img style=\"width:100%;display:block;\" src=\"https://gd2.alicdn.com/imgextra/i2/479184430/O1CN01gwbN931iaz4TzqzmG_!!479184430.jpg_400x400.jpg\" />\r\n\t\t\t\t\t\t<img style=\"width:100%;display:block;\" src=\"https://gd3.alicdn.com/imgextra/i3/479184430/O1CN018wVjQh1iaz4aupv1A_!!479184430.jpg_400x400.jpg\" />\r\n\t\t\t\t\t\t<img style=\"width:100%;display:block;\" src=\"https://gd4.alicdn.com/imgextra/i4/479184430/O1CN01tWg4Us1iaz4auqelt_!!479184430.jpg_400x400.jpg\" />\r\n\t\t\t\t\t\t<img style=\"width:100%;display:block;\" src=\"https://gd1.alicdn.com/imgextra/i1/479184430/O1CN01Tnm1rU1iaz4aVKcwP_!!479184430.jpg_400x400.jpg\" />\r\n\t\t\t\t\t</div>\r\n\t\t\t\t`,\r\n\t\t\t\tspecList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\tname: '尺寸',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\t\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tname: '颜色',\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\tspecChildList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'XS',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'S',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'M',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'L',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 5,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'XL',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 6,\r\n\t\t\t\t\t\tpid: 1,\r\n\t\t\t\t\t\tname: 'XXL',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 7,\r\n\t\t\t\t\t\tpid: 2,\r\n\t\t\t\t\t\tname: '白色',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 8,\r\n\t\t\t\t\t\tpid: 2,\r\n\t\t\t\t\t\tname: '珊瑚粉',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 9,\r\n\t\t\t\t\t\tpid: 2,\r\n\t\t\t\t\t\tname: '草木绿',\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\t// 表单数据\r\n\t\t\t\tform: [],\r\n\t\t\t\t// 已填写数据\r\n\t\t\t\tform_data: {\r\n\t\t\t\t\r\n\t\t\t\t},\r\n\t\t\t\tis_show:true\n\t\t\t};\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.project_id = options.id;\r\n\t\t\tlet platform = this.$store.state.systemInfo.platform\r\n\t\t\tif(platform=='IOS'){\r\n\t\t\t\tthis.is_show = false;\r\n\t\t\t}\n\t\t\tthis.ready();\r\n\t\t\twx.showShareMenu({\r\n\t\t\t\t\twithShareTicket:true,\r\n\t\t\t\t\tmenus:[\"shareAppMessage\",\"shareTimeline\"]\r\n\t\t\t\t})\n\t\t},\r\n\t\tonShareAppMessage() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\ttitle: this.project.name,//标题\r\n\t\t\t\t\t\timageUrl: '',//封面\r\n\t\t\t\t\t\tpath: `/pages/product/product?id=${this.project_id}`//此处链接为要分享的页面链接\t\r\n\t\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// 分享到朋友圈\r\n\t\t\tonShareTimeline() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: this.project.name,//标题\r\n\t\t\t\t\timageUrl: this.project.picture,//封面\r\n\t\t\t\t\tpath: `/pages/product/product?id=${this.project_id}`//此处链接为要分享的页面链接\t\r\n\t\t\t\t};\r\n\t\t\t},\n\t\tfilters: {},\n\t\tmethods: {\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// action\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tready: function() {\n\t\t\t\tthis.apiGetDetail(this.project_id).then(data => {\r\n\t\t\t\t\tconsole.log(data)\n\t\t\t\t\tthis.project = data;\r\n\t\t\t\t\tthis.form = data.pt_config.items;\r\n\t\t\t\t\tthis.form_data = data.form ? data.form.json_data : {};\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// handler\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\tonApplyHandler: function() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/product/order/order?project_id=' + this.project_id\n\t\t\t\t})\n\t\t\t},\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t//\n\t\t\t// api\n\t\t\t//\n\t\t\t//-----------------------------------------------------------------------------------------------\n\t\t\t/**\n\t\t\t * 获取项目详情\n\t\t\t * @param {Object} id\n\t\t\t */\n\t\t\tapiGetDetail: function(id) {\n\t\t\t\treturn this.$http.get('/v1/project_detail', {\n\t\t\t\t\tparams: {\n\t\t\t\t\t\tid\n\t\t\t\t\t}\n\t\t\t\t}).then((res) => {\n\t\t\t\t\treturn Promise.resolve(res.data.data);\n\t\t\t\t});\n\t\t\t},\r\n\t\t\t//规格弹窗开关\r\n\t\t\ttoggleSpec() {\r\n\t\t\t\tif(this.specClass === 'show'){\r\n\t\t\t\t\tthis.specClass = 'hide';\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.specClass = 'none';\r\n\t\t\t\t\t}, 250);\r\n\t\t\t\t}else if(this.specClass === 'none'){\r\n\t\t\t\t\tthis.specClass = 'show';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\t//\r\n\t\t\t// hander\r\n\t\t\t//\r\n\t\t\t//-----------------------------------------------------------------------------------------------\r\n\t\t\tonFormSubmitHandler: function(e) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tif (!this.$store.state.user.token) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\tcontent: '登录后才能进行操作',\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\tself.reload = true;\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/login/login?type=wx\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar formdata = JSON.stringify(e.detail.value);\r\n\t\t\t\t\tfor (var i in e.detail.value) {\r\n\t\t\t\t\t\tlet v = e.detail.value[i];\r\n\t\t\t\t\t\tlet obj = this.form.find(o => o.field_name === i);\r\n\t\t\t\t\t\tconsole.log(obj);\r\n\t\t\t\t\t\tif(obj['is_show']==1){\r\n\t\t\t\t\t\t\tif ((v instanceof Array && v.length == 0) || (!v)) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon:'none',\r\n\t\t\t\t\t\t\t\t\ttitle: '请填写完整的报名信息！'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log('formdata', formdata, e.detail.value);\r\n\t\t\t\t\t// uni.redirectTo({\r\n\t\t\t\t\t// \turl: './pay_result?sn_order='\r\n\t\t\t\t\t// })\r\n\t\t\t\t\tuni.showLoading();\r\n\t\t\t\t\tthis.apiPay(this.project_id, this.project.price, formdata).then(data => {\r\n\t\t\t\t\t\tconsole.log(data);\r\n\t\t\t\t\t\t// 仅作为示例，非真实参数信息。\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\t\t\t...data.pay,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\turl: './pay_result?sn_order=' + data.sn_order\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\t\ttitle: '支付失败',\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tonFormResetHandler: function() {\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t/**\r\n\t\t\t * 创建订单\r\n\t\t\t * @param {Object} project_id\r\n\t\t\t * @param {Object} money\r\n\t\t\t * @param {Object} json_data\r\n\t\t\t */\r\n\t\t\tapiPay: function(project_id, money, json_data) {\r\n\t\t\t\treturn this.$http.post('/v1/sign_up', {\r\n\t\t\t\t\tproject_id,\r\n\t\t\t\t\tmoney,\r\n\t\t\t\t\tjson_data\r\n\t\t\t\t}).then((res) => {\r\n\t\t\t\t\treturn Promise.resolve(res.data.data);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgoService(){\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\turl: '/pages/service/service'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoShare: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/product/sign-in-share/sign-in-share?project_id=' + this.project_id\r\n\t\t\t\t});\r\n\t\t\t},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.panel {\n\t\tbackground: #fff;\n\t\tmargin-bottom: 30rpx;\n\n\t\t.head {\n\t\t\tfont-size: 34rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\n\t.root-box {\n\t\tcolor: #666;\n\t\tbackground: #f2f2f2;\n\t\tpadding-bottom: 120rpx;\n\n\t\t/* 基本信息 */\n\t\t.base-box {\n\t\t\t.cover-box {\n\t\t\t\t.cover {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 400rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.info-box {\n\t\t\t\tpadding: 30rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666;\n\n\t\t\t\tview {\n\t\t\t\t\tmargin-bottom: 5rpx;\n\t\t\t\t}\n\n\t\t\t\t.title {\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\n\t\t\t\t.label {\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\n\t\t\t\t.course_date {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* 讲师 */\n\t\t.teacher-box {\n\t\t\tpadding: 30rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\n\t\t\t.item {\n\t\t\t\tpadding: 30rpx 0;\n\t\t\t\tborder-bottom: solid 1px #eee;\n\t\t\t}\n\n\t\t\t.item:last-child {\n\t\t\t\tborder-bottom: unset;\n\t\t\t}\n\n\t\t\t.info-box {\n\t\t\t\t.name {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.avatar-box {\n\t\t\t\tmargin-right: 30rpx;\n\n\t\t\t\t.avatar {\n\t\t\t\t\twidth: 128rpx;\n\t\t\t\t\theight: 128rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/* 课程详情 */\n\t\t.content-box {\n\t\t\tpadding: 30rpx;\n\t\t}\n\n\t\t/* 立即报名 */\n\t\t.btn-box {\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tbackground: #fff;\n\t\t\tflex: 1;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\theight: 100rpx;\n\t\t\tborder-top: solid 1px #eee;\n\t\t\talign-items: stretch;\n\t\t\tpadding-left: 30rpx;\n\t\t\tline-height: 100rpx;\n\n\t\t\t.info {\n\t\t\t\tflex: 1;\n\t\t\t\ttext-align: center;\n\t\t\t\tcolor: #ff0000;\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tbackground-color: $uni-color-error;\n\t\t\t\tcolor: #fff;\n\t\t\t\tpadding: 0 80rpx;\n\t\t\t}\n\n\t\t}\n\t}\r\n\t/* 规格选择弹窗 */\r\n\t.attr-content{\r\n\t\tpadding: 10upx 30upx;\r\n\t\t.a-t{\r\n\t\t\tdisplay: flex;\r\n\t\t\timage{\r\n\t\t\t\twidth: 250upx;\r\n\t\t\t\theight: 130upx;\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t//margin-top: -40upx;\r\n\t\t\t\tborder-radius: 8upx;;\r\n\t\t\t}\r\n\t\t\t.right{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tpadding-left: 24upx;\r\n\t\t\t\tfont-size: $font-sm + 2upx;\r\n\t\t\t\tcolor: $font-color-base;\r\n\t\t\t\tline-height: 42upx;\r\n\t\t\t\t.price{\r\n\t\t\t\t\tfont-size: $font-lg;\r\n\t\t\t\t\tcolor: #FD6D24;\r\n\t\t\t\t\tmargin-bottom: 10upx;\r\n\t\t\t\t}\r\n\t\t\t\t.selected-text{\r\n\t\t\t\t\tmargin-right: 10upx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.attr-list{\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\tcolor: $font-color-base;\r\n\t\t\tpadding-top: 30upx;\r\n\t\t\tpadding-left: 10upx;\r\n\t\t}\r\n\t\t.item-list{\r\n\t\t\tpadding: 20upx 0 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\ttext{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground: #eee;\r\n\t\t\t\tmargin-right: 20upx;\r\n\t\t\t\tmargin-bottom: 20upx;\r\n\t\t\t\tborder-radius: 100upx;\r\n\t\t\t\tmin-width: 60upx;\r\n\t\t\t\theight: 60upx;\r\n\t\t\t\tpadding: 0 20upx;\r\n\t\t\t\tfont-size: $font-base;\r\n\t\t\t\tcolor: $font-color-dark;\r\n\t\t\t}\r\n\t\t\t.selected{\r\n\t\t\t\tbackground: #fbebee;\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t/*  弹出层 */\r\n\t.popup {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 99;\r\n\t\t\r\n\t\t&.show {\r\n\t\t\tdisplay: block;\r\n\t\t\t.mask{\r\n\t\t\t\tanimation: showPopup 0.2s linear both;\r\n\t\t\t}\r\n\t\t\t.layer {\r\n\t\t\t\tanimation: showLayer 0.2s linear both;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.hide {\r\n\t\t\t.mask{\r\n\t\t\t\tanimation: hidePopup 0.2s linear both;\r\n\t\t\t}\r\n\t\t\t.layer {\r\n\t\t\t\tanimation: hideLayer 0.2s linear both;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.none {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t\t.mask{\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 1;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t\t}\r\n\t\t.layer {\r\n\t\t\tposition: fixed;\r\n\t\t\tz-index: 99;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tmin-height: 40vh;\r\n\t\t\tborder-radius: 10upx 10upx 0 0;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t.btn{\r\n\t\t\t\theight: 66upx;\r\n\t\t\t\tline-height: 66upx;\r\n\t\t\t\tborder-radius: 100upx;\r\n\t\t\t\tbackground: $uni-color-primary;\r\n\t\t\t\tfont-size: $font-base + 2upx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin: 30upx auto 20upx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes showPopup {\r\n\t\t\t0% {\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes hidePopup {\r\n\t\t\t0% {\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\topacity: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes showLayer {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: translateY(120%);\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: translateY(0%);\r\n\t\t\t}\r\n\t\t}\r\n\t\t@keyframes hideLayer {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: translateY(0);\r\n\t\t\t}\r\n\t\t\t100% {\r\n\t\t\t\ttransform: translateY(120%);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.form {\r\n\t\tflex: 1;\r\n\t\tpadding: 5rpx 0;\r\n\t\r\n\t\t.form-item {\r\n\t\t\tpadding: 5rpx 5rpx;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\r\n\t\t\t.control-label {\r\n\t\t\t\twidth: 250rpx;\r\n\t\t\t}\r\n\t\r\n\t\t\t.input-box {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tborder: solid 1px #e5e5e5;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tpadding: 10rpx 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\t.btn-box {\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\tright: 0;\r\n\t\t\tleft: 0;\r\n\t\r\n\t\t\t.btn {\r\n\t\t\t\tbackground-color: $uni-color-error;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tborder-radius: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.btn1 {\r\n\t\t\t// position: fixed;\r\n\t\t\t// bottom: 0;\r\n\t\t\t// right: 0;\r\n\t\t\t// left: 0;\r\n\t\t\t// margin: 30rpx auto;\r\n\t\t\t\r\n\t\t\tbackground-color: #FD6D24;\r\n\t\t\twidth: 700rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t}\r\n\t}\r\n\t.buy-left {\r\n\t\t// display: flex;\r\n\t\t// flex-direction: column;\r\n\t\tjustify-content: space-evenly;\r\n\t\talign-items: center;\r\n\t\timage {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t}\r\n\t\ttext {\r\n\t\t\tmargin-top: 5rpx;\r\n\t\t\tmargin-left: 5rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #ff6229;\r\n\t\t}\r\n\t}\r\n\t.sharebtn {\r\n\t  margin: 0;\r\n\t  padding: 0;\r\n\t  outline: none;\r\n\t  border-radius: 0;\r\n\t  background-color: transparent;\r\n\t  line-height: inherit;\r\n\t  width: max-content;\r\n\t}\r\n\t/* 客服 */\r\n\t.kf_button{\r\n\t background-color: rgba(255, 255, 255, 0);\r\n\t border: 0px;\r\n\t height: 80rpx;\r\n\t left: 200;\r\n\t bottom: 5rpx;\r\n\t position: fixed;\r\n\t}\r\n\t.kf_button::after{\r\n\t border: 0px;\r\n\t}\r\n\t.kf_image{\r\n\t z-index: 9999;\r\n\t width: 50rpx;\r\n\t height: 50rpx;\r\n\t}\r\n\t\r\n\t/* 底部操作菜单 */\r\n\t.page-bottom{\r\n\t\tposition:fixed;\r\n\t\tleft: 0;\r\n\t\tbottom:0;\r\n\t\tz-index: 95;\r\n\t\tdisplay: flex;\r\n\t\t//justify-content: center;\r\n\t\talign-items: center;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-evenly;\r\n\t\twidth: 100%;\r\n\t\theight: 100upx;\r\n\t\tbackground: rgba(255,255,255,.9);\r\n\t\tbox-shadow: 0 0 20upx 0 rgba(0,0,0,.5);\r\n\t\tborder-radius: 16upx;\r\n\t\t\r\n\t\t.p-b-btn{\r\n\t\t\tdisplay:flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: $font-sm;\r\n\t\t\tcolor: $font-color-base;\r\n\t\t\t// width: 96upx;\r\n\t\t\theight: 80upx;\r\n\t\t\t.yticon{\r\n\t\t\t\tfont-size: 40upx;\r\n\t\t\t\tline-height: 48upx;\r\n\t\t\t\tcolor: $font-color-light;\r\n\t\t\t}\r\n\t\t\t&.active, &.active .yticon{\r\n\t\t\t\tcolor: $uni-color-primary;\r\n\t\t\t}\r\n\t\t\t.icon-fenxiang2{\r\n\t\t\t\tfont-size: 42upx;\r\n\t\t\t\ttransform: translateY(-2upx);\r\n\t\t\t}\r\n\t\t\t.icon-shoucang{\r\n\t\t\t\tfont-size: 46upx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.action-btn-group{\r\n\t\t\tdisplay: flex;\r\n\t\t\theight: 76upx;\r\n\t\t\tborder-radius: 50px;\r\n\t\t\toverflow: hidden;\r\n\t\t\tbox-shadow: 0 20upx 40upx -16upx #fa436a;\r\n\t\t\tbox-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);\r\n\t\t\tbackground: #FD6D24;\r\n\t\t\tmargin-left: 100upx;\r\n\t\t\tposition:relative;\r\n\t\t\t&:after{\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition:absolute;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tright: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\theight: 28upx;\r\n\t\t\t\twidth: 0;\r\n\t\t\t\tborder-right: 1px solid rgba(255,255,255,.5);\r\n\t\t\t}\r\n\t\t\t.action-btn{\r\n\t\t\t\tdisplay:flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 180upx;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tfont-size: $font-base ;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tborder-radius: 0;\r\n\t\t\t\tbackground: transparent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.contact-btn {\r\n\t  display: inline-block;\r\n\t  position: absolute;\r\n\t  width: 20%;\r\n\t  background: salmon;\r\n\t    opacity: 0;\r\n\t}\r\n\t.buy{\r\n\t\twidth: 300upx;\r\n\t\theight: 80upx;\r\n\t\tbackground-color: #FD6D24;\r\n\t\tborder-radius: 30upx;\r\n\t\tfont-size: 34upx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #fff;\r\n\t\t// border: 3upx solid #fff;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80upx;\r\n\r\n\t}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&id=2fbdbe34&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./product.vue?vue&type=style&index=0&id=2fbdbe34&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753662929649\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}