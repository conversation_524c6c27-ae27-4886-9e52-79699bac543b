/* 新增的现代化样式 */
.course-container.data-v-3e425c46 {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}
.search-header.data-v-3e425c46 {
  background: rgba(255, 255, 255, 0.95);
  padding: 20rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.search-box.data-v-3e425c46 {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.search-icon.data-v-3e425c46 {
  font-size: 32rpx;
  color: #999;
  margin-right: 15rpx;
}
.search-input.data-v-3e425c46 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.main-content.data-v-3e425c46 {
  flex: 1;
  display: flex;
  background: #fff;
}
.category-nav.data-v-3e425c46 {
  width: 200rpx;
  background: #f8f9fa;
  border-right: 1rpx solid #e9ecef;
}
.category-item.data-v-3e425c46 {
  position: relative;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #e9ecef;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}
.category-item.active.data-v-3e425c46 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
}
.category-indicator.data-v-3e425c46 {
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: #fff;
  border-radius: 0 3rpx 3rpx 0;
}
.category-name.data-v-3e425c46 {
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
}
.category-count.data-v-3e425c46 {
  font-size: 20rpx;
  opacity: 0.8;
  margin-top: 5rpx;
}
.course-list.data-v-3e425c46 {
  flex: 1;
  background: #fff;
}
.loading-container.data-v-3e425c46 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.loading-spinner.data-v-3e425c46 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  -webkit-animation: spin-data-v-3e425c46 1s linear infinite;
          animation: spin-data-v-3e425c46 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-text.data-v-3e425c46 {
  font-size: 28rpx;
  color: #999;
}
@-webkit-keyframes spin-data-v-3e425c46 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-3e425c46 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.course-groups.data-v-3e425c46 {
  padding: 20rpx;
}
.course-group.data-v-3e425c46 {
  margin-bottom: 30rpx;
}
.empty-state.data-v-3e425c46 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}
.empty-icon.data-v-3e425c46 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.empty-text.data-v-3e425c46 {
  font-size: 28rpx;
  color: #999;
}
.course-grid.data-v-3e425c46 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 20rpx;
  padding: 20rpx 0;
}
.course-card.data-v-3e425c46 {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.course-card.data-v-3e425c46:hover {
  -webkit-transform: translateY(-5rpx);
          transform: translateY(-5rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}
.course-cover.data-v-3e425c46 {
  position: relative;
  width: 100%;
  height: 180rpx;
  overflow: hidden;
}
.cover-image.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.course-card:hover .cover-image.data-v-3e425c46 {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.course-badge.data-v-3e425c46 {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background: #28a745;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}
.course-badge.premium.data-v-3e425c46 {
  background: #ff6b6b;
}
.course-info.data-v-3e425c46 {
  padding: 20rpx;
}
.course-title.data-v-3e425c46 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.course-meta.data-v-3e425c46 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.course-teacher.data-v-3e425c46 {
  font-size: 24rpx;
  color: #666;
}
.course-students.data-v-3e425c46 {
  font-size: 22rpx;
  color: #999;
}
.course-tags.data-v-3e425c46 {
  display: flex;
  gap: 10rpx;
}
.course-tag.data-v-3e425c46 {
  background: #f8f9fa;
  color: #666;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}
.search.data-v-3e425c46 {
  height: 90rpx;
  background-color: #4b89ff;
  padding: 0 20rpx;
  padding-top: 30rpx;
}
.search-input.data-v-3e425c46 {
  position: relative;
  display: flex;
}
.search-input text.data-v-3e425c46 {
  font-size: 31rpx;
  color: #fff;
  margin-top: 10rpx;
}
.search-input image.data-v-3e425c46 {
  width: 25rpx;
  height: 14rpx;
  margin: 25rpx 20rpx 0 10rpx;
}
.search-input input.data-v-3e425c46 {
  width: 710rpx;
  height: 63rpx;
  border: 0;
  background-color: #fff;
  border-radius: 63rpx;
  font-size: 24rpx;
  padding-left: 20rpx;
  box-sizing: border-box;
}
.search-input .goods-search.data-v-3e425c46 {
  width: 28rpx;
  height: 28rpx;
  position: absolute;
  right: 10rpx;
  top: -5rpx;
  z-index: 99;
}
.scroll.data-v-3e425c46 {
  height: calc(100vh - 5rpx);
  display: flex;
}
.scroll-left.data-v-3e425c46 {
  flex: 2;
}
.scroll-left view.data-v-3e425c46 {
  height: 120rpx;
  background-color: #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  letter-spacing: 2rpx;
}
.scroll-left .active.data-v-3e425c46 {
  background-color: #fff;
}
.scroll-right.data-v-3e425c46 {
  flex: 5;
  background-color: #fff;
  padding: 0 10rpx;
  box-sizing: border-box;
}
.scroll-right .item.data-v-3e425c46 {
  display: inline-block;
  width: 220rpx;
  height: 60rpx;
  background-color: #eee;
  text-align: center;
  line-height: 60rpx;
  border-radius: 60rpx;
  margin: 20rpx 12rpx 0;
}
.scroll-right .item text.data-v-3e425c46 {
  font-size: 30rpx;
  font-weight: 700;
  letter-spacing: 4rpx;
  color: #333;
}
.list-box .item-box.data-v-3e425c46 {
  padding: 10rpx 10rpx;
  background-color: #fff;
  border-radius: 10rpx;
  color: #666;
  font-size: 28rpx;
  border-bottom: 1rpx solid #ebebeb;
}
.list-box .item-box .top-box.data-v-3e425c46 {
  position: relative;
  padding: 20rpx;
}
.list-box .item-box .top-box .cover-box-hot.data-v-3e425c46 {
  width: 35%;
  height: auto;
  min-height: 120rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box-hot .cover.data-v-3e425c46 :after {
  background-color: red;
  border-radius: 10rpx;
  color: #fff;
  content: "hot";
  font-size: 25rpx;
  line-height: 1;
  padding: 2rpx 6rpx;
  position: absolute;
  left: 5rpx;
  top: 5rpx;
}
.list-box .item-box .top-box .cover-box-hot .button.data-v-3e425c46 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-box.data-v-3e425c46 {
  width: 150rpx;
  height: auto;
  min-height: 150rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-box .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-box .button.data-v-3e425c46 {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: blue;
  color: white;
  padding: 5rpx 10rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .cover-large-box.data-v-3e425c46 {
  width: 100%;
  height: auto;
  height: 200rpx;
  position: relative;
}
.list-box .item-box .top-box .cover-large-box .cover.data-v-3e425c46 {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.list-box .item-box .top-box .cover-large-box .button.data-v-3e425c46 {
  position: absolute;
  bottom: 0;
  right: 0;
  -webkit-transform: translateX(0);
          transform: translateX(0);
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: white;
  padding: 15rpx 20rpx;
  font-size: 20rpx;
}
.list-box .item-box .top-box .info-box.data-v-3e425c46 {
  flex: 1;
  margin-left: 15rpx;
  height: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
.list-box .item-box .top-box .info-box .publish-date.data-v-3e425c46 {
  font-size: 32rpx;
  font-weight: bold;
}
.list-box .item-box .top-box .info-box .lang-box.data-v-3e425c46 {
  font-size: 24rpx;
}
.list-box .item-box .top-box .info-box .title.data-v-3e425c46 {
  font-weight: bold;
  font-size: 24rpx;
  color: #666666;
}
.list-box .item-box .top-box .info-box .end-date.data-v-3e425c46 {
  font-size: 20rpx;
  color: #999999;
}
.list-box .item-box .top-box .info-box .total.data-v-3e425c46 {
  font-size: 20rpx;
  color: #39b54a;
}
.list-box .item-box .top-box .info-box .des.data-v-3e425c46 {
  font-size: 22rpx;
  color: #8f8f94;
}
.list-box .item-box .top-box .info-box .price.data-v-3e425c46 {
  font-size: 24rpx;
  color: red;
  float: right;
}
.list-box .item-box .top-box .info-box .end.data-v-3e425c46 {
  font-size: 24rpx;
  color: blue;
  width: 100%;
}
.list-box .item-box .margin-tb-sm.data-v-3e425c46 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.list-box .item-box .margin-tb-sm .text-sm.data-v-3e425c46 {
  font-size: 24rpx;
}
.list-box .item-box .margin-tb-sm .title.data-v-3e425c46 {
  overflow: hidden;
  word-break: break-all;
  /* break-all(允许在单词内换行。) */
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
.list-box .item-box .margin-tb-sm .uni-row.data-v-3e425c46 {
  flex-direction: row;
}
.list-box .item-box .margin-tb-sm .align-center.data-v-3e425c46 {
  align-items: center;
}
.list-box .item-box .margin-tb-sm .margin-left-sm.data-v-3e425c46 {
  margin-left: 20rpx;
}

