@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-04522d66 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-04522d66 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-04522d66 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-04522d66 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-04522d66 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-04522d66 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-04522d66::after {
  border: none;
}
.u-hover-class.data-v-04522d66 {
  opacity: 0.7;
}
.u-primary-light.data-v-04522d66 {
  color: #ecf5ff;
}
.u-warning-light.data-v-04522d66 {
  color: #fdf6ec;
}
.u-success-light.data-v-04522d66 {
  color: #f5fff0;
}
.u-error-light.data-v-04522d66 {
  color: #fef0f0;
}
.u-info-light.data-v-04522d66 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-04522d66 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-04522d66 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-04522d66 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-04522d66 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-04522d66 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-04522d66 {
  color: #398ade;
}
.u-warning-dark.data-v-04522d66 {
  color: #f1a532;
}
.u-success-dark.data-v-04522d66 {
  color: #53c21d;
}
.u-error-dark.data-v-04522d66 {
  color: #e45656;
}
.u-info-dark.data-v-04522d66 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-04522d66 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-04522d66 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-04522d66 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-04522d66 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-04522d66 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-04522d66 {
  color: #9acafc;
}
.u-warning-disabled.data-v-04522d66 {
  color: #f9d39b;
}
.u-success-disabled.data-v-04522d66 {
  color: #a9e08f;
}
.u-error-disabled.data-v-04522d66 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-04522d66 {
  color: #c4c6c9;
}
.u-primary.data-v-04522d66 {
  color: #3c9cff;
}
.u-warning.data-v-04522d66 {
  color: #f9ae3d;
}
.u-success.data-v-04522d66 {
  color: #5ac725;
}
.u-error.data-v-04522d66 {
  color: #f56c6c;
}
.u-info.data-v-04522d66 {
  color: #909399;
}
.u-primary-bg.data-v-04522d66 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-04522d66 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-04522d66 {
  background-color: #5ac725;
}
.u-error-bg.data-v-04522d66 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-04522d66 {
  background-color: #909399;
}
.u-main-color.data-v-04522d66 {
  color: #303133;
}
.u-content-color.data-v-04522d66 {
  color: #606266;
}
.u-tips-color.data-v-04522d66 {
  color: #909193;
}
.u-light-color.data-v-04522d66 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-04522d66 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-04522d66 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-04522d66 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-04522d66 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-04522d66::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.panel.data-v-04522d66 {
  margin-bottom: 50rpx;
}
.panel .head.data-v-04522d66 {
  display: flex;
  font-size: 32rpx;
  margin: 30rpx;
  font-weight: bold;
}
.panel .body.data-v-04522d66 {
  padding: 30rpx;
}
.panel .title-icon.data-v-04522d66 {
  width: 10rpx;
  background-color: #007aff;
  margin-right: 20rpx;
}
.panel .des.data-v-04522d66 {
  font-size: 24rpx;
  font-weight: 100;
  color: #999;
}
.root.data-v-04522d66 {
  display: flex;
}
.root-box.data-v-04522d66 {
  height: 1000rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 文章内容 */
  /* 打卡 */
  /* 翻译 */
  /* 点评 */
}
.root-box .main-box .head-box .left-box.data-v-04522d66 {
  display: flex;
  flex: 1;
}
.root-box .main-box .head-box .left-box .lang.data-v-04522d66 {
  background: #aaa;
  padding: 5rpx 15rpx;
  font-size: 24rpx;
  border-radius: 10rpx;
  color: #fff;
  font-weight: unset;
  margin: 0 10rpx;
}
.root-box .main-box .head-box .right-box .gui-icons.data-v-04522d66 {
  color: #dd524d;
}
.root-box .main-box .title-box.data-v-04522d66 {
  font-size: 40rpx;
}
.root-box .main-box .cover-box.data-v-04522d66 {
  margin: 30rpx 0;
  height: 600rpx;
}
.root-box .main-box .cover-box .cover.data-v-04522d66 {
  width: 100%;
  height: 100%;
}
.root-box .main-box .content-box .rich-box.data-v-04522d66 {
  margin-bottom: 20rpx;
}
.root-box .main-box .content-foot-box.data-v-04522d66 {
  border-top: solid 1px #eee;
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #aaa;
  line-height: 40rpx;
  margin-top: 20rpx;
}
.root-box .main-box .declaration.data-v-04522d66 {
  color: #dd524d;
}
.root-box .sign-in-box .sign-in-time.data-v-04522d66 {
  color: #999;
  text-align: center;
  font-size: 24rpx;
  line-height: 50rpx;
}
.root-box .sign-in-box .btn-box.data-v-04522d66 {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.root-box .sign-in-box .btn-box .btn.data-v-04522d66 {
  border: solid 1px #dd524d;
  background-color: #dd524d;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx;
  text-align: center;
  border-radius: 40rpx;
}
.root-box .sign-in-box .btn-box .done.data-v-04522d66 {
  background-color: unset;
  color: #dd524d;
}
.root-box .tran-box .title-box.data-v-04522d66 {
  display: flex;
  align-items: flex-end;
}
.root-box .comment-box .content-box.data-v-04522d66 {
  font-size: 28rpx;
  border: solid 1px #eee;
  padding: 30rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  color: #999;
}
.nopublish.data-v-04522d66 {
  height: 400rpx;
  text-align: center;
  color: #c5c5c5;
  font-size: 28rpx;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 页面左右间距 */
/* 水平间距 */
/* 水平间距 */
.u-line-1.data-v-b90080f4 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2.data-v-b90080f4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3.data-v-b90080f4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4.data-v-b90080f4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5.data-v-b90080f4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-reset-button.data-v-b90080f4 {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button.data-v-b90080f4::after {
  border: none;
}
.u-hover-class.data-v-b90080f4 {
  opacity: 0.7;
}
.u-primary-light.data-v-b90080f4 {
  color: #ecf5ff;
}
.u-warning-light.data-v-b90080f4 {
  color: #fdf6ec;
}
.u-success-light.data-v-b90080f4 {
  color: #f5fff0;
}
.u-error-light.data-v-b90080f4 {
  color: #fef0f0;
}
.u-info-light.data-v-b90080f4 {
  color: #f4f4f5;
}
.u-primary-light-bg.data-v-b90080f4 {
  background-color: #ecf5ff;
}
.u-warning-light-bg.data-v-b90080f4 {
  background-color: #fdf6ec;
}
.u-success-light-bg.data-v-b90080f4 {
  background-color: #f5fff0;
}
.u-error-light-bg.data-v-b90080f4 {
  background-color: #fef0f0;
}
.u-info-light-bg.data-v-b90080f4 {
  background-color: #f4f4f5;
}
.u-primary-dark.data-v-b90080f4 {
  color: #398ade;
}
.u-warning-dark.data-v-b90080f4 {
  color: #f1a532;
}
.u-success-dark.data-v-b90080f4 {
  color: #53c21d;
}
.u-error-dark.data-v-b90080f4 {
  color: #e45656;
}
.u-info-dark.data-v-b90080f4 {
  color: #767a82;
}
.u-primary-dark-bg.data-v-b90080f4 {
  background-color: #398ade;
}
.u-warning-dark-bg.data-v-b90080f4 {
  background-color: #f1a532;
}
.u-success-dark-bg.data-v-b90080f4 {
  background-color: #53c21d;
}
.u-error-dark-bg.data-v-b90080f4 {
  background-color: #e45656;
}
.u-info-dark-bg.data-v-b90080f4 {
  background-color: #767a82;
}
.u-primary-disabled.data-v-b90080f4 {
  color: #9acafc;
}
.u-warning-disabled.data-v-b90080f4 {
  color: #f9d39b;
}
.u-success-disabled.data-v-b90080f4 {
  color: #a9e08f;
}
.u-error-disabled.data-v-b90080f4 {
  color: #f7b2b2;
}
.u-info-disabled.data-v-b90080f4 {
  color: #c4c6c9;
}
.u-primary.data-v-b90080f4 {
  color: #3c9cff;
}
.u-warning.data-v-b90080f4 {
  color: #f9ae3d;
}
.u-success.data-v-b90080f4 {
  color: #5ac725;
}
.u-error.data-v-b90080f4 {
  color: #f56c6c;
}
.u-info.data-v-b90080f4 {
  color: #909399;
}
.u-primary-bg.data-v-b90080f4 {
  background-color: #3c9cff;
}
.u-warning-bg.data-v-b90080f4 {
  background-color: #f9ae3d;
}
.u-success-bg.data-v-b90080f4 {
  background-color: #5ac725;
}
.u-error-bg.data-v-b90080f4 {
  background-color: #f56c6c;
}
.u-info-bg.data-v-b90080f4 {
  background-color: #909399;
}
.u-main-color.data-v-b90080f4 {
  color: #303133;
}
.u-content-color.data-v-b90080f4 {
  color: #606266;
}
.u-tips-color.data-v-b90080f4 {
  color: #909193;
}
.u-light-color.data-v-b90080f4 {
  color: #c0c4cc;
}
.u-safe-area-inset-top.data-v-b90080f4 {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right.data-v-b90080f4 {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom.data-v-b90080f4 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left.data-v-b90080f4 {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
.data-v-b90080f4::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 文字尺寸 */
/*文字颜色*/
/* 边框颜色 */
/* 图片加载中颜色 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.root-box.data-v-b90080f4 {
  display: flex;
  margin: 0rpx;
}
.root-box .head-box .nav-box.data-v-b90080f4 {
  display: flex;
  justify-content: center;
}
.root-box .head-box .nav-box .nav-btn.data-v-b90080f4 {
  border: solid 1px #007aff;
  border-radius: 40rpx 0 0 40rpx;
  padding: 8rpx 30rpx;
  font-size: 28rpx;
  color: #007aff;
}
.root-box .head-box .nav-box .nav-btn.data-v-b90080f4:last-child {
  border-radius: 0 40rpx 40rpx 0;
}
.root-box .head-box .nav-box .active.data-v-b90080f4 {
  background-color: #007aff;
  color: #fff;
}
.root-box .content-box.data-v-b90080f4 {
  flex: 1;
  border-top: solid 1px #eee;
  margin: 0;
}
.root-box .content-box .swiper.data-v-b90080f4 {
  height: 100%;
}
.gui-header-content.data-v-b90080f4 {
  width: 100%;
  flex: 1;
  text-align: center;
  margin: 0;
}
.nav-box.data-v-b90080f4 {
  display: flex;
  justify-content: center;
  border-bottom: solid 1px #eeeeee;
}
.nav-box .nav-btn.data-v-b90080f4 {
  border: solid 1px #007aff;
  border-radius: 40rpx 0 0 40rpx;
  padding: 8rpx 30rpx;
  font-size: 35rpx;
  color: #007aff;
}
.nav-box .nav-btn.data-v-b90080f4:last-child {
  border-radius: 0 40rpx 40rpx 0;
}
.nav-box .active.data-v-b90080f4 {
  background-color: #007aff;
  color: #fff;
}
.gui-align-items-center.data-v-b90080f4 {
  align-items: center;
}
.gui-rows.data-v-b90080f4 {
  flex-direction: row;
}
.gui-nowrap.data-v-b90080f4 {
  flex-direction: row;
  flex-wrap: nowrap;
}
.gui-flex.data-v-b90080f4 {
  display: flex;
}
.gui-padding.data-v-b90080f4 {
  padding-left: 30rpx;
  padding-right: 30rpx;
}
/* 左右内间距 */

