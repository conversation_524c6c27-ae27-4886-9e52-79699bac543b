<view class="container data-v-00a52fe6"><block wx:if="{{type==1}}"><view class="data-v-00a52fe6"><view class="scroll data-v-00a52fe6"><scroll-view class="scroll-left data-v-00a52fe6" scroll-y="true"><block wx:for="{{classifyList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['postSonflData',['$0',index],[[['classifyList','',index,'id']]]]]]]}}" class="{{['data-v-00a52fe6',(activeIndex===index)?'active':'']}}" bindtap="__e"><block wx:if="{{activeIndex===index}}"><text style="color:blue;margin-right:10rpx;font-weight:bold;" class="data-v-00a52fe6">|</text></block>{{item.name}}</view></block></scroll-view><scroll-view class="scroll-right data-v-00a52fe6" scroll-y="true"><block wx:if="{{son_fls.type==1}}"><uni-section vue-id="14014de0-1" class="data-v-00a52fe6" bind:__l="__l" vue-slots="{{['default']}}"><uni-collapse bind:input="__e" vue-id="{{('14014de0-2')+','+('14014de0-1')}}" data-ref="collapse" value="{{value}}" data-event-opts="{{[['^input',[['__set_model',['','value','$event',[]]]]]]}}" class="data-v-00a52fe6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><uni-collapse-item vue-id="{{('14014de0-3-'+index)+','+('14014de0-2')}}" title="{{item.$orig.name}}" class="data-v-00a52fe6" bind:__l="__l" vue-slots="{{['default']}}"><view class="list-box data-v-00a52fe6"><block wx:if="{{item.g0===0}}"><empty1 vue-id="{{('14014de0-4-'+index)+','+('14014de0-3-'+index)}}" class="data-v-00a52fe6" bind:__l="__l"></empty1></block><block wx:for="{{item.$orig.class}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['son_fls.class','',index],['class','',index1]]]]]]]}}" class="item-box lp-flex-column data-v-00a52fe6" bindtap="__e"><view class="top-box lp-flex data-v-00a52fe6"><view class="cover-box lp-flex-center data-v-00a52fe6"><image class="cover data-v-00a52fe6" src="{{item1.picture}}"></image></view><view class="info-box lp-flex-column data-v-00a52fe6"><view class="title data-v-00a52fe6">{{item1.title}}</view></view></view></view></block></view></uni-collapse-item></block></uni-collapse></uni-section></block><block wx:else><view class="list-box data-v-00a52fe6"><block wx:if="{{$root.g1===0}}"><empty1 vue-id="14014de0-5" class="data-v-00a52fe6" bind:__l="__l"></empty1></block><block wx:for="{{son_fls.class}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['son_fls.class','',index1]]]]]]]}}" class="item-box lp-flex-column data-v-00a52fe6" bindtap="__e"><view class="top-box lp-flex data-v-00a52fe6"><view class="cover-box lp-flex-center data-v-00a52fe6"><image class="cover data-v-00a52fe6" src="{{item1.picture}}"></image></view><view class="info-box lp-flex-column data-v-00a52fe6"><view class="title data-v-00a52fe6">{{item1.title}}</view></view></view></view></block></view></block></scroll-view></view></view></block><block wx:else><view class="data-v-00a52fe6"><view class="list-box data-v-00a52fe6"><block wx:if="{{$root.g2===0}}"><empty1 vue-id="14014de0-6" class="data-v-00a52fe6" bind:__l="__l"></empty1></block><block wx:for="{{classifyList}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['navToDetailPage',['$0'],[[['classifyList','',index1]]]]]]]}}" class="item-box lp-flex-column data-v-00a52fe6" bindtap="__e"><view class="top-box lp-flex data-v-00a52fe6"><view class="cover-box lp-flex-center data-v-00a52fe6"><image class="cover data-v-00a52fe6" src="{{item1.picture}}"></image></view><view class="info-box lp-flex-column data-v-00a52fe6"><view class="title data-v-00a52fe6">{{item1.title}}</view></view></view></view></block></view></view></block></view>