# 优化功能快速使用指南

## 🚀 立即开始使用优化功能

### 1. 🔍 **搜索功能优化**

#### 已优化的搜索页面
- **页面位置**：`pages/category/search.vue`
- **功能特点**：防抖搜索、智能缓存、搜索建议、关键词高亮

#### 使用方法
```javascript
// 搜索页面已自动集成，用户直接使用即可体验：
// 1. 输入关键词时会显示搜索建议
// 2. 搜索结果会高亮显示关键词
// 3. 重复搜索会使用缓存，响应更快
// 4. 搜索历史会自动保存和管理
```

#### 在其他页面中使用搜索优化器
```javascript
import { SearchOptimizer } from '@/utils/search-optimizer.js';

export default {
  data() {
    return {
      searchOptimizer: new SearchOptimizer()
    };
  },
  methods: {
    handleSearch(keyword) {
      // 防抖搜索，500ms延迟
      this.searchOptimizer.debounceSearch(keyword, (result) => {
        console.log('搜索结果:', result);
      });
    },
    
    getSuggestions(keyword) {
      // 获取搜索建议
      return this.searchOptimizer.getSuggestions(keyword);
    }
  }
};
```

### 2. 🎬 **视频播放优化**

#### 已优化的播放页面
- **页面位置**：`pages/course/course.vue`
- **功能特点**：智能预加载、质量自适应、断点续播、自定义控制

#### 使用优化的视频播放器
```vue
<template>
  <optimized-video-player 
    :src="videoUrl" 
    :poster="videoPoster"
    :video-id="currentVideoId"
    :course-id="courseId"
    :autoplay="true"
    :show-custom-controls="true"
    @play="handlePlay"
    @pause="handlePause"
    @timeupdate="handleTimeUpdate"
    @request-next-video="handlePreloadNext"
  />
</template>

<script>
import OptimizedVideoPlayer from '@/components/optimized-video-player.vue';

export default {
  components: {
    OptimizedVideoPlayer
  },
  methods: {
    handlePlay() {
      console.log('视频开始播放');
    },
    
    handlePreloadNext(callback) {
      // 提供下一个视频URL进行预加载
      const nextVideoUrl = this.getNextVideoUrl();
      if (callback) callback(nextVideoUrl);
    }
  }
};
</script>
```

### 3. 🛡️ **错误处理**

#### 统一错误处理
```javascript
import ErrorHandler from '@/utils/error-handler.js';

// 在任何地方处理错误
try {
  await someAsyncOperation();
} catch (error) {
  ErrorHandler.handle(error, '操作上下文');
}

// 包装异步函数自动处理错误
const safeAsyncFunction = ErrorHandler.wrapAsync(async () => {
  // 你的异步操作
}, '函数上下文');

// 带重试的操作
const result = await ErrorHandler.retry(async () => {
  return await riskyOperation();
}, 3, 1000); // 重试3次，间隔1秒
```

### 4. 💾 **内存管理**

#### 在页面中使用cleanup mixin
```javascript
import cleanup from '@/mixins/cleanup.js';

export default {
  mixins: [cleanup], // 自动防止内存泄漏
  
  methods: {
    loadData() {
      // 使用安全的定时器（会自动清理）
      this.$setTimeout(() => {
        console.log('定时器执行');
      }, 1000);
      
      // 使用安全的网络请求（会自动取消）
      this.$request({
        url: '/api/data',
        success: (res) => {
          console.log(res);
        }
      });
    }
  }
};
```

### 5. 📊 **性能监控**

#### 监控页面性能
```javascript
import Performance from '@/utils/performance.js';

export default {
  onLoad() {
    // 开始监控页面加载
    const endPageLoad = Performance.trackPageLoad('course-detail');
    
    this.$nextTick(() => {
      // 页面加载完成
      endPageLoad();
    });
  },
  
  methods: {
    async loadData() {
      // 监控API调用性能
      return Performance.trackApiCall('loadCourseData', async () => {
        return await this.$http.get('/api/course');
      });
    }
  }
};
```

### 6. 🖼️ **优化图片组件**

#### 使用通用图片组件
```vue
<template>
  <!-- 基础使用 -->
  <common-image 
    :src="imageUrl" 
    width="200rpx" 
    height="150rpx"
  />
  
  <!-- 高级配置 -->
  <common-image 
    :src="imageUrl" 
    width="300rpx" 
    height="200rpx"
    :lazy-load="true"
    :show-retry="true"
    :max-retries="3"
    border-radius="10rpx"
    quality="high"
    @load="handleImageLoad"
    @error="handleImageError"
  />
</template>
```

### 7. 🌐 **统一请求**

#### 使用优化的请求库
```javascript
import request from '@/utils/unified-request.js';

// 基础请求（自动缓存GET请求）
const data = await request.get('/api/courses', { page: 1 });

// 带重试的重要请求
const result = await request.requestWithRetry({
  url: '/api/payment',
  method: 'POST',
  data: paymentData,
  retryCount: 3
});

// 文件上传
const uploadResult = await request.upload('/api/upload', filePath, {
  name: 'file',
  formData: { userId: 123 },
  onProgress: (progress) => {
    console.log('上传进度:', progress);
  }
});
```

## 🧪 **测试优化效果**

### 访问测试页面
1. 在项目中导航到测试页面（需要在pages.json中添加路由）
2. 或者直接在代码中测试各项功能

### 测试搜索优化
```javascript
// 在搜索页面中测试
// 1. 快速输入关键词，观察防抖效果
// 2. 重复搜索相同关键词，观察缓存效果
// 3. 查看搜索建议和历史记录功能
```

### 测试视频优化
```javascript
// 在课程播放页面中测试
// 1. 观察视频加载速度
// 2. 测试质量切换功能
// 3. 验证断点续播功能
// 4. 检查播放控制的流畅性
```

## 📈 **监控优化效果**

### 性能指标
```javascript
// 获取性能报告
import Performance from '@/utils/performance.js';
const report = Performance.getPerformanceReport();
console.log('性能报告:', report);

// 监控内存使用
const memoryInfo = Performance.getMemoryUsage();
console.log('内存使用:', memoryInfo);
```

### 搜索统计
```javascript
// 获取搜索统计
import { SearchOptimizer } from '@/utils/search-optimizer.js';
const searchOptimizer = new SearchOptimizer();
const stats = searchOptimizer.getSearchStats();
console.log('搜索统计:', stats);
```

### 视频统计
```javascript
// 获取播放统计
import { VideoOptimizer } from '@/utils/video-optimizer.js';
const videoOptimizer = new VideoOptimizer();
const stats = videoOptimizer.getPlaybackStats();
console.log('播放统计:', stats);
```

## 🔧 **常见问题解决**

### Q: 搜索建议不显示？
A: 确保已正确导入SearchOptimizer并在onLoad中初始化

### Q: 视频播放卡顿？
A: 检查网络状况，优化器会自动降低质量

### Q: 内存使用过高？
A: 确保所有页面都使用了cleanup mixin

### Q: 图片加载失败？
A: common-image组件会自动重试，检查图片URL是否正确

## 📞 **获取帮助**

- 查看详细文档：`docs/` 目录
- 参考测试页面：`pages/test/optimization-test.vue`
- 检查工具源码：`utils/` 目录

---

**开始使用这些优化功能，立即提升您的应用性能和用户体验！**
