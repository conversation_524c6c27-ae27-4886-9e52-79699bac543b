
.practice-list.data-v-e1193002 {
	padding: 30rpx;
}
.list-header.data-v-e1193002 {
	text-align: center;
	margin-bottom: 40rpx;
}
.header-title.data-v-e1193002 {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.header-desc.data-v-e1193002 {
	font-size: 28rpx;
	color: #666;
}
.practice-item.data-v-e1193002 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.item-icon.data-v-e1193002 {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}
.item-icon .iconfont.data-v-e1193002 {
	color: #fff;
	font-size: 36rpx;
}
.item-info.data-v-e1193002 {
	flex: 1;
}
.item-title.data-v-e1193002 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.item-desc.data-v-e1193002 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
}
.item-meta.data-v-e1193002 {
	display: flex;
	gap: 15rpx;
}
.meta-duration.data-v-e1193002 {
	font-size: 24rpx;
	color: #999;
}
.meta-difficulty.data-v-e1193002 {
	font-size: 24rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.meta-difficulty.easy.data-v-e1193002 {
	background: #e8f5e8;
	color: #52c41a;
}
.meta-difficulty.medium.data-v-e1193002 {
	background: #fff7e6;
	color: #fa8c16;
}
.meta-difficulty.hard.data-v-e1193002 {
	background: #fff2f0;
	color: #ff4d4f;
}
.item-status.data-v-e1193002 {
	margin-left: 20rpx;
}
.status-score.data-v-e1193002 {
	font-size: 28rpx;
	font-weight: bold;
	color: #2094CE;
}
.status-new.data-v-e1193002 {
	font-size: 24rpx;
	color: #fff;
	background: #ff4d4f;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}
.practice-content.data-v-e1193002 {
	padding: 30rpx;
}
.progress-header.data-v-e1193002 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.progress-info.data-v-e1193002 {
	text-align: center;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}
.progress-bar.data-v-e1193002 {
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}
.progress-fill.data-v-e1193002 {
	height: 100%;
	background: #2094CE;
	transition: width 0.3s;
}
.audio-player.data-v-e1193002 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.audio-controls.data-v-e1193002 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.play-btn.data-v-e1193002 {
	width: 80rpx;
	height: 80rpx;
	background: #2094CE;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}
.play-btn .iconfont.data-v-e1193002 {
	color: #fff;
	font-size: 36rpx;
}
.audio-info.data-v-e1193002 {
	flex: 1;
}
.audio-title.data-v-e1193002 {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 8rpx;
}
.audio-time.data-v-e1193002 {
	font-size: 24rpx;
	color: #999;
}
.speed-btn.data-v-e1193002 {
	padding: 10rpx 20rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
}
.audio-progress.data-v-e1193002 {
	height: 6rpx;
	background: #f0f0f0;
	border-radius: 3rpx;
	overflow: hidden;
}
.progress-track.data-v-e1193002 {
	height: 100%;
	position: relative;
}
.progress-played.data-v-e1193002 {
	height: 100%;
	background: #2094CE;
}
.question-content.data-v-e1193002 {
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}
.question-title.data-v-e1193002 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}
.option-item.data-v-e1193002 {
	display: flex;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 15rpx;
	border: 2rpx solid #f0f0f0;
	border-radius: 16rpx;
	transition: all 0.3s;
}
.option-item.selected.data-v-e1193002 {
	border-color: #2094CE;
	background: #f0f9ff;
}
.option-item.correct.data-v-e1193002 {
	border-color: #52c41a;
	background: #f6ffed;
}
.option-item.wrong.data-v-e1193002 {
	border-color: #ff4d4f;
	background: #fff2f0;
}
.option-label.data-v-e1193002 {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 20rpx;
}
.option-item.selected .option-label.data-v-e1193002 {
	background: #2094CE;
	color: #fff;
}
.option-text.data-v-e1193002 {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}
.action-buttons.data-v-e1193002 {
	display: flex;
	gap: 20rpx;
}
.btn-secondary.data-v-e1193002, .btn-primary.data-v-e1193002 {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	border: none;
}
.btn-secondary.data-v-e1193002 {
	background: #f0f0f0;
	color: #666;
}
.btn-primary.data-v-e1193002 {
	background: #2094CE;
	color: #fff;
}
.result-page.data-v-e1193002 {
	text-align: center;
	padding: 60rpx 30rpx;
}
.result-header.data-v-e1193002 {
	margin-bottom: 60rpx;
}
.result-score.data-v-e1193002 {
	font-size: 120rpx;
	font-weight: bold;
	color: #2094CE;
}
.result-text.data-v-e1193002 {
	font-size: 48rpx;
	color: #666;
}
.result-details.data-v-e1193002 {
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
}
.detail-item.data-v-e1193002 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.detail-item.data-v-e1193002:last-child {
	border-bottom: none;
}
.detail-label.data-v-e1193002 {
	font-size: 32rpx;
	color: #666;
}
.detail-value.data-v-e1193002 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.result-actions.data-v-e1193002 {
	display: flex;
	gap: 20rpx;
}

